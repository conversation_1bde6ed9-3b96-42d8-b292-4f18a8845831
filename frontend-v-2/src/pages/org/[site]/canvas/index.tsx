import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import Seo from "@/components/Seo";
import AppLayout from "@/components/Layout/AppLayout";
import LoadingIndicator from "@/components/LoadingIndicator";
import useModal from "@/hooks/useModal";
import CanvasDrawer from "@/components/Canvas/CanvasDrawer";
import CommandControl from "@/components/Canvas/CommandControl";
import RedTeam from "@/components/Canvas/RedTeam";
import Integrations from "@/components/Canvas/Integrations";

const CanvasPage = () => {
  const { open, handleOpen, handleClose } = useModal();

  const [type, setType] = useState<string>("");
  const [isIframeLoading, setIsIframeLoading] = useState<boolean>(true);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== process.env.NEXT_PUBLIC_NODE_FLOW_DOMAIN) return;

      switch (event.data.type) {
        case "COMMAND_CONTROL":
        case "RED_TEAM":
        case "INTEGRATIONS":
          setType(event.data.type);
          handleOpen();
          break;
        default:
          setType("");
          break;
      }
    };

    window.addEventListener("message", handleMessage);

    return () => window.removeEventListener("message", handleMessage);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <AppLayout>
      <Seo templateTitle="Canvas" />

      {isIframeLoading && (
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            zIndex: 10,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "background.default",
          }}
        >
          <LoadingIndicator />
        </Box>
      )}

      <Box
        component="iframe"
        src={process.env.NEXT_PUBLIC_NODE_FLOW_DOMAIN}
        sx={{
          flex: 1,
          width: "100%",
          height: "100%",
          p: 0,
          m: 0,
          border: 0,
        }}
        onLoad={() => setIsIframeLoading(false)}
      />

      <CanvasDrawer open={open} handleClose={handleClose}>
        {type === "COMMAND_CONTROL" && <CommandControl />}
        {type === "RED_TEAM" && <RedTeam />}
        {type === "INTEGRATIONS" && <Integrations />}
      </CanvasDrawer>
    </AppLayout>
  );
};

export default CanvasPage;
