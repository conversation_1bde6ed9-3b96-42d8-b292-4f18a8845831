import React, { useState } from "react";
import { Tab, Tabs, Typography } from "@mui/material";
import { TABS } from "@/components/Integration/constants";
import FullPageIntegration from "@/pages/org/[site]/setting/integration/full-page";
import FloatingChatWidgetIntegration from "@/pages/org/[site]/setting/integration/floating-chat-widget";
import DatabaseIntegration from "@/pages/org/[site]/setting/integration/database";
import ShopifyIntegration from "@/pages/org/[site]/setting/integration/shopify";
import WhatsAppAPIIntegration from "@/pages/org/[site]/setting/integration/whatsapp";
import WhatsAppQR from "@/pages/org/[site]/setting/integration/whatsapp-qr";
import FacebookIntegration from "@/pages/org/[site]/setting/integration/facebook";
import WixIntegration from "@/pages/org/[site]/setting/integration/wix";
import TelegramIntegration from "@/pages/org/[site]/setting/integration/telegram";

const Integrations = () => {
  const [tabIndex, setTabIndex] = useState<number>(0);

  return (
    <>
      <Tabs
        value={tabIndex}
        variant="scrollable"
        scrollButtons={false}
        sx={{
          mt: -2,
          borderBottom: 1,
          borderColor: "divider",
          mb: 2.5,
          "& a": {
            minHeight: "48px",
            gap: 1,
            svg: {
              m: "0 !important",
            },
            "&.Mui-disabled": {
              opacity: 0.5,
            },
          },
        }}
      >
        {TABS.map((tab, index) => (
          <Tab
            key={index}
            value={index}
            icon={tab.icon}
            iconPosition="top"
            label={
              tab.disabled && tab.label !== "" ? (
                <Typography
                  sx={{
                    fontSize: 14,
                    textAlign: "center",
                  }}
                >
                  {tab.label}
                  <Typography
                    component="span"
                    sx={{
                      display: "block",
                      fontSize: 12,
                      lineHeight: 1.25,
                      fontStyle: "italic",
                    }}
                  >
                    Coming Soon
                  </Typography>
                </Typography>
              ) : (
                tab.label
              )
            }
            sx={{
              textTransform: "none",
              fontWeight: "normal",
              ...(index === tabIndex && {
                textShadow: (theme) => `0 0 1px ${theme.palette.primary.main}`,
              }),
              ...(tab.label === "" && {
                minWidth: 0,
              }),
            }}
            disabled={tab.disabled}
            disableRipple
            suppressHydrationWarning
            onClick={() => setTabIndex(index)}
          />
        ))}
      </Tabs>

      {tabIndex === 0 && <FullPageIntegration withLayout={false} />}
      {tabIndex === 1 && <FloatingChatWidgetIntegration withLayout={false} />}
      {tabIndex === 2 && <DatabaseIntegration withLayout={false} />}
      {tabIndex === 4 && <ShopifyIntegration withLayout={false} />}
      {tabIndex === 5 && <WhatsAppAPIIntegration withLayout={false} />}
      {tabIndex === 6 && <WhatsAppQR withLayout={false} />}
      {tabIndex === 7 && <FacebookIntegration withLayout={false} />}
      {tabIndex === 8 && <WixIntegration withLayout={false} />}
      {tabIndex === 9 && <TelegramIntegration withLayout={false} />}
    </>
  );
};

export default Integrations;
