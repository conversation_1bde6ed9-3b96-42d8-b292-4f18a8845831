import React, { useEffect, useState } from "react";
import { Box, Button, CircularProgress, Typography } from "@mui/material";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import useAlert from "@/hooks/useAlert";
import { triggerRedTeamBatchRun } from "@/services/red-team";
import BatchTable from "@/components/RedTeam/BatchTable";
import useModal from "@/hooks/useModal";
import GenerateEvaluationDialog from "@/dialogs/GenerateEvaluationDialog";
import { useRedTeamBatch } from "@/services/red-team";
import usePagination from "@/hooks/usePagination";

const RedTeam = () => {
  const {
    rowCount,
    setRowCount,
    paginationModel,
    setPaginationModel,
    queryOptions,
    handleFilterModelChange,
    handleSortModelChange,
  } = usePagination();

  const { setAlertInfo } = useAlert();
  const {
    open: generateOpen,
    handleOpen: handleGenerateOpen,
    handleClose: handleGenerateClose,
  } = useModal();
  const { data, isLoading } = useRedTeamBatch({
    ...paginationModel,
    ...queryOptions,
  });

  useEffect(() => {
    setRowCount((prev) => (data ? data.count : prev));
  }, [data, setRowCount]);

  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const createRedTeamRun = async (numConversations: number, seed?: string) => {
    setIsGenerating(true);

    await triggerRedTeamBatchRun({
      numberOfConversations: numConversations,
      seedNumber: seed,
      onSuccess: () => {
        handleGenerateClose();
      },
      setAlertInfo,
    });

    setIsGenerating(false);
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "flex-start",
          justifyContent: "space-between",
          flexWrap: "wrap",
          columnGap: 2,
          rowGap: 1,
          mb: 1.5,
        }}
      >
        <Box>
          <Typography
            component="h4"
            sx={{
              fontWeight: "bold",
              color: "text.primary",
              mb: 0.25,
            }}
          >
            Red Team Batch Runs
          </Typography>

          <Typography
            sx={{
              fontSize: 14,
            }}
          >
            Explore all potential user questions, ensuring comprehensive and
            accurate answers!
          </Typography>
        </Box>

        <Button
          variant="outlined"
          startIcon={
            isGenerating ? (
              <CircularProgress
                size={16}
                sx={{
                  color: "rgba(0, 0, 0, 0.26)",
                  svg: {
                    transform: "scale(0.75)",
                  },
                }}
              />
            ) : (
              <AddOutlinedIcon
                sx={{
                  fontSize: "16px !important",
                }}
              />
            )
          }
          sx={{
            backgroundColor: "background.default",
            borderRadius: 2,
            textTransform: "none",
            lineHeight: 1.25,
            py: 1,
            ".MuiButton-startIcon": {
              mr: 0.5,
            },
          }}
          onClick={handleGenerateOpen}
          disabled={isGenerating}
        >
          Create New Run
        </Button>
      </Box>

      <BatchTable
        batches={data?.results || []}
        isLoading={isLoading}
        rowCount={rowCount}
        paginationModel={paginationModel}
        setPaginationModel={setPaginationModel}
        handleFilterModelChange={handleFilterModelChange}
        handleSortModelChange={handleSortModelChange}
      />

      <GenerateEvaluationDialog
        open={generateOpen}
        handleClose={handleGenerateClose}
        onGenerate={createRedTeamRun}
        isGenerating={isGenerating}
      />
    </>
  );
};

export default RedTeam;
