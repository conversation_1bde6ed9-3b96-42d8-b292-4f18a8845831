import React from "react";
import { Box, Drawer, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const CanvasDrawer = ({
  open,
  handleClose,
  children,
}: {
  open: boolean;
  handleClose: () => void;
  children: React.ReactNode;
}) => (
  <Drawer
    anchor="right"
    open={open}
    onClose={handleClose}
    sx={{ zIndex: (theme) => theme.zIndex.drawer + 2 }}
  >
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        position: "relative",
        width: "50vw",
        p: 3,
      }}
      role="presentation"
    >
      <IconButton
        sx={{
          position: "absolute",
          top: 4,
          right: 4,
          color: "text.primary",
          opacity: 0.5,
        }}
        size="small"
        disableRipple
        onClick={handleClose}
      >
        <CloseIcon fontSize="small" />
      </IconButton>

      {children}
    </Box>
  </Drawer>
);

export default CanvasDrawer;
