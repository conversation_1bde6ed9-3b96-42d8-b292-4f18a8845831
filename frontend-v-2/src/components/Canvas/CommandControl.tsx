import React, { useEffect, useState } from "react";
import useAuth from "@/hooks/useAuth";
import useAlert from "@/hooks/useAlert";
import { SettingObj, initialSettingObj } from "@/interfaces/setting";
import { updateSetting, useSetting } from "@/services/settings";
import { getEditedObject } from "@/utils/settings";
import { alertError, alertSuccess } from "@/utils/alert";
import { Box, Tab, Tabs, Typography } from "@mui/material";
import { CHATBOT_SETTING_TABS } from "@/components/Settings/constants";
import LoadingIndicator from "@/components/LoadingIndicator";
import { SaveSettingsSection } from "@/components/Settings/SettingComponents";
import EmptyMessage from "@/components/EmptyMessage";
import ChatbotStyle from "@/components/Settings/ChatbotStyle";
import ChatbotPersona from "@/components/Settings/ChatbotPersona";
import { ChatbotFeature } from "@/pages/org/[site]/setting/chatbot/feature";

const CommandControl = () => {
  const [tabIndex, setTabIndex] = useState<number>(0);

  const { user } = useAuth();
  const { setAlertInfo } = useAlert();

  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [updatedSetting, setUpdatedSetting] =
    useState<SettingObj>(initialSettingObj);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const {
    data: setting,
    isLoading: isSettingFetching,
    mutate: settingMutate,
  } = useSetting("external");

  useEffect(() => {
    if (setting) {
      setUpdatedSetting(setting);
    }
  }, [setting]);

  const isUserLoading =
    user === undefined || (user !== undefined && user.id === -1);

  const isSettingLoading = setting !== undefined && updatedSetting.id === -1;

  const isLoading = isUserLoading || isSettingFetching || isSettingLoading;

  const isSameSetting =
    setting !== undefined &&
    JSON.stringify(setting) === JSON.stringify(updatedSetting);

  const disabled =
    isSaving || isLoading || updatedSetting.tone.trim() === "" || isSameSetting;

  const handleUpdate = async () => {
    if (setting === undefined || disabled) return;

    setIsSaving(true);

    try {
      const { id, ...body } = getEditedObject(setting, updatedSetting);
      const isEmptySettingObj =
        Object.keys(body).length === 0 && body.constructor === Object;
      !isEmptySettingObj && (await updateSetting(id, body, imageFile));

      alertSuccess("Chatbot settings updated!", setAlertInfo);
      setImageFile(null);
      !isEmptySettingObj && settingMutate();
    } catch (error) {
      alertError("Failed to update chatbot settings.", setAlertInfo);
    }

    setIsSaving(false);
  };

  return (
    <>
      <Tabs
        value={tabIndex}
        variant="scrollable"
        scrollButtons={false}
        sx={{
          mt: -2,
          borderBottom: 1,
          borderColor: "divider",
          mb: 2.5,
          "& a": {
            minHeight: "48px",
            gap: 1,
            svg: {
              m: "0 !important",
            },
            "&.Mui-disabled": {
              opacity: 0.5,
            },
          },
        }}
      >
        {CHATBOT_SETTING_TABS.map((tab, index) => (
          <Tab
            key={index}
            value={index}
            icon={tab.icon}
            iconPosition="top"
            label={
              tab.disabled && tab.label !== "" ? (
                <Typography
                  sx={{
                    fontSize: 14,
                    textAlign: "center",
                  }}
                >
                  {tab.label}
                  <Typography
                    component="span"
                    sx={{
                      display: "block",
                      fontSize: 12,
                      lineHeight: 1.25,
                      fontStyle: "italic",
                    }}
                  >
                    Coming Soon
                  </Typography>
                </Typography>
              ) : (
                tab.label
              )
            }
            sx={{
              textTransform: "none",
              fontWeight: "normal",
              ...(index === tabIndex && {
                textShadow: (theme) => `0 0 1px ${theme.palette.primary.main}`,
              }),
              ...(tab.label === "" && {
                minWidth: 0,
              }),
            }}
            disabled={tab.disabled}
            disableRipple
            suppressHydrationWarning
            onClick={() => setTabIndex(index)}
          />
        ))}
      </Tabs>

      {isLoading ? (
        <LoadingIndicator />
      ) : updatedSetting.id !== -1 ? (
        <>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 3,
              opacity: isSaving ? 0.5 : 1,
              pointerEvents: isSaving ? "none" : "auto",
            }}
          >
            {tabIndex === 0 && (
              <ChatbotStyle
                setting={updatedSetting}
                setSetting={setUpdatedSetting}
                setImageFile={setImageFile}
              />
            )}

            {tabIndex === 1 && (
              <ChatbotPersona
                setting={updatedSetting}
                setSetting={setUpdatedSetting}
              />
            )}

            {tabIndex === 2 && <ChatbotFeature setting={updatedSetting} />}
          </Box>

          {tabIndex !== 2 && (
            <SaveSettingsSection
              handleUpdate={handleUpdate}
              isSaving={isSaving}
              disabled={disabled}
            />
          )}
        </>
      ) : (
        <EmptyMessage title="No setting found" />
      )}
    </>
  );
};

export default CommandControl;
