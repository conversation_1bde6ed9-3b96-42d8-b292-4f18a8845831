import React, {
  create<PERSON>ontext,
  FC,
  PropsWithChildren,
  useContext,
  useState,
} from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { useLocalStorage } from "usehooks-ts";
import {
  Badge,
  Box,
  Collapse,
  Drawer,
  Icon,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AccountTreeOutlinedIcon from "@mui/icons-material/AccountTreeOutlined";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
import FeedbackOutlinedIcon from "@mui/icons-material/FeedbackOutlined";
import CenterFocusWeakOutlinedIcon from "@mui/icons-material/CenterFocusWeakOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import AssignmentOutlinedIcon from "@mui/icons-material/AssignmentOutlined";
import ChatOutlinedIcon from "@mui/icons-material/ChatOutlined";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import PeopleAltOutlinedIcon from "@mui/icons-material/PeopleAltOutlined";
import DashboardOutlinedIcon from "@mui/icons-material/DashboardOutlined";
import GroupOutlinedIcon from "@mui/icons-material/GroupOutlined";
import { getTenantSchemaName, getTenantUrl } from "@/utils/url";
import { getIntegrationUrlFromSource } from "@/utils/integration";
import { useActionCount, useAllCustomActions } from "@/services/action";
import Logo from "@/components/Layout/Logo";
import { TABS as KNOWLEDGE_BASE_TABS } from "@/components/KnowledgeBase/constants";
import { TABS as ACTION_TABS } from "@/components/Action/constants";
import { TABS as INTEGRATION_TABS } from "@/components/Integration/constants";
import { TABS as SETTINGS_TABS } from "@/components/Settings/constants";

export const SIDEBAR_WIDTH = 240;

type Link = {
  label: string;
  url: string;
  icon: React.ReactNode;
  isActive: boolean;
  disabled?: boolean;
  count?: number;
  children?: Omit<Link, "children">[];
};

const Sidebar = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const { pathname } = useRouter();

  const { isOpen, toggleSidebar } = useSidebar();
  const tenantSchemaName = getTenantSchemaName();

  const { data } = useActionCount();
  const { data: customActionData } = useAllCustomActions();

  const links: Link[] =
    tenantSchemaName !== ""
      ? [
          {
            label: "Actions",
            url: getTenantUrl("/action/appointment"),
            icon: <AssignmentOutlinedIcon />,
            isActive: pathname.includes("/action"),
            count: data?.total || 0,
            children: (customActionData?.results
              ? ACTION_TABS.concat(
                  customActionData.results.map((cad) => ({
                    label: cad.name,
                    link: "custom-action?tab=" + cad.name,
                    icon: cad.icon_svg ? (
                      <Icon className="material-icons-outlined">
                        {cad.icon_svg}
                      </Icon>
                    ) : (
                      <FeedbackOutlinedIcon />
                    ),
                    countKey: cad.snake_cased_name + "_count",
                    disabled: !cad.is_activated,
                  }))
                )
              : ACTION_TABS
            ).map((tab) => ({
              label: tab.label,
              url: getTenantUrl(`/action/${tab.link}`),
              icon: tab.icon,
              isActive:
                pathname.endsWith(`/action/${tab.link}`) ||
                pathname.endsWith(`/action/${tab.link}/`),
              disabled: tab.disabled,
              count: data?.[tab.countKey] || 0,
            })),
          },
          {
            label: "Preview",
            url: getTenantUrl(
              `/preview/${getIntegrationUrlFromSource("Web Application")}`
            ),
            icon: <CenterFocusWeakOutlinedIcon />,
            isActive: pathname.includes("/preview"),
            children: INTEGRATION_TABS.filter(
              (tab) => tab.hasPreview && tab.label !== ""
            ).map((tab) => ({
              label: tab.label,
              url: getTenantUrl(`/preview/${tab.link}`),
              icon: tab.icon,
              isActive:
                pathname.endsWith(`/preview/${tab.link}`) ||
                pathname.endsWith(`/preview/${tab.link}/`),
              disabled: tab.disabled,
            })),
          },
          {
            label: "Chats",
            url: getTenantUrl("/chat"),
            icon: <ChatOutlinedIcon />,
            isActive: pathname.match(/\/chat(\/|$)/) !== null,
          },
          {
            label: "Canvas",
            url: getTenantUrl("/canvas"),
            icon: <AccountTreeOutlinedIcon />,
            isActive: pathname.includes("/canvas"),
          },
          {
            label: "Knowledge Base",
            url: getTenantUrl("/knowledge-base/document"),
            icon: <DescriptionOutlinedIcon />,
            isActive:
              pathname.includes("/knowledge-base") &&
              !pathname.includes("/setting/knowledge-base"),
            children: KNOWLEDGE_BASE_TABS.map((tab) => ({
              label: tab.label,
              url: getTenantUrl(`/knowledge-base/${tab.link}`),
              icon: tab.icon,
              isActive:
                pathname.endsWith(`/knowledge-base/${tab.link}`) ||
                pathname.endsWith(`/knowledge-base/${tab.link}/`),
              disabled: tab.disabled,
            })),
          },
          {
            label: "CRM",
            url: getTenantUrl("/crm"),
            icon: <PeopleAltOutlinedIcon />,
            isActive: pathname.includes("/crm"),
          },
          {
            label: "Analytics",
            url: getTenantUrl("/analytics"),
            icon: <AssessmentOutlinedIcon />,
            isActive: pathname.includes("/analytics"),
          },
          {
            label: "Settings",
            url: getTenantUrl("/setting/team"),
            icon: <SettingsOutlinedIcon />,
            isActive: pathname.includes("/setting"),
            children: SETTINGS_TABS.map((tab) => ({
              label: tab.label,
              url: getTenantUrl(
                `/setting/${
                  tab.link.includes("integration")
                    ? `${tab.link}/${getIntegrationUrlFromSource(
                        "Web Application"
                      )}`
                    : tab.link.includes("chatbot")
                    ? `${tab.link}/style`
                    : tab.link.includes("knowledge-base")
                    ? `${tab.link}/image-tag`
                    : tab.link
                }`
              ),
              icon: tab.icon,
              isActive: pathname.includes(`/setting/${tab.link}`),
              disabled: tab.disabled,
            })),
          },
        ]
      : [
          {
            label: "My Sites",
            url: "/",
            icon: <DashboardOutlinedIcon />,
            isActive: pathname === "/",
          },
          {
            label: "Users",
            url: "/user",
            icon: <GroupOutlinedIcon />,
            isActive: pathname.includes("/user"),
          },
        ];

  const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>(() => {
    const initialState: { [key: string]: boolean } = {};

    links.forEach((link) => {
      if (link.children?.some((child) => child.isActive)) {
        initialState[link.label] = true;
      }
    });

    return initialState;
  });

  const toggleMenu = (label: string) =>
    setOpenMenus((prev) => ({ ...prev, [label]: !prev[label] }));

  return (
    <Drawer
      variant={isMobile ? "temporary" : "persistent"}
      anchor="left"
      sx={{
        width: SIDEBAR_WIDTH,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: SIDEBAR_WIDTH,
          boxSizing: "border-box",
        },
        zIndex: (theme) =>
          isMobile ? theme.zIndex.drawer + 2 : theme.zIndex.drawer,
      }}
      open={isOpen}
      onClose={toggleSidebar}
    >
      <Toolbar sx={{ height: 80 }}>
        <Logo />

        <IconButton
          sx={{
            position: "absolute",
            top: 4,
            right: 4,
            color: "text.primary",
            opacity: 0.5,
          }}
          size="small"
          disableRipple
          onClick={toggleSidebar}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </Toolbar>

      <Box sx={{ overflowY: "auto", overflowX: "hidden" }}>
        <List>
          {links.map((link, index) => {
            const hasChildren = link.children && Array.isArray(link.children);
            const isOpen = openMenus[link.label] || false;

            return (
              <React.Fragment key={index}>
                <ListItem disablePadding>
                  <ListItemButton
                    {...(hasChildren
                      ? { onClick: () => toggleMenu(link.label) }
                      : { LinkComponent: Link, href: link.url })}
                    selected={link.isActive}
                    disabled={link.disabled}
                    sx={{
                      "& .MuiTypography-root": {
                        fontSize: 14,
                      },
                      "& .MuiSvgIcon-root": {
                        fontSize: 20,
                      },
                      gap: 1.5,
                      ...(link.isActive && {
                        color: "primary.main",
                        textShadow: (theme) =>
                          `0 0 1px ${theme.palette.primary.main}`,
                        "& .MuiListItemIcon-root": {
                          color: "primary.main",
                        },
                      }),
                    }}
                    disableRipple
                  >
                    <ListItemIcon sx={{ minWidth: 0 }}>
                      {link.count !== undefined ? (
                        <Badge
                          badgeContent={link.count}
                          max={9}
                          color="primary"
                          sx={{ mr: "0 !important" }}
                        >
                          {link.icon}
                        </Badge>
                      ) : (
                        link.icon
                      )}
                    </ListItemIcon>

                    <ListItemText primary={link.label} />

                    {hasChildren && (
                      <ExpandMoreOutlinedIcon
                        sx={{
                          fontSize: "18px !important",
                          opacity: link.isActive ? 1 : 0.5,
                          transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
                          transition: (theme) =>
                            theme.transitions.create("transform"),
                        }}
                      />
                    )}
                  </ListItemButton>
                </ListItem>

                {hasChildren && (
                  <Collapse in={isOpen} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {link?.children?.map((child, childIndex) => (
                        <ListItem key={childIndex} disablePadding>
                          <ListItemButton
                            LinkComponent={Link}
                            href={child.url}
                            selected={child.isActive}
                            disabled={child.disabled}
                            sx={{
                              "& .MuiTypography-root": {
                                fontSize: 14,
                              },
                              "& .MuiSvgIcon-root": {
                                fontSize: 20,
                              },
                              "& .custom-icon": {
                                width: 20,
                                height: 20,
                              },
                              pl: 4,
                              gap: 1.5,
                              ...(child.isActive && {
                                color: "primary.main",
                                textShadow: (theme) =>
                                  `0 0 1px ${theme.palette.primary.main}`,
                                "& .MuiListItemIcon-root": {
                                  color: "primary.main",
                                },
                              }),
                            }}
                            disableRipple
                          >
                            <ListItemIcon sx={{ minWidth: 0 }}>
                              {child.count !== undefined ? (
                                <Badge
                                  badgeContent={child.count}
                                  max={9}
                                  color="primary"
                                  sx={{ mr: "0 !important" }}
                                >
                                  {child.icon}
                                </Badge>
                              ) : (
                                child.icon
                              )}
                            </ListItemIcon>

                            <ListItemText primary={child.label} />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </Collapse>
                )}
              </React.Fragment>
            );
          })}
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;

export const SidebarInset = ({ children }: { children: React.ReactNode }) => {
  const { isOpen } = useSidebar();

  return (
    <Box
      className="image-bg"
      sx={{
        display: "flex",
        flexDirection: "column",
        flex: 1,
        pl: { md: isOpen ? `${SIDEBAR_WIDTH}px` : 0 },
        transition: (theme) => theme.transitions.create("padding-left"),
      }}
    >
      {children}
    </Box>
  );
};

const SidebarContext = createContext<{
  isOpen: boolean;
  toggleSidebar: () => void;
}>({
  isOpen: true,
  toggleSidebar: () => {},
});

export const SidebarProvider: FC<PropsWithChildren> = ({ children }) => {
  const [isOpen, setIsOpen] = useLocalStorage("sidebar-open", true);
  const [isOpenMobile, setIsOpenMobile] = useState<boolean>(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const toggleSidebar = () =>
    isMobile ? setIsOpenMobile(!isOpenMobile) : setIsOpen(!isOpen);

  return (
    <SidebarContext.Provider
      value={{
        isOpen: isMobile ? isOpenMobile : isOpen,
        toggleSidebar,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebar = () => {
  const context = useContext(SidebarContext);

  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }

  return context;
};
