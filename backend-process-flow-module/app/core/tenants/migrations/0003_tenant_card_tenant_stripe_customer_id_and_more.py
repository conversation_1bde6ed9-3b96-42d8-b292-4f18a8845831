# Generated by Django 4.2.5 on 2023-09-18 07:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0001_initial'),
        ('tenants', '0002_alter_tenant_on_trial_alter_tenant_paid_until'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenant',
            name='card',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tenants', to='payment.card'),
        ),
        migrations.AddField(
            model_name='tenant',
            name='stripe_customer_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='tenant',
            name='stripe_email',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='tenant',
            name='subscription_plan',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tenant', to='payment.subscriptionplan'),
        ),
    ]
