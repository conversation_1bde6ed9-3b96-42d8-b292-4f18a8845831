# Generated by Django 4.2.5 on 2023-12-13 02:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('payment', '0002_remove_card_created_by_remove_card_updated_by'),
        ('tenants', '0003_tenant_card_tenant_stripe_customer_id_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='tenant',
            name='card',
        ),
        migrations.RemoveField(
            model_name='tenant',
            name='on_trial',
        ),
        migrations.RemoveField(
            model_name='tenant',
            name='paid_until',
        ),
        migrations.AddField(
            model_name='tenant',
            name='subscription_exp_datetime',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='tenant',
            name='trial_exp_datetime',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='tenant',
            name='stripe_customer_id',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='tenant',
            name='stripe_email',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='tenant',
            name='subscription_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subscription_plans', to='payment.subscriptionplan'),
        ),
    ]
