from django.db import models
from tenant_schemas.models import TenantMixin
from app.core.payment.models import SubscriptionPlan


class Tenant(
    TenantMixin
):  # become tenant, create 1 more bridge model to track customer x user
    name = models.CharField(max_length=100)
    test_contact = models.CharField(
        max_length=17, blank=True
    )  # contact number for testing purpose, if exist, all reminders / notifications will be sent here
    stripe_email = models.CharField(max_length=100, blank=True, default="")
    stripe_customer_id = models.CharField(max_length=100, blank=True, default="")
    trial_exp_datetime = models.DateTimeField(blank=True, null=True)
    subscription_exp_datetime = models.DateTimeField(blank=True, null=True)
    subscription_plan = models.ForeignKey(
        SubscriptionPlan,
        related_name="subscription_plans",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    created_on = models.DateField(auto_now_add=True)

    # default true, schema will be automatically created and synced when it is saved
    auto_create_schema = True
    auto_drop_schema = True  # probably turn this off in future.

    def __str__(self):
        return self.name
