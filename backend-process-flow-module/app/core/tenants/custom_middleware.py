# from demo_related.helper import DEMO_FURNISHING_SCHEMA_NAME
# from tenants.models import Tenant


from backend.settings import TENANT_BACKEND_URL


class HostnameMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        host = request.get_host()
        path = request.path_info.lstrip("/")

        # Optionally, you might want to adjust the path or do additional handling here
        if path.startswith("org/"):
            # elif path.startswith("org/"):
            tenant_slug = path.split("/")[1]
            if ":" in TENANT_BACKEND_URL:
                tenant_backend_url = TENANT_BACKEND_URL.split(":")[0]
            else:
                tenant_backend_url = TENANT_BACKEND_URL
            new_host = f"{tenant_slug}.{tenant_backend_url}"

            # Need to convert _ to -
            new_host = new_host.replace("_", "-")


            # Changing the host of the request
            request.META["HTTP_HOST"] = new_host

            # Update path_info to remove the organization segment
            request.path_info = "/" + "/".join(path.split("/")[2:])
        else:
            new_host = f"public.{host}"

        # print("modified host: ", new_host)  # demo.localhost:8000
        # print("modified_path: ", request.path_info)  # /chat/conversation/list

        response = self.get_response(request)
        return response
