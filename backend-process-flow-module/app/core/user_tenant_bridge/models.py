from django.db import models
import app.utility.models as utility_models
from django.contrib.auth import get_user_model
import app.core.authapi.models as authapi_models
import app.core.tenants.models as tenants_models

User = get_user_model()


class UserTenantBridge(utility_models.BaseModel):
    user = models.ForeignKey(
        User, related_name="user_tenant_bridges", on_delete=models.CASCADE
    )
    tenant = models.ForeignKey(
        tenants_models.Tenant,
        related_name="user_tenant_bridges",
        on_delete=models.CASCADE,
    )
    role = models.CharField(
        choices=authapi_models.ROLE_LIST, max_length=255, default=authapi_models.STAFF
    )

    def __str__(self):
        return f"{self.user.email}-{self.tenant}"
