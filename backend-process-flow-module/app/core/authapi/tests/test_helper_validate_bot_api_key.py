from django.test import TestCase
from django.conf import settings
from app.core.authapi.helper import validate_bot_api_key
from app.utility.custom_exception import CustomException


class TestValidateBotApiKey(TestCase):
    """
    Test suite for validate_bot_api_key function in authapi helper.
    Tests validation of bot API keys against the configured key.
    """

    def test_valid_api_key(self):
        """
        Test that validation passes when the correct API key is provided.
        Should not raise any exception.
        """
        # Using the actual BOT_API_KEY from settings
        try:
            validate_bot_api_key(settings.BOT_API_KEY)
        except CustomException:
            self.fail("validate_bot_api_key raised CustomException unexpectedly!")

    def test_invalid_api_key(self):
        """
        Test that validation fails with incorrect API key.
        Should raise CustomException with status code 403.
        """
        invalid_keys = [
            "invalid_key",
            "",
            None,
            "123456",
            settings.BOT_API_KEY + "extra",
        ]

        for invalid_key in invalid_keys:
            with self.assertRaises(CustomException) as context:
                validate_bot_api_key(invalid_key)
            
            # Verify the exception details
            self.assertEqual(str(context.exception), "Invalid bot API key")
            self.assertEqual(context.exception.status_code, 403) 