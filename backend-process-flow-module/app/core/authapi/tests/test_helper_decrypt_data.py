import os
from unittest.mock import patch
from django.test import Test<PERSON>ase
from cryptography.fernet import <PERSON><PERSON><PERSON>
from app.core.authapi.helper import decrypt_data

class DecryptDataTests(TestCase):
    """
    Test suite for the decrypt_data function in the auth helper module.
    
    This test suite verifies the encryption/decryption functionality using <PERSON><PERSON>t,
    including successful decryption and various error cases.
    
    Setup:
        - Creates a test encryption key
        - Patches the environment variable for the encryption key
        
    Test Cases:
        - Successful decryption of valid encrypted data
        - Handling of invalid tokens
        - Handling of empty strings
        - Handling of invalid data types
        - Handling of wrong encryption keys
    """

    def setUp(self) -> None:
        """
        Set up the test environment before each test.
        Creates a test encryption key and patches the environment variable.
        """
        self.test_key = Fernet.generate_key()
        self.patcher = patch.dict(os.environ, {'FERNET_KEY': self.test_key.decode()})
        self.patcher.start()
        self.fernet = Fernet(self.test_key)

    def tearDown(self) -> None:
        """Clean up the test environment after each test."""
        self.patcher.stop()

    def test_decrypt_data_success(self) -> None:
        """
        Test successful decryption of properly encrypted data.
        
        Steps:
        1. Create and encrypt a test message
        2. Decrypt the encrypted message
        3. Verify the decrypted result matches the original
        """
        # Arrange
        original_text = "test_secret_message"
        encrypted_data = self.fernet.encrypt(original_text.encode()).decode()

        # Act
        decrypted_text = decrypt_data(encrypted_data)

        # Assert
        self.assertEqual(decrypted_text, original_text)
        self.assertIsInstance(decrypted_text, str)

    def test_decrypt_data_invalid_token(self) -> None:
        """
        Test that decryption fails appropriately with an invalid token.
        
        Expects a cryptography.fernet.InvalidToken exception to be raised.
        """
        # Arrange
        invalid_encrypted_data = "invalid_token"

        # Act & Assert
        with self.assertRaises(Exception):
            decrypt_data(invalid_encrypted_data)

    def test_decrypt_data_empty_string(self) -> None:
        """
        Test that decryption fails appropriately with an empty string.
        
        Expects a cryptography.fernet.InvalidToken exception to be raised.
        """
        # Act & Assert
        with self.assertRaises(Exception):
            decrypt_data("")

    def test_decrypt_data_none_input(self) -> None:
        """
        Test that decryption fails appropriately with None input.
        
        Expects an AttributeError when trying to decrypt None since
        None type doesn't have encode() method.
        """
        # Act & Assert
        with self.assertRaises(AttributeError):
            decrypt_data(None)  # type: ignore

    def test_decrypt_data_wrong_key(self) -> None:
        """
        Test that decryption fails when using the wrong encryption key.
        
        Steps:
        1. Encrypt data with the original key
        2. Attempt to decrypt with a different key
        3. Verify that the decryption fails
        """
        # Arrange
        original_text = "test_secret_message"
        encrypted_data = self.fernet.encrypt(original_text.encode()).decode()
        
        # Change environment key
        wrong_key = Fernet.generate_key()
        with patch.dict(os.environ, {'FERNET_KEY': wrong_key.decode()}):
            # Act & Assert
            with self.assertRaises(Exception):
                decrypt_data(encrypted_data)