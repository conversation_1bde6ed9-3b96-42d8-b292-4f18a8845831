from django.test import TestCase
from django.test import override_settings
from unittest.mock import patch, Mock

from app.core.authapi.helper import encrypt_data, decrypt_data

class TestEncryption(TestCase):
    """Test suite for encryption functionality in the authapi helper module."""

    @patch('app.core.authapi.helper.get_fernet')
    def test_encrypt_data_success(self, mock_get_fernet):
        """Test successful encryption of a basic string.
        
        Tests that:
        - The encryption function is called with correct parameters
        - The encrypted result is properly decoded to string
        - The Fernet instance is retrieved exactly once
        """
        # Arrange
        test_data = "sensitive_data"
        mock_fernet = Mock()
        mock_encrypted = b"encrypted_data"
        mock_fernet.encrypt.return_value = mock_encrypted
        mock_get_fernet.return_value = mock_fernet

        # Act
        result = encrypt_data(test_data)

        # Assert
        mock_get_fernet.assert_called_once()
        mock_fernet.encrypt.assert_called_once_with(test_data.encode())
        self.assertEqual(result, mock_encrypted.decode())

    @patch('app.core.authapi.helper.get_fernet')
    def test_encrypt_data_with_special_characters(self, mock_get_fernet):
        """Test encryption of string containing special characters.
        
        Verifies that strings with special characters (@, #, $, etc.)
        are properly encrypted and decoded.
        """
        # Arrange
        test_data = "sensitive@data#123$"
        mock_fernet = Mock()
        mock_encrypted = b"encrypted_special_chars"
        mock_fernet.encrypt.return_value = mock_encrypted
        mock_get_fernet.return_value = mock_fernet

        # Act
        result = encrypt_data(test_data)

        # Assert
        mock_fernet.encrypt.assert_called_once_with(test_data.encode())
        self.assertEqual(result, mock_encrypted.decode())

    @patch('app.core.authapi.helper.get_fernet')
    def test_encrypt_data_empty_string(self, mock_get_fernet):
        """Test encryption of an empty string.
        
        Ensures that empty strings can be encrypted without errors
        and return expected results.
        """
        # Arrange
        test_data = ""
        mock_fernet = Mock()
        mock_encrypted = b"encrypted_empty"
        mock_fernet.encrypt.return_value = mock_encrypted
        mock_get_fernet.return_value = mock_fernet

        # Act
        result = encrypt_data(test_data)

        # Assert
        mock_fernet.encrypt.assert_called_once_with(test_data.encode())
        self.assertEqual(result, mock_encrypted.decode())

    @patch('app.core.authapi.helper.get_fernet')
    def test_encrypt_data_integration(self, mock_get_fernet):
        """Integration test for the encryption process.
        
        Tests the complete encryption flow by simulating actual encryption behavior,
        where the encrypted result maintains the same format as input.
        """
        # Arrange
        test_data = "test_data"
        mock_fernet = Mock()
        # Simulate real encryption by making encrypt return the input data as bytes
        mock_fernet.encrypt.side_effect = lambda x: x
        mock_get_fernet.return_value = mock_fernet

        # Act
        encrypted = encrypt_data(test_data)
        
        # Assert
        self.assertIsInstance(encrypted, str)
        mock_fernet.encrypt.assert_called_once()

    def test_encrypt_data_raises_value_error(self):
        """Test error handling when encryption key is not available.
        
        Verifies that the encryption function raises ValueError when
        the required environment variable for the encryption key is not set.
        """
        # Arrange
        test_data = "test_data"
        
        # Act & Assert
        with self.assertRaises(ValueError):
            with patch('os.getenv', return_value=None):
                encrypt_data(test_data) 