# from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.response import Response
from rest_framework import status

from app.core.authapi.serializers import CustomTokenObtainPairSerializer

from django.contrib.auth import get_user_model
from django.utils import timezone
import app.core.authapi.helper as authapi_helper

from tenant_schemas.utils import schema_context

User = get_user_model()


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        schema_name = authapi_helper.get_schema_name()
        if "org" in request.data:
            if request.data["org"] != "":
                schema_name = request.data["org"]
        with schema_context(schema_name):
            res = super().post(request, *args, **kwargs)

            user_id = res.data["id"]  # from custom serializer

            if user_id:
                user_instance = User.objects.get(id=user_id)
                today = timezone.now()
                if not (user_instance.start_date <= today <= user_instance.end_date):
                    return Response(
                        status=status.HTTP_400_BAD_REQUEST,
                        data={"body": "The account is not within activated date!"},
                    )
                _, ip_address, country = get_user_id_ip_address_country(request)
                user_instance.last_login = timezone.now()
                # TODO: check whether login correct (esp staff acc cannot login admin website)

                user_instance.last_login_ip = ip_address
                user_instance.country = country
                user_instance.save()

        return res
