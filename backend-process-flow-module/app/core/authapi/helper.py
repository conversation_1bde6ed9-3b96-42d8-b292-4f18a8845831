from django.db import connections, router
from django.contrib.auth import get_user_model, authenticate
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail

import app.core.user_tenant_bridge.models as user_tenant_bridge_models
import app.core.tenants.models as tenants_models
from app.utility.custom_exception import CustomException
import app.utility.date_helper as utility_date_helper
from backend.settings import BOT_API_KEY

import string
import random
import os
import re

from typing import List
from cryptography.fernet import Fernet

User = get_user_model()


def get_schema_name():
    User = get_user_model()
    using = router.db_for_read(User)
    connection = connections[using]
    saved_schema = connection.schema_name
    return saved_schema


def add_user_to_user_tenant_bridge(user_instance, tenant_instance, tenant_role):
    user_tenant_bridge_models.UserTenantBridge.objects.get_or_create(
        user=user_instance, tenant=tenant_instance, role=tenant_role
    )


def create_user(
    email,
    password,
    full_name,
    contact,
    tenant_role,
    tenant_instance,
):
    user_instance, created = User.objects.get_or_create(email=email)
    if created:
        user_instance.set_password(password)
        user_instance.contact = contact
        user_instance.full_name = full_name
        user_instance.save()
    else:
        user_instance = authenticate(email=email, password=password)
        if not user_instance:
            raise Exception("User already exists but password is incorrect.")

    add_user_to_user_tenant_bridge(user_instance, tenant_instance, tenant_role)
    return user_instance


def send_user_email_with_html_template(
    html_path: str, subject: str, from_: str, to_: List[str], **kwargs
):
    # send email
    html_message = render_to_string(
        html_path,
        {**kwargs},
    )
    plain_message = strip_tags(html_message)
    res = send_mail(
        subject,
        plain_message,
        from_,
        to_,
        html_message=html_message,
    )
    print(f"sent email titled '{subject}' to {to_},  with res: ", res)


# Function to get the encryption key from environment variables
def get_fernet():
    encryption_key = os.getenv("FERNET_KEY")

    if not encryption_key:
        raise ValueError("Encryption key not found in environment variables.")
    return Fernet(encryption_key)

# Function to encrypt data
def encrypt_data(data):
    fernet = get_fernet()
    return fernet.encrypt(data.encode()).decode()

# Function to decrypt data
def decrypt_data(data: str) -> str:
    """
    Decrypt the provided encrypted data string using Fernet encryption.
    
    Args:
        data (str): The encrypted data string to decrypt
        
    Returns:
        str: The decrypted data string
        
    Raises:
        ValueError: If the encryption key is not found
        Exception: If decryption fails due to invalid data or wrong key
    """
    fernet = get_fernet()
    return fernet.decrypt(data.encode()).decode()

def validate_bot_api_key(api_key: str) -> None:
    """
    Validate that the provided bot API key matches the configured key.
    
    Args:
        api_key (str): The API key to validate
        
    Raises:
        CustomException: If the API key does not match the configured key
    """
    if api_key != BOT_API_KEY:
        raise CustomException("Invalid bot API key", 403)
    