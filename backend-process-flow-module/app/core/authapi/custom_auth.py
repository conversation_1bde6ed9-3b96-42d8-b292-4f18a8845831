from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db import connections, router
import user_tenant_bridge.models as user_tenant_bridge_models


class CustomAuthBackend(ModelBackend):
    def authenticate(self, request, username=None, email=None, password=None, **kwargs):
        try:
            from_schema = request.data.get("from_schema", "").replace("-", "_")
        except:
            from_schema = ""
        User = get_user_model()
        using = router.db_for_read(User)
        connection = connections[using]
        schema_name = connection.schema_name
        email = username if username else email
        # Try to fetch the user in the current schema based on email.
        try:
            if from_schema:
                user_tenant_bridge = (
                    user_tenant_bridge_models.UserTenantBridge.objects.get(
                        user__email=email, tenant__schema_name=schema_name
                    )
                )
                user = user_tenant_bridge.user
            else:
                user = User.objects.get(email=email)

        except Exception as e:
            print("error: ", e)
            user = None

        # If not found and we are not in the 'public' schema,
        # try fetching a superuser from the 'public' schema.
        if not user:
            try:
                connection.schema_name = "public"
                user = User.objects.get(email=email, is_superuser=True)
            except User.DoesNotExist:
                user = None
            finally:
                connection.schema_name = schema_name

        # Check if the provided password matches.
        if user and user.check_password(password):
            return user
        return None
