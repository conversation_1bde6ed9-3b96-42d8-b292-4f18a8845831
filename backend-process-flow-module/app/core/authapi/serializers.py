import app.core.authapi.models as authapi_models
import app.core.authapi.helper as authapi_helper

import app.documents.document.models as document_models

from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
from djoser.serializers import UserCreateSerializer
from django.contrib.auth.password_validation import validate_password

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken

from tenant_schemas.utils import schema_context
import app.core.tenants.models as tenants_models

import app.utility.models as utility_models

User = get_user_model()  # Retrieve custom user model


class CustomUserCreateSerializer(UserCreateSerializer):
    # tokens = serializers.SerializerMethodField()
    # profile_id = serializers.SerializerMethodField()
    def create(self, validated_data):
        # Convert email to lowercase before saving the user
        validated_data["email"] = validated_data["email"].lower()
        validated_data["is_active"] = True
        # Call the parent class's create() method to save the user
        return super().create(validated_data)

    class Meta(UserCreateSerializer.Meta):
        model = User
        fields = UserCreateSerializer.Meta.fields + (
            "is_active",
            "country",
            "contact",
            "role",
            "full_name",
            "start_date",
            "end_date",
            "created_at",
        )


# Frontend schema name = with '-', backend schema name = with '_'
class CustomUserSerializer(serializers.ModelSerializer):
    tenant_information = serializers.SerializerMethodField()

    def get_tenant_information(self, user_instance):
        tenant_information = user_instance.user_tenant_bridges.order_by(
            "tenant__schema_name"
        ).values_list("tenant__schema_name", "tenant__name", "role")

        tenant_dict_list = [
            {"schema_name": schema_name.replace("_", "-"), "name": name, "role": role}
            for schema_name, name, role in tenant_information
        ]

        all_tenant_information = (
            tenants_models.Tenant.objects.all()
            .order_by("schema_name")
            .values_list("schema_name", "name")
        )
        if user_instance.role == authapi_models.ADMIN:
            tenant_dict_list = [
                {
                    "schema_name": schema_name.replace("_", "-"),
                    "name": name,
                    "role": authapi_models.ADMIN,
                }
                for schema_name, name in all_tenant_information
            ]

        return tenant_dict_list

    is_enable_chat = serializers.SerializerMethodField()

    def get_is_enable_chat(self, user_instance):
        schema_name = authapi_helper.get_schema_name()
        document_number = 0
        try:
            with schema_context(schema_name):
                document_number = document_models.Document.objects.filter(
                    status=utility_models.SUCCEED
                ).count()
        except:
            pass

        return document_number > 0

    is_document_exist = serializers.SerializerMethodField()

    def get_is_document_exist(self, user_instance):
        schema_name = authapi_helper.get_schema_name()
        with schema_context(schema_name):
            document_number = 0
            try:
                document_number = document_models.Document.objects.count()
            except:
                pass

        return document_number > 0

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "is_active",
            "country",
            "contact",
            "role",
            "tenant_information",
            "is_document_exist",
            "is_enable_chat",
            "full_name",
            "start_date",
            "end_date",
            "created_at",
            "last_login",
        ]


class CustomUserBulkCreateSerializer(serializers.ModelSerializer):
    def create(self, validated_data):
        user = User.objects.create(**validated_data)
        user.set_password(validated_data["password"])
        user.save()
        return user

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "password",
            "is_active",
            "country",
            "contact",
            "role",
            "full_name",
            "start_date",
            "end_date",
            "created_at",
        ]


class ShortCustomUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "contact", "full_name"]


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        # This data variable will contain refresh and access tokens
        data = super().validate(attrs)
        # You can add more User model's attributes like username,email etc. in the data dictionary like this.
        data["id"] = self.user.id
        return data


class ChangePasswordSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    password2 = serializers.CharField(write_only=True, required=True)
    old_password = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ("old_password", "password", "password2")

    def validate(self, attrs):
        if attrs["password"] != attrs["password2"]:
            raise serializers.ValidationError("Password fields didn't match.")

        return attrs

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is not correct")
        return value

    def update(self, instance, validated_data):
        instance.set_password(validated_data["password"])
        instance.save()

        return instance
