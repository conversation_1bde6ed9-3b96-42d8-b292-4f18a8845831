from django.db import models
from django.contrib.auth.models import (
    AbstractBaseUser,
    BaseUserManager,
    PermissionsMixin,
)
from datetime import datetime
from django.core.validators import RegexValidator

ADMIN = "Admin"
STAFF = "Staff"
ROLE_LIST = [(ADMIN, ADMIN), (STAFF, STAFF)]

# phone_regex = RegexValidator(
#     regex=r"^\+?1?\d{9,15}$",
#     message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
# )


class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        """Create user by username and password"""
        if not email:
            raise ValueError("User must have an email!")
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self.db)
        return user

    def create_superuser(self, email, password):
        """Create superuser by username and password"""
        user = self.create_user(email=email, password=password)
        user.is_active = True
        user.role = ADMIN
        user.is_superuser = True
        user.save(using=self.db)
        return user


class User(AbstractBaseUser, PermissionsMixin):
    """Custom User model"""

    def get_default_end_date():
        return datetime.strptime("9999-12-31", "%Y-%m-%d")

    USERNAME_FIELD = "email"
    username = None
    email = models.EmailField(max_length=255, unique=True)
    full_name = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    last_login_ip = models.CharField(
        max_length=45, blank=True
    )  # 45 is the longest ip address characters
    contact = models.CharField(max_length=17, blank=True)
    country = models.CharField(
        max_length=100, blank=True
    )  # longest country name -> 56 characters
    role = models.CharField(choices=ROLE_LIST, max_length=255, default=STAFF)
    is_superuser = models.BooleanField(default=False)
    start_date = models.DateTimeField(default=datetime.now)  # license start date
    end_date = models.DateTimeField(default=get_default_end_date)  # license end date
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = UserManager()

    @property
    def is_staff(self):
        return self.is_superuser

    def __str__(self):
        return self.__repr__()

    def __repr__(self):
        return f"{self.email!r}"

    class Meta:
        ordering = ["full_name", "email"]
