import app.core.authapi.helper as authapi_helper


from django.contrib.auth.tokens import default_token_generator
from django.conf import settings as backend_settings

from d<PERSON><PERSON> import utils
from djoser.conf import settings

from templated_mail.mail import BaseEmailMessage

import app.utility.date_helper as utility_date_helper


class PasswordResetEmail(BaseEmailMessage):
    # template_name = "email/password_reset.html"
    def get_context_data(self):
        # PasswordResetEmail can be deleted
        context = super().get_context_data()

        user = context.get("user")
        context["uid"] = utils.encode_uid(user.pk)
        context["token"] = default_token_generator.make_token(user)

        return context

    def send(self, to, *args, **kwargs):
        schema_name = authapi_helper.get_schema_name()
        if schema_name == "public":
            schema_name = ""
        html_path = "email/password_reset.html"
        title = f"UtterUnicorn{f' X {schema_name}' if schema_name else ''}"
        subject = f"{title}: Password Reset Notification"
        from_ = f"UtterUnicorn <{backend_settings.DEFAULT_FROM_EMAIL}>"

        context_data = self.get_context_data()

        schema_name_replaced = str(schema_name).replace("_", "-")

        # kwargs
        url = f"{settings.HTTPS_APP_URL_1}{f'/org/{schema_name_replaced}' if schema_name else ''}{settings.PASSWORD_RESET_CONFIRM_URL.format(**context_data)}"
        target_email = context_data.get("user").email
        target_name = (
            context_data.get("user").full_name
            if context_data.get("user").full_name
            else target_email.split("@")[0]
        )
        current_year_str = utility_date_helper.get_current_year_str()
        custom_kwargs = {
            "uid": context_data.get("uid"),
            "token": context_data.get("token"),
            "url": url,
            "preferred_name": target_name,
            "email": target_email,
            "current_year_str": current_year_str,
        }
        authapi_helper.send_user_email_with_html_template(
            html_path,
            subject,
            from_,
            [target_email],
            **custom_kwargs,
        )


class PasswordChangedConfirmationEmail(BaseEmailMessage):
    # template_name = "email/password_changed_confirmation.html"

    def send(self, to, *args, **kwargs):
        schema_name = authapi_helper.get_schema_name()
        if schema_name == "public":
            schema_name = ""
        html_path = "email/password_changed_confirmation.html"
        title = f"UtterUnicorn{f' X {schema_name}' if schema_name else ''}"
        subject = f"{title}: Password Changed Notification"
        from_ = f"UtterUnicorn <{backend_settings.DEFAULT_FROM_EMAIL}>"

        context_data = self.get_context_data()

        target_email = context_data.get("user").email
        target_name = (
            context_data.get("user").full_name
            if context_data.get("user").full_name
            else target_email.split("@")[0]
        )
        current_year_str = utility_date_helper.get_current_year_str()
        # kwargs
        custom_kwargs = {
            "preferred_name": target_name,
            "email": target_email,
            "current_year_str": current_year_str,
        }
        authapi_helper.send_user_email_with_html_template(
            html_path,
            subject,
            from_,
            [target_email],
            **custom_kwargs,
        )
