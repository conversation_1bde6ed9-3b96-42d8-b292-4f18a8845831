from django.db import models
from app.utility.models import BaseModel
import app.chats.chat.models as chat_models


# 5 stages:
# 1. User click on "Begin Chatting" ->Create IPAddressRecord without conversation (Who view chatbot)
# 2. User Send the first message -> Collect Email (create email instance) -> Update conversation id to the IP Address Record (Who use chatbot)
# 3. User Send the >= 5 message -> (Who likes chatbot) (considering to remove this)
# 4. User Send the 1 to 10 message -> Trigger CTA Download (Who view CTA)
#   a) Run "GetMarketingMaterial" in document view + create Download Material Button Record
#   b) Return doc to render at the FE
# 5. User get to see which material to download -> Email input box (Who click CTA)
#   a) User input the email > Run "SendMarketingMaterial" in document view
#   b) update Download Material Button Record clicked_at and email + Download email returned to the user


# Create your models here.
class IPAddressRecord(BaseModel):
    """
    ip_address: string
    created_at: tracking the time
    created_by: tracking the user
    """

    ip_address = models.CharField(
        max_length=45, blank=True, null=True
    )  # 45 is the longest ip address characters
    country = models.Char<PERSON>ield(
        max_length=100, blank=True, null=True
    )  # longest country name -> 56 characters
    conversation = models.OneToOneField(
        chat_models.Conversation,
        related_name="ip_address_record",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["-created_at"]
