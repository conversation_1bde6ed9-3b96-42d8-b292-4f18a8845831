from typing import List, Dict, Union
from pydantic import BaseModel, Field


class AmelioResponseOutput(BaseModel):
    bot_response: List[str] = Field(description="List of messages in Markdown format")
    conversation_id: str = Field(description="Conversation ID")
    context_summary: str = Field(description="A concise summary of the current conversation context")
    decision_rationale: str = Field(description="Explanation of why this response was chosen")
    additional_context: Dict[str, Union[str, float, List[str], Dict[str, str]]] = Field(
        default_factory=lambda: {
            "user_intent": "",
            "confidence_score": 0.0,
            "previous_states": [],
            "relevant_entities": {}
        }
    )
    is_completed: bool = Field(description="Whether the response is completed")