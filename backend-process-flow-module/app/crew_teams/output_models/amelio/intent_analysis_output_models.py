from typing import List, Optional

from pydantic import BaseModel, Field


# Define a nested model for the context
class Context(BaseModel):
    name: Optional[str] = None
    mobile: Optional[str] = None
    nric: Optional[str] = None
    dob: Optional[str] = None
    appointment_types: Optional[List[str]] = None
    branch: Optional[str] = None
    doctor: Optional[str] = None
    date: Optional[str] = None
    time: Optional[str] = None
    appointment_request: Optional[str] = None

class AmelioFlowStageIdentification(BaseModel):
    node_id: str  # The ID of the identified node in the defined flow (e.g., 'start-1')
    state_id: str  # The ID of the state that is relevant to the user's intent
    context: Context = Field(default_factory=Context)  # Use the nested model as default


class RubricScore(BaseModel):
    analysis_quality: dict = {
        "score": int,
        "rating": str,  # Options: "below_standard", "basic", "good", "professional"
        "reason": str
    }
    conciseness: dict = {
        "score": int,
        "rating": str,  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise" 
        "reason": str
    }
    relevance_of_information: dict = {
        "score": int,
        "rating": str,  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
        "reason": str
    }


class ResponseOutput(BaseModel):
    bot_response: List[str]
    context_summary: str
    decision_rationale: str
    additional_context: dict = {
        "user_intent": str,
        "confidence_score": float,
        "previous_states": list,
        "relevant_entities": dict
    }
    is_completed: bool
