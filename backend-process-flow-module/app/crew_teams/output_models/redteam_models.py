from typing import List, Dict

from pydantic import BaseModel

"""
This module contains Pydantic models for representing customer profiles and scenarios
used in red team evaluations. These models define the structure for customer data,
their interactions, personalities, and overall scenarios.
"""


class Contact(BaseModel):
    """
    Represents contact information for a customer.

    Attributes:
        phone (str): Contact number
        email (str): Email address
    """
    phone: str
    email: str


class Demographics(BaseModel):
    """
    Represents demographic information for a customer.

    Attributes:
        age (str): Age range
        occupation (str): Professional background
        location (str): Area in Singapore
        cultural_background (str): Cultural context
    """
    age: str
    occupation: str
    location: str
    cultural_background: str


class CustomerProfile(BaseModel):
    """
    Represents a customer profile with detailed identifying information.

    Attributes:
        name (str): Customer's full name
        contact (Contact): Contact information
        demographics (Demographics): Demographic information
    """
    name: str
    contact: Contact
    demographics: Demographics


class Timing(BaseModel):
    """
    Represents event timing information.

    Attributes:
        start_time (str): Event start time
        end_time (str): Event end time
    """
    start_time: str
    end_time: str


class Children(BaseModel):
    """
    Represents information about children attending the event.

    Attributes:
        count (str): Number of children
        age_range (str): Age range of children
    """
    count: str
    age_range: str


class Attendees(BaseModel):
    """
    Represents attendee information for the event.

    Attributes:
        adults (str): Number of adults
        children (Children): Information about children attending
    """
    adults: str
    children: Children


class Occasion(BaseModel):
    """
    Represents occasion details for the event.

    Attributes:
        type (str): Private or Corporate
        specific_occasion (str): Type of celebration/event
        theme (str): Event theme
        budget (str): Budget range
    """
    type: str
    specific_occasion: str
    theme: str
    budget: str


class SpecialRequirements(BaseModel):
    """
    Represents special requirements for the event.

    Attributes:
        dietary (str): Dietary restrictions
        cultural (str): Cultural considerations
        other (str): Other special needs
    """
    dietary: str
    cultural: str
    other: str


class EventDetails(BaseModel):
    """
    Represents complete event details.

    Attributes:
        date (str): Event date
        timing (Timing): Event timing information
        location (str): Event venue/location
        attendees (Attendees): Attendee information
        occasion (Occasion): Occasion details
        special_requirements (SpecialRequirements): Special requirements
    """
    date: str
    timing: Timing
    location: str
    attendees: Attendees
    occasion: Occasion
    special_requirements: SpecialRequirements


class JourneyStage(BaseModel):
    """
    Represents the customer's journey stage in the event planning process.

    Attributes:
        current_stage (str): Current stage in customer journey
        next_steps (str): Expected next steps
        timeline (str): Expected timeline for decision-making
    """
    current_stage: str
    next_steps: str
    timeline: str


class CustomerPersonality(BaseModel):
    """
    Represents the personality traits and communication preferences of a customer.

    Attributes:
        temperament (str): Customer's temperament
        communication_style (str): Preferred communication method and style
        decision_making (str): Decision-making approach
        emotional_state (str): Current emotional state
    """
    temperament: str
    communication_style: str
    decision_making: str
    emotional_state: str


class ScenarioContext(BaseModel):
    """
    Represents the context of the customer's event planning scenario.

    Attributes:
        motivation (str): Reason for planning this event
        concerns (str): Key concerns or priorities
        expectations (str): Expected outcomes and service level
        previous_experience (str): Past experience with event planning
    """
    motivation: str
    concerns: str
    expectations: str
    previous_experience: str


class CustomerScenario(BaseModel):
    """
    Represents a complete customer scenario including profile, event details, and context.

    Attributes:
        customer_profile (CustomerProfile): Detailed profile of the customer
        event_details (EventDetails): Complete event details
        journey_stage (JourneyStage): Customer's journey stage
        personality (CustomerPersonality): Customer's personality traits
        scenario_context (ScenarioContext): Context of the scenario
    """
    customer_profile: CustomerProfile
    event_details: EventDetails
    journey_stage: JourneyStage
    personality: CustomerPersonality
    scenario_context: ScenarioContext
    objectives: Dict[str, str]


class CustomerScenarioList(BaseModel):
    """
    A container for multiple customer scenarios.

    Attributes:
        scenarios (List[CustomerScenario]): List of customer scenarios
    """
    scenarios: List[CustomerScenario]


class ScoreMetric(BaseModel):
    """
    Represents a single scoring metric with score, rating and explanation.

    Attributes:
        score (int): Numeric score from 0-10
        rating (str): Qualitative rating of the score
        reason (str): Detailed explanation for the assigned score
    """
    score: int
    rating: str
    reason: str


class ScenarioScore(BaseModel):
    """
    Represents the scoring metrics for a customer scenario.

    Attributes:
        accuracy (ScoreMetric): Assessment of scenario's alignment with customer intent and context
        detail (ScoreMetric): Assessment of scenario's descriptive depth and completeness
        relevance (ScoreMetric): Assessment of how well inputs are integrated into scenario
        realism (ScoreMetric): Assessment of scenario's real-world authenticity
    """
    accuracy: ScoreMetric
    detail: ScoreMetric
    relevance: ScoreMetric
    realism: ScoreMetric


class ScenarioScoreList(BaseModel):
    """
    A container for multiple scenario scores.

    Attributes:
        scenario_scores (List[ScenarioScore]): List of scenario scoring metrics
    """
    scenario_scores: List[ScenarioScore]


class BotResponseModel(BaseModel):
    """
    Model representing a bot response.

    Attributes:
        bot_response (str): The response message from the bot
    """
    bot_response: str


class FormulatedMessagesModel(BaseModel):
    """
    Model representing formulated messages to send to customer service.

    Attributes:
        formulated_messages (List[str]): List of messages to send to customer service
    """
    formulated_messages: List[str]


class ConversationQuestionScoreModel(BaseModel):
    """
    Model representing the scoring metrics for a conversation question.

    Attributes:
        accuracy (ScoreMetric): Assessment of how well the scenario aligns with provided inputs
        detail (ScoreMetric): Assessment of the comprehensiveness and descriptive depth
        relevance (ScoreMetric): Assessment of context integration and utilization
        realism (ScoreMetric): Assessment of plausibility and authenticity
    """
    accuracy: ScoreMetric
    detail: ScoreMetric 
    relevance: ScoreMetric
    realism: ScoreMetric


class BotResponseAnalysisModel(BaseModel):
    """
    Model representing the analysis of a bot response.

    Attributes:
        objective_achieved (bool): Whether the conversation objective was achieved
        follow_up_response (str): Suggested follow-up message if objective not achieved
    """
    objective_achieved: str
    follow_up_response: str


class ConversationAnalysisModel(BaseModel):
    """
    Model representing the analysis of a conversation.
    """
    accuracy: ScoreMetric
    detail: ScoreMetric
    relevance: ScoreMetric
    realism: ScoreMetric


class AssessorRubicScore(BaseModel):
    """
    Model representing the rubric scoring metrics for assessing bot responses.

    Attributes:
        responsiveness (dict): Assessment of bot's response time and engagement
        accuracy (dict): Assessment of correctness and relevance of responses
        clarity (dict): Assessment of how clear and understandable responses are
        tone (dict): Assessment of appropriateness of communication style
    """
    responsiveness: dict = {
        "score": int,
        "rating": str,  # Options: "poor", "fair", "good", "excellent"
        "reason": str
    }
    accuracy: dict = {
        "score": int,
        "rating": str,  # Options: "poor", "fair", "good", "excellent" 
        "reason": str
    }
    clarity: dict = {
        "score": int,
        "rating": str,  # Options: "poor", "fair", "good", "excellent"
        "reason": str
    }
    tone: dict = {
        "score": int,
        "rating": str,  # Options: "poor", "fair", "good", "excellent"
        "reason": str
    }


class AssessorRecommendation(BaseModel):
    """
    Model representing a recommendation for improving a task or agent.

    Attributes:
        name (str): Name of the task or agent that needs improvement
        yaml_path (str): Path to the YAML file defining the task or agent
        reason (str): Detailed explanation of why modification is needed
        recommendation (str): Actionable feedback to improve the task or agent
    """
    name: str
    _type: str
    yaml_path: str 
    reason: str
    recommendation: list


class AssessorRecommendations(BaseModel):
    """
    Model representing a list of recommendations from the assessor.

    Attributes:
        recommendations (List[AssessorRecommendation]): List of recommendations for improvements
    """
    recommendations: List[AssessorRecommendation]


class UpdatePromptTaskOutput(BaseModel):
    original_prompt: str
    updated_prompt: str


class EvaluateAnalysisTaskOutput(BaseModel):
    responsiveness: dict
    clarity: dict
    effectiveness: dict