from pydantic import BaseModel


class ProposalServiceValidation(BaseModel):
    """
    Validates whether the response is a proposal or service recommendation.

    Attributes:
        proposal_or_service (bool): True if the response is a proposal or service recommendation, False otherwise.
        reason (str): Explanation for the validation result.
    """
    proposal_or_service: bool
    reason: str


class HappierResponseOutput(BaseModel):
    context_summary: str
    decision_rationale: str
    additional_context: dict = {
        "user_intent": str,
        "confidence_score": float,
        "previous_states": list,
        "relevant_entities": dict
    }
    is_completed: bool