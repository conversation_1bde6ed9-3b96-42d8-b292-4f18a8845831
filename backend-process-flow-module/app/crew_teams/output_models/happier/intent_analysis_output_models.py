from typing import List, Optional, Literal

from pydantic import BaseModel, Field


# Define a nested model for the context
class Context(BaseModel):
    name: Optional[str] = None
    contact_number: Optional[str] = None
    email: Optional[str] = None
    event_date: Optional[str] = None
    event_start_time: Optional[str] = None
    event_end_time: Optional[str] = None
    event_location: Optional[str] = None
    number_of_adults_attending: Optional[int] = None
    number_of_kids_attending: Optional[str] = None
    occasion_type: Optional[str] = None
    occasion: Optional[str] = None
    theme: Optional[str] = None
    budget: Optional[str] = None
    event_additional_details: Optional[str] = None
    services_list: List[str] = Field(default_factory=list)  # Default as empty list
    customer_agreed_to_collect_information: Optional[bool] = None
    billing_address: Optional[str] = None
    selected_proposal: Optional[str] = None


class FlowStageIdentification(BaseModel):
    node_id: str  # The ID of the identified node in the defined flow (e.g., 'start-1')
    # state_id: str  # The ID of the state that is relevant to the user's intent
    context: Context = Field(default_factory=Context)  # Use the nested model as default


class RubricScore(BaseModel):
    analysis_quality: dict = {
        "score": int,
        "rating": str,  # Options: "below_standard", "basic", "good", "professional"
        "reason": str
    }
    conciseness: dict = {
        "score": int,
        "rating": str,  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise" 
        "reason": str
    }
    relevance_of_information: dict = {
        "score": int,
        "rating": str,  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
        "reason": str
    }


class ResponseOutput(BaseModel):
    bot_response: List[str]
    context_summary: str
    decision_rationale: str
    additional_context: dict = {
        "user_intent": str,
        "confidence_score": float,
        "previous_states": list,
        "relevant_entities": dict
    }
    is_completed: bool


class HappierCrewResponseOutput(BaseModel):
    bot_response: List[str]

class TopicOutput(BaseModel):
    topic_type: str  # Options: continuous, new, old, fetch_more_messages
    reason: str
    context: Context = Field(default_factory=Context)  # Use the nested model as default


class RestartProposalOutput(BaseModel):
    restart_proposal: str
    reason: str

class HappierContext(BaseModel):
    name: Optional[str] = None
    contact_number: Optional[str] = None
    email: Optional[str] = None
    event_date: Optional[str] = None
    event_start_time: Optional[str] = None
    event_end_time: Optional[str] = None
    event_location: Optional[str] = None
    number_of_adults_attending: Optional[int] = None
    number_of_kids_attending: Optional[str] = None
    occasion_type: Optional[str] = None
    occasion: Optional[str] = None
    theme: Optional[str] = None
    budget: Optional[str] = None
    event_additional_details: Optional[str] = None
    services_list: List[str] = Field(default_factory=list)  # Default as empty list
    customer_agreed_to_collect_information: Optional[bool] = None
    billing_address: Optional[str] = None
    selected_proposal: Optional[str] = None


class IntentInfo(BaseModel):
    intent: str
    confidence: str  # High/Medium/Low
    reasoning: str
    course_of_action: Literal["Event Proposal Generation", "Proposal Revision", "Business Enquiry", "Proposal Confirmation", "Manager Escalation"]
    reason_for_course_of_action: str

class InferredIntentsOutput(BaseModel):
    inferred_intents: List[IntentInfo]


class IntentAnalysisOutput(BaseModel):
    intent: str
    confidence: Literal["High", "Medium", "Low"]
    reasoning: str
    course_of_action: Literal["Event Proposal Generation", "Proposal Revision", "Business Enquiry", "Proposal Confirmation", "Manager Escalation", "Unknown Intent"]
    reason_for_course_of_action: str
    node_id: str
    next_node_id: str
    context: Context

class IntentAnalysisOutputList(BaseModel):
    intents: List[IntentAnalysisOutput] = Field(default_factory=list)
