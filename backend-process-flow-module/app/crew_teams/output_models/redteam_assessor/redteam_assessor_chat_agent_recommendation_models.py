from typing import List

from pydantic import BaseModel, Field


class UpdateChatPromptTaskOutput(BaseModel):
    type_: str = Field(..., description="Recommendation for Task or Agent")
    name: str = Field(..., description="Name of the task or agent")
    component: str = Field(..., description="The component needing change")
    reasoning: str = Field(..., description="Detailed reasoning for the recommendation")
    impact_analysis: str = Field(..., description="Impact of the recommendation")
    risk_analysis: str = Field(..., description="Risk analysis of the recommendation")
    expected_outcome: str = Field(..., description="Expected outcome of the recommendation")

class UpdateChatPromptTaskOutputArray(BaseModel):
    tasks: List[UpdateChatPromptTaskOutput]