from typing import Literal

from pydantic import BaseModel, Field


class ScoreMetric(BaseModel):
    """Represents a scoring metric with numeric score, impact rating, and explanation."""
    score: int = Field(..., description="Numeric score for the metric")
    rating: Literal["low_impact", "moderate_impact", "high_impact", "exceptional_impact"] = Field(..., description="Impact rating for the metric")
    reason: str = Field(..., description="Explanation for the assigned score, describing strengths and areas for improvement")


class RecommendationScoringModel(BaseModel):
    """Model for scoring various aspects of a recommendation."""
    clarity: ScoreMetric
    impact: ScoreMetric
    feasibility: ScoreMetric
    evidence: ScoreMetric
    risk: ScoreMetric


class AgentRecommendation(BaseModel):
    """Represents a recommendation for an agent, including explanations and proposed changes."""
    agent_name: str
    explanation: list[str]
    proposed_changes: list[str]
    impact_assessment: list[str]


class FinalAgentSelectionModel(BaseModel):
    """Model representing the final selection of agent recommendations."""
    final_recommendations: list[AgentRecommendation]
