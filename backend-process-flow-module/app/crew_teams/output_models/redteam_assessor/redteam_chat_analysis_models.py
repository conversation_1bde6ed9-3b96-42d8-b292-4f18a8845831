from pydantic import BaseModel


class ConversationSummaryModel(BaseModel):
    """
    Model representing a summary of a conversation.

    Attributes:
        conversation_summary (str): A concise summary of the conversation.
    """
    conversation_summary: str


class ObjectiveEvaluationModel(BaseModel):
    """
    Model representing the evaluation of an objective within a conversation.

    Attributes:
        objective_met (bool): Indicates whether the objective was met.
        objective_reasoning (Optional[str]): Reasoning for why the objective failed, if applicable.
    """
    objective_met: bool
    objective_reasoning: str


class ScoreMetric(BaseModel):
    """
    Represents a single scoring metric with score, rating, and explanation.

    Attributes:
        score (int): Numeric score from 0-10.
        rating (str): Qualitative rating of the score.
        reason (str): Detailed explanation for the assigned score.
    """
    score: int
    rating: str
    reason: str


class ToneEvaluationModel(BaseModel):
    """
    Model representing the tone evaluation of a conversation.

    Attributes:
        tone_evaluation (ScoreMetric): The evaluation metrics for tone.
    """
    tone_evaluation: ScoreMetric

class QualityEvaluationModel(BaseModel):
    """
    Model representing the quality evaluation of a conversation.

    Attributes:
        accuracy (ScoreMetric): The evaluation metrics for accuracy.
        clarity (ScoreMetric): The evaluation metrics for clarity.
        empathy (ScoreMetric): The evaluation metrics for empathy.
        relevance (ScoreMetric): The evaluation metrics for relevance.
    """
    accuracy: ScoreMetric
    clarity: ScoreMetric
    empathy: ScoreMetric
    relevance: ScoreMetric


class CustomerSatisfactionMetrics(BaseModel):
    """
    Model representing detailed customer satisfaction metrics.

    Attributes:
        satisfaction_score (int): Numeric score from 1-10 indicating overall satisfaction.
        satisfaction_level (str): Qualitative assessment of satisfaction level.
        key_factors (List[str]): Main factors contributing to satisfaction/dissatisfaction.
        improvement_recommendations (List[str]): Specific recommendations for improving customer experience.
    """
    satisfaction_score: int
    satisfaction_level: str
    key_factors: list[str]
    improvement_recommendations: list[str]


class CustomerSatisfactionModel(BaseModel):
    """
    Model representing the complete customer satisfaction analysis.

    Attributes:
        satisfaction_metrics (CustomerSatisfactionMetrics): Detailed satisfaction evaluation metrics.
    """
    satisfaction_metrics: CustomerSatisfactionMetrics


class FlowAnalysisMetrics(BaseModel):
    """
    Model representing detailed conversation flow analysis metrics.

    Attributes:
        bottlenecks (List[FlowBottleneck]): List of identified conversation bottlenecks.
        flow_efficiency_score (int): Overall score for conversation flow (1-10).
        primary_issues (List[str]): Main issues affecting conversation flow.
        improvement_suggestions (List[str]): Recommendations for improving flow.
    """
    flow_efficiency_score: int
    primary_issues: list[str]
    improvement_suggestions: list[str]


class FlowAnalysisModel(BaseModel):
    """
    Model representing the complete flow analysis of a conversation.

    Attributes:
        flow_metrics (FlowAnalysisMetrics): Detailed flow analysis metrics.
    """
    flow_metrics: FlowAnalysisMetrics


class ImprovementAction(BaseModel):
    """
    Model representing a specific improvement action.

    Attributes:
        action (str): The specific action to be taken.
        expected_impact (str): Expected outcome of implementing this action.
        implementation_difficulty (str): Estimated difficulty (Easy/Medium/Hard).
        timeline (str): Suggested implementation timeline.
    """
    action: str
    expected_impact: str
    implementation_difficulty: str
    timeline: str


class PrioritizedImprovements(BaseModel):
    """
    Model representing improvements grouped by priority level.

    Attributes:
        priority_level (str): Priority level (High/Medium/Low).
        improvements (List[ImprovementAction]): List of improvement actions.
        rationale (str): Explanation for the priority assignment.
    """
    priority_level: str
    improvements: list[ImprovementAction]
    rationale: str


class ImprovementPlanMetrics(BaseModel):
    """
    Model representing the complete improvement plan metrics.

    Attributes:
        prioritized_improvements (List[PrioritizedImprovements]): Improvements organized by priority.
        critical_areas (List[str]): Most critical areas needing attention.
        quick_wins (List[str]): Easily implementable improvements with good impact.
        long_term_goals (List[str]): Strategic long-term improvement goals.
    """
    prioritized_improvements: list[PrioritizedImprovements]
    critical_areas: list[str]
    quick_wins: list[str]
    long_term_goals: list[str]


class ImprovementPlanModel(BaseModel):
    """
    Model representing the complete improvement plan analysis.

    Attributes:
        improvement_metrics (ImprovementPlanMetrics): Detailed improvement plan metrics.
    """
    improvement_metrics: ImprovementPlanMetrics


class PromptChange(BaseModel):
    """
    Model representing a specific recommended change to a prompt.

    Attributes:
        current_prompt (str): The current prompt text being analyzed.
        suggested_prompt (str): The suggested improved prompt.
        rationale (str): Detailed explanation of why this change is needed.
        expected_improvements (List[str]): Specific improvements expected from this change.
        potential_risks (List[str]): Potential risks or downsides to consider.
    """
    current_prompt: str
    suggested_prompt: str
    rationale: str
    expected_improvements: list[str]
    potential_risks: list[str]


class AgentPromptRecommendation(BaseModel):
    """
    Model representing recommendations for a specific agent's prompts.

    Attributes:
        agent_name (str): Name of the agent being analyzed.
        task_name (str): Name of the task this agent performs.
        current_performance (str): Assessment of current prompt effectiveness.
        prompt_changes (List[PromptChange]): Specific changes recommended for the prompts.
        system_level_suggestions (List[str]): Broader suggestions for agent behavior.
    """
    agent_name: str
    task_name: str
    current_performance: str
    prompt_changes: list[PromptChange]


class PromptRecommendationMetrics(BaseModel):
    """
    Model representing the complete prompt improvement analysis.

    Attributes:
        agent_recommendations (List[AgentPromptRecommendation]): Recommendations for each agent.
        global_patterns (List[str]): System-wide patterns or issues identified.
        cross_agent_improvements (List[str]): Suggestions that affect multiple agents.
        priority_changes (List[str]): Most critical prompt changes needed.
    """
    agent_recommendations: list[AgentPromptRecommendation]


class OutputQualityAnalysisModel(BaseModel):
    """
    Model for analyzing the quality of an output across multiple dimensions.

    Attributes:
        completeness_score (ScoreMetric): Evaluation of the output's completeness.
        accuracy_score (ScoreMetric): Evaluation of the output's accuracy.
        clarity_score (ScoreMetric): Evaluation of the output's clarity.
        relevance_score (ScoreMetric): Evaluation of the output's relevance.
        impact_score (ScoreMetric): Evaluation of the output's impact.
    """
    completeness_score: ScoreMetric
    accuracy_score: ScoreMetric
    clarity_score: ScoreMetric
    relevance_score: ScoreMetric
    impact_score: ScoreMetric
