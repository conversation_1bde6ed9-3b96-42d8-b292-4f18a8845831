from typing import List

from pydantic import BaseModel


class Improvement(BaseModel):
    """
    Represents a single improvement suggestion.

    Attributes:
        improvement (str): The suggested improvement.
        reasoning (str): The rationale behind the improvement.
        expected_impact (str): The anticipated effect of implementing the improvement.
    """
    improvement: str
    reasoning: str
    expected_impact: str


class TopImprovementsModel(BaseModel):
    """
    Contains a list of top improvement suggestions.

    Attributes:
        top_improvements (List[Improvement]): A list of the most important improvements.
    """
    top_improvements: List[Improvement]


class RepeatedIssue(BaseModel):
    """
    Represents a recurring issue in the system.

    Attributes:
        issue (str): Description of the repeated issue.
        frequency (str): How often the issue occurs.
        pattern_analysis (str): Analysis of the issue's occurrence pattern.
    """
    issue: str
    frequency: str
    pattern_analysis: str


class RepeatedIssuesModel(BaseModel):
    """
    Contains a list of repeated issues.

    Attributes:
        repeated_issues (List[RepeatedIssue]): A list of identified recurring issues.
    """
    repeated_issues: List[RepeatedIssue]


class KnowledgeGap(BaseModel):
    """
    Represents identified knowledge gaps and recommendations to address them.

    Attributes:
        identified_gaps (List[str]): List of identified knowledge gaps.
        recommendations (List[str]): List of recommendations to address the gaps.
    """
    identified_gaps: List[str]
    recommendations: List[str]

class KnowledgeGapsModel(BaseModel):
    """
    Contains information about knowledge gaps.

    Attributes:
        knowledge_gaps (KnowledgeGap): Details about identified knowledge gaps and recommendations.
    """
    knowledge_gaps: KnowledgeGap


class SatisfactionScoreItem(BaseModel):
    """
    Represents a single satisfaction score item.

    Attributes:
        score (int): Numerical score.
        rating (str): Qualitative rating.
        reason (str): Explanation for the given score and rating.
    """
    score: int
    reason: str

class SatisfactionScoreModel(BaseModel):
    """
    Contains satisfaction scores for different aspects of the service.

    Attributes:
        overall_satisfaction (SatisfactionScoreItem): Score for overall satisfaction.
    """
    overall_satisfaction: SatisfactionScoreItem


class QualityAssessmentItem(BaseModel):
    """
    Represents a single quality assessment item.

    Attributes:
        score (int): Numerical score for the quality aspect.
        rating (str): Qualitative rating for the quality aspect.
        reason (str): Explanation for the given score and rating.
    """
    score: int
    rating: str
    reason: str

class QualityAssessmentModel(BaseModel):
    """
    Contains quality assessments for different aspects of the service.

    Attributes:
        completeness (QualityAssessmentItem): Assessment of completeness.
        accuracy (QualityAssessmentItem): Assessment of accuracy.
        relevance (QualityAssessmentItem): Assessment of relevance.
        actionability (QualityAssessmentItem): Assessment of actionability.
    """
    completeness: QualityAssessmentItem
    accuracy: QualityAssessmentItem
    relevance: QualityAssessmentItem
    actionability: QualityAssessmentItem
