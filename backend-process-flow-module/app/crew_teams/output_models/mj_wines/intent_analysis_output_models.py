from typing import List, Optional

from pydantic import BaseModel, Field


# Define a nested model for the context
class MJWinesContext(BaseModel):
    event: Optional[str] = None
    pairing_food: Optional[str] = None
    price_per_bottle: Optional[str] = None
    wine_variety: Optional[str] = None
    purchase_details: Optional[List[dict]] = None
    add_to_existing_cart: Optional[bool] = None
    existing_items: Optional[List[dict]] = None


class MJWinesFlowStageIdentification(BaseModel):
    node_id: str  # The ID of the identified node in the defined flow (e.g., 'start-1')
    state_id: str  # The ID of the state that is relevant to the user's intent
    context: MJWinesContext = Field(
        default_factory=MJWinesContext
    )  # Use the nested model as default
