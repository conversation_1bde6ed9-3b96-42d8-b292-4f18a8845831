import importlib
import inspect
import json
import logging
import os
import re
import traceback
from pathlib import Path
from typing import List, Optional

import yaml
from crewai import LLM, Agent, Crew, Process, Task
from crewai.tools.base_tool import Tool
from langchain_openai import ChatOpenAI
from openai import OpenAI

import app.utility.general_helper as utility_general_helper
from app.core.authapi import helper as authapi_helper
from app.crew_teams.output_models.bot_response_checker_models import *
from app.crew_teams.output_models.chat_metadata_retriever_models import *
from app.crew_teams.output_models.happier.intent_analysis_output_models import *
from app.crew_teams.output_models.happier.response_output_models import *
from app.crew_teams.output_models.redteam_assessor.redteam_assessor_chat_agent_recommendation_models import *
from app.crew_teams.output_models.redteam_assessor.redteam_assessor_chat_agent_selector_models import *
from app.crew_teams.output_models.redteam_assessor.redteam_batch_analysis_models import *
from app.crew_teams.output_models.redteam_assessor.redteam_chat_analysis_models import *
from app.crew_teams.output_models.redteam_models import *
from app.crew_teams.tools.chromadb_tools import *
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.crew_teams.output_models.amelio.response_output_models import *

from app.utility.chat_constant import GPT_4_MINI_MODEL_CHOICES
from app.crew_teams.output_models.amelio.intent_analysis_output_models import *
from app.crew_teams.output_models.mj_wines.intent_analysis_output_models import *

logger = logging.getLogger(__name__)


def replace_placeholder(json_str):
    # Clean and parse result
    json_str = (
        json_str.replace("```json", "")
        .replace("```markdown", "")
        .replace("```", "")
        .replace("None", "null")
        .replace("True", "true")
        .replace("False", "false")
    )

    return json_str


def patch_up_json_output(json_str):
    json_str = replace_placeholder(json_str)

    # Replace double quotes with single quotes
    json_str = re.sub(r"(\b[a-zA-Z_][a-zA-Z0-9_]*\b)(?=\s*:)", r'"\1"', json_str)

    # Replace single quotes with double quotes
    json_str = re.sub(r"(?<!\w)'(.*?)'(?!\w)", r'"\1"', json_str)

    return json_str


# Get GPT to generate a valid JSON
def patch_json_with_prompt(client, result, e):

    check_proposal_json_message = utility_general_helper.clean_up_prompt(
        f"""You are an expert in writing JSON. I have an invalid JSON format and i want you to fix it.

        Invalid JSON:  
        {result}

        Error message:
        {str(e.args)}

        Guideline:
        1. DO NOT ADD any comments, opinion or feedback in the response.
        2. ONLY return the json response
        3. Make sure it is complied with the JSON standard where by there is no None, True or False. All needs to be null, true or false.
        4. Avoid using " and : at all costs within the sentences/phrases, as they break the JSON format.

        Return me a valid JSON format.
        """
    )

    chat_gpt_response = get_gpt_response(
        client,
        [{"role": "user", "content": check_proposal_json_message}],
        model=GPT_4_MINI_MODEL_CHOICES[0],
    )

    result = patch_up_json_output(chat_gpt_response)

    return result


class AgentCrew:
    """Template Understanding Crew for analyzing PDF templates and creating schemas"""

    def __init__(self, config_path: str, seed_number: Optional[int] = None):
        self.config = self._load_config(config_path)
        self.seed_number = seed_number

        # Configure LLMs
        self.llm = {}
        for model_config in self.config["models"]:
            if str(model_config["name"]).startswith("gpt"):
                if self.seed_number:
                    self.llm[model_config["name"]] = ChatOpenAI(
                        model=model_config["name"],
                        temperature=model_config["temperature"],
                        seed=self.seed_number,
                    )
                else:
                    self.llm[model_config["name"]] = ChatOpenAI(
                        model=model_config["name"],
                        temperature=model_config["temperature"],
                    )
            elif str(model_config["name"]).startswith("groq"):
                print("Calling groq")
                if self.seed_number:
                    self.llm[model_config["name"]] = LLM(
                        model=model_config["name"],
                        seed=self.seed_number,
                    )
                else:
                    self.llm[model_config["name"]] = LLM(
                        model=model_config["name"],
                    )
            else:
                if self.seed_number:
                    self.llm[model_config["name"]] = LLM(
                        model=model_config["name"],
                        base_url=f"{os.environ.get('BEEFY_BASE_URL')}:{model_config['port']}",
                        api_key=os.environ.get("BEEFY_API_KEY"),
                        seed=self.seed_number,
                        custom_llm_provider="openai",
                    )
                else:
                    self.llm[model_config["name"]] = LLM(
                        model=model_config["name"],
                        base_url=f"{os.environ.get('BEEFY_BASE_URL')}:{model_config['port']}",
                        api_key=os.environ.get("BEEFY_API_KEY"),
                        custom_llm_provider="openai",
                    )

        self.scoring_model = None
        self.tools = self._load_tools()
        self.guardrails = self._load_guardrails()

    def _load_config(self, config_path: str) -> dict:
        with open(config_path, "r") as file:
            return yaml.safe_load(file)

    def _load_tools(self):
        """Load all @tool decorated Tool instances from modules under app.crew_teams.tools into a dictionary"""
        _tools = {}
        schema_name = authapi_helper.get_schema_name()
        tools_directory = os.path.join(
            os.path.dirname(__file__), "tools", schema_name
        )  # Adjust the path if needed

        def process_directory(
            directory, package_prefix=f"app.crew_teams.tools.{schema_name}"
        ):
            # Iterate through all items in the directory
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)

                # Skip __pycache__ directories
                if item == "__pycache__":
                    continue

                if os.path.isdir(item_path) and not item.startswith("_"):
                    # Recursively process subdirectories
                    new_prefix = f"{package_prefix}.{item}"
                    process_directory(item_path, new_prefix)

                elif item.endswith(".py") and item != "__init__.py":
                    # Process Python files
                    module_name = (
                        f"{package_prefix}.{item[:-3]}"  # Strip the .py extension
                    )

                    try:
                        # Dynamically import each module
                        module = importlib.import_module(module_name)

                        # Iterate over each attribute in the module to find instances of Tool
                        for name, obj in inspect.getmembers(module):
                            # Check if the object is an instance of Tool (decorated by @tool)
                            if isinstance(obj, Tool):
                                _tools[f"{name}"] = (
                                    obj  # Store with module name prefix for uniqueness
                                )
                    except ImportError as e:
                        print(f"Error importing {module_name}: {e}")

        if os.path.exists(tools_directory):
            # Start processing from the root tools directory
            process_directory(tools_directory)
        return _tools

    def _load_guardrails(self):
        """Load all @tool decorated Tool instances from modules under app.crew_teams.tools into a dictionary"""
        _guardrails = {}
        schema_name = authapi_helper.get_schema_name()
        guardrails_client_directory = os.path.join(
            os.path.dirname(__file__), "guardrails", schema_name
        )  # Adjust the path if needed
        guardrails_common_directory = os.path.join(
            os.path.dirname(__file__), "guardrails", "common"
        )  # Adjust the path if needed

        def process_directory(
            directory, package_prefix=f"app.crew_teams.guardrails.{schema_name}"
        ):
            # Iterate through all items in the directory
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)

                # Skip __pycache__ directories
                if item == "__pycache__":
                    continue

                if os.path.isdir(item_path) and not item.startswith("_"):
                    # Recursively process subdirectories
                    new_prefix = f"{package_prefix}.{item}"
                    process_directory(item_path, new_prefix)

                elif item.endswith(".py") and item != "__init__.py":
                    # Process Python files
                    module_name = (
                        f"{package_prefix}.{item[:-3]}"  # Strip the .py extension
                    )

                    try:
                        # Dynamically import each module
                        module = importlib.import_module(module_name)

                        # Iterate over each attribute in the module to find guardrail functions
                        for name, obj in inspect.getmembers(module):
                            if inspect.isfunction(obj):
                                _guardrails[f"{name}"] = (
                                    obj  # Store with module name prefix for uniqueness
                                )
                    except ImportError as e:
                        print(f"Error importing {module_name}: {e}")

        if os.path.exists(guardrails_client_directory):
            # Start processing from the root guardrails directory
            process_directory(guardrails_client_directory)

        if os.path.exists(guardrails_common_directory):
            # Start processing from the root guardrails directory
            process_directory(
                guardrails_common_directory,
                package_prefix=f"app.crew_teams.guardrails.common",
            )

        return _guardrails

    @staticmethod
    def _chain_validations(*validators):
        """Chain multiple validators together."""

        def combined_validator(result):
            for validator in validators:
                success, data = validator(result)
                if not success:
                    return (False, data)
                result = data
            return (True, result)

        return combined_validator

    def _output_crew_result(self, tasks):
        # Update tasks with agent information based on the task's 'agent' field
        task_dicts = []
        task_full_dicts = []

        agents_dict = {agent.id: agent.__dict__ for agent in list(self.agents.values())}

        for task in tasks:
            task_dict = task.output.__dict__.copy()

            # Basic output dict with summary info
            output_dict = {
                "summary": task_dict["summary"],
                "raw": task_dict["raw"],
                "agent": task_dict["agent"],
                "pydantic": task_dict["pydantic"],
            }
            task_dicts.append(output_dict)

            # Full output dict with complete task and agent details
            full_dict = task_dict.copy()
            agent_id = task.agent.id
            if agent_id in agents_dict:
                full_dict["agent_details"] = agents_dict[agent_id]
            task_full_dicts.append(full_dict)

        return task_dicts, task_full_dicts

    def create_agents(self):
        self.agents = {}
        for agent_id, agent_config in self.config["agents"].items():
            # Remove the model key from the agent config
            if "model" in agent_config:
                model = agent_config["model"]
                del agent_config["model"]
            else:
                model = self.config["default_model"]

            if "tools" in agent_config:
                agent_config["tools"] = [
                    self.tools[tool_name] for tool_name in agent_config["tools"]
                ]

            self.agents[agent_id] = Agent(
                llm=self.llm[model],
                config=agent_config,
            )

        self.agents_dict = {
            agent.__dict__["role"]: agent.__dict__
            for agent in list(self.agents.values())
        }

    def create_tasks(self, **kwargs) -> List[Task]:
        # Import any output models specified in task configs
        output_models = {}
        for task_config in self.config["tasks"].values():
            if "json_output" in task_config:
                model_name = task_config["json_output"]
                if model_name not in output_models:
                    # Get the model class from the already imported models
                    output_models[model_name] = globals()[model_name]

            if "output_pydantic" in task_config:
                pydantic_model_name = task_config["output_pydantic"]
                if pydantic_model_name not in output_models:
                    # Get the model class from the already imported models
                    output_models[pydantic_model_name] = globals()[pydantic_model_name]

        tasks = {}
        for task_id, task_config in self.config["tasks"].items():
            # Create a copy of task config to avoid modifying the original
            task_kwargs = task_config.copy()

            # Format description with arguments if specified
            if "arguments" in task_kwargs:
                # Get the required variables for this task
                task_vars = {arg: kwargs[arg] for arg in task_kwargs["arguments"]}
                task_kwargs["description"] = task_kwargs["description"].format(
                    **task_vars
                )
                # Remove arguments key as it's not needed for Task creation
                del task_kwargs["arguments"]

            # Get context tasks if specified
            context = []
            if "context" in task_kwargs:
                context = [tasks[task_name] for task_name in task_kwargs["context"]]
                task_kwargs["context"] = context

            # Add agent reference
            task_kwargs["agent"] = self.agents[task_kwargs["agent"]]

            # Add output model if specified
            if "json_output" in task_kwargs:
                task_kwargs["json_output"] = output_models[task_kwargs["json_output"]]

            if "output_pydantic" in task_kwargs:
                task_kwargs["output_pydantic"] = output_models[
                    task_kwargs["output_pydantic"]
                ]

            if "guardrails" in task_kwargs:
                task_kwargs["guardrail"] = self._chain_validations(
                    *[
                        self.guardrails[guardrail_name]
                        for guardrail_name in task_kwargs["guardrails"]
                    ]
                )

            # Create task
            tasks[task_id] = Task(**task_kwargs)

            if "evaluation_task" in task_id:
                if "json_output" in task_kwargs:
                    self.scoring_model = task_kwargs["json_output"]
                elif "output_pydantic" in task_kwargs:
                    self.scoring_model = task_kwargs["output_pydantic"]

        # Return tasks in original config order
        return list(tasks.values())

    def _retrieve_result(self, client, task_output):
        if task_output.pydantic is not None:
            pydantic_model = task_output.pydantic
            pydantic_model = pydantic_model.model_dump()
            return pydantic_model

        else:
            # Remove JSON formatting
            result = replace_placeholder(task_output.__dict__["raw"])

            try:
                # Convert to dict
                result = json.loads(result)
            except json.JSONDecodeError as e:
                print("Warning: Result is not valid JSON")

                try:
                    result = patch_json_with_prompt(client, result, e)
                    result = patch_up_json_output(result)
                    result = json.loads(result)

                except json.JSONDecodeError:
                    print("Warning: Result is not valid JSON")
                    result = "Json output is not valid JSON"
                    raise
            return result

    def run(self, **kwargs) -> dict:
        if not hasattr(self, "agents"):
            self.create_agents()

        attempts = 0
        max_attempts = 3
        last_error = None
        previous_feedback = None

        client = OpenAI()

        while attempts < max_attempts:
            try:
                result = None
                # Add previous feedback to kwargs
                kwargs["previous_feedback"] = previous_feedback

                # Recreate tasks with updated kwargs including feedback
                tasks = self.create_tasks(**kwargs)

                crew = Crew(
                    agents=list(self.agents.values()),
                    tasks=tasks,
                    process=(
                        Process.sequential
                        if self.config["crew_config"]["process"] == "sequential"
                        else Process.hierarchy
                    ),
                    verbose=self.config["crew_config"]["verbose"],
                )

                result = crew.kickoff()

                total_score = None
                if self.scoring_model:

                    # Remove JSON formatting
                    result = self._retrieve_result(client, result)

                    # Get evaluation scores if evaluation task exists
                    total_score = 0
                    for field in self.scoring_model.__fields__.keys():
                        if field in result and "score" in result[field]:
                            total_score += result[field]["score"]
                        else:
                            print(
                                f"Warning: Field '{field}' or 'score' key is missing in the result"
                            )

                # If score meets threshold, return result
                if (
                    total_score is None
                    or total_score
                    >= self.config["crew_config"]["output_quality_threshold"]
                ):

                    if self.config["crew_config"].get(
                        "content_task_output_json_flag", False
                    ):
                        result = tasks[
                            self.config["crew_config"]["content_task_output"]
                        ].output
                        result = self._retrieve_result(client, result)

                    else:
                        result = tasks[
                            self.config["crew_config"]["content_task_output"]
                        ].output.__dict__["raw"]

                    # Output the agent and task output into a dict
                    task_output, task_full_dicts = self._output_crew_result(tasks)

                    final_task_output_index = self.config["crew_config"][
                        "content_task_output"
                    ]

                    return result, task_output, task_full_dicts, final_task_output_index
                else:
                    print(
                        "Score is less than threshold",
                        total_score,
                        self.config["crew_config"]["output_quality_threshold"],
                    )

            except Exception as e:
                traceback.print_exc()
                last_error = e
                print(f"Attempt {attempts + 1} failed with error: {str(e)}")

                if result is None:
                    result = str(e)

            attempts += 1
            previous_feedback = result

        # Exceeded max attempts
        raise RuntimeError(
            f"Failed to analyze template after {max_attempts} attempts. Last error: {str(last_error)}"
        )
