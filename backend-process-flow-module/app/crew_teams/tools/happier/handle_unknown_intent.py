import logging
import traceback
from typing import Dict, List

from crewai.tools import tool
from django_tenants.utils import schema_context
from openai import OpenAI

import app.chats.chat.models as chat_models
import app.utility.chat_constant as chat_constant
import app.utility.chat_helper as chat_helper
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.redteam.models import RedTeamConversation

logger = logging.getLogger(__name__)


async def generate_response(openai_client, message: str, history: List[Dict]) -> str:
    """
    Generate a response based on the message and conversation history.
    """
    # Construct prompt with context
    prompt = f"""
    Previous conversation:
    {history}
    
    Current message:
    {message}
    
    Please provide a helpful response that addresses the user's needs.
    If the intent of the user's message is unclear, please ask for clarification.
    If the user's intent is clear, provide a response that is appropriate for the intent.
    """
    
    proposal_response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    return proposal_response


def extract_historical_messages(conversation_id: int, schema_name: str, is_redteam: bool) -> list:
    with schema_context(schema_name):
        if is_redteam:
            conversation_instance = RedTeamConversation.objects.get(id=conversation_id)
        else:
            conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
        historical_messages = chat_helper.get_historical_messages(conversation_instance)
        historical_messages_str = "\n".join(
        [f"{msg['role']}: {msg['content']}" for msg in historical_messages]
    )
    return historical_messages_str, conversation_instance


@tool("handle_unknown_intent")
def handle_unknown_intent(
    incoming_message: str,
    conversation_id: str,
    schema_name: str,
    is_redteam: str = "true",
) -> str:
    """
    Handle messages with unknown intent by analyzing the message,
    checking conversation history, and generating appropriate responses.
    
    Args:
        message: The incoming message from the user
        conversation_id: Unique identifier for the conversation
        max_retries: Maximum number of clarification attempts
    
    Returns:
        str: Response to the user
    
    Raises:
        MessageTooVagueError: If message remains unclear after max retries
    """
    try:
        logger.info("Handling unknown intent", extra={"conversation_id": conversation_id,
                                                    "schema_name": schema_name,
                                                    "is_redteam": is_redteam,
                                                    "incoming_message": incoming_message})
        is_redteam = str(is_redteam).lower() == "true"

        client = OpenAI()

        # Get conversation history
        history = extract_historical_messages(conversation_id, schema_name, is_redteam)

        response = generate_response(client, incoming_message, history)

        return f"""Tool execution completed! Your task is to communicate the following response to the customer: 
        {response}"""
        
    except Exception as e:
        logger.error("Error in handling unknown intent", extra={"conversation_id": conversation_id,
                                                              "schema_name": schema_name,
                                                              "is_redteam": is_redteam,
                                                              "incoming_message": incoming_message,
                                                              "error": str(e)})
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
