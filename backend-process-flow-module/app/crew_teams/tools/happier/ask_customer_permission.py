import logging
import traceback

from crewai.tools import tool

from app.crew_teams.tools.happier.formulate_response import formulate_response

logger = logging.getLogger(__name__)


@tool
def ask_customer_permission(conversation_id: int, customer_agreed_to_collect_information: str = "None", schema_name: str = "None", incoming_message: str = None) -> str:
    """
    Check if the customer has agreed to collect their information.
    
    Args:
        conversation_id (int): ID of the conversation
        customer_agreed_to_collect_information (str): String indicating if customer agreed ('True', 'False', or None)
            - True: Customer has agreed to collect information
            - False: Customer does not agreed to collect information
            - None: Customer has not yet been asked
        schema_name (str): Name of the schema
        incoming_message (str): The latest message from the customer
        
    Returns:
        str: A message indicating the permission status or requesting permission
    """
    try:
        logger.info(f"Executing ask_customer_permission with conversation_id: {conversation_id}, customer_agreed_to_collect_information: {customer_agreed_to_collect_information}")

        # Handle None case (not yet asked)
        if str(customer_agreed_to_collect_information).lower() in ("none", "null", ""):
            response_to_customer = """Tool execution completed and you MUST ONLY return the following message at it is. DO NOT add any additional information!
            
            To create a custom event package that fits your needs and budget, we’d love to learn a bit more about your event. Would you be comfortable sharing some details about your event with us? 
            We value your privacy and will only use these details to provide you with the best possible service. Or if you’d prefer, we can also have one of our colleagues reach out to you directly."""

            return response_to_customer
        
        # To prevent circular import
        from app.crew_teams.tools.happier.gather_event_information import (
            gather_event_information,
        )
        
        return gather_event_information.__dict__['func'](conversation_id=conversation_id,
                                                         schema_name=schema_name,
                                                         customer_agreed_to_collect_information=customer_agreed_to_collect_information,
                                                         incoming_message=incoming_message)
          
    except Exception as e:
        logger.error(f"Error executing ask_customer_permission: {e}")
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
