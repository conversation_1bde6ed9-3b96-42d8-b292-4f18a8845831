import logging
import traceback
from typing import Optional

from crewai.tools import tool
from django.utils import timezone
from openai import OpenAI
from django_tenants.utils import schema_context

from app.chats.chat.models import Message, Conversation
from app.chats.chat_action_tracker.models import MessageToSend
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.utility.chat_constant import GPT_4_MINI_MODEL_CHOICES


logger = logging.getLogger(__name__)

@tool
def escalation_to_human(conversation_id: int, schema_name: str, incoming_message: Optional[str] = None, name: str = None, email: str = None) -> str:
    """
    Escalate the conversation to a human agent by:
    1. Retrieving the last 10 messages from the chat
    2. Summarizing the conversation
    3. Creating a message to send to the human agent
    4. Setting the conversation to non-auto-reply
    5. Returning a response to the customer
    
    Args:
        conversation_id (int): ID of the conversation
        schema_name (str): Name of the schema for database operations
        incoming_message (str, optional): The latest message from the customer
        name (str): Optional. The customer's name.
        email (str): Optional. The customer's email.
        
    Returns:
        str: A message indicating that a staff member will reach out
    """
    try:
        with schema_context(schema_name):
            logger.info(f"Executing escalation_to_human with conversation_id: {conversation_id}")

            # Check if name and email are provided
            if not name or not email:
                logger.warning("name and email are not provided. Will need to request from the customer.")
                missing_info = []
                if not name:
                    missing_info.append("name")
                if not email:
                    missing_info.append("email")

                response_to_customer = f"""Before we can escalate this conversation, we need some additional information from you. {', '.join(missing_info)}"""
                return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message, conversation_id=conversation_id)
            
            # Get the conversation and last 10 messages
            conversation = Conversation.objects.get(id=conversation_id)
            messages = Message.objects\
                .filter(conversation=conversation)\
                .order_by('-created_at')[:10]
            
            # Generate conversation summary using GPT
            messages_text = "\n".join([f"{msg.sender}: {msg.message}" for msg in reversed(messages)])
            summary_prompt = f"Please summarize this conversation concisely with the purpose of letting your colleague to be able to follow up with the customer:\n{messages_text}"

            client = OpenAI()
            
            # Use GPT to generate summary
            conversation_summary = get_gpt_response(
                client,
                [{"role": "user", "content": summary_prompt}],
                model=GPT_4_MINI_MODEL_CHOICES[0],
            )
            
            # Create message to send to human agent
            notification_message = f"Customer Escalation Required\n\nChat ID: {conversation_id}\n\nConversation Summary:\n{conversation_summary}"
            
            MessageToSend.objects.create(
                # contact="6597288974",
                contact="6592717106",
                uu_message=messages[0],
                message=notification_message,
                message_type="Page Admin",
                status="Pending",
                to_be_sent_at=timezone.now()
            )
            
            # Set conversation to non-auto-reply
            conversation.is_auto_reply = False
            conversation.save()
            
            # Return response to customer
            response_to_customer = """Tool execution completed and you MUST ONLY return the following message at it is. DO NOT add any additional information!
            
            Thank you for your patience. One of our staff members will reach out to you as soon as possible. We have notified them about your request."""
            
            return response_to_customer
        
    except Exception as e:
        logger.error(f"Error in escalation_to_human: {str(e)}\n{traceback.format_exc()}")
        raise e
