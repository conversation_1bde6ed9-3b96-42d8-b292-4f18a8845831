import json
from typing import Dict, Optional

import pandas as pd
from crewai.tools import tool
from openai import OpenAI

import app.utility.general_helper as utility_general_helper


def validate_inputs(
    number_of_adults: Optional[int],
    number_of_kids_and_age_range: Optional[str],
    occasion_type: Optional[str],
    occasion: Optional[str],
    theme: Optional[str],
) -> Optional[str]:
    """Validate inputs and return missing arguments if any."""
    missing_args = []
    if not number_of_adults:
        missing_args.append("number_of_adults")
    if not number_of_kids_and_age_range:
        missing_args.append("number_of_kids_and_age_range")
    if not occasion_type:
        missing_args.append("occasion_type")
    if not occasion:
        missing_args.append("occasion")
    if not theme:
        missing_args.append("theme")
    
    if missing_args:
        return f"Missing arguments: {', '.join(missing_args)}"
    return None


def read_budget_data(csv_url: str) -> pd.DataFrame:
    """Read budget data from a CSV and return as a DataFrame."""
    return pd.read_csv(csv_url)


def format_budget_data(budget_table: pd.DataFrame) -> str:
    """Format the budget data into a readable string."""
    budget_suggestion = budget_table[
            [
                "Occasion",
                "Typical budget per child for activities (SGD)",
                "Typical budget per adult for activities (SGD)",
                "Typical budget per area for decor (SGD)",
            ]
        ]

    return budget_suggestion.to_dict(orient="records")


def generate_ai_prompt(
    event_details: Dict[str, str], budget_suggestion_str: dict
) -> str:
    """Generate the prompt for the AI model."""
    return f"""
    The customer wants to plan an {event_details['occasion']} event but has not provided a budget.

    Here's the event details:
    ====
    Number of adults attending the event: {event_details['number_of_adults']}
    Number of kids and age range attending the event: {event_details['number_of_kids_and_age_range']}
    Event Occasion Type: {event_details['occasion_type']}
    Event Occasion: {event_details['occasion']}
    Event Theme: {event_details['theme']}
    ====

    Here's the reliable typical budget per child/adult for activities and area for decor: 
    =====
    {budget_suggestion_str}
    =====

    Based on the typical budget and the event details, your task now is to calculate what's the final suitable budget for the customer if having few activities and 1 decoration package.

    For example for a Kids Birthday Party:
    1. Typical budget per child for activities is $SGD30 to $SGD50; There are 20 children hence the budget for activities for kids is $SGD600 to $SGD1000.
    2. Typical budget per adult for activities is N/A to the Kids Birthday Party.
    3. Typical budget per area for decor is $SGD500 to $SGD800; 
    4. Hence the total recommended budget is $SGD800 + $SGD650 = $SGD1450.

    For example for Family Day:
    1. Typical budget per child for activities is $SGD30 to $SGD50; There are 20 children hence the budget for activities for kids is $SGD600 to $SGD1000.
    2. Typical budget per adult for activities is $SGD15 to $SGD30; There are 20 adults hence the budget for activities for adults is $SGD300 to $SGD600.
    3. Typical budget per area for decor is $SGD1500 to $SGD2000;
    4. Hence the total recommended budget is $SGD800 + $SGD450 + $SGD1750 = $SGD3200.

    For example for Team Building:
    1. Typical budget per child for activities is N/A to the Team Building.
    2. Typical budget per adult for activities is $SGD50 to $SGD80; There are 20 adults hence the the budget for activities for adults is $SGD1000 to $SGD1600.
    3. Typical budget per area for decor is $SGD1000 to $SGD1500;
    4. Hence the total recommended budget is $SGD1300 + $SGD1250 = $SGD2550.

    Now, help me calculate the correct budget range and budget amount, and return me a JSON output, following the below JSON format. No small talk, no instructions, no additional information. Just the JSON format. 
    {{
        "budget_range": "<min price> to <max price>",
        "recommended_budget": "<The exact amount, average between the min price and max price>",
        "reason": "<The reason why you think the budget is appropriate>"
    }}
    """


def get_budget_suggestion_from_ai(prompt: str) -> Dict:
    """Send the prompt to the AI model and return the parsed JSON response."""
    client = OpenAI()
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        temperature=0,
    )
    return utility_general_helper.extract_json_from_str(
        response.choices[0].message.content.strip()
    )


def prepare_customer_response(budget_json: Dict, number_of_adults: int, number_of_kids_and_age_range: str, occasion_type: str, occasion: str, theme: str) -> str:
    """Prepare a concise and friendly response for the customer."""
    return f"""The customer wants to plan an {occasion} event but has not provided a budget. Here are the event details:
    Number of adults attending the event: {number_of_adults}, 
    Number of kids and age range attending the event: {number_of_kids_and_age_range}, 
    Event Occasion Type: {occasion_type}, 
    Event Occasion: {occasion}, 
    Event Theme: {theme}.

    Most of our clients with similar events typically spend between {budget_json['budget_range']}, which includes activities and decoration. For the best experience, we recommend a budget of {budget_json['recommended_budget']}. {budget_json['reason']}

    Please ask the customer if this range is comfortable and whether they would like to proceed with generating the proposal. End off your response with an open ended question. Your objective is to entice the customer to provide their budget so that we can generate a customized proposal for their event. Make sure your response is short and to the point as we are replying in a whatsapp social media platform.

    DO NOT START THE RESPONSE WITH "Hi" or "Hello". You are already in a conversation with the customer.
    """


def recommend_event_budget(
    number_of_adults: int,
    number_of_kids_and_age_range: str,
    occasion_type: str,
    occasion: str,
    theme: str,
    budget_csv_url: str = "https://docs.google.com/spreadsheets/u/2/d/1On1F8gTdxIEiT1iUAAF5fcGSipEYdAZEbM4Oxnvot3U/export?format=csv&id=1On1F8gTdxIEiT1iUAAF5fcGSipEYdAZEbM4Oxnvot3U&gid=1921587072",
) -> str:
    """
    Recommends a suitable budget range and amount for an event based on the given details.

    This function orchestrates the budget recommendation process by validating inputs, 
    reading typical budget data from a CSV, generating an AI prompt with event-specific 
    details, retrieving the budget suggestion from an AI model, and formatting the response 
    for the customer. It ensures a streamlined and automated approach to providing accurate 
    budget recommendations tailored to customer needs.

    Args:
        number_of_adults (int): 
            The number of adults attending the event. Must be an integer greater than or equal to 0.
        
        number_of_kids_and_age_range (str): 
            A string representing the number of kids attending and their age range 
            (e.g., "10 kids, ages 5-10"). Use "0" or an equivalent to indicate no kids.

        occasion_type (str): 
            Specifies whether the event is private or corporate (e.g., "Private", "Corporate").

        occasion (str): 
            The type of event or occasion (e.g., "Birthday", "Wedding", "Team Building").

        theme (str): 
            The theme of the event (e.g., "Superheroes", "Classic White"). Use "no preference" 
            if the customer has no specific theme.

        budget_csv_url (str): 
            URL or file path to the CSV file containing typical budget data. The file must include 
            columns for:
            - Occasion
            - Typical budget per child for activities (SGD)
            - Typical budget per adult for activities (SGD)
            - Typical budget per area for decor (SGD)

    Returns:
        str: 
            A formatted response for the customer, including:
            - Budget range (minimum to maximum).
            - Recommended budget amount (average of the range).
            - A brief explanation of why the budget is appropriate.
            If inputs are missing, it returns an error message specifying which arguments are required.

    Workflow:
        1. **Input Validation**:
            Ensures all required details are provided. If any input is missing, returns an error message.

        2. **Read and Format Budget Data**:
            Reads the budget data from the provided CSV and formats it into a readable string.

        3. **Generate AI Prompt**:
            Creates a detailed prompt incorporating event details and budget data for the AI model.

        4. **Retrieve Budget Suggestion from AI**:
            Sends the generated prompt to the AI model, which calculates:
            - Budget range.
            - Recommended budget (average).
            - Reason for the recommendation.

        5. **Prepare Customer Response**:
            Formats the AI response into a user-friendly output suitable for customer communication.

    Example:
        >>> recommend_event_budget(
                number_of_adults=20,
                number_of_kids_and_age_range="10 kids, ages 5-10",
                occasion_type="Private",
                occasion="Birthday",
                theme="Superheroes",
                budget_csv_url="https://example.com/budget_data.csv"
            )
        "Sounds great! Most of our clients with similar events typically spend between SGD 1500 to SGD 2000. 
         This range includes activities and decoration. For the best experience, I suggest a budget of SGD 1750. 
         This budget ensures a memorable experience for all attendees."

    Raises:
        - FileNotFoundError: If the CSV file cannot be found.
        - ValueError: If the CSV data is improperly formatted or missing required columns.

    Notes:
        - This function assumes that the AI model is accessible and capable of processing the generated prompt.
        - The CSV file must include standardized budget data for various event types.
    """
    print("Recommend Event Budget...")
    # Step 1: Validate Inputs
    validation_error = validate_inputs(
        number_of_adults, number_of_kids_and_age_range, occasion_type, occasion, theme
    )
    if validation_error:
        return validation_error

    # Step 2: Read and Format Budget Data
    budget_data = read_budget_data(budget_csv_url)
    budget_suggestion_dict = format_budget_data(budget_data)

    # Step 3: Generate AI Prompt
    event_details = {
        "number_of_adults": number_of_adults,
        "number_of_kids_and_age_range": number_of_kids_and_age_range,
        "occasion_type": occasion_type,
        "occasion": occasion,
        "theme": theme,
    }
    prompt = generate_ai_prompt(event_details, budget_suggestion_dict)

    # Step 4: Get Budget Suggestion from AI
    budget_json = get_budget_suggestion_from_ai(prompt)
    budget_suggestion_json = utility_general_helper.extract_json_from_str(
        budget_json
    )
    if isinstance(budget_suggestion_json, str):
        budget_suggestion_json = json.loads(budget_suggestion_json)

    # Step 5: Prepare and Return Customer Response
    response_prompt = prepare_customer_response(budget_suggestion_json, number_of_adults, number_of_kids_and_age_range, occasion_type, occasion, theme)
    response = get_budget_suggestion_from_ai(response_prompt)

    # Step 6: Prepare and Return Customer Response
    return f"""Tool execution completed! This is the message from the tool back to the customer:
    {response}
    """
