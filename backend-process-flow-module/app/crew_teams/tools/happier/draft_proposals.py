import ast
import time
import json
import logging
import math
import re
import traceback
from datetime import datetime
from pprint import pprint
from typing import Any, Callable, Dict, List, Literal

import pandas as pd
from crewai.tools import tool
from django.utils import timezone
from django_tenants.utils import schema_context
from openai import OpenAI
from pydantic import BaseModel, Field

import app.chats.chat.models as chat_models
import app.documents.document.models as document_models
import app.utility.chat_constant as chat_constant
import app.utility.chat_helper as chat_helper
import app.utility.date_helper as utility_date_helper
import app.utility.general_helper as utility_general_helper
import app.utility.location_helper as utility_location_helper
from app.crew_teams.tools.happier.fetch_master_data import (
    check_for_discounts,
    enrich_by_surcharge_fee,
    fetch_master_services_pricing,
    filter_master_services_by_budget,
)
from app.crew_teams.tools.happier.gather_event_information import (
    gather_event_information,
)
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.redteam.models import RedTeamConversation, RedTeamMessage
from backend.settings import WEBSOCKET_URL, BOT_API_KEY
import app.core.authapi.helper as authapi_helper
from app.utility.websocket_client import send_message

logger = logging.getLogger(__name__)


def validate_time_and_hours(start_time: str, end_time: str, hours_count: float) -> bool:
    """
    Validates if the start_time and end_time tally with the hours_count.

    :param start_time: The start time in "HHMM" format as a string.
    :param end_time: The end time in "HHMM" format as a string.
    :param hours_count: The total hours as a float.
    :return: True if the times tally with the hours_count, False otherwise.
    """
    try:
        # Convert start_time and end_time to datetime objects
        start_dt = datetime.strptime(start_time, "%H%M")
        end_dt = datetime.strptime(end_time, "%H%M")
        
        # Calculate the difference in hours
        time_difference = (end_dt - start_dt).total_seconds() / 3600

        print("Time difference: ", time_difference)
        print("Hours count: ", hours_count)
        
        # Compare with hours_count
        return abs(time_difference - hours_count) < 1e-6  # Allowing small precision error
    except ValueError as e:
        print(f"Error parsing times: {e}")
        return False


def validate_inputs(conversation_id: int, required_fields: dict) -> List[str]:
    """
    Validate required fields for proposal generation.

    Args:
        conversation_id (int): ID of the conversation.
        required_fields (dict): Dictionary of fields and their values.

    Returns:
        List[str]: List of missing fields.
    """
    missing_fields = [key for key, value in required_fields.items() if not value]
    if not conversation_id:
        missing_fields.append("conversation_id")
    return missing_fields


def _parse_budget(budget_str: str) -> int | None:
    """
    Parse budget string into integer value.

    Args:
        budget_str (str): Budget string in format 'SGD1,000' or similar.

    Returns:
        int: Parsed budget value, or None if parsing fails.
    """
    match = re.search(r"([\d,]+)", str(budget_str))
    if match:
        return int(match.group(1).replace(",", ""))
        
    return None


def determine_budget_range(openai_client: OpenAI, budget: int) -> List[int]:
    """
    Determine the range of budgets based on the input budget.

    Args:
        openai_client (OpenAI): The OpenAI client.
        budget (int): The input budget.

    Returns:
        List[int]: List of budgets in the range.
    """

    def _get_budget_amount(openai_client: OpenAI, budget_str: str, maximum_or_minimum: str, phrase: str) -> int:
        prompt = f"""This is the budget provided by our customer: {budget_str}.

        Please calculate the {maximum_or_minimum} {phrase} provided by our customer.

        Only return the {maximum_or_minimum} budget as a number. There should not be any other text in the response or currency.
        """

        response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
        response = float(response.strip())

        # Round up to the nearest hundreds
        response = math.ceil(response / 100) * 100

        return float(response)

    try:
        lower_budget = float(math.ceil(budget / 100) * 100)

    except Exception as e:
        print(f"Unable to convert budget to integer: {e}. Proceed to use GPT to calculate budget.")
        lower_budget = _get_budget_amount(openai_client, budget, maximum_or_minimum="minimum", phrase="budget which should be the lowest of the budget")

    try:
        higher_budget = float(math.ceil(budget * 1.2 / 100) * 100)

    except Exception as e:
        print(f"Unable to convert budget to integer: {e}. Proceed to use GPT to calculate budget.")
        higher_budget = _get_budget_amount(openai_client, budget, maximum_or_minimum="maximum", phrase=r"budget which should be +20% of the budget")

    print(f"{lower_budget=}, {higher_budget=}")

    return lower_budget, higher_budget


def _format_proposal_response(proposals: List[Dict], event_details: Dict) -> Dict[str, Any]:
    """
    Format the proposal response into a customer-friendly format.

    Args:
        proposals (List[Dict]): List of generated proposals.
        event_details (Dict): Dictionary containing event details.

    Returns:
        Dict[str, Any]: Formatted proposal response.
    """
    return {
        "proposals": proposals,
        "event_details": event_details,
        "message": "Here are your customized event proposals!"
    }


def extract_historical_messages(conversation_id: int, schema_name: str, is_redteam: bool) -> list:
    with schema_context(schema_name):
        if is_redteam:
            conversation_instance = RedTeamConversation.objects.get(id=conversation_id)
        else:
            conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
        historical_messages = chat_helper.get_historical_messages(conversation_instance)
        historical_messages_str = "\n".join(
        [f"{msg['role']}: {msg['content']}" for msg in historical_messages]
    )
    return historical_messages_str, conversation_instance


def extract_rubric(occasion):
    if str(occasion).lower() == "corporate":
        scoring_rubrics = "https://docs.google.com/spreadsheets/u/2/d/1D1sNsolbaRi4f8ylYVnwD9DShq9hUwaYxqyLt2WW-qc/export?format=csv&id=1D1sNsolbaRi4f8ylYVnwD9DShq9hUwaYxqyLt2WW-qc&gid=1332723587"

    # Use private scoring rubrics if not specified/ private
    else:
        scoring_rubrics = "https://docs.google.com/spreadsheets/u/2/d/1D1sNsolbaRi4f8ylYVnwD9DShq9hUwaYxqyLt2WW-qc/export?format=csv&id=1D1sNsolbaRi4f8ylYVnwD9DShq9hUwaYxqyLt2WW-qc&gid=1715088844"

    scoring_rubrics_table = pd.read_csv(scoring_rubrics)

    return scoring_rubrics_table


def define_user_requirements(openai_client, historical_messages_str: str, incoming_message: str, event_kwargs) -> List[Dict]:
    """
    Define the event requirements based on conversation history and the latest customer message.

    Args:
        openai_client: An instance of the OpenAI client used for generating AI responses.
        historical_messages_str (str): A string containing the historical conversation messages.
        incoming_message (str): The latest message received from the customer.
        event_kwargs (dict): A dictionary containing event details such as name, date, start time, end time, 
                             event location, number of adults, number of kids and age range, occasion type, 
                             occasion, theme, budget, and additional details.

    Returns:
        List[Dict]: A list of dictionaries representing the user requirements as determined by the AI response.
    """
    # Get the user requirements summary
    user_requirements_prompt = utility_general_helper.clean_up_prompt(f"""
    Based on the conversation history and the latest message from the customer, here's the information we have gathered so far.

    Conversation History (Note: May include past proposals which should be used as reference only and not override current planning):
    {historical_messages_str}

    Latest Customer Message:
    {incoming_message}

    Event Information:
    Name: {event_kwargs.get('name', 'not specified')}
    Date: {event_kwargs.get('date', 'not specified')}
    Start Time: {event_kwargs.get('start_time', 'not specified')}
    End Time: {event_kwargs.get('end_time', 'not specified')}
    Location: {event_kwargs.get('event_location', 'not specified')}
    Number of Adults: {event_kwargs.get('number_of_adults', 'not specified')}
    Number of Kids and Age Range: {event_kwargs.get('number_of_kids_and_age_range', 'not specified')}
    Occasion Type: {event_kwargs.get('occasion_type', 'not specified')}
    Occasion: {event_kwargs.get('occasion', 'not specified')}
    Theme: {event_kwargs.get('theme', 'not specified')}
    Additional Details: {event_kwargs.get('event_additional_details', 'none')}

    Summarize our understanding of what the customer is looking for as well as any service/decorations that the customer expressed interest in. We will need to infer from our conversation history and the latest message from the customer.

    Additionally, derive clarifying questions to better understand the audience and event context:

    1. Who are the audience members (age range, demographics)?
    2. What is the occasion (e.g., birthday, corporate, family celebration)?
    3. What are the interests or goals of the event? (e.g., fun, bonding, elegance, relaxation)
    4. Are there any specific activities or decorations the customer mentioned wanting to include?
    5. Is there a specific theme or tone the customer wants to set (e.g., formal, playful, casual)?
    6. Are there logistical details we need to confirm, such as setup space, indoor/outdoor venue, or timeline constraints?
    7. IMPORTANT!: Exclude all budget requirements as it has been set for the proposal.
    """)

    response = get_gpt_response(openai_client, [{"role": "user", "content": user_requirements_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    return response


def gather_suggestions_for_services(openai_client: OpenAI, user_requirements: str, master_services: pd.DataFrame) -> List[Dict]:
    """
    Gather suggestions for services based on user requirements and master services.

    Args:
        openai_client: An instance of the OpenAI client used for generating AI responses.
        user_requirements (str): GPT-generated summary of user requirements and preferences
        master_services (pd.DataFrame): DataFrame containing all available services and their details

    Returns:
        List[Dict]: List of suggestions for services based on user requirements and master services
    """
    full_services = (
        master_services[
            [
                "Name", 
                "Description",
                "Category",
                "SubCategory"
            ]
        ]
    )
    full_services.columns = ["Name", "Description", "Category", "Sub Category"]

    # Analyze user requirements and propose high-level activity types
    services_recommendation_prompt = utility_general_helper.clean_up_prompt(f"""
    Based on the offerings we have (master services) and the user requirements, we aim to determine a high-level idea of the types of activities to propose to the customer.

    Conversation History (Note: May include past proposals which should be used as reference only and not override current planning):
    {full_services.to_dict(orient='records')}

    User Requirements Summary:
    {user_requirements}

    Analyze our understanding of what the customer is looking for and any specific activities or decorations they expressed interest in. We need to infer this from the conversation history and the latest message from the customer.

    Based on this analysis, proceed to the next step:

    **Determine Activity Types**:

    1. **Identify Potential Activities**:
        - Generate a list of potential activities based on the audience, occasion, theme, and goals.
        - Suggest activities that align with typical preferences for the occasion type (e.g., birthday, corporate) if any information is missing.

    2. **Filter Activities**:
        - Ensure the proposed activities align with the provided details, such as age range, theme, and preferences.

    3. **Suggest Comprehensive Activity Packages**:
        - Group the recommended activities into balanced packages, covering:
          - **Entertainment:** (e.g., performances, interactive shows).
          - **Decor:** (e.g., themed setups, visual enhancements).
          - **Food & Snacks:** (e.g., unique culinary experiences).
          - **Interactive Activities:** (e.g., workshops, participative events).
        - If information is incomplete, recommend packages based on successful past experiences for similar occasions.

    4. **Address Missing Information**:
        - Make informed assumptions for any missing critical details (like theme, age range, or number of attendees) and explain the reasoning behind your suggestions.
        - Propose follow-up questions to refine the activity packages.

    Use this structured approach to propose activity types that not only meet customer requirements but also ensure creativity and practicality. Aim to offer combinations that promise a memorable and engaging experience.
    """)

    response = get_gpt_response(openai_client, [{"role": "user", "content": services_recommendation_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    return response


def get_services_choices(openai_client: OpenAI, user_requirements: str, services_recommendation: str, master_services: pd.DataFrame) -> List[Dict]:
    """
    Filter and select appropriate services based on event requirements and constraints.

    Args:
        openai_client: An instance of the OpenAI client used for generating AI responses.
        user_requirements (str): GPT-generated summary of user requirements and preferences
        services_recommendation (str): GPT-generated summary of services recommendation
        master_services (pd.DataFrame): DataFrame containing all available services and their details

    Returns:
        List[Dict]: List of filtered and scored service options with their details
    """
    class RecommendedService(BaseModel):
        product_service_id: str = None
        service_name: str = None
        relevance_score: float = None
        reason: str = None
        theme_compatibility: float = None
        financial_impact_score: float = None
        priority: Literal["high", "medium", "low"] = None
        rank: int = None

    class RecommendedServices(BaseModel):
        recommended_services: List[RecommendedService] = None

    full_services = (
        master_services[
            [
                "ProductServiceID",
                "Name", 
                "Description",
                "PricingModel",
                "Category",
                "SubCategory",
                "MOQ",
                "UOM",
            ]]
    )
    full_services.columns = ["Product Service ID", "Name", "Description", "Pricing Model",
                             "Category", "Sub Category", "Minimum Order Quantity", "Unit of Measurement"]

    # Create prompt for service selection
    service_selection_prompt = utility_general_helper.clean_up_prompt(f"""
    Based on the customer requirements and preferences:
    {user_requirements}

    And our recommendations:
    {services_recommendation}

    You need to analyze our available services and select the most appropriate ones.
    Here are all our available services:
    {full_services.to_dict(orient='records')}

    Please return a JSON array of the top 6 most suitable services, where each service is an object with the following structure:
    [
        {{
            "product_service_id": "ID of the service",
            "service_name": "Name of the service",
            "relevance_score": <float between 0-1>,
            "reason": "Brief explanation of why this service is relevant",
            "theme_compatibility": <float between 0-1>,
            "financial_impact_score": <float between 0-1>,
            "priority": "high/medium/low",
            "rank": <integer rank based on suitability>
        }},
    ]

    Consider these factors in your selection:
    1. Event type and occasion appropriateness
    2. Theme compatibility
    3. Service category relevance
    4. Pricing model suitability
    5. Financial feasibility: prioritize services that offer the best value while meeting the requirements.
    6. Customer benefit: select services that align with the customer's stated goals, preferences, and budget.
    7. Attempt to mix and match services or decorations from different Sub Categories so as to allow for a more diverse experience.

    Calculate the "financial_impact_score" based on:
    - Cost-effectiveness of the service relative to the alternatives.
    - Alignment with the customer's stated budget constraints.

    Focus on services that provide the best overall value and create the most impact for the event while being budget-friendly.

    RETURN THE RESPONSE AS A JSON ARRAY.
    """)

    attempt = 5
    counter = 0
    while counter < attempt:
        try:
            # Get GPT response for service selection
            response = get_gpt_response(openai_client, [{"role": "user", "content": service_selection_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0], response_format=RecommendedServices)

            response = utility_general_helper.extract_list_from_str(response)
            response = json.loads(response)
            return response

        except Exception as e:
            print(f"Error processing service choices: {str(e)}")
            counter += 1

    if counter == attempt:
        print("Unable to get service choices after multiple attempts. Returning empty list.")
        return []


def enrich_services_with_details(services_choices: List[Dict], master_services: pd.DataFrame) -> List[Dict]:
    enriched_services = []
    
    for service in services_choices:
        # Get the product service ID
        product_service_id = service['product_service_id']
        
        # Find the matching row in master_services
        service_details = master_services[master_services['ProductServiceID'] == product_service_id]
        
        if not service_details.empty:
            # Create a copy of the original service dict
            enriched_service = service.copy()
            
            # Add the additional details from master_services
            enriched_service.update({
                'name': service_details['Name'].iloc[0],
                'description': service_details['Description'].iloc[0],
                'working_pace': service_details['WorkingPace'].iloc[0],
                'base_price': service_details['BasePrice'].iloc[0],
                'pricing_model': service_details['PricingModel'].iloc[0],
                'category': service_details['Category'].iloc[0],
                'sub_category': service_details['SubCategory'].iloc[0],
                'unit_of_measurement': service_details['UOM'].iloc[0],
                'minimum_order_quantity': service_details['MOQ'].iloc[0]
            })
            
            enriched_services.append(enriched_service)
        else:
            # If no matching service found, include the original service without enrichment
            enriched_services.append(service)
    
    return enriched_services


def generate_proposal_plan(openai_client, _proposals_json: list, event_metadata: dict, user_event_requirements: str, budget_limit: int, inital_recommendation_response: dict) -> list:
    """
    Generates a proposal plan by determining service duplication, event duration, and pricing metrics.

    Args:
        openai_client: The OpenAI client instance.
        _proposals_json (list): List of shortlisted services.
        event_metadata (dict): Metadata containing event details.
        user_event_requirements (str): Specific user requirements for the event.
        budget_limit (int): The budget limit for the event.
        inital_recommendation_response (dict): Recommendations for services in the previous proposal.

    Returns:generate_proposal_plan
        list: A list of planned proposals with updated service details.
    """
    # Determine the event duration
    duration_prompt = f"""
    Based on the Event Metadata, determine the total duration of the event in hours.

    Event Metadata:
    {json.dumps(event_metadata, indent=4)}

    RETURN THE RESPONSE AS A JSON OBJECT.
    {{"max_event_duration_hours": <int>}}
    """
    event_duration_response = get_gpt_response(openai_client, [{"role": "user", "content": duration_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
    print(event_duration_response)

    for service in _proposals_json:
        # For decor, there is no hour to it. It should be a flat rate
        if "decor" in service['category'].lower():

            # Decor is a package with a fixed price for the full duration
            # Calculate pricing metrics for services with fixed package price
            pricing_prompt = f"""For the current service, construct a pricing table reflecting the maximum event duration with a fixed package price. Note that this service does not support sub-hour pricing; the package is ONLY applicable for the entire duration as it is a decoration service.

            Shortlisted Service:
            {service}

            Maximum Event Duration:
            {event_duration_response}

            Pricing Table:
            | Duration (hours) | Package Price |
            |------------------|---------------|
            | {event_duration_response} | {service['base_price']} |
            
            Note: This service is charged as a flat rate for the full duration without sub-hour adjustments.
            """
            price_metrics_response = get_gpt_response(openai_client, [{"role": "user", "content": pricing_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
            print(price_metrics_response)
            service['pricing_model'] = price_metrics_response

        else:
            # Determine maximum number of duplicated services
            duplication_prompt = f"""
            Based on the Event Metadata, determine the maximum number of duplicated services that can be engaged. The minimum number is 1.

            - **Customer Preferences**:
            1. Consider extending service hours wherever possible to enhance the experience.
            2. For events with **less than 30 kids**, you do not need to have duplicated services.
            3. For events with **more than 30 kids**, plan for additional talent to ensure activities run smoothly.

            Event Metadata:
            {json.dumps(event_metadata, indent=4)}

            Shortlisted Service:
            {service}

            RETURN THE RESPONSE AS A JSON ARRAY.
            {{"max_duplicated_services": <int>}}
            """
            response = get_gpt_response(openai_client, [{"role": "user", "content": duplication_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
            print(response)
            
            # Calculate pricing metrics
            pricing_prompt = f"""
            Based on the pricing model information from the service, calculate a pricing table that varies with two dimensions: event duration and number of duplicated services. The table should display combinations of all possible durations and duplicated services, starting from the smallest possible pricing to the maximum amount of pricing, considering the maximum duration and maximum duplicated service.

            IMPORTANT:
            - For the minimum duration and number of services, you MUST refer to the provided "minimum_order_quantity" and "unit_of_measurement" in the service information.
            - For the maximum duration and number of services, you MUST refer to the "max_event_duration_hours" and "max_duplicated_services" from the previous response.

            Shortlisted Service:
            {service}

            Maximum Event Duration:
            {event_duration_response}

            Maximum Duplicated Service:
            {response}

            Pricing Table:
            | Duration (hours) | Number of Services | Total Price |
            |------------------|--------------------|-------------|
            """
            price_metrics_response = get_gpt_response(openai_client, [{"role": "user", "content": pricing_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
            print(price_metrics_response)
            service['pricing_model'] = price_metrics_response

    attempts = 0
    error_feedback = ""
    while attempts < 5:
        try:
            # Create the prompt to determine which services to choose and the duration and number of duplicated services
            event_metadata_without_budget = {key: value for key, value in event_metadata.items() if key != "budget"}
            print("?"*50)
            print(f"{budget_limit=}")
            prompt = f"""
            Based on the pricing metrics, determine the best possible combination of services that can be offered to the customer, considering the event information and user requirements.

            Event Metadata:
            {json.dumps(event_metadata_without_budget, indent=4)}

            Shortlisted Services:
            {_proposals_json}

            Previous Proposal Recommendations (If any):
            {inital_recommendation_response if isinstance(inital_recommendation_response, str) else json.dumps(inital_recommendation_response, indent=4)}

            User Requirements:
            {user_event_requirements}

            Budget Limit:
            {budget_limit}

            Error Feedback (if any):
            {error_feedback}

            Instructions:
            - **Proposal Design**:
            1. Each proposal must strategically combine services to cover the entire event duration, ensuring seamless transitions and maximum enjoyment for attendees.
            2. You MUST COVER THE FULL DURATION of the event. For the services, you are able to mix and match services from the Shortlisted Services list.
            3. You MUST find a balance between one service for the whole event or mulitple services that cover the whole event.
            4. YOUR TOTAL PROPOSAL COST MUST NOT EXCEED THE `BUDGET LIMIT`. (IMPORTANT!) Reduce the number of services or reduce the duration if the total cost exceeds the budget. This is determined by adding all the subprices together.
            5. We should encourage to have a healthy mix of services/ decorations so that the experience is more diversified and engaging.
            6. STRICTLY FOLLOW THE BUDGET SPECIFIED IN `Budget Limit` but do not exceed it. You are allowed to fully maximize the budget by adding in more services and/or extending the duration.
            7. As some services have a minimum quantity of hours/ talent, you MUST ensure that the total number of hours/ talent is greater than or equal to the minimum quantity. Adjust the start and end time accordingly.

            - **Customer Preferences**:
            1. Consider extending service hours wherever possible to enhance the experience.
            2. Prioritize extending hours for services with a `work_pace` requirement, especially when dealing with **less than 30 kids**.
            3. For events with **more than 30 kids**, plan for additional talent to ensure activities run smoothly.

            - **Pricing and Configuration**:
            1. Adhere to the price metrics for each service.
            2. The sum of subprices for all services should not exceed the budget limit.

            - **Budget Management**:
            1. You MUST keep the total proposal cost within the `Budget Limit` as specified`.
            2. Recommend the most effective service configurations that balance cost and customer satisfaction.

            OUTPUT FORMAT:
            {{
                "<product_service_id (example: PCCFP1H00030)>": {{
                    "start_time": <Service start time in 24-hour format, e.g., "1400">,
                    "end_time": <Service end time in 24-hour format, e.g., "1600">,
                    "hours_count": <Number of hours between start_time and end_time as a float value>,
                    "number_of_talent": <Number of human staff involved in delivering this service. Set to 0 if no human staff involved>,
                    "subprice": <Subprice for this service as a float value based on the pricing model of the service.>,
                }},
                ...   
            }}
            RETURN THE RESPONSE AS A JSON FORMAT.
            """

            response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MODEL_CHOICES[0])
            response = response.replace("```json", "").replace("```", "")
            response = utility_general_helper.extract_json_from_str(response)
            recommendation_response = json.loads(response)

            # Ensure that the start time, end time and hours count are valid
            for service_id, service_details in recommendation_response.items():
                if not validate_time_and_hours(service_details['start_time'], service_details['end_time'], service_details['hours_count']):
                    error_feedback = f"Start time, end time, and hours count does not tally for {service_id}. The Start Time: {service_details['start_time']}, End Time: {service_details['end_time']} is not equal to Hours Count: {service_details['hours_count']}."
                    raise ValueError(error_feedback)

            # Write another prompt to check if the recommendation is the same
            if inital_recommendation_response:
                prompt = [
                    {"role": "system", 
                    "content": "You are an expert in identifying two proposals if it is the same in terms of the services, duration of services and number of talents per service (If applicable). Your task is to response if the current proposal is exactly the same as your inital proposal."},
                    {"role": "user", "content": f"""Here is your initial proposal: {json.dumps(inital_recommendation_response)}
                    
                    Determine if the intial proposal's service is exactly the same as the current proposal: {json.dumps(recommendation_response)}

                    *Do note that it is possible to have the same set of services but different start and end time, or different number of talents. 
                    
                    Response "Yes" if it is the same or "No" if it is not"""},
                ]

                response = get_gpt_response(openai_client, prompt, model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

                if response.lower() == "yes":
                    print("The recommendation is the same as the initial recommendation")
                    error_feedback = "[!IMPORTANT] The service selected, duration of the service and number of talents is the same as the previous proposal. Change the combination of services, duration and number of talents. You MUST NOT repeat the same service combination."
                    raise ValueError(error_feedback)

            break
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Error occurred: {e}. Retrying {attempts + 1}/5...")
            attempts += 1

            if attempts == 5:
                raise e

    print("="*100)
    print(_proposals_json)
    print("="*100)
    print(response)
    print("="*100)

    finalized_proposal = []

    class SubpriceResponse(BaseModel):
        subprice: float = Field(..., description="Subprice for this service as a float value")

    # Determine the final cost of the proposal
    for service in _proposals_json:
        if service['product_service_id'] in recommendation_response:
            service['hours_count'] = recommendation_response[service['product_service_id']]['hours_count']
            service['number_of_talent'] = recommendation_response[service['product_service_id']]['number_of_talent']
            service['start_time'] = recommendation_response[service['product_service_id']]['start_time']
            service['end_time'] = recommendation_response[service['product_service_id']]['end_time']

            # Calculate the total cost of the proposal
            proposal_prompt = f"""You are an experienced event planner specialized in calculating the total cost of a service/ decoration required.
            You will be provided with the pricing model, the hours count, and the number of talent involved in the service/ decoration.
            Your task is to calculate the total cost of the service/ decoration based on the pricing model, hours count, and number of talent involved.

            Pricing Model:
            {service['pricing_model']}

            Hours Count:
            {service['hours_count']}

            Number of Talent:
            {service['number_of_talent']}

            OUTPUT FORMAT:
            {{
                "subprice": <Subprice for this service as a float value>,
            }}

            RETURN THE RESPONSE AS A JSON FORMAT.
            """
            attempts = 0
            while attempts < 5:
                try:
                    subprice_response = get_gpt_response(openai_client, [{"role": "user", "content": proposal_prompt}], model=chat_constant.GPT_4_MODEL_CHOICES[0], response_format=SubpriceResponse)
                    subprice_response = subprice_response.replace("```json", "").replace("```", "")
                    subprice_response = utility_general_helper.extract_json_from_str(subprice_response)
                    subprice_response = json.loads(subprice_response)
                    break
                except Exception as e:
                    print(f"Error occurred: {e}. Retrying {attempts + 1}/5...")
                    attempts += 1
            
            service['subprice'] = subprice_response['subprice']

            finalized_proposal.append(service)

    return finalized_proposal, recommendation_response


def draft_final_proposal(openai_client, _proposals_json: list, event_metadata: dict, user_event_requirements: str, total_cost: float, total_discount: float, discount_conditions: str, proposal_id: str, is_first_proposal: bool):

    cleaned__proposals_json = []
    for proposal in _proposals_json:
        cleaned_service = {
            "product_service_id": proposal["product_service_id"],
            "name": proposal["name"],
            "reason": proposal["reason"],
            "description": proposal["description"],
            "start_time": proposal["start_time"],
            "end_time": proposal["end_time"],
            "number_of_talent": proposal["number_of_talent"],
            "price": proposal["subprice"]
        }
        cleaned__proposals_json.append(cleaned_service)

    proposal_name = "Recommended Proposal" if is_first_proposal else "Alternative Proposal"

    has_discount = total_cost - total_discount > 0.0

    if has_discount:
        proposal_prompt = f"""
        We have finalized the services we want to provide, including both the total and discounted costs, along with the conditions for the discounts.

        Now, draft a proposal template in markdown format. This template is for my customer, so we do not need to show all the information.

        The markdown format must be concise, intuitive for the customer to easily digest the information. 

        The proposal should include the following sections:
        - **Event Details**: Include the event location, date, and duration.
        - **Services**: List the services.
        - **Pricing**: Display the total cost and discount information. (!IMPORTANT: If there is no discount, do not display the discount information.)

        Services will include the following details:
        - **Service/Decoration Name**: Provide the name.
        - **Time**: Include start and end times.
        - **Talent**: Display only if it is at least 1. (!IMPORTANT)
        - **Description**: Provide a brief description of the service/decoration.

        Output format:
        **<Name of Proposal (Recommended Proposal/ Alternative Proposal)>**  
        **Proposal ID:** <ID>

        **Event Details:**  
        - **Location:** <Location>  
        - **Date:** <Date in DD-MMM-YYYY format>  
        - **Time:** <Start Time> - <End Time>  

        **Pricing:**  
        - **Total Proposal Price:** <Total Cost>  
        - **Discounted Price (Valid till {discount_conditions} in DD-MMM-YYYY format):** <Total Discount> [ONLY IF there is a discount] 

        **Services:**  
        1. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        2. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        [Subsequent services in the same format as above]

        -----------------------------------

        Proposal Name:
        {proposal_name}

        Proposal ID:
        {proposal_id}

        Total Proposal Cost: 
        {total_cost}

        Total Cost after discount if the following condition is met: 
        {total_discount}

        User Requirements:
        {user_event_requirements}

        Services/ Decorations to include:
        {cleaned__proposals_json}

        Event Metadata:
        {json.dumps(event_metadata, indent=4)}

        Return the response in markdown format where it is in a concise and intuitive way for the customer to digest the information.

        No other information, no small-talk, no filler words.

        RETURN IN A MARKDOWN FORMAT with NO blank lines.
        """
    
    else:
        proposal_prompt = f"""
        We have finalized the services we want to provide, including the total costs.

        Now, draft a proposal template in markdown format. This template is for my customer, so we do not need to show all the information.

        The markdown format must be concise, intuitive for the customer to easily digest the information. 

        The proposal should include the following sections:
        - **Event Details**: Include the event location, date, and duration.
        - **Services**: List the services.
        - **Pricing**: Display the total cost

        Services will include the following details:
        - **Service/Decoration Name**: Provide the name.
        - **Time**: Include start and end times.
        - **Talent**: Display only if it is at least 1. (!IMPORTANT)
        - **Description**: Provide a brief description of the service/decoration.

        Output format:
        **<Name of Proposal (Recommended Proposal/ Alternative Proposal)>**  
        **Proposal ID:** <ID>

        **Event Details:**  
        - **Location:** <Location>  
        - **Date:** <Date in DD-MMM-YYYY format>  
        - **Time:** <Start time> - <End time>  

        **Pricing:**  
        - **Total Proposal Price:** <Total Cost>

        **Services:**  
        1. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        2. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        [Subsequent services in the same format as above]

        -----------------------------------

        Proposal Name:
        {proposal_name}

        Proposal ID:
        {proposal_id}

        Total Proposal Cost: 
        {total_cost}

        User Requirements:
        {user_event_requirements}

        Services/ Decorations to include:
        {cleaned__proposals_json}

        Event Metadata:
        {json.dumps(event_metadata, indent=4)}

        Return the response in markdown format where it is in a concise and intuitive way for the customer to digest the information.

        No other information, no small-talk, no filler words.

        RETURN IN A MARKDOWN FORMAT with NO blank lines.
        """

    proposal_response = get_gpt_response(openai_client, [{"role": "user", "content": proposal_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    if has_discount:
        proposal_format_prompt = f"""Your task is to convert the following information into a markdown format.
        
        # Proposal:
        {proposal_response}

        # Conditions:
        1. The markdown MUST be micely formatted with new lines in between sections and each services.
        2. Each service should be labelled with a number.
        3. For pricing, it should be user friendly format. (Eg, $1000 should be written in $1,000.)
        4. For all dates, use DD-MMM-YYYY format.
        5. For time, use HH:MM AM/PM format.
        6. ONLY show the talent if there is at least 1 talent.

        # Output format:
        **<Name of Proposal (Recommended Proposal/ Alternative Proposal)>**  
        **Proposal ID:** <ID>

        **Event Details:**  
        - **Location:** <Location>  
        - **Date:** <Date in DD-MMM-YYYY format>  
        - **Time:** <Start time> - <End time>  

        **Pricing:**  
        - **Total Proposal Price:** <Total Cost>

        **Services:**  
        1. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        2. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        [Subsequent services in the same format as above]
        """
    
    else:
        proposal_format_prompt = f"""Your task is to convert the following information into a markdown format.
        
        # Proposal:
        {proposal_response}

        # Conditions:
        1. The markdown MUST be micely formatted with new lines in between sections and each services.
        2. Each service should be labelled with a number.
        3. For pricing, it should be user friendly format. (Eg, $1000 should be written in $1,000.)
        4. For all dates, use DD-MMM-YYYY format.
        5. For time, use HH:MM AM/PM format.
        6. ONLY show the talent if there is at least 1 talent.

        # Output format:
        **<Name of Proposal (Recommended Proposal/ Alternative Proposal)>**  
        **Proposal ID:** <ID>

        **Event Details:**  
        - **Location:** <Location>  
        - **Date:** <Date in DD-MMM-YYYY format>  
        - **Time:** <Start time> - <End time>  

        **Pricing:**  
        - **Total Proposal Price:** <Total Cost>

        **Services:**  
        1. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        2. **Service/Decoration Name:** <Service/Decoration Name>  
        - **Time:** <Start Time> - <End Time>  
        - **Talent:** <Talent> [ONLY IF there is at least 1 talent]
        - **Description:** <Description>

        [Subsequent services in the same format as above]
        """

    proposal_response = get_gpt_response(openai_client, [{"role": "user", "content": proposal_format_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    proposal_response = proposal_response.replace("```markdown", "").replace("```json", "").replace("```", "")

    return proposal_response, has_discount


def save_proposal_to_db(proposal, conversation_id, schema_name, is_redteam):
    with schema_context(schema_name):
        # Get the conversation object
        if is_redteam:
            conversation_instance = RedTeamConversation.objects.get(id=conversation_id)
        else:
            conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)

        conversation_metadata = conversation_instance.metadata

        if "proposals" not in conversation_metadata.keys():
            conversation_metadata["proposals"] = {}

        all_proposal = conversation_metadata.get("proposals", {})
        # Calculate the maximum proposal id from the dictionary keys
        if all_proposal:
            latest_proposal_id = max(all_proposal.keys(), key=lambda x: int(x[1:]))
        else:
            latest_proposal_id = "P000"
        
        # New proposal id
        new_proposal_id = "P" + str(int(latest_proposal_id[1:]) + 1).zfill(3)

        # Save into the conversation metadata
        proposal['proposal_id'] = new_proposal_id 
        conversation_metadata["proposals"][new_proposal_id] = proposal
        conversation_instance.metadata = conversation_metadata
        conversation_instance.save()

        return new_proposal_id


def save_proposal_message_to_db(proposals_list, conversation_id, schema_name, is_redteam):
    with schema_context(schema_name):
        # Make sure there are no other pending messages
        if not chat_models.Message.objects.filter(conversation_id=conversation_id, bot_reply_status="pending").exists():
            # Create the proposal messages
            for proposal in proposals_list:
                if not is_redteam:

                    # Send the message back to websocket
                    # Construct websocket URL with schema name
                    url = f"{WEBSOCKET_URL}/org/{schema_name.replace('_', '-')}/v1/chat/"
                    print("url: ", url)

                    # Construct the encrypted token
                    data = {
                        "uuid": "",
                        "chat_id": conversation_id,
                        "bot_api_key": BOT_API_KEY,
                    }

                    # Convert dictionary to JSON string before encryption
                    json_data = json.dumps(data)
                    encrypted_token = authapi_helper.encrypt_data(json_data)

                    # Build bot response message
                    message = {
                        "header": {"type": "mid_chat_processing_bot_response"},
                        "body": {
                            "encrypted_token": encrypted_token,
                            "source": "Web Application",
                            "message_type": "machine",
                            "bot_reply": {
                                "bot_message": [proposal],
                            },  
                            "conversation_id": conversation_id,
                        },
                    }

                    logger.info(f"Mid Chat Processing Bot response message: {message}")

                    success = send_message(url, message)
                    logger.info(f"Message sent to websocket successfully: {success}")

                    time.sleep(0.5)

                else:
                    RedTeamMessage.objects.create(
                        message_type="bot_text",
                        message=proposal,
                        conversation_id=conversation_id,
                        bot_reply_status="completed",
                    )


@tool("draft_proposals")
def draft_proposals(
    conversation_id: int,
    incoming_message: str = None,
    name: str = None,
    email: str = None,
    contact_number: str = None,
    services_list: list = None,
    date: str = None,
    start_time: str = None,
    end_time: str = None,
    event_location: str = None,
    number_of_adults: int = None,
    number_of_kids_and_age_range: str = None,
    occasion_type: str = None,
    occasion: str = None,
    theme: str = None,
    budget: str = None,
    schema_name: str = None,
    event_additional_details: str = None,
    is_confirm_with_collected_information_and_proceed_with_proposal_generation: str = None,
    is_redteam: str = "true",
    customer_agreed_to_collect_information: str = None,
) -> Dict[str, Any]:
    """
    Main function to draft event proposals based on customer requirements.

    This function orchestrates the entire process of generating and refining proposals,
    including applying discounts and surcharges, formatting, and validation.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        incoming_message (str): Optional. The latest message from the customer.
        name (str): Optional. The customer's name.
        email (str): Optional. The customer's email address.
        contact_number (str): Optional. The customer's contact number.
        email (str): Optional. The customer's email address.
        services_list (list): Optional. A list of services and decoration packages the customer is interested in, 
            e.g., [{"name": "balloon sculpting", "notes": "3 hours, come 30min early"}].
        date (str): Optional. The event date in the format %Y-%m-%d, must be a future date.
        start_time (str): Optional. The start time of the event.
        end_time (str): Optional. The end time of the event.
        event_location (str): Optional. The event location (address or postal code in Singapore).
        number_of_adults (int): Optional. The number of adults attending the event (can be 0).
        number_of_kids_and_age_range (str): Optional. The number and age range of kids attending the event (can be 0 if no kids).
        occasion_type (str): Optional. Specifies if the event is private or corporate.
        occasion (str): Optional. The type of event (e.g., birthday, wedding, corporate party).
        theme (str): Optional. The theme of the event (e.g., colors, characters, or vibe). Use "no preference" if the customer has no specific theme.
        budget (str): Optional. The event budget in the format SGD{comma thousand separated amount}. If a range is provided, the average is taken.
        schema_name (str): Optional. The name of the schema to use.
        event_additional_details (str): Optional. Additional notes or details about the event.
        is_confirm_with_collected_information_and_proceed_with_proposal_generation (str): Optional. Set to `True` 
            if the customer confirms the collected information and agrees to generate a proposal, `False` otherwise.
        is_redteam (str): Optional. Set to `true` unless it is explictly mentioned. Else, set to `false`. This is only for testing purposes.
        customer_agreed_to_collect_information (str): Optional. Set to `False` unless it is explictly mentioned. Else, set to `true`.

    Returns:
        Dict[str, Any]: Formatted proposal response.
    """
    try:
        logger.info("Executing draft_proposals tool...", extra={
            "conversation_id_": conversation_id,
            "name_": name,
            "services_list_": services_list,
            "date_": date,
            "start_time_": start_time,
            "end_time_": end_time,
            "event_location_": event_location,
            "number_of_adults_": number_of_adults,
            "number_of_kids_and_age_range_": number_of_kids_and_age_range,
            "occasion_type_": occasion_type,
            "occasion_": occasion,
            "theme_": theme,
            "budget_": budget,
            "schema_name_": schema_name,
            "event_additional_details_": event_additional_details,
            "is_confirm_with_collected_information_and_proceed_with_proposal_generation_": is_confirm_with_collected_information_and_proceed_with_proposal_generation,
            "is_redteam_": is_redteam,
            "customer_agreed_to_collect_information_": customer_agreed_to_collect_information
        })

        import time

        start = time.time()
        # Validate inputs
        required_fields = {
            "name": name,
            "services_list": services_list,
            "date": date,
            "start_time": start_time,
            "end_time": end_time,
            "event_location": event_location,
            "number_of_adults": number_of_adults,
            "number_of_kids_and_age_range": number_of_kids_and_age_range,
            "occasion_type": occasion_type,
            "occasion": occasion,
            "theme": theme,
            "budget": budget,
        }
        
        missing_fields = validate_inputs(conversation_id, required_fields)
        if missing_fields:
            return gather_event_information.__dict__['func'](
                conversation_id=conversation_id,
                name=name,
                contact_number=contact_number,
                email=email,
                services_list=services_list,
                date=date,
                start_time=start_time,
                end_time=end_time,
                event_location=event_location,
                number_of_adults=number_of_adults,
                number_of_kids_and_age_range=number_of_kids_and_age_range,
                occasion_type=occasion_type,
                occasion=occasion,
                theme=theme,
                budget=budget,
                schema_name=schema_name,
                event_additional_details=event_additional_details,
                is_confirm_with_collected_information_and_proceed_with_proposal_generation=is_confirm_with_collected_information_and_proceed_with_proposal_generation,
                customer_agreed_to_collect_information=customer_agreed_to_collect_information
            )

        # Parse and validate flags
        # is_confirm = str(is_confirm_with_collected_information_and_proceed_with_proposal_generation).lower() == "true"
        is_redteam = str(is_redteam).lower() == "true"

        # Process budget and date
        budget_value = _parse_budget(budget)
        event_date = utility_date_helper.ensure_date_in_future(date)

        # Extract the conversation history and scoring rubrics for grading.
        historical_messages_str, conversation_instance = extract_historical_messages(conversation_id, schema_name, is_redteam)
        # scoring_rubrics = extract_rubric(occasion)

        # Preparation for proposal generation
        client = OpenAI()

        # Step 1: Fetch the master services data based on the occasion
        master_services = fetch_master_services_pricing(occasion_type)

        # Step 2: Determine the event budget
        lower_budget, higher_budget = determine_budget_range(client, budget_value)

        final_proposal = []
        recommendation_response = None
        # For each budget, we will recommend different proposal options
        is_first_proposal = True # This is to toggle the proposal name
        has_discount = False
        for budget_limit in [lower_budget, higher_budget]:

            # Step 3: Filter by budget
            master_services_filtered = filter_master_services_by_budget(budget_limit, master_services)

            # Step 4: Get the ranking of the services/ decorations
            event_metadata ={
                "name": name,
                "date": event_date,
                "start_time": start_time,
                "end_time": end_time,
                "event_location": event_location,
                "number_of_adults": number_of_adults,
                "number_of_kids_and_age_range": number_of_kids_and_age_range,
                "occasion_type": occasion_type,
                "occasion": occasion,
                "theme": theme,
                "budget": budget_limit,
                "services_list": services_list,
                "event_additional_details": event_additional_details,
                "incoming_message": incoming_message
            }
            user_event_requirements = define_user_requirements(client, historical_messages_str, incoming_message, event_metadata)
            services_recommendation = gather_suggestions_for_services(client, user_event_requirements, master_services_filtered)
            services_choices = get_services_choices(client, user_event_requirements, services_recommendation, master_services_filtered)

            print(services_choices)

            # Step 5: Enrich the services with additional details
            services_with_details = enrich_services_with_details(services_choices, master_services_filtered)
            print("Step 5: Enrich the services with additional details")
            print(services_with_details)

            # Step 6: Generate the proposal plan
            services_with_details, recommendation_response = generate_proposal_plan(client, services_with_details, event_metadata, user_event_requirements, budget_limit, recommendation_response)
            print("Step 6: Generate the proposal plan")
            print(services_with_details)

            # Step 7: Get the surcharge fee
            services_with_details = enrich_by_surcharge_fee(client, event_location, date, services_with_details)
            print("Step 7: Get the surcharge fee")
            print(services_with_details)

            # Step 8: Apply discounts and Calculate the total cost, discounted cost
            discount_dict = check_for_discounts(client, services_with_details)
            print("Step 8: Apply discounts and Calculate the total cost, discounted cost")
            print(discount_dict)

            # Step 9: Get the proposal Id and save it to the chat metadata
            proposal_to_save = {
                "name": name,
                "email": email,
                "date": date,
                "start_time": start_time,
                "end_time": end_time,
                "event_location": event_location,
                "number_of_adults": number_of_adults,
                "number_of_kids_and_age_range": number_of_kids_and_age_range,
                "occasion_type": occasion_type,
                "occasion": occasion,
                "theme": theme,
                "budget": budget_limit,
                "services_list": services_list,
                "event_additional_details": event_additional_details,
                "contact_number": contact_number,
                "discounted_price": discount_dict.get("total_discounted_amount", 0),
                "discounted_date": discount_dict.get("last_date_of_discount", "").strftime("%Y-%m-%d") if discount_dict.get("last_date_of_discount") else "",
                "total_cost": discount_dict.get("total_cost", 0)
            }
            proposal_id = save_proposal_to_db(proposal_to_save, conversation_id, schema_name, is_redteam)
            print("Step 9: Get the proposal Id and save it to the chat metadata")
            print(proposal_id)

            # Step 10: Draft the final proposal
            proposal_response, this_proposal_has_discount = draft_final_proposal(client, services_with_details, event_metadata, user_event_requirements, discount_dict.get("total_cost", 0), discount_dict.get("total_discounted_amount", 0), discount_dict.get("last_date_of_discount", ""), proposal_id, is_first_proposal)
            print("Step 10: Draft the final proposal")
            print(proposal_response)

            final_proposal.append(proposal_response)

            is_first_proposal = False
            if has_discount is False and this_proposal_has_discount is True:
                has_discount = True
        
        end = time.time()
        print(f"Time taken: {end - start}")

        print("Final proposal: ", final_proposal)

        # Update the db with the final proposals
        save_proposal_message_to_db(final_proposal, conversation_id, schema_name, is_redteam)

        if has_discount and discount_dict.get("last_date_of_discount", ""):
            return utility_general_helper.clean_up_prompt(
            f"""Tool execution completed and you MUST ONLY return the following message at it is. DO NOT add any additional information! 
            
            Here's your proposals. If you’d like to move forward, please let us know which proposal ID (e.g., P001) you’d like to select, or feel free to explore more options at  https://happier.sg/entertainment-catalogue/ .
            
            Please confirm the proposal early to enjoy the discounted price.

            Would it be possible to let us know a convenient day to follow up with you on your decision?""")

        return utility_general_helper.clean_up_prompt(
        f"""Tool execution completed and you MUST ONLY return the following message at it is. DO NOT add any additional information!

        Here's your proposals. If you’d like to move forward, please let us know which proposal ID (e.g., P001) you’d like to select, or feel free to explore more options at  https://happier.sg/entertainment-catalogue/ .
        
        Would it be possible to let us know a convenient day to follow up with you on your decision?""")
    
    except Exception as e:
        logger.error(f"Error in draft_proposals tool: {e}", extra={
            "conversation_id_": conversation_id,
            "name_": name,
            "services_list_": services_list,
            "date_": date,
            "start_time_": start_time,
            "end_time_": end_time,
            "event_location_": event_location,
            "number_of_adults_": number_of_adults,
            "number_of_kids_and_age_range_": number_of_kids_and_age_range,
            "occasion_type_": occasion_type,
            "occasion_": occasion,
            "theme_": theme,
            "budget_": budget,
            "schema_name_": schema_name,
            "event_additional_details_": event_additional_details,
            "is_confirm_with_collected_information_and_proceed_with_proposal_generation_": is_confirm_with_collected_information_and_proceed_with_proposal_generation,
            "is_redteam_": is_redteam,
            "customer_agreed_to_collect_information_": customer_agreed_to_collect_information
        })
        logger.error(traceback.format_exc())

        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
