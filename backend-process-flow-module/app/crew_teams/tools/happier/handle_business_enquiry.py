import logging
import re
import os
import json
import time
import traceback
from typing import Optional
from pydantic import BaseModel, <PERSON>
from typing import Literal

import pandas as pd
from crewai.tools import tool
from django_tenants.utils import schema_context
from openai import OpenAI
import app.chats.chat.models as chat_models
from app.redteam.models import RedTeamMessage
import app.core.authapi.helper as authapi_helper
from app.utility.websocket_client import send_message


import app.documents.document.models as document_models
import app.utility.chat_constant as chat_constant
from app.crew_teams.tools.happier.fetch_master_data import fetch_master_services_pricing
from app.crew_teams.utils.gpt_integration import get_gpt_response
from backend.settings import AWS_STORAGE_BUCKET_NAME, WEBSOCKET_URL, BOT_API_KEY

logger = logging.getLogger(__name__)


def insert_relevant_images(openai_client: OpenAI, suggestion: str, schema_name: str) -> str:
    """Removes pricing information from the suggestion."""

    with schema_context(schema_name):
        image_instances = document_models.KnowledgeImage.objects.all()
        image_tagging_list = [
            {"name": image_instance.name, "url": f"https://{AWS_STORAGE_BUCKET_NAME}.s3.ap-southeast-1.amazonaws.com{image_instance.file.url}"}
            for image_instance in image_instances
        ]

    image_prompt = f"""
    Below is the suggestion we would like to present to the customer:

    {suggestion}

    To make the suggestion more appealing, we will select one relevant image for each category of suggestion. These images will complement the services and decorations suggested, ensuring the customer can easily read through the recommendations without interruptions.

    Formatting Guidelines:
    1. Preserve the content, reasonings, and suggestions as they are.
    2. As and when applicable, append images at the end using the markdown format: "![Image](path to the image)"
    3. Do not include images in the format [Kids Party Backdrop with Balloons](path to the image). If you are unable to find an appropriate image, do not include in your response. Rather keep the message as it is than to add (path to the image) to it.

    Images Dictionary List:
    {image_tagging_list}

    YOU MUST RETURN IN A MARKDOWN FORMAT.
    """

    suggestion_response = get_gpt_response(openai_client, [{"role": "user", "content": image_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    return suggestion_response


def save_split_service_decor_messages_to_db(message: str):
    # Define the regex pattern for the image tag
    image_pattern = r'\!\[.*?\]\(.*?\)'

    # Find all image tag matches along with their positions
    matches = list(re.finditer(image_pattern, message))

    # Create chunks by splitting after each image tag
    chunks = []
    start_index = 0

    for match in matches:
        # end position of the current image tag
        end_index = match.end()
        chunk = message[start_index:end_index].strip()
        chunks.append(chunk)
        start_index = end_index

    # Optionally add any trailing text after the last image tag if needed
    if start_index < len(message):
        chunks.append(message[start_index:].strip())

    return chunks


def save_recommendations_message_to_db(recommendation_message: list | str, conversation_id, schema_name, is_redteam):
    with schema_context(schema_name):
        # Make sure there are no other pending messages
        if not chat_models.Message.objects.filter(conversation_id=conversation_id, bot_reply_status="pending").exists():
        
            if is_redteam:
                RedTeamMessage.objects.create(
                    message_type="bot_text",
                    message=recommendation_message,
                    conversation_id=conversation_id,
                    bot_reply_status="completed",
                )

            else:
                # Send the message back to websocket
                # Construct websocket URL with schema name
                url = f"{WEBSOCKET_URL}/org/{schema_name.replace('_', '-')}/v1/chat/"
                print("url: ", url)

                # Construct the encrypted token
                data = {
                    "uuid": "",
                    "chat_id": conversation_id,
                    "bot_api_key": BOT_API_KEY,
                }
                # Convert dictionary to JSON string before encryption
                json_data = json.dumps(data)
                encrypted_token = authapi_helper.encrypt_data(json_data)

                if not isinstance(recommendation_message, list):
                    recommendation_message = [recommendation_message]

                # Build bot response message
                for recommendation in recommendation_message:
                    message = {
                        "header": {"type": "mid_chat_processing_bot_response"},
                        "body": {
                            "encrypted_token": encrypted_token,
                            "source": "Web Application",
                            "message_type": "machine",
                            "bot_reply": {
                                "bot_message": [recommendation],
                            },  
                            "conversation_id": conversation_id,
                        },
                    }

                    logger.info(f"Mid Chat Processing Bot response message: {message}")

                    # Send message via websocket asynchronously
                    success = send_message(url, message)
                    logger.info(f"Message sent to websocket successfully: {success}")

                    time.sleep(0.5)

                logger.info("Recommendation messages sent to websocket successfully.")


def generate_options_based_on_interest(openai_client, incoming_message, search_keyword, initial_enriched_df):
    # Generate response        
    prompt = f"""
    Based on the information from the incoming message, craft a short and concise response addressing the customer's question using the context of their interest and the services we offer. If the customer is inquiring about available service options, recommend up to 3 services.

    Message Details:
    {incoming_message}

    Customer's Interest:
    {search_keyword}

    Available Services List:
    {initial_enriched_df.to_dict(orient='records')}

    The response should:
    - Address the customer's question directly and professionally.
    - Highlight the customer's interest and what they are looking for.
    - Recommend up to 3 relevant services if applicable.
    - Ensure the message is concise, customer-centric, and suitable for WhatsApp.
    - Do not add any additional context or information. All information MUST BE TAKEN FROM Available Services List.
    - Your response tone should be engaging for sales context so that you can sell the service.
    - End your response with an open ended question for the customer to ask more questions. Or a call to action for the customer provide more information so that you can generate a customized proposal for the customer.

    Output must be formatted in MARKDOWN.
    """

    suggestion_response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    print(suggestion_response)

    return suggestion_response


def add_pricing_to_response(openai_client, suggestion_response, sub_enriched_df):
    prompt = f"""You are an expert in pricing and sales. Your task now is to look at the suggested services and determine the price range for each of them.
        
    Conditions:
    - If PricingModelID has multiplier (e.g. first hour x1.2), it means that the minimum price for that item is x1.2 of the base price. (!IMPORTANT)
    - If PricingModelID is set/package/session, it means that the minimum price for that item is the base price. (!IMPORTANT)

    Here is your suggested services with price range:
    {suggestion_response}
    
    Here is the Available Services List with PricingModelID:
    {sub_enriched_df.to_dict(orient='records')}

    You MUST NOT change the suggested services. 
    
    Return in a json format as shown below.
    {{
        services: [
            {{
                "Name": "Service Name",
                "Minimum Amount": "<Using the Base Price and PricingModelID to calculate the minimum price>"
            }}
        ],
        ...
    }}
    """
    price_suggestion_response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    print(price_suggestion_response)

    prompt = f"""You are an expert in pricing and sales. Your task now is to integrate the price into the message.
    
    Here is your suggested services with price range:
    {suggestion_response}
    
    These are the pricing for the services:
    {price_suggestion_response}

    You MUST NOT change the suggested services. ONLY include the price in the message. (The price is starting from $X)
    
    Output must be formatted in MARKDOWN.
    """
    suggestion_response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    print(suggestion_response)

    return suggestion_response


def generate_response_for_offering_enquiry(openai_client, incoming_message, initial_enriched_df):
    # Generate response        
    prompt = f"""You are a top tier expert in customer service. Your task is to response to the customer's enquiry on your business offerings.

    Message from Customer:
    {incoming_message}

    Available Services List:
    {initial_enriched_df.to_dict(orient='records')}

    The response should:
    - Address the customer's question directly and professionally.
    - Ensure the message is concise, customer-centric, and suitable for WhatsApp.
    - Do not add any additional context or information. All information MUST BE TAKEN FROM Available Services List.
    - Your response tone should be engaging for sales context so that you can sell the service.
    - End your response with an open ended question for the customer to ask more questions. Or a call to action for the customer provide more information so that you can generate a customized proposal for the customer.

    Output must be formatted in MARKDOWN.
    """

    suggestion_response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    print(suggestion_response)

    return suggestion_response


def determine_which_type_of_enquiry(openai_client, incoming_message):
    # Generate response        
    prompt = f"""You are an expert in customer service. Your customer has sent you the following messages to enquiry more on the business.
    {incoming_message}

    Your task is to determine which type of enquiry the customer is making.
    1. User asking for offerings/ services/ packages without any specific service mentions
    2. User asking for price of a specific service
    3. Any other business enquiry

    Output must be formatted in JSON.

    Example:
    {{
        "type": "1"
    }}
    """

    class EnquiryType(BaseModel):
        type: Literal["1", "2", "3"] = Field(description="Type of business enquiry")

    suggestion_response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0], response_format=EnquiryType)

    print(suggestion_response)

    return suggestion_response


@tool("handle_business_enquiry")
def handle_business_enquiry(conversation_id: int, search_keyword: str = None, occasion_type: str = None, incoming_message: str = None, schema_name: str = None) -> str:
    """
    Handle business enquiries by searching through master services and providing cost estimates.
    
    Args:
        conversation_id (int): ID of the conversation
        search_keyword (str): Keyword to filter services by name, description, category, or subcategory
        occasion_type (str): Type of occasion ('corporate' or 'private')
        incoming_message (str): The incoming message
        schema_name (str): The schema name
        
    Returns:
        str: A response containing relevant services and cost ranges
    """
    try:
        logger.info(f"Executing handle_business_enquiry with conversation_id: {conversation_id}, search_keyword: {search_keyword}, occasion_type: {occasion_type}, incoming_message: {incoming_message}, schema_name: {schema_name}")

        # Fetch master data
        if occasion_type and occasion_type.lower() == "corporate":
            df = fetch_master_services_pricing(occasion_type=occasion_type)
        elif occasion_type and occasion_type.lower() == "private":
            df = fetch_master_services_pricing(occasion_type=occasion_type)
        else:
            df = pd.concat([fetch_master_services_pricing(occasion_type="corporate"), fetch_master_services_pricing(occasion_type="private")])
        
        openai_client = OpenAI()
        
        # Filter DataFrame based on keyword across multiple columns
        if not search_keyword:
            # Convert search keyword to lowercase for case-insensitive search
            search_keyword = search_keyword.lower()
            filtered_df = df[
                df['Name'].str.lower().str.contains(search_keyword, na=False) |
                df['Description'].str.lower().str.contains(search_keyword, na=False) |
                df['Category'].str.lower().str.contains(search_keyword, na=False) |
                df['SubCategory'].str.lower().str.contains(search_keyword, na=False)
            ]
        else:
            filtered_df = df
        
        if filtered_df.empty:
            return f"I couldn't find any services matching '{search_keyword}'. Could you please try with a different keyword or let me know more about what you're looking for?"

        # Remove the information of "by <x> talent" from the service name
        filtered_df['Name'] = filtered_df['Name'].apply(lambda name: name.split(' by ')[0])

        # Calculate price ranges by category
        price_ranges = filtered_df.groupby('SubCategory')['BasePrice'].agg(['min', 'max']).reset_index()

        # Merge price ranges back into filtered_df
        enriched_df = filtered_df.merge(price_ranges, on='SubCategory')

        enriched_df = enriched_df[['Category', 'SubCategory', 'Name',  'Description', 'PricingModelID', 'BasePrice', 'min', 'max']]
        enriched_df.columns = ['Category', 'SubCategory', 'Name', 'Description', 'PricingModelID', 'BasePrice', 'Lower Base Price Range', 'Upper Price Range']
        
        # There are a few options that we can reply with
        # 1. User asking for offerings only
        # 2. User asking for price of a specific service
        # 3. Any other business enquiry
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                enquiry_type = determine_which_type_of_enquiry(openai_client, incoming_message)
                if isinstance(enquiry_type, str):
                    enquiry_type = json.loads(enquiry_type)
                break
            except json.JSONDecodeError:
                retry_count += 1
                logger.warning(f"Failed to parse enquiry_type as JSON (attempt {retry_count}/{max_retries})")
                if retry_count == max_retries:
                    logger.error("Max retries reached. Using default enquiry type.")
                    enquiry_type = {"type": "3"}  # Default to "other business enquiry"
        
        print("Enquiry type:", enquiry_type)
        if enquiry_type["type"] == "1":
            initial_enriched_df = enriched_df[['Category', 'SubCategory', 'Name', 'Description']]
            suggestion_response = generate_response_for_offering_enquiry(openai_client, incoming_message, initial_enriched_df)

        elif enquiry_type["type"] == "2":
            # Filter for the initial_enriched_df
            initial_enriched_df = enriched_df[['Category', 'SubCategory', 'Name', 'Description']]
            suggestion_response = generate_options_based_on_interest(openai_client, incoming_message, search_keyword, initial_enriched_df)

            # Ensure that the price is correctly shown
            sub_enriched_df = enriched_df[['Name', 'PricingModelID', 'BasePrice']]
            # Inject pricing into the response.
            suggestion_response = add_pricing_to_response(openai_client, suggestion_response, sub_enriched_df)

            # Insert relevant images
            suggestion_response = insert_relevant_images(openai_client, suggestion_response, schema_name)

        else:
            initial_enriched_df = enriched_df[['Category', 'SubCategory', 'Name', 'Description']]
            suggestion_response = generate_response_for_offering_enquiry(openai_client, incoming_message, initial_enriched_df)

        print(suggestion_response)

        # Replace image tags with the desired format
        suggestion_response = re.sub(r'!\[.*?\]\((https.*?)\)', r'![Image](\1)', suggestion_response)

        suggestion_response = re.sub(r"!\[.*?\]\(Image url\)", "", suggestion_response)

        suggestion_response = suggestion_response.replace("```markdown", "")
        suggestion_response = suggestion_response.replace("```json", "")
        suggestion_response = suggestion_response.replace("```", "")

        suggestion_response = save_split_service_decor_messages_to_db(suggestion_response)

        if len(suggestion_response) > 1:
            # Update the db with the recommendations message
            is_redteam = str(os.environ.get("REDTEAM_RUN", "False")).lower() == "true"

            save_recommendations_message_to_db(recommendation_message=suggestion_response, conversation_id=conversation_id, schema_name=schema_name, is_redteam=is_redteam)

            return f"""Tool execution completed! Your task is to communicate the following response to the customer:

            To view our full service list, you can visit our https://happier.sg/entertainment-catalogue/ to explore more options.
            """
        
        suggestion_response = suggestion_response[0]
        
        return f"""Tool execution completed! Your task is to communicate the following response to the customer:
        {suggestion_response}."""
            
    except Exception as e:
        logger.error(f"An error occurred in handle_business_enquiry: {e}")
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""