import json
import logging
import traceback
from datetime import datetime
from typing import List

from crewai.tools import tool
from django_tenants.utils import schema_context
from openai import OpenAI

import app.chats.chat.models as chat_models
import app.chats.chat_action.models as chat_action_models
import app.utility.chat_constant as chat_constant
import app.utility.general_helper as utility_general_helper
import app.utility.models as utility_models
from app.chats.chat.models import Conversation
from app.chats.chat_action_tracker.models import (
    CallListAction,
    CustomChatActionTracker,
    MessageToSend,
)
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.redteam.models import RedTeamConversation

logger = logging.getLogger(__name__)


def validate_inputs(conversation_id: int, required_fields: dict) -> List[str]:
    """
    Validate required fields for proposal generation.

    Args:
        conversation_id (int): ID of the conversation.
        required_fields (dict): Dictionary of fields and their values.

    Returns:
        List[str]: List of missing fields.
    """
    missing_fields = [key for key, value in required_fields.items() if not value]
    if not conversation_id:
        missing_fields.append("conversation_id")
    return missing_fields


def extract_proposal_from_chat_metadata(conversation_id: int, schema_name: str, is_redteam: bool, selected_proposal: str) -> dict:
    """
    Extracts a specific proposal from the chat metadata of a conversation.

    Args:
        conversation_id (int): The unique identifier of the conversation.
        schema_name (str): The name of the schema to use for the database context.
        is_redteam (bool): Indicates whether the conversation belongs to the RedTeam.
        selected_proposal (str): The key of the proposal to extract from the metadata.

    Returns:
        dict: The extracted proposal data if found, otherwise an empty dictionary.
    """
    with schema_context(schema_name):
        conversation = (
            RedTeamConversation.objects.filter(id=conversation_id).first()
            if is_redteam
            else Conversation.objects.filter(id=conversation_id).first()
        )

        if not conversation:
            return {}

        metadata = conversation.metadata
        if not metadata or "proposals" not in metadata:
            return {}

        proposal_data = metadata.get("proposals", {})
        if not proposal_data:
            return {}

        return proposal_data.get(selected_proposal, {})


def create_custom_chat_action_setting(schema_name: str) -> chat_action_models.CustomChatActionSetting | None:
    """
    Creates a custom chat action setting for a conversation.

    Args:
        schema_name (str): The name of the schema to use for the database context.

    Returns:
        CustomChatActionSetting | None: The created custom chat action setting.
    """
    with schema_context(schema_name):
        custom_chat_action_setting_instance, _ = (
            chat_action_models.CustomChatActionSetting.objects.get_or_create(
                name="Event Booking",
                defaults={
                    "description": "Event Booking Log",
                    "is_activated": True,
                    "icon_svg": "",
                    "on_approve": r"""def on_approve(custom_chat_action_tracker_id):
        print("On approved function to be updated.")
    """,
                    "on_reject": r"""def on_reject(custom_chat_action_tracker_id):
        print("On reject function to be updated.")""",
                },
            )
        )

        return custom_chat_action_setting_instance


def check_existing_soft_book_proposal(conversation_id: int, schema_name: str) -> bool:
    """
    Checks if a soft book proposal already exists for the conversation.

    Args:
        conversation_id (int): The unique identifier of the conversation.
        schema_name (str): The name of the schema to use for the database context.

    Returns:
        bool: True if a soft book proposal exists, False otherwise.
    """
    with schema_context(schema_name):
        custom_chat_action_tracker_instance = CustomChatActionTracker.objects.filter(conversation_id=conversation_id, status=utility_models.PENDING)

        if custom_chat_action_tracker_instance.exists():
            return True

        return False


def response_to_existing_soft_book_proposal(openai_client: OpenAI, proposal: dict) -> str:
    """
    Generates a response back to customer to explain that there is an existing soft book proposal.

    Args:
        openai_client (OpenAI): The OpenAI client.
        proposal (dict): The proposal data.

    Returns:
        str: The generated response.
    """
    prompt = f"""Your task is to provide a response back to the customer to explain that there is an existing soft book proposal.
    The proposal is as follows:

    Proposal Id: {proposal.get('proposal_id')}
    Date: {proposal.get('date')}
    Time: {proposal.get('start_time')} - {proposal.get('end_time')}
    Location: {proposal.get('event_location')}
    """

    response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    return response


def create_chat_action_tracker(conversation_id: int, custom_chat_action_setting_instance: chat_action_models.CustomChatActionSetting, proposal: dict, selected_proposal: str, conversation_queue_id: int, schema_name: str) -> CustomChatActionTracker | None:
    """
    Creates a custom chat action tracker for a conversation.

    Args:
        conversation_id (int): The unique identifier of the conversation.
        custom_chat_action_setting_instance (CustomChatActionSetting): The custom chat action setting instance.
        proposal (dict): The proposal data.
        selected_proposal (str): The selected proposal.
        conversation_queue_id (int): The unique identifier of the conversation queue.
        schema_name (str): The name of the schema to use for the database context.

    Returns:
        CustomChatActionTracker | None: The created custom chat action tracker.
    """
    with schema_context(schema_name):
        # Get the message instance from the queue
        conversation_queue_obj = chat_models.ConversationQueue.objects.filter(id=conversation_queue_id).first()

        if not conversation_queue_obj:
            return None

        message = conversation_queue_obj.messages_in_processing

        # Get the latest message by getting the max in the array
        if not message:
            return None

        # Get the max id from the array
        max_message_id = max(message) if message else None
        if max_message_id is None:
            return None
        
        custom_chat_action_tracker_instance = CustomChatActionTracker.objects.create(
            tracker_identifier=selected_proposal,
            conversation_id=conversation_id,
            message_id=max_message_id,
            status=utility_models.PENDING,
            remarks=str(proposal.get("event_additional_details")),
            additional_fields=json.dumps(proposal),
            custom_chat_action_setting=custom_chat_action_setting_instance,
        )

        logger.info(f"Created custom_chat_action_tracker_instance: {custom_chat_action_tracker_instance}")

        return custom_chat_action_tracker_instance


def set_callback_action_to_customer(client, conversation_id, custom_chat_action_tracker_instance, proposal, selected_proposal, conversation_queue_id, schema_name):

    # Need to extract out the discounted price and the discounted date
    prompt = f"""Your task is look at the proposal and extract the discounted price and the discounted date. 

    Proposal:
    {proposal}

    Response in JSON format as below:
    {{
        "discounted_price": "discounted_price",
        "discounted_date": "discounted_date <Format %d %b %Y>" 
    }}
    """
    
    attempts = 0
    while attempts < 5:
        try:
            response = get_gpt_response(client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
            response = response.replace("```json", "").replace("```", "")
            response = utility_general_helper.extract_json_from_str(response)
            response = json.loads(response)
            break
        except Exception as e:
            print(f"Error occurred: {e}. Retrying {attempts + 1}/5...")
            attempts += 1  

    with schema_context(schema_name):

        print("="*100)
        print(proposal)

        CallListAction.objects.create(
            conversation_id=conversation_id,
            message=custom_chat_action_tracker_instance.message,
            name=proposal.get("name"),
            contact=proposal.get("contact_number"),
            email=proposal.get("email"),
            context=f"This is the reminder to the customer to confirm the proposal (ID: {selected_proposal}) for the discounted price of {response.get('discounted_price')} by {response.get('discounted_date')}.",
        )


def generate_tool_response(openai_client, proposal, selected_proposal):
    """
    Generates a response for the provided tool.

    Args:
        openai_client: The OpenAI client instance.
        proposal: The proposal data.
        selected_proposal: The selected proposal details.
    """

    # Generate the response to the customer
    prompt = f"""Your task is to provide a response to the customer to confirm the soft booking of the proposal below. Reformat the information into the expected output format.
    
    Proposal Details:
    {proposal}
    
    EXPECTED OUTPUT FORMAT:
    Here's what we've soft booked for you:
    ###
    We're excited to help bring your event vision to life. Here’s a sneak peek at what we’ve got planned for you:
    - **Proposal ID:** {selected_proposal}
    - **Event Date:** {{date}}
    - **Event Time:** From {{start_time}} to {{end_time}}
    - **Location:** {{event_location}}
    - **Occasion Type:** {{occasion_type}}
    - **Occasion:** {{occasion}}
    - **Theme:** {{theme}}
    - **Kids Attending:** {{number_of_kids_and_age_range}}
    - **Adults Attending:** {{number_of_adults}}
    - **Email:** {{email}}
    - **Contact Number:** {{contact_number}}
    - **Services/Decoration:** 
    {{services_list}}
    - **Total Price:** {{total_cost}}

    We're currently doing a final check on availability and will get back to you within 24 hours with the official quote and next steps. Thank you for choosing us to make your event unforgettable! 😊
    """

    response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    return response


def notify_client_admin(openai_client: OpenAI, proposal: dict, schema_name: str):
    # Generate the response to the customer
    prompt = f"""Your task is to convert the proposal details into a summary message to your event planner team. In this message, you MUST INCLUDE all the information for them to coordinate with the client and vendor.

    Proposal Details:
    {proposal}

    As you will be sending it via Whatsapp platform, the message must be short and to the point.

    EXPECTED OUTPUT FORMAT:
    - **Proposal ID:** {{proposal_id}}
    - **Event Date:** {{date}}
    - **Event Time:** From {{start_time}} to {{end_time}}
    - **Location:** {{event_location}}
    - **Occasion Type:** {{occasion_type}}
    - **Occasion:** {{occasion}}
    - **Theme:** {{theme}}
    - **Kids Attending:** {{number_of_kids_and_age_range}}
    - **Adults Attending:** {{number_of_adults}}
    - **Email:** {{email}}
    - **Contact Number:** {{contact_number}}
    - **Services/Decoration:** 
    {{services_list}}
    - **Total Price:** {{total_cost}}
    """

    response = get_gpt_response(openai_client, [{"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
    
    with schema_context(schema_name):
        MessageToSend.objects.create(
            target_source=utility_models.WHATSAPP_QR,
            contact="6592717106",
            # contact="6597288974",
            message=response,
            status=utility_models.PENDING,
            to_be_sent_at=datetime.now(),
            message_type=utility_models.PAGE_ADMIN_TEXT
        )


@tool("confirm_proposal")
def confirm_proposal(
    conversation_id: int = None,
    conversation_queue_id: int = None,
    selected_proposal: str = None,
    billing_address: str = None,
    schema_name: str = None,
    is_redteam: str = "true"):
    """
    Confirms a proposal by validating inputs and preparing necessary data for soft booking.

    This function handles the confirmation of a customer's proposal by performing several tasks:
    - Validates the presence of essential inputs.
    - Extracts the selected proposal's details from the chat metadata.
    - Checks if a billing address is provided.
    - If applicable, checks for an existing soft book proposal.
    - Creates necessary action trackers and settings for the proposal confirmation process.
    - Sets a callback action to remind the customer about the proposal confirmation.
    - Generates a response back to the customer confirming the soft booking details.

    Args:
        conversation_id (int): The unique identifier of the conversation.
        conversation_queue_id (int): The unique identifier of the conversation queue.
        selected_proposal (str): The key of the proposal to confirm.
        billing_address (str): The billing address for the proposal.
        schema_name (str): The schema name for the database context.
        is_redteam (str): Indicates if the conversation belongs to the RedTeam.

    Returns:
        str: A message to be communicated back to the customer, indicating the status of the proposal confirmation.
    """
    try:
        logger.info(f"Executing confirm_proposal with conversation_id: {conversation_id}, selected_proposal: {selected_proposal}")

        # Validate inputs
        required_fields = {
            "selected_proposal": selected_proposal
        }
        
        missing_fields = validate_inputs(conversation_id, required_fields)
        if missing_fields:
            return f"""Tool execution completed! Your task is to communicate to the customer that you are unable to confirm the proposal as you are missing {', '.join(missing_fields)} information as it is required to proceed with the event planning process."""

        is_redteam = True if str(is_redteam).lower() == "true" else False

        # Extract the selected proposal from chat metadata
        proposal = extract_proposal_from_chat_metadata(conversation_id, schema_name, is_redteam, selected_proposal)

        if not proposal:
            return "Tool execution completed! This is the message from the tool back to the customer: We are unable to find the proposal you selected. Would you like to let me know which proposal you would like to confirm."

        # Check if billing address is provided
        if not billing_address:
            return "Tool execution completed! This is the message from the tool back to the customer: Before soft booking the proposal, we need the billing address. Ask the customer what is the billing address."

        proposal['billing_address'] = billing_address

        client = OpenAI()

        # Now we need to create the CustomChatActionSetting
        if not is_redteam:
            custom_chat_action_setting_instance = create_custom_chat_action_setting(schema_name)
            
            # Check if the proposal has been soft book
            if check_existing_soft_book_proposal(conversation_id, schema_name):
                response = response_to_existing_soft_book_proposal(client, proposal)
                return f"""Tool execution completed! This is the message from the tool back to the customer: 
                {response}"""

            # Create the chat action tracker
            custom_chat_action_tracker_instance = create_chat_action_tracker(conversation_id, custom_chat_action_setting_instance, proposal, selected_proposal, conversation_queue_id, schema_name)

            # Set a callback action to the customer to remind them to confirm the proposal for the discounted price
            set_callback_action_to_customer(client, conversation_id, custom_chat_action_tracker_instance, proposal, selected_proposal, conversation_queue_id, schema_name)

            # Notify the client admin
            notify_client_admin(client, proposal, schema_name)

        # Generate the response to the customer
        response = generate_tool_response(client, proposal, selected_proposal)
        return f"""Tool execution completed! This is the message from the tool back to the customer: 
        {response}"""

    except Exception as e:

        logger.error(f"Error occurred in confirm_proposal: {e}")
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
