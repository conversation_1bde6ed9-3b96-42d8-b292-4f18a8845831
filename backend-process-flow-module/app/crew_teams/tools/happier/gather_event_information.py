import logging
import traceback
from typing import Optional

from crewai.tools import tool
from tenant_schemas.utils import schema_context

from app.chats.chat.models import Conversation
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.tools.happier.recommend_event_budget import recommend_event_budget
from app.crew_teams.tools.happier.recommend_event_services_and_decorations_addons import (
    recommend_event_services_and_decorations_addons,
)

logger = logging.getLogger(__name__)


def get_contact_number_from_whatsapp(conversation_id: int, schema_name: str) -> str:
    """
    Retrieves the contact number associated with a WhatsApp conversation.

    Args:
        conversation_id (int): The unique identifier of the conversation.
        schema_name (str): The name of the schema to use for the database context.

    Returns:
        str: The contact number extracted from the conversation's third-party ID.
    """
    with schema_context(schema_name):
        conversation_instance = Conversation.objects.get(id=conversation_id)
        phone_number = conversation_instance.third_party_id
        return phone_number


# Function to collect personal information
def collect_personal_information(name: Optional[str], contact_number: Optional[str], email: Optional[str], customer_agreed_to_collect_information: Optional[str], schema_name: str, incoming_message: str, conversation_id: int) -> str:
    """
    Checks for missing personal details and returns a concise prompt requesting only the required information.
    
    Args:
        name (str): The customer's name.
        contact_number (str): The customer's contact number.
        email (str): The customer's email (optional).
        customer_agreed_to_collect_information (str): Whether the customer has agreed to collect information.
        schema_name (str): The name of the schema.
        incoming_message (str): The latest message from the customer

    Returns:
        str: A message requesting the missing details. If all details are provided, returns an empty string.
    """
    missing_info = []
    if not name:
        missing_info.append("name")
    if not contact_number:
        missing_info.append("contact number")
    if not email and customer_agreed_to_collect_information:
        missing_info.append("email")

    if missing_info and customer_agreed_to_collect_information:
        response_to_customer = f"""To create your perfect event package, we just need a few more details: {', '.join(missing_info)}. Could you please share these with us? Your information will be kept confidential and used only for your custom package. Thanks!"""
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message, conversation_id=conversation_id)

    elif missing_info and not customer_agreed_to_collect_information:
        response_to_customer = f"""To give you the best package, we just need your {', '.join(missing_info)}. It'll be our little secret - promise! 😊 Mind sharing?"""
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message, conversation_id=conversation_id)

    return ""


# Function to collect basic event details
def collect_basic_event_details(
    date: str, 
    start_time: str, 
    end_time: str, 
    event_location: str, 
    schema_name: str, 
    incoming_message: str,
    number_of_adults: int,
    number_of_kids_and_age_range: str,
    occasion_type: str,
    occasion: str,
    theme: str,
    conversation_id: str) -> str:
    """
    Checks for missing basic event details and returns a prompt to gather them if needed.
    """
    missing_info = []
    if not date:
        missing_info.append("event date")
    if not start_time:
        missing_info.append("event start time")
    if not end_time:
        missing_info.append("event end time")
    if not event_location:
        missing_info.append("event location or postal code")
    if not number_of_adults:
        missing_info.append("number of adults attending the event")
    if not number_of_kids_and_age_range:
        missing_info.append("number and age range of kids attending the event")
    if not occasion_type:
        missing_info.append("occasion type (private or corporate)")
    if not occasion:
        missing_info.append("event occasion (e.g., birthday, wedding)")
    if not theme:
        missing_info.append("event theme")

    if missing_info:
        response_to_customer = f"""Tool execution completed! This is the message from the tool back to the customer: 
        Would you be able to provide us the following: {', '.join(missing_info)}. 
        It will be important for us to tailor the perfect package for you."""
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message, conversation_id=conversation_id)
    return ""


# # Function to collect additional event details
# def collect_additional_event_details(
#     number_of_adults: int,
#     number_of_kids_and_age_range: str,
#     occasion_type: str,
#     occasion: str,
#     theme: str,
#     schema_name: str,
#     incoming_message: str
# ) -> str:
#     """
#     Checks for missing additional event details and returns a prompt to gather them if needed.
#     """
#     missing_info = []
#     if not number_of_adults:
#         missing_info.append("number of adults attending the event")
#     if not number_of_kids_and_age_range:
#         missing_info.append("number and age range of kids attending the event")
#     if not occasion_type:
#         missing_info.append("occasion type (private or corporate)")
#     if not occasion:
#         missing_info.append("event occasion (e.g., birthday, wedding)")
#     if not theme:
#         missing_info.append("event theme")

#     if missing_info:
#         response_to_customer = f"""Here's the context you can use when crafting your reply to the customer:
#         We are now close to finalizing the customized proposal. We'll need a few more pieces of information regarding the {', '.join(missing_info)}.

#         If the customer hesitates or refuses to provide this information, you can explain that these details are critical for the customized proposal because:
#         1. They help us tailor the event experience to their specific needs and preferences.
#         2. This information allows us to provide accurate pricing and ensure we have the right resources available.
#         3. It enables us to suggest appropriate themes, activities, or services that best match their event type and attendee demographics."""

#         return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message)

#     return ""


# Function to summarize event details
def summarize_event_details(plan_event_args: dict) -> dict:
    """
    Summarizes all collected details and asks the customer to confirm.
    """
    services_list = plan_event_args.get('services_list', [])
    services_markdown = "\n".join(f"- {service}" for service in services_list)

    details = f"""
    ## Event Details:

    ### Contact Information
    - **Name:** {plan_event_args.get('name')}
    - **Contact:** {plan_event_args.get('contact_number')}
    - **Email:** {plan_event_args.get('email', 'Not provided')}

    ### Event Information
    - **Date:** {plan_event_args.get('date')}
    - **Time:** {plan_event_args.get('start_time')} - {plan_event_args.get('end_time')}
    - **Location:** {plan_event_args.get('event_location')}

    ### Attendance
    - **Adults:** {plan_event_args.get('number_of_adults')}
    - **Kids:** {plan_event_args.get('number_of_kids_and_age_range')}

    ### Occasion Details
    - **Occasion:** {plan_event_args.get('occasion')}
    - **Theme:** {plan_event_args.get('theme')}
    - **Budget:** {plan_event_args.get('budget')}

    ### Services Requested
    {services_markdown}
    """

    if plan_event_args.get('event_additional_details'):
        details += f"\n\n### Additional Notes\n{plan_event_args.get('event_additional_details')}"

    return f"""Tool execution completed! This is the message from the tool back to the customer: 
    Thanks for sharing your event details with us. Here's a quick rundown:

{details}

Does everything look good? Let us know if there's anything you'd like to tweak or add before we get started on your proposal draft! 😊"""


def handle_unknown_fields(email: str, date: str, start_time: str, end_time: str, event_location: str, number_of_adults: int, number_of_kids_and_age_range: str, occasion_type: str, occasion: str, theme: str) -> str:
    missing_info = [
        field for field, value in [
            ("email", email),
            ("date", date),
            ("start_time", start_time),
            ("end_time", end_time),
            ("event_location", event_location),
            ("number_of_adults", number_of_adults),
            ("number_of_kids_and_age_range", number_of_kids_and_age_range),
            ("occasion_type", occasion_type),
            ("occasion", occasion),
            ("theme", theme),
        ] if str(value).lower() == "not given"
    ]

    if missing_info:
        return f"""Tool execution completed! This is the message from the tool back to the customer: 
        🎉 Thanks for sharing your event details with us! We're excited to help plan your special occasion. Check out our awesome packages here: [Happier Entertainment Catalogue](https://happier.sg/entertainment-catalogue/).

        Could you please provide your {', '.join(missing_info)}? This will help us tailor the perfect package for you.

        Let us know if there's anything specific you have in mind or any more details you can share to make your event unforgettable!"""


@tool("gather_event_information")
def gather_event_information(
    conversation_id: int,
    name: str = None,
    contact_number: str = None,
    email: str = None,
    incoming_message: str = None,
    services_list: list = None,
    date: str = None,
    start_time: str = None,
    end_time: str = None,
    event_location: str = None,
    number_of_adults: int = None,
    number_of_kids_and_age_range: str = None,
    occasion_type: str = None,
    occasion: str = None,
    theme: str = None,
    budget: str = None,
    schema_name: str = None,
    event_additional_details: str = None,
    is_confirm_with_collected_information_and_proceed_with_proposal_generation: str = None,
    customer_agreed_to_collect_information: str = None,
):
    """
    Main function to orchestrate gathering all necessary event information from the customer.

    This function collects and validates event-related details provided by the customer, identifies any missing 
    information, recommends suitable services, and generates a proposal if all details are confirmed. It ensures 
    a seamless flow in gathering customer information and handles missing or incomplete inputs by returning prompts.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        name (str): Optional. The customer's name.
        contact_number (str): Optional. The customer's contact number.
        email (str): Optional. The customer's email address.
        incoming_message (str): Optional. The latest message from the customer.
        services_list (list): Optional. A list of services and decoration packages the customer is interested in, 
            e.g., [{"name": "balloon sculpting", "notes": "3 hours, come 30min early"}].
        date (str): Optional. The event date in the format %Y-%m-%d, must be a future date.
        start_time (str): Optional. The start time of the event.
        end_time (str): Optional. The end time of the event.
        event_location (str): Optional. The event location (address or postal code in Singapore).
        number_of_adults (int): Optional. The number of adults attending the event (can be 0).
        number_of_kids_and_age_range (str): Optional. The number and age range of kids attending the event (can be 0 if no kids).
        occasion_type (str): Optional. Specifies if the event is private or corporate.
        occasion (str): Optional. The type of event (e.g., birthday, wedding, corporate party).
        theme (str): Optional. The theme of the event (e.g., colors, characters, or vibe). Use "no preference" if the customer has no specific theme.
        budget (str): Optional. The event budget in the format SGD{comma thousand separated amount}. If a range is provided, the average is taken.
        schema_name (str): Optional. The name of the schema to use.
        event_additional_details (str): Optional. Additional notes or details about the event.
        is_confirm_with_collected_information_and_proceed_with_proposal_generation (str): Optional. Set to `True` 
            if the customer confirms the collected information and agrees to generate a proposal, `False` otherwise.
        customer_agreed_to_collect_information (str): String indicating if customer agreed ('True', 'False', or None)
            - True: Customer has agreed to collect information
            - False: Customer does not agreed to collect information
            - None: Customer has not yet been asked
    Returns:
        dict or str: A dictionary containing the event details or a prompt asking the customer for missing information. 
        If all information is confirmed, generates a proposal or summarizes the event details for customer approval.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Gather missing personal information if `name` or `contact_number` is missing.
        3. Collect basic event details such as `date`, `start_time`, `end_time`, and `event_location`.
        4. Collect additional event details such as attendees, occasion type, theme, and budget.
        5. Recommend services if `services_list` is not provided.
        6. If all information is confirmed, generate a proposal.
        7. If information is incomplete, return a summary of the collected details and request confirmation.
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool gather_event_information is called with conversation_id: {conversation_id}", extra={
                "conversation_id_": conversation_id,
                "name_": name,
                "contact_number_": contact_number,
                "email_": email,
                "services_list_": services_list,
                "date_": date,
                "start_time_": start_time,
                "end_time_": end_time,
                "event_location_": event_location,
                "number_of_adults_": number_of_adults,
                "number_of_kids_and_age_range_": number_of_kids_and_age_range,
                "occasion_type_": occasion_type,
                "occasion_": occasion,
                "theme_": theme,
                "budget_": budget,
                "event_additional_details_": event_additional_details,
                "is_confirm_with_collected_information_and_proceed_with_proposal_generation_": is_confirm_with_collected_information_and_proceed_with_proposal_generation,
                "customer_agreed_to_collect_information_": customer_agreed_to_collect_information,
            }
        )

        # If the customer has not yet agreed to collect information

        # If the customer has not yet asked for permission
        if str(customer_agreed_to_collect_information).lower() in ("none", "null", ""):
            # To prevent circular import
            from app.crew_teams.tools.happier.ask_customer_permission import (
                ask_customer_permission,
            )
             
            return ask_customer_permission.__dict__['func'](conversation_id=conversation_id,
                                                           customer_agreed_to_collect_information=customer_agreed_to_collect_information,
                                                           schema_name=schema_name,
                                                           incoming_message=incoming_message)

        customer_agreed_to_collect_information = True if str(customer_agreed_to_collect_information).lower() == "true" else False

        if str(contact_number).lower() == "none" or not contact_number:
            # Proceed to check the database if it is a whatsapp conversation
            contact_number = get_contact_number_from_whatsapp(conversation_id=conversation_id, schema_name=schema_name)

        # Step 1: Gather missing personal information
        personal_info_status = collect_personal_information(name, contact_number, email, customer_agreed_to_collect_information, schema_name, incoming_message, conversation_id)
        if personal_info_status:
            return personal_info_status

        # Step 2: If the user doesnt want to provide their information, we will share the catalogue with them
        if not customer_agreed_to_collect_information:
            
            response_to_customer = """Here's the context you can use when crafting your reply to the customer:
            Thank you for sharing your contact information with us. We appreciate your trust. At this point, we don't have enough details to create a customized proposal for your event. However, we'd love to share our catalogue with you, which showcases all our services:
            [Happier Entertainment Catalogue](https://happier.sg/entertainment-catalogue/)

            Please feel free to browse through our offerings. Once you have more specific details about your event, we'd be happy to work on a tailored proposal for you. If you have any questions about our services or need clarification on anything, don't hesitate to reach out. We're here to help make your event truly special!"""

            return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message, conversation_id=conversation_id)
        
        # Step 3: Gather missing basic event details
        if customer_agreed_to_collect_information:
            basic_info_status = collect_basic_event_details(
                date, start_time, end_time, event_location, schema_name, incoming_message,
                number_of_adults,
                number_of_kids_and_age_range,
                occasion_type,
                occasion,
                theme,
                conversation_id
            )
            if basic_info_status:
                return basic_info_status

        # # Step 4: Gather additional event details
        # additional_info_status = collect_additional_event_details(
        #     number_of_adults,
        #     number_of_kids_and_age_range,
        #     occasion_type,
        #     occasion,
        #     theme,
        #     schema_name,
        #     incoming_message
        # )
        # if additional_info_status:
        #     return additional_info_status

        is_confirm_with_collected_information_and_proceed_with_proposal_generation = True if str(is_confirm_with_collected_information_and_proceed_with_proposal_generation).lower() == "true" else False

        # Step 5: Generate proposal if confirmed
        # unknown_fields_status = handle_unknown_fields(
        #     email,
        #     date,
        #     start_time,
        #     end_time,
        #     event_location,
        #     number_of_adults,
        #     number_of_kids_and_age_range,
        #     occasion_type,
        #     occasion,
        #     theme,
        # )
        # if unknown_fields_status:
        #     return unknown_fields_status

        # Step 4: Recommend budget if needed
        if not budget or str(budget).lower() in ["none", "not given"]:
            budget_status = recommend_event_budget(
                number_of_adults,
                number_of_kids_and_age_range,
                occasion_type,
                occasion,
                theme,
            )
            return budget_status

        # Step 5: Recommend services if needed
        if not services_list or str(budget).lower() in ['none', 'not given', '[]']:
            services_status = recommend_event_services_and_decorations_addons(
                conversation_id,
                start_time,
                end_time,
                number_of_adults,
                number_of_kids_and_age_range,
                occasion_type,
                occasion,
                theme,
                budget,
                incoming_message,
                schema_name,
                event_additional_details,
            )
            return services_status

        # Step 6: Summarize details for customer confirmation
        return summarize_event_details({
            "name": name,
            "contact_number": contact_number,
            "email": email,
            "date": date,
            "start_time": start_time,
            "end_time": end_time,
            "event_location": event_location,
            "number_of_adults": number_of_adults,
            "number_of_kids_and_age_range": number_of_kids_and_age_range,
            "occasion_type": occasion_type,
            "occasion": occasion,
            "theme": theme,
            "budget": budget,
            "services_list": services_list,
            "event_additional_details": event_additional_details,
        })
    except Exception as e:
        logger.error(f"Tool gather_event_information failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "name_": name,
            "contact_number_": contact_number,
            "schema_name_": schema_name,
            "email_": email,
            "services_list_": services_list,
            "date_": date,
            "start_time_": start_time,
            "end_time_": end_time,
            "event_location_": event_location,
            "number_of_adults_": number_of_adults,
            "number_of_kids_and_age_range_": number_of_kids_and_age_range,
            "occasion_type_": occasion_type,
            "occasion_": occasion,
            "theme_": theme,
            "budget_": budget,
            "event_additional_details_": event_additional_details,
            "is_confirm_with_collected_information_and_proceed_with_proposal_generation_": is_confirm_with_collected_information_and_proceed_with_proposal_generation,
            "customer_agreed_to_collect_information_": customer_agreed_to_collect_information,
        })
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
