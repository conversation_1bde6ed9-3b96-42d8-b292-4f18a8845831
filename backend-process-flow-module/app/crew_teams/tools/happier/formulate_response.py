import datetime
import logging

from openai import OpenAI
from tenant_schemas.utils import schema_context

import app.utility.chat_constant as chat_constant
import app.utility.general_helper as utility_general_helper
from app.chats.bot.models import BotSetting
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.chats.chat.models import Message
from app.redteam.models import RedTeamConversation, RedTeamMessage
from app.chats.chat.serializers import MessageMinimalSerializer

logger = logging.getLogger(__name__)


def _get_bot_setting(schema_name):
    """
    Retrieve the bot tone setting for a given schema.

    Args:
        schema_name (str): The name of the schema.

    Returns:
        str: The bot tone setting.
    """
    with schema_context(schema_name):
        bot_setting_obj = BotSetting.objects.filter(bot_type="external").first()
        bot_tone = bot_setting_obj.tone

        return bot_tone


def get_recent_messages(schema_name, conversation_id, redteam_run, limit=30):
    """
    Fetch the last 30 messages from a conversation.

    Args:
        schema_name (str): Tenant schema name
        conversation_id (str): Conversation ID
        limit (int): Maximum number of messages to retrieve (default: 30)
        redteam_run (bool): Flag to indicate if the chat is a redteam run.

    Returns:
        list: List of Message objects
    """
    with schema_context(schema_name):
        # if redteam_run:
        #     messages = (
        #         RedTeamMessage.objects.filter(conversation_id=conversation_id)
        #         .select_related("sender")
        #         .prefetch_related("state_machines")
        #         .order_by("-created_at")[:limit]
        #     )
        #     messages = sorted(
        #         messages, key=lambda x: x.created_at
        #     )  # sort by created_at because the crew ai updates the messages in the list order
        #     data = RedTeamMessageMinimalSerializer(messages, many=True).data
        # else:
        messages = (
            Message.objects.filter(conversation_id=conversation_id)
            .select_related("sender")
            .prefetch_related("state_machines")
            .order_by("-created_at")[:10]
        )
        messages = sorted(
            messages, key=lambda x: x.created_at
        )  # sort by created_at because the crew ai updates the messages in the list order
        data = MessageMinimalSerializer(messages, many=True).data

        # Convert the messages into a list of messages
        messages = []
        for message in data:
            if message.get("sender"):
                sender = message.get("sender", {}).get("email") or message.get(
                    "message_type"
                )
            else:
                sender = message.get("message_type")
            messages.append(
                {
                    "sender": sender,
                    "message": message["message"],
                    "context": message["state_machines"],
                    "timestamp": message["timestamp"],
                    "attachments": message["attachments"],
                }
            )
        return messages
        

def formulate_response(schema_name: str, response_to_customer: str, incoming_message: str, conversation_id: str) -> str:
    """
    Formulate a response to the customer based on their message and the company's tone.

    This function uses OpenAI's GPT model to generate a response that aligns with
    the company's tone and addresses the customer's specific message.

    Args:
        schema_name (str): The name of the schema (company).
        response_to_customer (str): The template response to be adapted.
        incoming_message (str): The customer's original message.
        conversation_id (str): The ID of the conversation.

    Returns:
        str: The formulated response, or the original template if an error occurs.

    Raises:
        Exception: If there's an error in getting the OpenAI client or generating the response.
    """
    try:
        client = OpenAI()
        bot_tone = _get_bot_setting(schema_name)
        recent_messages = get_recent_messages(schema_name, conversation_id=conversation_id, redteam_run=False, limit=5)
        time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        check_proposal_json_message = utility_general_helper.clean_up_prompt(
            f"""As a concise sales professional at {schema_name}, craft a brief response aligned with our {bot_tone} tone. Address the customer's message directly and use the template as context.

            Recent Conversations with customer:
            {recent_messages}

            Customer's message:
            {incoming_message}

            Context for response:
            {response_to_customer}

            Current time:
            {time_now}

            Your task:
            1. Directly address the customer's specific message.
            2. Use the context to inform your response, but don't repeat it verbatim.
            3. Keep it extremely brief and to the point for WhatsApp.
            4. Maintain our {bot_tone} tone while showing empathy.
            5. Format in markdown if necessary for clarity.
            6. If you have already chat with the customer previously, you should not use "hi there". It should be more of addressing the customer name.
            7. If there is a 1 day gap between the customer last messages and the current time, it will be appropriate welcome them back.
            8. Do not use the words "Hi" or "Hello" in the response if there are recent messages with the customer.
            9. [!IMPORTANT] You are ONLY going to change the tone of the response.
            
            IMPORTANT: Provide ONLY the final, concise response. Ensure the customer feels heard and valued.
            """
        )

        chat_gpt_response = get_gpt_response(client,
                                            [{"role": "user", "content": check_proposal_json_message}], 
                                            model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

        chat_gpt_response = chat_gpt_response.replace("```Markdown", "").replace("```", "")

        return f"""Tool execution completed and you MUST ONLY return the following message at it is. DO NOT add any additional information!
        {chat_gpt_response}"""

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"Failed to get OpenAI client: {e}")
        return response_to_customer