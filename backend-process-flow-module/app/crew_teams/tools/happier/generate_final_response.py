import json
import logging
import os
import time
import traceback
from typing import Dict, Optional

import litellm
from crewai.flow.flow import Flow, listen, or_, router, start
from crewai.tools import tool

from app.crew_teams.team import AgentCrew
from backend.settings import BEEFY_API_KEY, BEEFY_BASE_URL, OPENAI_API_KEY

logger = logging.getLogger(__name__)


class ResponseFlow(Flow):
    def __init__(self, stage_execution_output: str = None, context: str = None, schema_name: str = None):
        super().__init__()
        # Sanitize input strings
        self.state['stage_execution_output'] = self._sanitize_text(stage_execution_output)
        # self.state['unread_messages'] = self._sanitize_text(unread_messages)
        # self.state['recent_messages'] = self._sanitize_text(recent_messages)
        self.state['context'] = self._sanitize_text(context)
        # self.state['following_nodes'] = self._sanitize_text(following_nodes)
        # self.state['bot_settings'] = self._sanitize_text(bot_settings)
        # self.state['bot_name'] = self._sanitize_text(bot_name)
        self.state['schema_name'] = self._sanitize_text(schema_name)
        self.state['formulated_response'] = ""
        self.state['final_context'] = {}
        self.state['decision'] = {}

    def _sanitize_text(self, text: Optional[str]) -> Optional[str]:
        """Sanitize text to handle Unicode encoding issues."""
        if text is None:
            return None
        # Replace problematic Unicode characters
        return text.encode('utf-8', errors='ignore').decode('utf-8')

    def run_crew_team(self, variables: dict, config_path: str, seed_number: Optional[int] = None) -> Dict:
        # Sanitize variables before passing to crew team
        sanitized_variables = {
            key: self._sanitize_text(value) if isinstance(value, str) else value
            for key, value in variables.items()
        }
        crew_team = AgentCrew(config_path=config_path, seed_number=seed_number)
        output, task_output, task_full_dicts, final_task_output_index = crew_team.run(**sanitized_variables)

        return output, task_output, task_full_dicts, final_task_output_index

    def request_call_to_beefy(self, model_port, model_name, prompt: list, max_retries:int = 3, timeout:int = 30):
        # Set the base url for the beefy server
        base_url = f"{BEEFY_BASE_URL}:{model_port}"

        retries = 0
        while retries < max_retries:
            try:
                ## set ENV variables for OpenAI
                os.environ["OPENAI_API_KEY"] = "NULL"

                response = litellm.completion(
                    model=model_name, 
                    messages=prompt,
                    # max_tokens=300,
                    base_url=base_url,
                    temperature=0.1,
                    custom_llm_provider="openai",
                    timeout=timeout,
                    headers={"Authorization": f"Bearer {BEEFY_API_KEY}",
                            "Content-Type": "application/json"})

                os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY
                
                return str(response.choices[0].message.content).strip()
            
            except litellm.exceptions.Timeout as e:
                print(f"Request timed out. Retrying... {retries + 1}/{max_retries}")
                retries += 1

                os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY

                if retries < max_retries:
                    time.sleep(retries)

        # Need to raise an exception here
        raise Exception("Request to beefy server timed out")

    @start()
    def start_method(self):
        crew_variables = {
            "stage_execution_output": self.state['stage_execution_output']
        }
        schema_name = self.state['schema_name']

        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/response_generation_decision_crew.yml"
        response_generation_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        
        # Set the state
        self.state['decision'] = response_generation_output

        logger.warning(f"Decision: {response_generation_output}")
        return response_generation_output

    @router(start_method)
    def decision_routing(self):
        decision = self.state.get("decision", {})

        if str(decision.get("proposal_or_service")).lower() == "true":
            return "retain_formatting"
        else:
            return "change_formatting"

    @listen("retain_formatting")
    def retain_formatting_function(self):
        self.state['formulated_response'] = self.state['stage_execution_output']

        return self.state['formulated_response']

    @listen("change_formatting")
    def change_formatting_function(self):
        # Call the custom model to get the output. Not compatible with crew flow (As of 20 Jan 2025)
        prompt = [
            {
                "role": "system",
                "content": (
                    "You are an expert at rephrasing bot responses while preserving their exact meaning. "
                    "Your job is to rewrite the response in your own words with a warm, empathetic tone, "
                    "ensuring it remains clear, concise (under 20 words), and professional. "
                    "Do not add, remove, or alter any details from the original response."
                )
            },
            {
                "role": "assistant",
                "content": (
                    "Your task is to analyze the original bot response and reword it using your own tone. "
                    "Make sure the new response conveys the same facts and feedback, is easy to understand, "
                    "and shows empathy and warmth. Use emojis only if absolutely necessary for clarity.\n\n"
                    f"Original Bot Response:\n{self.state['stage_execution_output']}\n\n"
                    "Provide only the refined response, with no additional commentary."
                )
            }
        ]

        response = self.request_call_to_beefy(model_port=8009, model_name="huggingface/unsloth/phi-4", prompt=prompt, max_retries=5)
        
        print("="*50)
        print("Output from custom model: ", response)
        print("="*50)
        crew_variables = {
            "updated_bot_response": response,
            "context": self.state['context']
        }
        schema_name = self.state['schema_name']

        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/response_generation_formatter_crew.yml"
        response_generation_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)

        logger.warning(f"Formulated response: {response_generation_output}")

        self.state['formulated_response'] = response_generation_output

        return self.state['formulated_response']

    # @listen(or_(retain_formatting_function, change_formatting_function))
    # def final_context(self, _):
    #     crew_variables = {
    #         "stage_execution_output": self.state['stage_execution_output'],
    #         "unread_messages": self.state['unread_messages'],
    #         "recent_messages": self.state['recent_messages'],
    #         "context": self.state['context']
    #     }
    #     schema_name = self.state['schema_name']

    #     yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/response_generation_context_crew.yml"
    #     response_generation_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)

    #     self.state['final_context'] = response_generation_output

    #     return self.state['final_context']

    @listen(or_(retain_formatting_function, change_formatting_function))
    def final_response(self, _):
        try:
            final_response = {
                "bot_response": self._sanitize_text(self.state['formulated_response']),
            }

            # if isinstance(self.state['final_context'], dict):
            #     sanitized_context = {
            #         key: self._sanitize_text(value) if isinstance(value, str) else value
            #         for key, value in self.state['final_context'].items()
            #     }
            #     final_response.update(sanitized_context)
            # else:
            #     final_response.update({
            #         "context_summary": "",
            #         "decision_rationale": "",
            #         "additional_context": {},
            #         "is_completed": False
            #     })

            return json.dumps(final_response, ensure_ascii=True)
        except Exception as e:
            logger.error(f"Error in final_response: {e}")
            return json.dumps({
                "error": "Failed to process response",
                "bot_response": "",
                "context_summary": "",
                "decision_rationale": "",
                "additional_context": {},
                "is_completed": False
            })


@tool
def generate_final_response(stage_execution_output: str = None, context: str = None, schema_name: str = None) -> str:
    """
    Generate a final response based on various inputs and context.
    
    Args:
        stage_execution_output (str, optional): Output from the previous stage execution.
        context (str, optional): Additional context for the conversation.
        schema_name (str, optional): Name of the schema being used.
        
    Returns:
        str: The final response generated based on the input parameters.
    """
    try:
        flow = ResponseFlow(stage_execution_output, context, schema_name)
        final_output = flow.kickoff()

        logger.warning("Returning final output")
        logger.warning(final_output)

        return f"""Tool execution successful! 
        {final_output}"""

    except Exception as e:
        logger.error(f"Error executing generate_final_response: {e}")
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
