import re
import os
from typing import Dict, Optional
import json
import logging
import time

import pandas as pd
from django.db import connection
from django_tenants.utils import schema_context
from openai import OpenAI

import app.chats.chat.models as chat_models
import app.documents.document.models as document_models
import app.utility.chat_constant as chat_constant
import app.utility.chat_helper as chat_helper
import app.utility.general_helper as utility_general_helper
import app.utility.models as utility_models
from app.crew_teams.tools.happier.fetch_master_data import (
    filter_master_services_by_budget,
)
from app.redteam.models import RedTeamMessage
from backend.settings import AWS_STORAGE_BUCKET_NAME, WEBSOCKET_URL, BOT_API_KEY
import app.core.authapi.helper as authapi_helper
from app.utility.websocket_client import send_message

logger = logging.getLogger(__name__)


def validate_event_details(
    start_time: Optional[str],
    end_time: Optional[str],
    number_of_adults: Optional[int],
    number_of_kids_and_age_range: Optional[str],
    occasion_type: Optional[str],
    occasion: Optional[str],
    theme: Optional[str],
    budget: Optional[str],
) -> Optional[str]:
    """Validates the event details and returns missing fields."""
    missing_args = []
    if not start_time:
        missing_args.append("start_time")
    if not end_time:
        missing_args.append("end_time")
    if not number_of_adults:
        missing_args.append("number_of_adults")
    if not number_of_kids_and_age_range:
        missing_args.append("number_of_kids_and_age_range")
    if not occasion_type:
        missing_args.append("occasion_type")
    if not occasion:
        missing_args.append("occasion")
    if not theme:
        missing_args.append("theme")
    if not budget:
        missing_args.append("budget")
    return f"Missing arguments: {', '.join(missing_args)}" if missing_args else None


def get_conversation_history(conversation_id: int, schema_name: str) -> str:
    """Retrieves and formats the conversation history for AI prompts."""
    with schema_context(schema_name):
        conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
        historical_messages = chat_helper.get_historical_messages(conversation_instance)
        return "\n".join(
            [f"{msg['role']}: {msg['content']}" for msg in historical_messages]
        )


def load_services_data(
    csv_url: str = "https://docs.google.com/spreadsheets/d/1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0/export?format=csv&id=1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0&gid=1504751653"
) -> pd.DataFrame:
    """Loads and processes services data from a CSV file."""
    data = pd.read_csv(csv_url)
    
    # Define pricing model functions
    pricing_model_function = {
        "session": lambda base, uom, working_pace: f"SGD{int(base)} for {uom}-session, (no partial, flat rate). Working Pace: {working_pace}.",
        "set": lambda base, uom, _: f"SGD{int(base)} per set.",
        "Package": lambda base, uom, working_pace: f"SGD{int(base)} per package. Working Pace: {working_pace}.",
    }
    
    # Add a default fallback function
    def default_pricing_model(base, uom, working_pace):
        return f"Unknown pricing model. Base Price: {base}, UOM: {uom}, Working Pace: {working_pace}."
    
    # Map the PricingModelID column to functions
    data["PricingModelFunction"] = data["PricingModelID"].map(
        lambda key: pricing_model_function.get(key, default_pricing_model)
    )
    
    # Apply the function to compute the PricingModel column
    data["PricingModel"] = data.apply(
        lambda x: x["PricingModelFunction"](x["BasePrice"], x["UOM"], x["WorkingPace"]),
        axis=1,
    )
    
    return data


def filter_services_by_occasion(data: pd.DataFrame, occasion_type: str) -> pd.DataFrame:
    """Filters services based on the occasion type (corporate or private)."""
    if str(occasion_type).lower().strip() == "corporate":
        return data[data["Name"].str.contains("corporate", case=False)]
    return data[~data["Name"].str.contains("corporate", case=False)]


def get_decor_priority_areas(csv_url: str, occasion_type: str) -> pd.DataFrame:
    """Retrieves and formats decor priority areas from a CSV file."""
    data = pd.read_csv(csv_url)
    return data[
        data["Client type"].str.lower().str.replace(" ", "")
        == str(occasion_type).lower().replace(" ", "")
    ]


def generate_ai_prompt_for_services(
    historical_messages: str,
    event_details: Dict[str, str],
    services_data: pd.DataFrame,
    incoming_message: str
) -> str:
    """Generates an AI prompt for recommending services."""
    services_suggestion = services_data[
        ["Name", "SubCategory", "Description"]
    ].astype(str).apply("|".join, axis=1)
    services_suggestion_str = (
        "Name | SubCategory | Description\n" + "\n".join(services_suggestion)
    )
    return f"""
    Conversation context with customer:
    {historical_messages}

    Incoming message:
    {incoming_message}
    
    Available services:
    {services_suggestion_str}

    Event details:
    Number of Adults: {event_details['number_of_adults']}
    Number of Kids and Age Range: {event_details['number_of_kids_and_age_range']}
    Occasion Type: {event_details['occasion_type']}
    Occasion: {event_details['occasion']}
    Theme: {event_details['theme']}
    Budget: SGD{event_details['budget']}
    Additional Notes: {event_details.get('event_additional_details', 'None')}

    Task: You are to recommend up to 4 services that align with the event details. The services should:
    - Enhance the event experience and ensure a memorable occasion.
    - Fit within the specified budget while adding significant value.
    - Be tailored to the event's theme and occasion.
    - Do not need to include event information or additional details.
    - Make sure no assumption of the price is provided. DO NOT PROVIDE THE PRICE.
    - As you are the sales of the company, services/ decoration packages that customer expressed interest in should be recommended first.
    - Take user's incoming message as a consideration when recommending services.
    - DO NOT NEED TO INCLUDE ANY CLOSING STATEMENT.

    The goal is to optimize the event planning process and provide the best service options for the customer.

    Return in a nice markdown format.
    """


def generate_ai_prompt_for_decor(
    historical_messages: str,
    event_details: Dict[str, str],
    decor_data: pd.DataFrame,
    decor_priority_data: pd.DataFrame,
    incoming_message: str
) -> str:
    """Generates an AI prompt for recommending decor packages."""
    decor_suggestion = decor_data[
        ["Name", "SubCategory", "Description"]
    ].astype(str).apply("|".join, axis=1)
    decor_suggestion_str = (
        "Name | SubCategory | Description\n" + "\n".join(decor_suggestion)
    )
    decor_priority_str = decor_priority_data[
        [
            "Occasion",
            "1st priority area to decorate",
            "2nd priority area to decorate",
            "3rd priority area to decorate",
        ]
    ].astype(str).apply("|".join, axis=1)
    return f"""
    Below is the conversation I am having with my customer:
    {historical_messages}

    Incoming message:
    {incoming_message}

    These are the list of decor packages and priority areas:
    {decor_suggestion_str}
    {decor_priority_str}

    Event details provided:
    Number of Adults: {event_details['number_of_adults']}
    Number of Kids and Age Range: {event_details['number_of_kids_and_age_range']}
    Occasion Type: {event_details['occasion_type']}
    Occasion: {event_details['occasion']}
    Theme: {event_details['theme']}
    Budget: SGD{event_details['budget']}
    Additional Notes: {event_details.get('event_additional_details', 'None')}

    Based on your event details, we have curated a selection of decor packages that are perfectly suited for your occasion. Here are up to two of our top recommendations:

    1. [Decor Package Name]: This package includes [user friendly and concise description], which is ideal for a {event_details['occasion']} with a {event_details['theme']} theme. It is designed to enhance the ambiance and provide a memorable experience for your guests.

    2. [Decor Package Name]: With this package, you get [user friendly and concise description] that aligns with the {event_details['occasion_type']} nature of your event, ensuring a captivating setting.

    Task: Recommend up to 2 decoration packages that align with the event details. The decorations should:
    - Enhance the event experience and ensure a memorable occasion.
    - Fit within the specified budget while adding significant value.
    - Be tailored to the event's theme and occasion.
    - Do not need to include event information or additional details.
    - Make sure no assumption of the price is provided. DO NOT PROVIDE THE PRICE.
    - As you are the sales of the company, services/ decoration packages that customer expressed interest in should be recommended first.
    - Take user's incoming message as a consideration when recommending services.
    - DO NOT NEED TO INCLUDE ANY CLOSING STATEMENT.

    Return in a nice markdown format.
    """


def remove_pricing_from_suggestion(suggestion: str) -> str:
    """Removes pricing information from the suggestion."""
    return f"""
    Below is the suggestion that we would like to present to the customer:
    {suggestion}

    However, we would like to REMOVE ALL pricing information from the suggestion.
    Do not change the content, reasonings and suggestions information. ONLY remove pricing information from the suggestion.
    """


def insert_relevant_images(suggestion: str, schema_name: str) -> str:
    """Removes pricing information from the suggestion."""

    with schema_context(schema_name):
        image_instances = document_models.KnowledgeImage.objects.all()
        image_tagging_list = [
            {"name": image_instance.name, "url": f"https://{AWS_STORAGE_BUCKET_NAME}.s3.ap-southeast-1.amazonaws.com{image_instance.file.url}"}
            for image_instance in image_instances
        ]

    return f"""
    Below is the suggestion we would like to present to the customer:
    {suggestion}

    To make the suggestion more appealing, we will insert relevant images at the bottom of the message. These images will complement the services and decorations suggested, ensuring the customer can easily read through the recommendations without interruptions.

    Formatting Guidelines:
    1. Preserve the content, reasonings, and suggestions as they are.
    2. Append images at the end using the MARKDOWN FORMAT ONLY. (E.g. ![image](<Image url>)) DO NOT USE CHANGE "![image]" into something else.
    3. If you are unable to find an appropriate image, do not include in your response. Rather keep the message as it is then to add (path to the image) to it.
    4. FOR ALL IMAGES PATH, DO NOT USE THE URL. ONLY USE THE INFORMATION FROM IMAGES DICTIONARY LIST.

    Images Dictionary List:
    {image_tagging_list}

    Recommended Formatting:
    <Suggested services and reasonings 1>
    <Suggested services image 1>

    <Suggested services and reasonings 2>
    <Suggested services image 2>


    DO NOT include any feedback, opinion or suggestion. JUST RETURN THE SERVICES AND IMAGE TAGS.
    """


def create_combined_service_decor_prompt(services_message: str, decor_message: str) -> str:
    """
    Crafts a single prompt combining service and decoration recommendations.
    Ensures a natural flow from services to decorations and includes guidelines for formatting.

    Args:
        services_message (str): The message detailing the recommended services.
        decor_message (str): The message detailing the recommended decorations.

    Returns:
        str: A single markdown-formatted prompt.
    """
    return f"""
    Below is the final message for services and decorations to present to the customer, aimed at closing the sale:

    **Services Recommendation:**
    {services_message}

    **Decoration Recommendation:**
    {decor_message}

    **Guidelines for Final Message:**
    1. Ensure a smooth transition from services to decorations.
    2. Use markdown headings for clarity, such as `**Services Recommendation:**` and `**Decoration Recommendation:**`.
    3. Maintain a persuasive and customer-focused tone.
    4. Keep the narrative engaging and free of interruptions.
    5. If images are included, place them at the end, formatted as `![Image](URL)`.
    6. Avoid personal opinions or feedback. Address the message directly to the customer.
    7. Exclude all pricing information.
    8. Keep the content concise and user friendly.

    Message Structure:
    - Introduction to services and how they add value.
    - Detailed services recommendation.
    - Transition into decoration options.
    - Detailed decoration recommendation.
    - DO NOT NEED TO INCLUDE ANY CLOSING STATEMENT.

    The service recommendations and decoration recommendations MUST BE split into 2 elements in the array.
    The output should be an array format below. 
    [<service recommendation>, <decoration recommendation>]

    Ensure the service and decoration messages are in its own markdown format.
    """


def split_service_decor_message(message: str) -> list[str]:
    """
    Splits the combined service and decoration message into separate messages.
    
    Args:
        message (str): The combined service and decoration message.
        
    Returns:
        list[str]: A list containing the service and decoration messages.
    """
    # Split the message into service and decoration sections
    service_section = message.split("**Decoration Recommendation:**")[0].strip()
    decor_section = message.split("**Decoration Recommendation:**")[1].strip()
    
    # Remove markdown formatting from service section
    service_section = service_section.replace("**", "").strip()
    
    # Remove markdown formatting from decor section
    decor_section = decor_section.replace("**", "").strip()
    
    return [service_section, decor_section]


def save_split_service_decor_messages_to_db(message: str):
    # Define the regex pattern for the image tag
    image_pattern = r'\!\[.*?\]\(.*?\)'

    # Find all image tag matches along with their positions
    matches = list(re.finditer(image_pattern, message))

    # Create chunks by splitting after each image tag
    chunks = []
    start_index = 0

    for match in matches:
        # end position of the current image tag
        end_index = match.end()
        chunk = message[start_index:end_index].strip()
        chunks.append(chunk)
        start_index = end_index

    # Optionally add any trailing text after the last image tag if needed
    if start_index < len(message):
        chunks.append(message[start_index:].strip())

    return chunks


def save_recommendations_message_to_db(recommendation_message: list | str, conversation_id, schema_name, is_redteam):
    with schema_context(schema_name):
        # Make sure there are no other pending messages
        if not chat_models.Message.objects.filter(conversation_id=conversation_id, bot_reply_status="pending").exists():
        
            if is_redteam:
                RedTeamMessage.objects.create(
                    message_type="bot_text",
                    message=recommendation_message,
                    conversation_id=conversation_id,
                    bot_reply_status="completed",
                )

            else:
                # Send the message back to websocket
                # Construct websocket URL with schema name
                url = f"{WEBSOCKET_URL}/org/{schema_name.replace('_', '-')}/v1/chat/"
                print("url: ", url)

                # Construct the encrypted token
                data = {
                    "uuid": "",
                    "chat_id": conversation_id,
                    "bot_api_key": BOT_API_KEY,
                }
                # Convert dictionary to JSON string before encryption
                json_data = json.dumps(data)
                encrypted_token = authapi_helper.encrypt_data(json_data)

                if not isinstance(recommendation_message, list):
                    recommendation_message = [recommendation_message]

                # Build bot response message
                for recommendation in recommendation_message:
                    message = {
                        "header": {"type": "mid_chat_processing_bot_response"},
                        "body": {
                            "encrypted_token": encrypted_token,
                            "source": "Web Application",
                            "message_type": "machine",
                            "bot_reply": {
                                "bot_message": [recommendation],
                            },  
                            "conversation_id": conversation_id,
                        },
                    }

                    logger.info(f"Mid Chat Processing Bot response message: {message}")

                    # Send message via websocket asynchronously
                    success = send_message(url, message)
                    logger.info(f"Message sent to websocket successfully: {success}")

                    time.sleep(0.5)

                logger.info("Recommendation messages sent to websocket successfully.")
                

def recommend_event_services_and_decorations_addons(
    conversation_id: int,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    number_of_adults: Optional[int] = None,
    number_of_kids_and_age_range: Optional[str] = None,
    occasion_type: Optional[str] = None,
    occasion: Optional[str] = None,
    theme: Optional[str] = None,
    budget: Optional[str] = None,
    incoming_message: Optional[str] = None,
    schema_name: Optional[str] = None,
    event_additional_details: Optional[str] = None,
) -> Dict[str, str]:
    """
    Recommends suitable services and decoration packages for an event.

    This function analyzes customer-provided event details and conversation history 
    to recommend tailored services and decoration packages that align with the 
    customer's occasion type, theme, and budget.

    Args:
        conversation_id (int): Unique identifier for the ongoing conversation.
        start_time (str, optional): Start time of the event.
        end_time (str, optional): End time of the event.
        number_of_adults (int, optional): Number of adults attending the event.
        number_of_kids_and_age_range (str, optional): Number and age range of kids attending.
        occasion_type (str, optional): Indicates if the event is private or corporate.
        occasion (str, optional): Type of occasion (e.g., birthday, wedding).
        theme (str, optional): Theme of the event.
        budget (str, optional): Budget for the event in SGD.
        incoming_message (str, optional): The incoming message from the user.
        schema_name (str, optional): Name of the schema to use.
        event_additional_details (str, optional): Additional details about the event.

    Returns:
        dict: A dictionary containing:
            - `services`: Recommended services with details and reasons.
            - `decor`: Recommended decor packages with details and reasons.
            - `combined_suggestions`: Optimal combination of services and decor within the budget.

    Workflow:
        1. Validate event details and return missing fields if any.
        2. Retrieve conversation history for context.
        3. Load and filter services and decor data based on occasion type.
        4. Use AI to generate tailored recommendations for services and decor.
        5. Combine the suggestions into a budget-conscious recommendation.
    """
    print("Recommend Event Services and Decorations Addons")
    # Step 1: Validate Inputs
    validation_error = validate_event_details(
        start_time,
        end_time,
        number_of_adults,
        number_of_kids_and_age_range,
        occasion_type,
        occasion,
        theme,
        budget,
    )
    if validation_error:
        return {"status": "error", "message": validation_error}

    # Step 2: Retrieve Conversation History
    historical_messages = get_conversation_history(conversation_id, schema_name)

    # Step 3: Load Data
    services_data = load_services_data("https://docs.google.com/spreadsheets/d/1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0/export?format=csv&id=1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0&gid=1504751653")
    decor_priority_data = get_decor_priority_areas("https://docs.google.com/spreadsheets/u/2/d/1On1F8gTdxIEiT1iUAAF5fcGSipEYdAZEbM4Oxnvot3U/export?format=csv&id=1On1F8gTdxIEiT1iUAAF5fcGSipEYdAZEbM4Oxnvot3U&gid=1921587072", occasion_type)

    # Step 4: Filter Data
    filtered_services = filter_services_by_occasion(services_data, occasion_type)
    filtered_decor = filtered_services[filtered_services["Category"].str.contains("decor")]

    # Step 5: Filter services by budget
    from app.crew_teams.tools.happier.draft_proposals import (
        _parse_budget,  # To prevent circular import
    )
    budget = _parse_budget(budget)
    filtered_services = filter_master_services_by_budget(budget, filtered_services)

    # Step 5: Generate AI Prompts
    event_details = {
        "start_time": start_time,
        "end_time": end_time,
        "number_of_adults": number_of_adults,
        "number_of_kids_and_age_range": number_of_kids_and_age_range,
        "occasion_type": occasion_type,
        "occasion": occasion,
        "theme": theme,
        "budget": budget,
        "event_additional_details": event_additional_details,
    }
    services_prompt = generate_ai_prompt_for_services(historical_messages, event_details, filtered_services, incoming_message)
    decor_prompt = generate_ai_prompt_for_decor(historical_messages, event_details, filtered_decor, decor_priority_data, incoming_message)

    # Step 6: AI Recommendation Calls
    client = OpenAI()
    services_response = client.chat.completions.create(
        model="gpt-4o-mini", messages=[{"role": "user", "content": services_prompt}], temperature=0
    )
    decor_response = client.chat.completions.create(
        model="gpt-4o-mini", messages=[{"role": "user", "content": decor_prompt}], temperature=0
    )

    # Step 7: Remove pricing from the suggestion list
    services_prompt = remove_pricing_from_suggestion(services_response.choices[0].message.content)
    services_response = client.chat.completions.create(
        model="gpt-4o-mini", messages=[{"role": "user", "content": services_prompt}], temperature=0
    )
    decor_prompt = remove_pricing_from_suggestion(decor_response.choices[0].message.content)
    decor_response = client.chat.completions.create(
        model="gpt-4o-mini", messages=[{"role": "user", "content": decor_prompt}], temperature=0
    )

    print("Step 7: ==================================")
    print(services_response.choices[0].message.content)
    print(decor_response.choices[0].message.content)

    # Step 8: Insert relevant images
    services_prompt = insert_relevant_images(services_response.choices[0].message.content, schema_name)
    services_response = client.chat.completions.create(
        model="gpt-4o-mini", messages=[{"role": "user", "content": services_prompt}], temperature=0
    )
    decor_prompt = insert_relevant_images(decor_response.choices[0].message.content, schema_name)
    decor_response = client.chat.completions.create(
        model="gpt-4o-mini", messages=[{"role": "user", "content": decor_prompt}], temperature=0
    )

    print("Step 8: ==================================")
    print(services_response.choices[0].message.content)
    print(decor_response.choices[0].message.content)

    # Remove specific markdown image phrases
    services_response_content = services_response.choices[0].message.content
    decor_response_content = decor_response.choices[0].message.content

    # Replace image tags with the desired format
    services_response_content = re.sub(r'!\[.*?\]\((https.*?)\)', r'![Image](\1)', services_response_content)
    decor_response_content = re.sub(r'!\[.*?\]\((https.*?)\)', r'![Image](\1)', decor_response_content)

    services_response_content = re.sub(r"!\[.*?\]\(Image url\)", "", services_response_content)
    decor_response_content = re.sub(r"!\[.*?\]\(Image url\)", "", decor_response_content)

    services_response_content = services_response_content.replace("```markdown", "")
    services_response_content = services_response_content.replace("```json", "")
    services_response_content = services_response_content.replace("```", "")

    decor_response_content = decor_response_content.replace("```markdown", "")
    decor_response_content = decor_response_content.replace("```json", "")
    decor_response_content = decor_response_content.replace("```", "")

    # # Step 9: Combine and Return Recommendations
    # final_prompt = create_combined_service_decor_prompt(services_response_content, decor_response_content)

    # retry = 3
    # attempt = 0
    # while attempt < retry:
    #     try:
    #         combined_response   = client.chat.completions.create(
    #             model="gpt-4o-mini", messages=[{"role": "user", "content": final_prompt}], temperature=0
    #         )
    #         final_response = combined_response.choices[0].message.content
    #         final_response.replace("```markdown", "")
    #         final_response.replace("```json", "")
    #         final_response.replace("```", "")

    #         # Convert into json
    #         final_response = json.loads(final_response)

    #         break

    #     except Exception as e:
    #         print(e)
    #         attempt += 1

    # Chunk the response
    final_response = []
    services_response_content = save_split_service_decor_messages_to_db(services_response_content)
    decor_response_content = save_split_service_decor_messages_to_db(decor_response_content)

    final_response.extend(services_response_content)
    final_response.extend(decor_response_content)

    # Update the db with the recommendations message
    is_redteam = str(os.environ.get("REDTEAM_RUN", "False")).lower() == "true"

    save_recommendations_message_to_db(recommendation_message=final_response, conversation_id=conversation_id, schema_name=schema_name, is_redteam=is_redteam)

    print("Step 9: ==================================")
    print(final_response)

    return utility_general_helper.clean_up_prompt(
    f"""Tool execution completed! 
    From our curated list of services and decorations, did any options catch your eye? Let us know which ones you’re interested in, and we’ll create a personalized proposal that perfectly matches your event’s style and needs.
    
    Or you can take a look at our https://happier.sg/entertainment-catalogue/ to explore more options.""")
