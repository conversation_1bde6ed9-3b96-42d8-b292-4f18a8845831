import json
import logging
import os
import time
import traceback
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Tuple
from tenant_schemas.utils import schema_context
from datetime import datetime

from crewai.flow.flow import Flow, listen, or_, router, start
from crewai.tools import tool

from app.crew_teams.team import Agent<PERSON><PERSON>
from app.chats.chat.serializers import FlowMappingSerializer, MessageMinimalSerializer
from app.chats.chat.models import Conversation, FlowMapping, Message, StateMachine
from app.redteam.models import RedTeamFlowMapping, RedTeamMessage
from app.redteam.serializers import (
    RedTeamFlowMappingSerializer,
    RedTeamMessageMinimalSerializer,
)
from app.utility.chat_engine_helper import (
    get_recent_messages_only,
    get_recent_messages_with_time_decay,
    get_active_flow_mapping,
    get_latest_context
)

logger = logging.getLogger(__name__)


class ContextIdentifierFlow(Flow):
    def __init__(self, conversation_id: int, schema_name: str, incoming_message: str, conversation_third_party_id: str, conversation_name: str):
        super().__init__()
        self.state['conversation_id'] = conversation_id
        self.state['schema_name'] = self._sanitize_text(schema_name)
        self.state['incoming_message'] = self._sanitize_text(incoming_message)
        self.state['conversation_third_party_id'] = self._sanitize_text(conversation_third_party_id)
        self.state['conversation_name'] = self._sanitize_text(conversation_name)
        self.state['topics'] = {}
        self.state['topic_relevance'] = {}
        self.state['final_context'] = ""
        self.state['message_batch_size'] = 30
        self.state['current_offset'] = 0

        self.state['recent_messages'] = self.retrieve_messages(
            conversation_id=conversation_id,
            limit=self.state['message_batch_size'],
            offset=self.state['current_offset'],
            schema_name=schema_name
        )
        self.state['recent_messages_only'] = get_recent_messages_only(self.state['recent_messages'])
        self.state['recent_messages_only_with_time_decay'], self.state['recent_messages_with_time_decay'] = get_recent_messages_with_time_decay(self.state['recent_messages'])
        self.state['active_flow'] = self.retrieve_active_flow(schema_name)
        self.state['active_flow_breakdown'] = self.breakdown_active_flow(self.state['active_flow'])

        self.state['latest_context'] = get_latest_context(self.state['recent_messages'])

    def _sanitize_text(self, text: Optional[str]) -> Optional[str]:
        """Sanitize text to handle Unicode encoding issues."""
        if text is None:
            return None
        # Replace problematic Unicode characters
        return text.encode('utf-8', errors='ignore').decode('utf-8')

    def run_crew_team(self, variables: dict, config_path: str, seed_number: Optional[int] = None) -> Tuple[Dict, List, List, int]:
        # Sanitize variables before passing to crew team
        sanitized_variables = {
            key: self._sanitize_text(value) if isinstance(value, str) else value
            for key, value in variables.items()
        }
        crew_team = AgentCrew(config_path=config_path, seed_number=seed_number)
        output, task_output, task_full_dicts, final_task_output_index = crew_team.run(**sanitized_variables)

        return output, task_output, task_full_dicts, final_task_output_index

    def _retrieve_messages_thread(self, conversation_id: int, schema_name: str, limit: int = 30, offset: int = 0) -> List:
        """
        Internal method to fetch messages from a conversation within a thread.
        This method is meant to be run in a separate thread as ORM queries cannot be run with async.
        """
        with schema_context(schema_name):
            try:
                redteam_run = False
                if redteam_run:
                    messages = (
                        RedTeamMessage.objects.filter(conversation_id=conversation_id)
                        .select_related("sender")
                        .prefetch_related("state_machines")
                        .order_by("-created_at")[offset:offset + limit]
                    )
                    messages = sorted(
                        messages, key=lambda x: x.created_at
                    )  # sort by created_at because the crew ai updates the messages in the list order
                    data = RedTeamMessageMinimalSerializer(messages, many=True).data
                else:
                    messages = (
                        Message.objects.filter(
                            conversation_id=conversation_id, bot_reply_status="completed"
                        )
                        .select_related("sender")
                        .prefetch_related("state_machines")
                        .order_by("-created_at")[offset:offset + limit]
                    )
                    messages = sorted(
                        messages, key=lambda x: x.created_at
                    )  # sort by created_at because the crew ai updates the messages in the list order
                    data = MessageMinimalSerializer(messages, many=True).data
                
                # Convert the messages into a list of messages
                result = []
                for message in data:
                    if message.get("sender"):
                        sender = message.get("sender", {}).get("email") or message.get(
                            "message_type"
                        )
                    else:
                        sender = message.get("message_type")
                    result.append(
                        {
                            "sender": sender,
                            "message": message["message"],
                            "context": message["state_machines"],
                            "timestamp": message["timestamp"],
                            "attachments": message["attachments"],
                        }
                    )
                return result
            except Exception as e:
                logger.error(f"Error retrieving messages in thread: {str(e)}")
                logger.error(traceback.format_exc())
                return []
                
    def retrieve_messages(self, conversation_id: int, schema_name: str, limit: int = 30, offset: int = 0) -> List:
        """
        Fetch the messages from a conversation using a thread since ORM queries cannot be run with async.

        Args:
            schema_name (str): Tenant schema name
            conversation_id (str): Conversation ID
            limit (int): Maximum number of messages to retrieve (default: 30)
            offset (int): Number of messages to skip (default: 0)

        Returns:
            list: List of Message objects
        """
        try:
            # Use ThreadPoolExecutor to run the database query in a separate thread
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(
                    self._retrieve_messages_thread, 
                    conversation_id, 
                    schema_name, 
                    limit, 
                    offset
                )
                return future.result()
        except Exception as e:
            logger.error(f"Error in thread execution for retrieving messages: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def retrieve_active_flow(self, schema_name: str) -> Dict:
        """
        Fetch the active flow from a conversation using a thread since ORM queries cannot be run with async.

        Args:
            schema_name (str): Tenant schema name

        Returns:
            dict: Active flow configuration
        """
        try:
            is_redteam_run = str(os.environ.get('REDTEAM_RUN', "")).lower() == "true"

            # Use ThreadPoolExecutor to run the database query in a separate thread
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(
                    get_active_flow_mapping, 
                    schema_name,
                    is_redteam_run
                )
                return future.result()
        except Exception as e:
            logger.error(f"Error in thread execution for retrieving active flow: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def breakdown_active_flow(self, active_flow: Dict) -> Dict:
        """
        Breaks down the active flow into separate business processes based on flow IDs.
        
        Args:
            active_flow (Dict): The complete active flow containing all business processes
            
        Returns:
            Dict: A dictionary mapping business process names to their respective nodes and connections
        """
        # Initialize the result dictionary
        result = {
            "manager_escalation": {"nodes": [], "connections": []},
            "event_proposal_generation": {"nodes": [], "connections": []},
            "proposal_revision": {"nodes": [], "connections": []},
            "business_enquiry": {"nodes": [], "connections": []},
            "proposal_confirmation": {"nodes": [], "connections": []}
        }
        
        # Extract all nodes from the active flow
        all_nodes = active_flow.get("nodes", [])
        all_connections = active_flow.get("connections", [])
        
        # Group nodes by flow ID prefix (flow1_, flow2_, etc.)
        flow_id_mapping = {
            "flow1-": "event_proposal_generation",
            "flow2-": "proposal_revision",
            "flow3-": "business_enquiry",
            "flow4-": "proposal_confirmation",
            "flow5-": "manager_escalation"
        }
        
        # Map nodes to their respective business processes
        for node in all_nodes:
            node_id = node.get("id", "")
            for prefix, process_name in flow_id_mapping.items():
                if node_id.startswith(prefix):
                    result[process_name]["nodes"].append(node)
                    break
        
        # Map connections to their respective business processes
        for connection in all_connections:
            from_id = connection.get("from", "")
            for prefix, process_name in flow_id_mapping.items():
                if from_id.startswith(prefix):
                    result[process_name]["connections"].append(connection)
                    break
        
        return result

    @start()
    def determine_user_intent(self):
        crew_variables = {
            "incoming_message": self.state['incoming_message'],
            "recent_messages_only_with_time_decay": self.state['recent_messages_only_with_time_decay'],
        }

        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/intent_analyzer_crew.yml"

        intents_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        self.state['user_intents'] = intents_output.get('inferred_intents', [])

    @listen(determine_user_intent)
    def process_each_user_intent(self):
        user_intents = self.state['user_intents']
        active_flow_breakdown = self.state['active_flow_breakdown']
        
        for intent in user_intents:
            # Set the offset to be 0
            self.state['current_offset'] = 0

            if str(intent['course_of_action']).lower() == "manager escalation":
                escalation_flow = active_flow_breakdown['manager_escalation']

                # Get the latest context for the escalation
                context_output = self.extract_relevant_context(escalation_flow)
                
                # Extend the dictionary with the new context
                intent.update(context_output)

            elif str(intent['course_of_action']).lower() == "event proposal generation":
                event_proposal_generation_flow = active_flow_breakdown['event_proposal_generation']

                # Get the latest context for the event proposal
                context_output = self.extract_relevant_context(event_proposal_generation_flow)

                # Check if the user wants to restart the event proposal
                restart_output = self.check_restart_proposal()
                print("restart_output", restart_output)
                if str(restart_output.get("restart_proposal", False)).lower() == "true":
                    
                    # Reset the context
                    current_context = context_output.get("context", {})
                    new_context = self.reset_context_output(current_context)
                    
                    # Update the context in the intent
                    intent.update({"context": new_context})

                else:
                    topics = self.determine_topics()
                    relevance_output = self.analyze_topic_relevance(topics)
                    new_context = self.relevance_routing(relevance_output)
                    
                    # Extend the dictionary with the new context
                    intent.update(new_context)

            elif str(intent['course_of_action']).lower() == "proposal revision":
                proposal_revision_flow = active_flow_breakdown['proposal_revision']

                context_output = self.extract_relevant_context(proposal_revision_flow)
                
                # Extend the dictionary with the new context
                intent.update(context_output)

            elif str(intent['course_of_action']).lower() == "business enquiry":
                business_enquiry_flow = active_flow_breakdown['business_enquiry']

                context_output = self.extract_relevant_context(business_enquiry_flow)
                
                # Extend the dictionary with the new context
                intent.update(context_output)

            elif str(intent['course_of_action']).lower() == "proposal confirmation":
                proposal_confirmation_flow = active_flow_breakdown['proposal_confirmation']

                context_output = self.extract_relevant_context(proposal_confirmation_flow)
                
                # Extend the dictionary with the new context
                intent.update(context_output)

            else:
                unknown_intent_flow = active_flow_breakdown['unknown intent']
                context_output = self.extract_relevant_context(unknown_intent_flow)
                
                # Extend the dictionary with the new context
                intent.update(context_output)

    def extract_relevant_context(self, process_flow):
        # Get the latest context for the escalation
        crew_variables = {
            "recent_messages": self.state['recent_messages_with_time_decay'],
            "unread_messages": self.state['incoming_message'],
            "conversation_third_party_id": self.state['conversation_third_party_id'],
            "conversation_name": self.state['conversation_name'],
            "active_flow_mapping": process_flow
        }

        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/intent_analyze_context.yml"

        context_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        
        return context_output

    def check_restart_proposal(self):
        crew_variables = {
            "incoming_message": self.state['incoming_message']
        }

        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/restart_context_check_crew.yml"

        restart_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        return restart_output

    def reset_context_output(self, context):
        crew_variables = {
            "incoming_message": self.state['incoming_message'],
            "context": context
        }

        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/reset_context_crew.yml"

        restart_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        return restart_output

    def determine_topics(self):
        # Step 1: Identify and summarize topics
        crew_variables = {
            "conversation_history": self.state['recent_messages_with_time_decay']
        }
        
        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/topic_identification_crew.yml"
        
        topic_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        
        logger.info(f"Identified topics: {topic_output}")
        return topic_output

    def analyze_topic_relevance(self, topics):
        # Step 3: Determine if the incoming message relates to existing topics
        crew_variables = {
            "topics": topics,
            "incoming_message": self.state['incoming_message'],
            "today_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/topic_relevance_crew.yml"
        
        relevance_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        
        logger.info(f"Topic relevance analysis: {relevance_output}")
        return relevance_output

    def relevance_routing(self, topic_relevance):
        topic_type = str(topic_relevance.get("topic_type", "")).lower()
        
        if topic_type == "continuous":
            # Continuing the latest topic
            return self.formulate_context(topic_relevance)
        elif topic_type == "new":
            # New topic
            return self.formulate_context(topic_relevance)
        elif topic_type == "old":
            # Returning to an old topic
            return self.formulate_context(topic_relevance)
        else:
            # No relevant topic found, need to search further back
            return self.fetch_more_history()

    def fetch_more_history(self):
        # Step 4: If no relevant topics found, fetch more messages from earlier
        self.state['current_offset'] += self.state['message_batch_size']
        
        additional_messages = self.retrieve_messages(
            conversation_id=self.state['conversation_id'],
            limit=self.state['message_batch_size'],
            offset=self.state['current_offset'],
            schema_name=self.state['schema_name']
        )
        
        # If no more messages, stop searching
        if not additional_messages:
            logger.info("No more messages found in conversation history")
            return self.formulate_context()
        
        # Analyze the additional messages for topics
        crew_variables = {
            "conversation_history": additional_messages
        }
        
        schema_name = self.state['schema_name']
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/topic_identification_crew.yml"
        
        topic_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        
        # Analyze relevance of these topics to the incoming message
        crew_variables = {
            "topics": topic_output,
            "incoming_message": self.state['incoming_message']
        }
        
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/topic_relevance_crew.yml"
        relevance_output, _, _, _ = self.run_crew_team(crew_variables, yaml_path)
        
        # If we found relevant topics, proceed to formulate context
        # Otherwise, continue searching if there are more messages
        if relevance_output.get("topic_type", "").lower() != "fetch_more_messages":
            return self.formulate_context(relevance_output)
        else:
            # Recursive call to fetch more messages if needed
            return self.fetch_more_history()

    def formulate_context(self, topic_relevance):
        # Step 5: Retrieve the final context
        context_output = topic_relevance.get("context", {})
        
        logger.info(f"Formulated context: {context_output}")
        return context_output

    @listen(process_each_user_intent)
    def get_next_node(self):
        user_intents = self.state['user_intents']
        active_flow = self.state['active_flow']

        for intent in user_intents:
            current_node_id = intent.get("node_id", None)

            # From the active flow get the next node using connections
            if current_node_id and active_flow:
                connections = active_flow.get("connections", [])
                to_node_ids = [conn["to"] for conn in connections if conn["from"] == current_node_id]
                nodes = {node["id"]: node for node in active_flow.get("nodes", [])}
                next_nodes = [nodes[node_id] for node_id in to_node_ids if node_id in nodes]
                
                if next_nodes:
                    intent["next_node_id"] = next_nodes[0]["id"]
            
    @listen(get_next_node)
    def return_user_intent(self):
        return self.state['user_intents']

@tool
def identify_context(conversation_id: int, schema_name: str, incoming_message: str, conversation_third_party_id: str = None, conversation_name: str = None) -> Dict:
    """
    Identify and validate the context for a conversation based on message history and topics.
    
    Args:
        conversation_id (int): ID of the conversation to analyze
        schema_name (str): Name of the schema being used
        incoming_message (str): The latest message from the user
        context (str, optional): Any existing context information
        conversation_third_party_id (str, optional): ID of the third-party conversation
        conversation_name (str, optional): Name of the conversation
        
    Returns:
        Dict: The formulated context with topic information and relevance
    """
    try:
        flow = ContextIdentifierFlow(
            conversation_id=conversation_id,
            schema_name=schema_name,
            incoming_message=incoming_message,
            conversation_third_party_id=conversation_third_party_id,
            conversation_name=conversation_name
        )
        result = flow.kickoff()
        logger.info("Returning final output")
        logger.info(result)

        return f"""Tool execution successful! 
        {result}"""

    except Exception as e:
        logger.error(f"Error in context identification: {str(e)}\n{traceback.format_exc()}")
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
