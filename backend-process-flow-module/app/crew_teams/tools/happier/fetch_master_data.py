import json
import math
from datetime import datetime, timedelta
from typing import Callable, Dict

import pandas as pd
from pydantic import BaseModel

import app.utility.chat_constant as chat_constant
import app.utility.general_helper as utility_general_helper
import app.utility.location_helper as utility_location_helper
from app.crew_teams.utils.gpt_integration import get_gpt_response


def _get_pricing_model_functions() -> Dict[str, Callable]:
    """
    Returns a dictionary mapping pricing model IDs to their calculation functions.
    
    Returns:
        Dict[str, Callable]: Dictionary of pricing model functions
    """
    return {
        "session": lambda base, uom, working_pace: f"SGD{int(base)} for {uom}-session, (no partial, flat rate). Working Pace: {working_pace}. Example: 1 {uom}-session, subtotal_price will be SGD{int(base)}; 2 {uom}-session, subtotal_price will be SGD{int(base)} + SGD{int(base)} = SGD{int(base)*2}",
        "set": lambda base, uom, working_pace: f"SGD{int(base)} per set (no partial, flat rate). Example: 1 set, subtotal_price will be SGD{int(base)}",
        "Package": lambda base, uom, working_pace: f"SGD{int(base)} per package (no partial, flat rate). Working Pace: {working_pace}. Example: 1 package, subtotal_price will be SGD{int(base)}",
        "first hour x1.2": lambda base, uom, working_pace: f"First hour block charged at SGD{int(base * 1.2)}, subsequently same service at SGD{base}-per-hourly-block (no partial). Working pace: {working_pace}. Example: 2.5 hours count, then the subtotal_price will be {int(base * 1.2)} + {int(base)} + {int(base)} = {int(base * 1.2) + int(base)*2} as no partial-hour allowed.",
        "first hour x2": lambda base, uom, working_pace: f"First hour block charged at SGD{int(base * 2)}, subsequently same service at SGD{base}-per-hourly-block (no partial). Working Pace: {working_pace}. Example: 2.5 hours count, then the subtotal_price will be {int(base * 2)} + {int(base)} + {int(base)} = {int(base * 2) + int(base)*2} as no partial-hour allowed.",
        "first 2 hours x1.1": lambda base, uom, working_pace: f"First 2 hours block charged at SGD{int(base * 1.1)} per hour, subsequently same service at SGDG${base}-per-hourly-block (no partial). Working Pace: {working_pace}. Example: 3 hours count, then the subtotal_price will be {int(base * 1.1)} + {int(base*1.1)} + {int(base)} = {int(base * 1.1)*2 + int(base)} as no partial-hour allowed.",
        "first 2 hours x1.38": lambda base, uom, working_pace: f"First 2 hours block charged at SGD{int(base * 1.38)} per hour, subsequently same service at SGDG${base}-per-hourly-block (no partial). Working Pace: {working_pace}. Example: 3 hours count, then the subtotal_price will be {int(base * 1.38)} + {int(base*1.38)} + {int(base)} = {int(base * 1.38)*2 + int(base)} as no partial-hour allowed.",
        "first 4 hours x1.8": lambda base, uom, working_pace: f"First 4 hours block charged at SGD{int(base * 1.8)} per hour, subsequently same service at SGDG${base}-per-hourly-block (no partial). Working Pace: {working_pace}. Example: 6 hours count, then the subtotal_price will be {int(base * 1.8)} + {int(base*1.8)} + {int(base * 1.8)} + {int(base*1.8)} + {int(base)} + {int(base)} = {int(base * 1.8)*4 + int(base)*2} as no partial-hour allowed.",
        "flat hourly": lambda base, uom, working_pace: f"SGD{base} per hour. Working Pace: {working_pace}. Example: 2 hours count, then the subtotal_price will be {int(base)} + {int(base)} = {int(base)*2} as no partial-hour allowed.",
    }


def _apply_pricing_model(df: pd.DataFrame) -> pd.DataFrame:
    """
    Applies pricing model functions to the services dataframe.
    
    Args:
        df (pd.DataFrame): Services dataframe
        
    Returns:
        pd.DataFrame: DataFrame with pricing model applied
    """
    pricing_model_functions = _get_pricing_model_functions()
    default_pricing_model_function = lambda base, uom, working_pace: "Pricing model not available"

    df["PricingModelFunction"] = (df["PricingModelID"].map(pricing_model_functions).fillna(default_pricing_model_function))

    df["PricingModel"] = df.apply(lambda x: x["PricingModelFunction"](x["BasePrice"], x["UOM"], x["WorkingPace"]),axis=1)
    
    return df


def fetch_master_services_pricing(occasion_type: str, csv_url: str = "https://docs.google.com/spreadsheets/d/1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0/export?format=csv&id=1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0&gid=1504751653") -> pd.DataFrame:
    """
    Fetch master services and pricing details.

    Args:
        occasion_type (str): The type of occasion, either 'corporate' or 'private'.
        csv_url (str): URL to the CSV containing master pricing details.

    Returns:
        pd.DataFrame: Pandas DataFrame of services and pricing.
    """
    happy_product_services_table = pd.read_csv(csv_url, delimiter=",")
    happy_product_services_table = _apply_pricing_model(happy_product_services_table)

    return filter_master_services_by_occasion(occasion_type, happy_product_services_table)


def filter_master_services_by_occasion(occasion_type: str, master_services_df: pd.DataFrame) -> pd.DataFrame:
    """
    Filters the master services DataFrame based on the occasion type.

    Args:
        occasion_type (str): The type of occasion, either 'corporate' or 'private'.
        master_services_df (pd.DataFrame): DataFrame containing all available services.

    Returns:
        pd.DataFrame: Filtered DataFrame with services matching the occasion type.
    """
    if occasion_type.lower() == "corporate":
        return master_services_df[master_services_df["Name"].str.contains("corporate", case=False)]
    else:
        return master_services_df[~master_services_df["Name"].str.contains("corporate", case=False)]


def filter_master_services_by_budget(budget: int, master_services_df: pd.DataFrame) -> pd.DataFrame:
    """
    Filters the master services DataFrame based on the budget.

    Args:
        budget (int): The budget for the event.
        master_services_df (pd.DataFrame): DataFrame containing all available services.

    Returns:
        pd.DataFrame: Filtered DataFrame with services within the budget.
    """
    valid_rows = []

    for _, row in master_services_df.iterrows():
        base_price = row["BasePrice"]
        
        # Check if base_price is a valid float
        try:
            base_price_cleaned = float(str(base_price).replace(',', ''))
            if base_price_cleaned <= budget:
                valid_rows.append(row)
        except ValueError:
            # Skip rows where BasePrice cannot be converted to float
            continue

    # Create a new DataFrame with valid rows
    filtered_df = pd.DataFrame(valid_rows, columns=master_services_df.columns)
    return filtered_df


def fetch_surcharge_table() -> pd.DataFrame:
    surcharges_csv = "https://docs.google.com/spreadsheets/u/2/d/1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0/export?format=csv&id=1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0&gid=834944614"
    return pd.read_csv(surcharges_csv, delimiter=",")


def extract_event_location_surcharge_details(surcharge_table: pd.DataFrame) -> str:
    event_location_surcharge_table = surcharge_table[surcharge_table["SurchargeID"].str.contains("L")]
    event_location_surcharge_details = event_location_surcharge_table[[
        "SurchargeID",
        "ProductServiceID?",
        "Circumstance",
        "Surcharge",
        "SurchargeType",
    ]].rename(columns={"ProductServiceID?": "Category"})
    return json.dumps(event_location_surcharge_details.to_dict(orient="records"))


def construct_event_location_header(event_location: str) -> str:
    is_transport_nearby = utility_location_helper.check_is_any_transport_nearby(event_location) if event_location else True
    if not is_transport_nearby:
        return f"The customer event location is {event_location}, and there is no bus stop or MRT station nearby the event location within 500m."
    return f"The customer event location is {event_location}."


def construct_event_location_full_str(event_date: str, event_location_surcharge_str: str, event_location_header_str: str) -> str:
    return utility_general_helper.clean_up_prompt(f"""Based on the provided event details, determine which surcharges are applicable from the Location Surcharge Master List. 
    Event Details:
    Date: {event_date}
    Location: {event_location_header_str}
    
    Follow these guidelines:

    1. **Evaluate Surcharges:**
    - Use the `Location Surcharge Master List` provided below:
        {event_location_surcharge_str}

    2. **Applicability Conditions:**
    - A surcharge is applicable **only if**:
        - The service category is either "Fringe Activity" or "Entertainer Highlights".
        - The location circumstance is fulfilled for the given `event_location` and `event_date`.
    - Ignore the surcharge if the service category does not match or the location circumstance is not fulfilled.

    3. **Output Requirements:**
    - For each applicable surcharge, return a JSON object with:
        - `SurchargeID`: The unique identifier for the surcharge.
        - `Reason`: A brief explanation for why the surcharge is applied.
    - If no surcharge is applicable, return an empty JSON array.
    - ONLY RETURN THE JSON ARRAY.

    ```json
    [
        {{
            "SurchargeID": "<SurchargeID>",
            "Reason": "<Reason>"
        }}
    ]
    ```""")


def service_surcharges_enrichment(openai_client, service_json: list, surcharge_list: pd.DataFrame, event_location_full_str: str, event_date: str) -> list:
    """
    Enriches each service with applicable surcharges by interacting with an AI model.

    Args:
        openai_client: The client to interact with the AI model.
        service_json (list): A list of service dictionaries to be enriched with surcharge information.
        surcharge_list (pd.DataFrame): DataFrame containing surcharge details.
        event_location_full_str (str): The full event location string.
        event_date (str): The event date.

    Returns:
        list: A list of services enriched with surcharge information, each containing 
              'total_surcharge_amount' and 'surcharge_reason'.
    """

    class SurchargeInfo(BaseModel):
        total_surcharge_amount: float
        surcharge_reason: str

    today_date = datetime.now().strftime("%Y-%m-%d")

    for service in service_json:

        cleaned_service = {
            "product_service_id": service["product_service_id"],
            "hours_count": service["hours_count"],
            "number_of_talent": service["number_of_talent"],
            "price": service["subprice"],
            "sub_category": service["sub_category"],
            "category": service["category"],

        }
        surcharge_prompt = f"""
        Determine if the following surcharges are applicable to the given service, and if applicable, calculate the surcharge amount. 
        Use the base price of the service to calculate the surcharge, and return the result in a dictionary format.

        Today's Date:
        {today_date}

        Service Details:
        {cleaned_service}

        Surcharge List:
        {surcharge_list.to_dict(orient="records")}

        Event Location:
        {event_location_full_str}

        Event Date:
        {event_date}

        Instructions:
        - For each surcharge in the `surcharge_list`, determine if it is applicable to the service based on the service details.
        - If applicable, calculate the surcharge amount based on the `base_price` (use a float for calculations).
        - Run through step by step to calculate the surcharge amount.

        - Finally, return the result in the following dictionary format:
        {{
            "total_surcharge_amount": float,
            "surcharge_reason": "string",
        }}

        RETURN IN A JSON FORMAT. YOU SHOULD ONLY RETURN A SINGLE JSON OBJECT.
        """
        attempts = 0
        while attempts < 5:
            try:
                response = get_gpt_response(openai_client, [{"role": "user", "content": surcharge_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0], response_format=SurchargeInfo)
                print(response)

                response = utility_general_helper.extract_json_from_str(response)
                response = response.replace("```json", "").replace("```", "")
                print("BEfore json load", response)
                
                response = json.loads(response)
                
                service.update(response)

                break

            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"Error occurred: {e}. Retrying {attempts + 1}/5...")
                attempts += 1

    return service_json

def enrich_by_surcharge_fee(client, event_location: str, event_date: str, services_with_details: pd.DataFrame) -> pd.DataFrame:
    """
    Enriches the provided services with surcharge fee details based on event location and date.

    Arguments:
        client: The client for whom the proposal is being generated.
        event_location (str): The location of the event.
        event_date (str): The date of the event.
        services_with_details (pd.DataFrame): DataFrame containing service details that need enrichment.

    Returns:
        pd.DataFrame: DataFrame containing the filtered surcharge details with columns 
                      'ProductServiceID?', 'Surcharge', and 'SurchargeType'.
    """
    surcharge_table = fetch_surcharge_table()
    
    if not event_location:
        return []

    event_location_surcharge_str = extract_event_location_surcharge_details(surcharge_table)
    event_location_header_str = construct_event_location_header(event_location)
    event_location_full_str = construct_event_location_full_str(event_date, event_location_surcharge_str, event_location_header_str)

    chat_gpt_response = get_gpt_response(client, [{"role": "user", "content": event_location_full_str}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    print("chat_gpt_response for surcharge: ", chat_gpt_response)

    updated_proposal_json = utility_general_helper.extract_list_from_str(chat_gpt_response)
    updated_proposal_json = json.loads(updated_proposal_json)

    # Now will need to load in the information from the surcharge_table
    surcharge_id = []
    for item in updated_proposal_json:
        surcharge_id.append(item["SurchargeID"])

    surcharge_table_filtered = surcharge_table[surcharge_table["SurchargeID"].isin(surcharge_id)]

    print("surcharge_table_filtered: ", surcharge_table_filtered)

    # For each of the services we will determine if the surcharge is applicable
    if not surcharge_table_filtered.empty:
        services_with_details = service_surcharges_enrichment(client, services_with_details, surcharge_table_filtered, event_location_full_str, event_date)

    return services_with_details


def fetch_discount_table() -> pd.DataFrame:
    discount_to_client_csv = "https://docs.google.com/spreadsheets/d/1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0/export?format=csv&id=1nF20F-U0GfWbHijwWBcnaxFLM-NKJzXsvzoSLq9-Uq0&gid=1780622689"
    discount_to_client_table = pd.read_csv(discount_to_client_csv, delimiter=",")

    return discount_to_client_table


def check_for_discounts(client, services_with_details):
    discount_to_client_table = fetch_discount_table()

    today_date = datetime.now().strftime("%Y-%m-%d")

    print("discount_to_client_table: ", discount_to_client_table)
    print("services_with_details: ", services_with_details)

    service_id_list = []
    sub_service_details = []
    for service in services_with_details:
        service_id_list.append(service["product_service_id"])
        sub_service_details.append({
            "product_service_id": service["product_service_id"],
            "base_price": service["subprice"]
        })

    # Filter the discount table based on the service IDs
    discount_to_client_table = discount_to_client_table[discount_to_client_table["ProductServiceID"].isin(service_id_list)]

    print("discount_to_client_table: ", discount_to_client_table)

    # Convert the DataFrame to a list of dictionaries
    discount_prompt = f"""
    Given the discount and service details, determine the best promotion for each service and calculate the applicable discounts.

    Today's Date:
    {today_date}

    Service Details:
    {sub_service_details}

    Discount List:
    {discount_to_client_table.to_dict(orient="records")}

    Instructions:
    - For each service, evaluate all applicable promotions from the `discount_list`.
    - Consider `ProductServiceID` to match discounts with services.
    - Determine the best promotion that benefits the customer most based on the discount percentage.
    - For each selected promotion, note the earliest expiry date for the discount.
    - Calculate the discount percentage from the base price of the service.

    - Return the results as an array of dictionaries in JSON format, each containing:
    {{
        "<product_service_id>":
            {{
                "ClientDiscountID": "string",
                "last_date_of_discount": "YYYY-MM-DD",
                "discounted_amount": float
            }},
        ...
    }}

    RETURN IN A JSON FORMAT
    """
    attempts = 0
    while attempts < 5:
        try:
            response = get_gpt_response(client, [{"role": "user", "content": discount_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])
            print(response)

            response = response.replace("```json", "").replace("```", "")
            response = utility_general_helper.extract_json_from_str(response)
            response = json.loads(response)
            
            for service in services_with_details:
                service.update(response[service["product_service_id"]])
            
            break

        except Exception as e:
            print(f"Error occurred: {e}. Retrying {attempts + 1}/5...")
            attempts += 1

    # Loop through the discount array to calculate the discounted amount
    last_date_of_discount_final = None
    total_discounted_amount = 0
    total_cost = 0
    for service in services_with_details:
        product_service_id = service["product_service_id"]

        # Get the service price
        sub_price = service["subprice"]
        total_cost += sub_price

        # Check if the service is in the discount table
        if response.get(product_service_id, None):
            # Find the discount for the service
            discount_id = response[product_service_id]["ClientDiscountID"]
            
            # Get the information from the discount table
            discount_info = discount_to_client_table[
                (discount_to_client_table["ClientDiscountID"] == discount_id) &
                (discount_to_client_table["ProductServiceID"] == product_service_id)
            ]
            print(discount_info)
            print(discount_id)
            print(product_service_id)
            if not discount_info.empty:
                discount_percentage = discount_info["ClientDiscount (%)"].values[0]
                discount_conditions = discount_info["ClientDiscountCondition"].values[0]
            else:
                discount_percentage = 0
                discount_conditions = ""

            # Calculate the discounted amount
            discounted_amount = float(sub_price) * (1 - discount_percentage / 100)

            # Get the time difference between today and the last date of discount
            today = datetime.now().date()
            if "within 72 hours" in discount_conditions:
                last_date_of_discount = today + timedelta(days=3)
            elif "within 1 week" in discount_conditions:
                last_date_of_discount = today + timedelta(days=7)
            else:
                last_date_of_discount = None
            
            if last_date_of_discount and last_date_of_discount_final is None:
                last_date_of_discount_final = last_date_of_discount
            elif last_date_of_discount and last_date_of_discount_final > last_date_of_discount:
                last_date_of_discount_final = last_date_of_discount

            print(sub_price)
            print(discount_percentage)
            print(discounted_amount)

            # Calculate the discounted amount
            total_discounted_amount += discounted_amount

    # Convert total cost and total discounted amount to nearest 10s
    total_cost = math.ceil(total_cost / 10) * 10
    total_discounted_amount = math.ceil(total_discounted_amount / 10) * 10

    return {
        "last_date_of_discount": last_date_of_discount_final,
        "total_discounted_amount": total_discounted_amount,
        "total_cost": total_cost
    }
