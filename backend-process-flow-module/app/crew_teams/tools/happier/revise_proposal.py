import ast
import copy
import json
import logging
import math
import re
import time
import traceback
from datetime import datetime
from pprint import pprint
from typing import Any, Callable, Dict, List, Literal

import pandas as pd
from crewai.tools import tool
from django.utils import timezone
from django_tenants.utils import schema_context
from openai import OpenAI
from pydantic import BaseModel, Field

import app.chats.chat.models as chat_models
import app.documents.document.models as document_models
import app.utility.chat_constant as chat_constant
import app.utility.chat_helper as chat_helper
import app.utility.date_helper as utility_date_helper
import app.utility.general_helper as utility_general_helper
import app.utility.location_helper as utility_location_helper
from app.crew_teams.tools.happier.fetch_master_data import (
    check_for_discounts,
    enrich_by_surcharge_fee,
    fetch_master_services_pricing,
    filter_master_services_by_budget,
)
from app.crew_teams.tools.happier.draft_proposals import (
    determine_budget_range,
    define_user_requirements,
    gather_suggestions_for_services,
    get_services_choices,
    enrich_services_with_details,
    generate_proposal_plan,
    draft_final_proposal,
    save_proposal_to_db,
    extract_historical_messages,
)
from app.crew_teams.tools.happier.gather_event_information import (
    gather_event_information,
)
from app.crew_teams.utils.gpt_integration import get_gpt_response
from app.redteam.models import RedTeamConversation, RedTeamMessage

logger = logging.getLogger(__name__)


def get_latest_proposals(conversation_metadata, count=2):
    """
    Retrieve the latest proposals from conversation metadata.
    
    Args:
        conversation_metadata (dict): The conversation metadata containing proposals
        count (int): Number of latest proposals to retrieve
        
    Returns:
        list: List of the latest proposals
    """
    if "proposals" not in conversation_metadata:
        return []
    
    all_proposals = conversation_metadata.get("proposals", {})
    if not all_proposals:
        return []
    
    # Sort proposal IDs by their numeric value (assuming format P001, P002, etc.)
    sorted_proposal_ids = sorted(all_proposals.keys(), key=lambda x: int(x[1:]), reverse=True)
    
    # Get the latest 'count' proposals
    latest_proposals = []
    for i in range(min(count, len(sorted_proposal_ids))):
        latest_proposals.append({
            "id": sorted_proposal_ids[i],
            "details": all_proposals[sorted_proposal_ids[i]]
        })
    
    return latest_proposals


def analyze_user_feedback(client, incoming_message, latest_proposals):
    """
    Analyze user feedback to understand what changes are requested.
    
    Args:
        client (OpenAI): The OpenAI client
        incoming_message (str): The user's message requesting changes
        latest_proposals (list): List of the latest proposals
        
    Returns:
        dict: Analysis of user feedback including requested changes
    """
    if not latest_proposals:
        return {"error": "No existing proposals found to revise"}
    
    # Extract proposal details for context
    proposal_context = []
    for proposal in latest_proposals:
        proposal_context.append(f"Proposal ID: {proposal['id']}")
        if 'services' in proposal['details']:
            for service in proposal['details']['services']:
                proposal_context.append(f"- {service.get('name', 'Unknown service')}: {service.get('description', 'No description')}")
    
    proposal_context_str = "\n".join(proposal_context)
    
    # Prompt for GPT to analyze the feedback
    prompt = f"""
    You are analyzing a customer's feedback about an event proposal. Based on their message, identify what specific changes they want to make to the proposal.
    
    Previous proposal details:
    {proposal_context_str}
    
    Customer's feedback:
    {incoming_message}
    
    Please analyze the feedback and identify:
    1. Which services they want to add, remove, or modify
    2. Any changes to duration, number of talents, or other service details
    3. Any budget considerations mentioned
    4. Any other specific requirements or preferences
    
    Format your response as a JSON object with the following structure:
    {{
        "add_services": [list of services to add with details],
        "remove_services": [list of services to remove],
        "modify_services": [list of services to modify with specific changes],
        "budget_changes": any budget considerations,
        "other_requirements": any other requirements or preferences
    }}
    """
    
    response = client.chat.completions.create(
        model="gpt-4-turbo",
        messages=[{"role": "user", "content": prompt}],
        response_format={"type": "json_object"}
    )
    
    try:
        feedback_analysis = json.loads(response.choices[0].message.content)
        return feedback_analysis
    except Exception as e:
        logger.error(f"Error parsing feedback analysis: {str(e)}")
        return {"error": "Failed to analyze feedback"}


def modify_services_based_on_feedback(services_with_details, feedback_analysis, master_services):
    """
    Modify the services list based on user feedback.
    
    Args:
        services_with_details (list): Current list of services with details
        feedback_analysis (dict): Analysis of user feedback
        master_services (pd.DataFrame): Master services data
        
    Returns:
        list: Modified list of services
    """
    modified_services = copy.deepcopy(services_with_details)
    
    # Remove services
    if "remove_services" in feedback_analysis and feedback_analysis["remove_services"]:
        modified_services = [
            service for service in modified_services 
            if not any(remove_service.lower() in service["name"].lower() 
                      for remove_service in feedback_analysis["remove_services"])
        ]
    
    # Modify existing services
    if "modify_services" in feedback_analysis and feedback_analysis["modify_services"]:
        for modification in feedback_analysis["modify_services"]:
            for service in modified_services:
                if modification.get("name", "").lower() in service["name"].lower():
                    # Update duration if specified
                    if "duration" in modification:
                        service["duration"] = modification["duration"]
                    
                    # Update number of talents if specified
                    if "talents" in modification:
                        service["talents"] = modification["talents"]
                    
                    # Update any other specified fields
                    for key, value in modification.items():
                        if key not in ["name", "duration", "talents"]:
                            service[key] = value
    
    # Add new services
    if "add_services" in feedback_analysis and feedback_analysis["add_services"]:
        for new_service in feedback_analysis["add_services"]:
            service_name = new_service.get("name", "")
            
            # Find the service in master services
            matching_services = master_services[
                master_services["name"].str.lower().str.contains(service_name.lower())
            ]
            
            if not matching_services.empty:
                service_row = matching_services.iloc[0]
                
                # Create new service entry
                new_service_entry = {
                    "name": service_row["name"],
                    "category": service_row["category"],
                    "sub_category": service_row["sub_category"],
                    "price": float(service_row["price"]),
                    "duration": new_service.get("duration", service_row.get("duration", 1)),
                    "talents": new_service.get("talents", service_row.get("talents", 1)),
                    "description": service_row.get("description", ""),
                    "notes": new_service.get("notes", "")
                }
                
                modified_services.append(new_service_entry)
    
    return modified_services


@tool("revise_proposal")
def revise_proposal(
    conversation_id: int,
    incoming_message: str = None,
    schema_name: str = None,
    is_redteam: str = "true",
):
    """
    Revise an existing proposal based on customer feedback.
    
    This function retrieves the latest proposals from the conversation metadata,
    analyzes the customer's feedback, and generates a revised proposal.
    
    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        incoming_message (str): Optional. The latest message from the customer with feedback.
        schema_name (str): Optional. The name of the schema to use.
        is_redteam (str): Optional. Set to `true` unless it is explicitly mentioned. Else, set to `false`.
        
    Returns:
        Dict[str, Any]: Formatted revised proposal response.
    """
    try:
        start = time.time()
        
        # Convert is_redteam to boolean
        is_redteam = is_redteam.lower() == "true"
        
        # Extract conversation metadata
        with schema_context(schema_name):
            if is_redteam:
                conversation = RedTeamConversation.objects.get(id=conversation_id)
            else:
                conversation = chat_models.Conversation.objects.get(id=conversation_id)
            
            conversation_metadata = conversation.metadata or {}
            
            # Check if there are existing proposals
            if "proposals" not in conversation_metadata or not conversation_metadata["proposals"]:
                return {
                    "error": "No existing proposals found to revise. Please generate a proposal first."
                }
            
            # Get the latest proposals for reference
            latest_proposals = get_latest_proposals(conversation_metadata)
            if not latest_proposals:
                return {
                    "error": "No existing proposals found to revise. Please generate a proposal first."
                }
            
            # Extract event details from the latest proposal
            latest_proposal = latest_proposals[0]["details"]
            name = latest_proposal.get("name", "")
            email = latest_proposal.get("email", "")
            contact_number = latest_proposal.get("contact_number", "")
            date = latest_proposal.get("date", "")
            start_time = latest_proposal.get("start_time", "")
            end_time = latest_proposal.get("end_time", "")
            event_location = latest_proposal.get("event_location", "")
            number_of_adults = latest_proposal.get("number_of_adults", 0)
            number_of_kids_and_age_range = latest_proposal.get("number_of_kids_and_age_range", "")
            occasion_type = latest_proposal.get("occasion_type", "")
            occasion = latest_proposal.get("occasion", "")
            theme = latest_proposal.get("theme", "")
            budget = latest_proposal.get("budget", 0)
            services_list = latest_proposal.get("services_list", [])
            event_additional_details = latest_proposal.get("event_additional_details", "")
            
            # Extract historical messages
            historical_messages_str = extract_historical_messages(conversation_id, schema_name, is_redteam)
            
            # Initialize OpenAI client
            client = OpenAI()
            
            # Analyze user feedback
            feedback_analysis = analyze_user_feedback(client, incoming_message, latest_proposals)
            if "error" in feedback_analysis:
                return {"error": feedback_analysis["error"]}
            
            # Adjust budget if mentioned in feedback
            budget_value = budget
            if "budget_changes" in feedback_analysis and feedback_analysis["budget_changes"]:
                try:
                    # Extract numeric value from budget_changes if it's a string
                    if isinstance(feedback_analysis["budget_changes"], str):
                        budget_match = re.search(r'\d+', feedback_analysis["budget_changes"])
                        if budget_match:
                            budget_value = int(budget_match.group())
                    elif isinstance(feedback_analysis["budget_changes"], (int, float)):
                        budget_value = feedback_analysis["budget_changes"]
                except Exception as e:
                    logger.error(f"Error parsing budget changes: {str(e)}")
            
            # Fetch master services data
            master_services = fetch_master_services_pricing(occasion_type)
            
            # Determine budget range (use only one budget for revision)
            revised_budget, _ = determine_budget_range(client, budget_value)
            
            # Filter master services by budget
            master_services_filtered = filter_master_services_by_budget(revised_budget, master_services)
            
            # Set up event metadata
            event_metadata = {
                "name": name,
                "date": date,
                "start_time": start_time,
                "end_time": end_time,
                "event_location": event_location,
                "number_of_adults": number_of_adults,
                "number_of_kids_and_age_range": number_of_kids_and_age_range,
                "occasion_type": occasion_type,
                "occasion": occasion,
                "theme": theme,
                "budget": revised_budget,
                "services_list": services_list,
                "event_additional_details": event_additional_details,
                "incoming_message": incoming_message
            }
            
            # Define user requirements based on historical messages and incoming message
            user_event_requirements = define_user_requirements(client, historical_messages_str, incoming_message, event_metadata)
            
            # Get service recommendations
            services_recommendation = gather_suggestions_for_services(client, user_event_requirements, master_services_filtered)
            
            # Get service choices
            services_choices = get_services_choices(client, user_event_requirements, services_recommendation, master_services_filtered)
            
            # Enrich services with details
            services_with_details = enrich_services_with_details(services_choices, master_services_filtered)
            
            # Modify services based on user feedback
            modified_services = modify_services_based_on_feedback(services_with_details, feedback_analysis, master_services_filtered)
            
            # Generate proposal plan
            services_with_details, recommendation_response = generate_proposal_plan(
                client, 
                modified_services, 
                event_metadata, 
                user_event_requirements, 
                revised_budget, 
                None
            )
            
            # Apply surcharge fees
            services_with_details = enrich_by_surcharge_fee(client, event_location, date, services_with_details)
            
            # Apply discounts and calculate costs
            discount_dict = check_for_discounts(client, services_with_details)
            
            # Prepare proposal to save
            proposal_to_save = {
                "name": name,
                "email": email,
                "date": date,
                "start_time": start_time,
                "end_time": end_time,
                "event_location": event_location,
                "number_of_adults": number_of_adults,
                "number_of_kids_and_age_range": number_of_kids_and_age_range,
                "occasion_type": occasion_type,
                "occasion": occasion,
                "theme": theme,
                "budget": revised_budget,
                "services_list": services_list,
                "event_additional_details": event_additional_details,
                "contact_number": contact_number,
                "discounted_price": discount_dict.get("total_discounted_amount", 0),
                "discounted_date": discount_dict.get("last_date_of_discount", "").strftime("%Y-%m-%d") if discount_dict.get("last_date_of_discount") else "",
                "is_revision": True,
                "based_on_proposal_id": latest_proposals[0]["id"]
            }
            
            # Save proposal to database
            proposal_id = save_proposal_to_db(proposal_to_save, conversation_id, schema_name, is_redteam)
            
            # Draft the final proposal
            proposal_response, has_discount = draft_final_proposal(
                client, 
                services_with_details, 
                event_metadata, 
                user_event_requirements, 
                discount_dict.get("total_cost", 0), 
                discount_dict.get("total_discounted_amount", 0), 
                discount_dict.get("last_date_of_discount", ""), 
                proposal_id, 
                True
            )
            
            # Add revision information to the proposal
            if isinstance(proposal_response, dict) and "proposal_text" in proposal_response:
                proposal_response["proposal_text"] = "REVISED PROPOSAL:\n\n" + proposal_response["proposal_text"]
                proposal_response["is_revision"] = True
                proposal_response["based_on_proposal_id"] = latest_proposals[0]["id"]
            
            final_proposal = [proposal_response]
            
            # Format the response
            formatted_response = {
                "proposal": final_proposal,
                "proposal_id": proposal_id,
                "has_discount": has_discount,
                "is_revision": True
            }
            
            end = time.time()
            print(f"Time taken to revise proposal: {end - start}")
            
            return formatted_response
            
    except Exception as e:
        logger.error(f"Error in revise_proposal: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to revise proposal: {str(e)}"}
