import json
import logging
import os
import traceback
import random

import requests
import app.utility.chat_constant as chat_constant
import app.chats.chat.models as chat_models

from typing import Optional, List
from crewai.tools import tool
from datetime import datetime, timedelta
from app.chats.chat.models import Conversation
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.tools.amelio.helper_functions.check_nric_format import check_nric_format
from app.crew_teams.tools.amelio.helper_functions.api_with_data import api_with_data
from app.crew_teams.tools.amelio.helper_functions.global_variables import consultation_types, doctor_with_branch_with_specialty, branch_details
from app.crew_teams.tools.amelio.helper_functions.google_calendar_service_for_new_appointment import GoogleCalendar
from app.crew_teams.tools.amelio.helper_functions.date_converter import convert_date_format
from django_tenants.utils import schema_context
from app.crew_teams.utils.gpt_integration import get_gpt_response
from openai import OpenAI

logger = logging.getLogger(__name__)
openai_client = OpenAI()

IS_REDTEAM_RUN = os.environ.get("REDTEAM_RUN")

# Convert date time to correct format
def convert_date_time_by_using_gpt(date, time):
    # Logging the input
    logger.info(f"Converting date and time: {date} {time}")
    
    prompt = f"""
        You are a date-time conversion assistant. Your task is to convert the given date and time to Singapore Time (SGT) format.

        Input:
        Date: {date}
        Time: {time}

        Instructions:
        1. Convert any date format (including relative dates like "next Tuesday") to "YYYY-MM-DD" format
        2. If the date is ambiguous (for example, "6/5/2025"), interpret it as day/month/year.
        3. Convert any time format to "HH:MM:SS+0800" format (SGT timezone)
        4. Use the current date {datetime.now().strftime('%Y-%m-%d')} as reference for relative dates
        5. Return ONLY the JSON response in this exact format, with no additional text:
        {{"date": "YYYY-MM-DD", "time": "HH:MM:SS+0800"}}

        For example, if input is "next Monday, 2pm", output should be exactly:
        {{"date": "2025-01-27", "time": "14:00:00+0800"}}
    """

    messages = [
        {
            "role": "system", 
            "content": "You are a date-time conversion assistant. Only respond with the JSON format specified, no explanations or additional text."
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]

    response = get_gpt_response(openai_client, messages, model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

    # Parse the JSON string into a Python dictionary
    try:
        import json
        # Clean the response - remove any markdown formatting if present
        if isinstance(response, str):
            response = response.replace('```json\n', '').replace('\n```', '').strip()
            response_dict = json.loads(response)
            return response_dict
        return response
    except json.JSONDecodeError as e:
        logging.error(f"Failed to parse date time response: {response}")
        logging.error(f"JSON decode error: {str(e)}")
        # Don't return fallback format, raise the error to handle it properly
        raise ValueError(f"Failed to parse date time response: {response}")

# Function to convert date by using gpt
def convert_date_by_using_gpt(date: str) -> str:
    prompt = f"""
        You are a date conversion assistant. Your task is to convert the given date to YYYY-MM-DD format.

        Input date: {date}

        Instructions:
        1. Convert any date format (including relative dates like "next Tuesday") to "YYYY-MM-DD" format.
        2. If the date is ambiguous (for example, "6/5/2025"), interpret it as day/month/year.
        3. Use the current date {datetime.now().strftime('%Y-%m-%d')} as reference for relative dates.
        4. Return ONLY the date in YYYY-MM-DD format, with no additional text or JSON.

        For example:
        - If input is "next Monday" -> "2025-02-24"
        - If input is "tomorrow" -> "2025-02-18"
        - If input is "2025-02-20" -> "2025-02-20"
        - If input is "6/5/2025" -> "2025-05-06"
    """

    messages = [
        {
            "role": "system", 
            "content": "You are a date conversion assistant. Only respond with the date in YYYY-MM-DD format, no explanations or additional text."
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]

    try:
        response = get_gpt_response(openai_client, messages, model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0]).strip()
        
        # Validate the response is in correct format
        datetime.strptime(response, '%Y-%m-%d')
        return response
    except Exception as e:
        logger.error(f"Error converting date '{date}': {str(e)}")
        return None


def get_contact_number_from_whatsapp(conversation_id: int, schema_name: str) -> str:
    """
    Retrieves the contact number associated with a WhatsApp conversation.

    Args:
        conversation_id (int): The unique identifier of the conversation.
        schema_name (str): The name of the schema to use for the database context.

    Returns:
        str: The contact number extracted from the conversation's third-party ID.
    """
    with schema_context(schema_name):
        conversation_instance = Conversation.objects.get(id=conversation_id)
        phone_number = conversation_instance.third_party_id
        return phone_number

# Function to collect personal information
def collect_personal_information(name: Optional[str], mobile: Optional[str], nric: Optional[str], schema_name: str, incoming_message: str) -> str:
    # Logging the input
    logger.info(f"Collecting personal information (Name, Mobile and NRIC)")
    
    # Check for missing information
    missing_info = []
    if not name:
        missing_info.append("name")
    if not mobile:
        missing_info.append("mobile number")
    if not nric:
        missing_info.append("nric")

    # Print missing information
    if missing_info:
        response_to_customer = f"""
        Hi there! To get started with your appointment, could you please share your {', '.join(missing_info)}? 
        
        Rest assured, your information will remain confidential and will only be used for this appointment. Thank you so much!
        """
        return response_to_customer

    return ""

# Function to collect appointment types
def collect_appointment_types(appointment_types: Optional[List[str]], schema_name: str, incoming_message: str) -> List[str]:
    # Logging the input
    logger.info(f"Collecting appointment types")
    
    # Check for missing information
    missing_info = []
    if not appointment_types:
        missing_info.append("appointment types")

    # Print missing information
    if missing_info:
        response_to_customer = f"""
        Thank you for sharing your information with us! To proceed with your appointment, could you please let us know the purpose of your appointment? 
        
        You can choose from the following options: 
        {', '.join(consultation_types)}
        """
        return response_to_customer

    return ""

# Function to standardize appointment type formatting
def standardize_appointment_types(appointment_types):
    """
    Converts appointment types to title case to ensure consistent matching
    with our database values.
    
    Args:
        appointment_types (list): List of appointment types in any case
        
    Returns:
        list: List of appointment types in title case
    """
    if not appointment_types:
        return []
    
    return [apt_type.title() for apt_type in appointment_types]

# Function to get available branches for appointment types
def get_available_branches_for_appointment_types(appointment_types):
    # Format appointment types to title case
    formatted_appointment_types = standardize_appointment_types(appointment_types)
    
    # Get available branches
    available_branch_ids = set()
    for doctor, branches in doctor_with_branch_with_specialty.items():
        for branch_name, branch_info in branches.items():
            # Check if all chosen appointment types are supported by this doctor at this branch
            if all(apt_type in branch_info["specialties"] for apt_type in formatted_appointment_types):
                available_branch_ids.add(branch_name)
    
    # Convert to list and get branch names
    available_branches = list(available_branch_ids)
    available_branch_names = [
        name["name"].title() for name in branch_details if name["id"] in available_branches
    ]
    
    return available_branch_names

# Function to collect branch
def collect_branch(branch: Optional[str], appointment_types: Optional[List[str]], schema_name: str, incoming_message: str) -> str:
    # Logging the input
    logger.info(f"Collecting branch")
    
    # Check for missing information
    missing_info = []
    if not branch:
        missing_info.append("branch")
    
    # Print missing information
    if missing_info:
        # Get available branches that can handle the chosen appointment types
        available_branch_names = get_available_branches_for_appointment_types(appointment_types)
        
        # If no available branches, return error message
        if not available_branch_names:
            response_to_customer = f"""
            I'm sorry, but we couldn't find any branches that offer all your chosen appointment types. 
            Please choose again your appointment types from our available services:
            
            {', '.join(consultation_types)}
            """
            return response_to_customer
        
        # Otherwise, return branch selection
        response_to_customer = f"""
        Thank you! Could you please let us know which {', '.join(missing_info)} you'd like to visit? 
        
        Here are the available options:
        {', '.join(available_branch_names)}
        """
        return response_to_customer

    return ""

# Function to collect doctor
def collect_doctor(doctor: Optional[str], appointment_types: Optional[List[str]], branch: Optional[str]) -> str:
    # Logging the input
    logger.info(f"Collecting doctor")

    # Format appointment types to title case
    formatted_appointment_types = standardize_appointment_types(appointment_types)
    
    # Get available doctors that can handle the chosen appointment types at the specified branch
    available_doctors = []
    
    prompt = f"""
    You are a branch identification assistant for Healthwerkz Medical Centre. Your task is to convert the user's input, which may be an abbreviation or shorthand (e.g., "cck", "bedok"), into one of the following full branch names:

    1. Healthwerkz Medical Centre @ Choa Chu Kang
    2. Healthwerkz Medical Centre @ Bedok
    3. Healthwerkz Medical Centre @ Centrium
    4. Healthwerkz Medical Centre @ Sembawang Blk 355

    Input:
    Branch: {branch}

    Instructions:
    1. Identify if the input corresponds to one of the four branches by checking for key terms or abbreviations.
    2. Map common abbreviations (e.g., "cck" to "Choa Chu Kang") to the full branch name.
    3. If the input clearly matches one of the branches, output the full branch name.
    4. If the input does not match any known branch, output null.
    5. Return ONLY the branch name as a plain string (in quotes) or null if no match, with no additional text.

    For example, if the input is "cck", the output should be exactly:
    "Healthwerkz Medical Centre @ Choa Chu Kang"
    """
    
    messages = [
        {
            "role": "system", 
            "content": "You are a branch identification assistant. Only respond with the direct branch name or null, with no explanations or additional text."
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]
    
    branch = get_gpt_response(openai_client, messages, model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0]).strip()
    
    print('-'*50)
    print(repr(branch))
    print('-'*50)
    
    branch = branch.replace('"', '').strip()
    
    # Convert branch to ID
    if branch == "Healthwerkz Medical Centre @ Choa Chu Kang":
        branch = "CCK"
    elif branch == "Healthwerkz Medical Centre @ Bedok":
        branch = "KCS"
    elif branch == "Healthwerkz Medical Centre @ Centrium":
        branch = "OR"
    elif branch == "Healthwerkz Medical Centre @ Sembawang Blk 355":
        branch = "SBW"
    else:
        branch = "None"
        
    # Logging the output
    logger.info(f"Branch ID: {branch}")
        
    if branch == "None":
        response_to_customer = f"""
        I'm sorry, branch that you have selected is not found.
        
        Please choose again your branch.
        """
        return response_to_customer
        
    for doctor_name, doctor_branches in doctor_with_branch_with_specialty.items():
        # Check if doctor works at the specified branch
        if branch in doctor_branches:
            branch_info = doctor_branches[branch]
            # Check if doctor can handle all requested appointment types at this branch
            if all(apt_type in branch_info["specialties"] for apt_type in formatted_appointment_types):
                available_doctors.append({
                    "name": doctor_name,
                    "id": branch_info["id"],
                    "specialties": branch_info["specialties"],
                    "schedule": branch_info["schedule"]
                })

    return available_doctors

# Function to collect date
def collect_date(date: Optional[str], schema_name: str, incoming_message: str) -> str:
    # Logging the input
    logger.info(f"Collecting date")
    
    missing_info = []
    if not date:
        missing_info.append("date")

    if missing_info:
        response_to_customer = f"""
        Thank you! Could you please let us know the {', '.join(missing_info)} of your appointment?
        """
        return response_to_customer

    return ""

# Function to collect time
def collect_time(time: Optional[str], date: Optional[str], available_doctors: Optional[str], appointment_types: Optional[List[str]], total_consultation_time: Optional[int], schema_name: str, incoming_message: str, conversation_id: int) -> str:
    # Logging the input
    logger.info(f"Collecting time")
    
    missing_info = []
    if not time:
        missing_info.append("time")
        
    # Save into the conversation metadata
    with schema_context(schema_name):
        conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
        
        conversation_metadata = conversation_instance.metadata
        
        if "doctor_name" not in conversation_metadata or "doctor_id" not in conversation_metadata or (conversation_metadata.get("doctor_name") is None and conversation_metadata.get("doctor_id") is None):
            missing_info.append("doctor")

    if missing_info:
        # Check if available_doctors is empty or None
        if not available_doctors:
            formatted_appointment_types = standardize_appointment_types(appointment_types)
            response_to_customer = f"""
            I'm sorry, but we couldn't find any doctors at the selected branch who specialize in your chosen appointment types ({', '.join(formatted_appointment_types)}).
            
            Could you please try a different branch or appointment type?
            """
            return response_to_customer
        
        parsed_date = datetime.strptime(date, "%Y-%m-%d")
        day_of_week = parsed_date.strftime("%A").lower()

        url = f"https://api.hb.sgimed.com/openapi/appointment?start_date={date}&end_date={date}"
        appointments = api_with_data(url, schema_name, incoming_message)
        
        # Logging the output
        logger.info(f"Fetched appointments: {appointments}")

        available_slots = []
        for doctor in available_doctors:
            doctor_id = doctor['id']
            doctor_schedule = doctor['schedule'].get(day_of_week, [])

            for schedule in doctor_schedule:
                start, end = schedule['time']
                current_time = datetime.combine(parsed_date, start)
                end_time = datetime.combine(parsed_date, end)

                while current_time + timedelta(minutes=total_consultation_time) <= end_time:
                    slot_end = current_time + timedelta(minutes=total_consultation_time)
                    is_available = True

                    for appointment in appointments:
                        if appointment and 'doctor' in appointment and appointment['doctor']:
                            doctor_info = appointment['doctor']
                            if isinstance(doctor_info, dict) and 'id' in doctor_info and doctor_info['id'] == doctor_id:
                                apt_start = datetime.strptime(appointment['start_time'], "%H:%M:%S").time()
                                apt_end = datetime.strptime(appointment['end_time'], "%H:%M:%S").time()
                                apt_start = datetime.combine(parsed_date, apt_start)
                                apt_end = datetime.combine(parsed_date, apt_end)

                                if (current_time <= apt_start < slot_end) or (current_time < apt_end <= slot_end) or (apt_start <= current_time and slot_end <= apt_end):
                                    is_available = False
                                    break

                    if is_available:
                        available_slots.append({
                            'id': doctor['id'],
                            'doctor': doctor['name'],
                            'start_time': current_time.strftime("%H:%M"),
                            'end_time': slot_end.strftime("%H:%M")
                        })

                    current_time += timedelta(minutes=15)

        if not available_slots:
            # Extract an available day from the first available doctor's schedule (e.g., 'friday')
            available_day = list(available_doctors[0]['schedule'].keys())[0]

            response_to_customer = f"""
            I'm sorry, but there are no available slots on your chosen date for the appointment types you selected ({', '.join(appointment_types)}).
            
            Please note that these appointment types are available only on {available_day.capitalize()}. Kindly choose a different date.
            """
            return response_to_customer
        else:
            # Randomly select up to 3 slots
            
            # First, get unique doctor IDs
            doctor_ids = list(set(slot['id'] for slot in available_slots))

            # Randomly select one doctor ID
            selected_doctor_id = random.choice(doctor_ids)
            
            # Find the doctor name from the available_doctors list using selected_doctor_id
            selected_doctor_name = next((doctor['name'] for doctor in available_doctors if doctor['id'] == selected_doctor_id), "Unknown Doctor")
            
            # Save into the conversation metadata
            with schema_context(schema_name):
                conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
                
                conversation_metadata = conversation_instance.metadata
                
                conversation_metadata["doctor_id"] = selected_doctor_id
                conversation_metadata["doctor_name"] = selected_doctor_name
                
                conversation_instance.metadata = conversation_metadata
                conversation_instance.save()

            # Filter slots for only the selected doctor
            doctor_slots = [slot for slot in available_slots if slot['id'] == selected_doctor_id]

            # Now select up to 3 slots from this doctor
            num_slots_to_choose = min(3, len(doctor_slots))
            suggested_slots = random.sample(doctor_slots, num_slots_to_choose)

            # Sort the suggested slots by start time
            suggested_slots.sort(key=lambda x: x['start_time'])
            
            response_to_customer = f"""
            Thank you for your patience. Based on your chosen appointment type(s), the expected duration is {total_consultation_time} minutes.

            Here are the available time slots:
            {', '.join([f"{slot['start_time']} - {slot['end_time']}" for slot in suggested_slots])}

            Please let us know which time slot works best for you, or if you'd like further assistance.
            """
            return response_to_customer

    return ""

# Function to collect appointment request
def collect_appointment_request(appointment_request: Optional[str], schema_name: str, incoming_message: str) -> str:
    """
    Checks for missing appointment request and returns a prompt requesting only the required information.

    Args:
        appointment_request (str): The customer's appointment request.
        schema_name (str): The name of the schema.
        incoming_message (str): The latest message from the customer

    Returns:
        str: A message requesting the missing details. If all details are provided, returns an empty string.
    """
    missing_info = []
    if not appointment_request:
        missing_info.append("appointment request")

    if missing_info:
        response_to_customer = f"""
        Thank you! Could you please let us know the {', '.join(missing_info)} of your appointment?
        
        If you don't have a specific request, you can simply reply as "No".
        """
        return response_to_customer

    return ""

# Function to calculate total consultation time
def calculate_total_consultation_time(appointment_types):
    """
    Calculates the total consultation time based on appointment types.
    Ensures case-insensitive matching.
    
    Args:
        appointment_types (list): List of appointment types
        
    Returns:
        int: Total consultation time in minutes
    """
    formatted_types = standardize_appointment_types(appointment_types)
    
    # Create a case-insensitive lookup dictionary
    consultation_types_lookup = {key.lower(): value for key, value in consultation_types.items()}
    
    total_time = 0
    for apt_type in formatted_types:
        # Try to find the time using case-insensitive matching
        apt_type_lower = apt_type.lower()
        if apt_type_lower in consultation_types_lookup:
            total_time += consultation_types_lookup[apt_type_lower]
        else:
            # Default to 15 minutes if not found
            total_time += 15
    
    return total_time

@tool("gather_new_appointment_information")
def gather_new_appointment_information(
    conversation_id: int,
    name: str = None,
    mobile: str = None,
    nric: str = None,
    appointment_types: List[str] = None,
    branch: str = None,
    doctor: str = None,
    date: str = None,
    time: str = None,
    appointment_request: Optional[str] = None,
    incoming_message: str = None,
    schema_name: str = None,
):
    """
    Main function to orchestrate gathering all necessary appointment information from the customer to make a new appointment.

    This function collects and validates appointment-related details provided by the customer, identifies any missing 
    information, recommends suitable services, and generates a proposal if all details are confirmed. It ensures 
    a seamless flow in gathering customer information and handles missing or incomplete inputs by returning prompts.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        name (str): Optional. The customer's name.
        mobile (str): Optional. The customer's mobile number.
        nric (str): Optional. The customer's NRIC.
        appointment_types (List[str]): Optional. The types of appointments chosen by the customer.
        branch (str): Optional. The branch chosen by the customer.
        doctor (str): Optional. The doctor chosen by the customer.
        date (str): Optional. The date of the appointment.
        time (str): Optional. The time of the appointment.
        appointment_request (str): Optional. The customer's appointment request.
        incoming_message (str): Optional. The latest message from the customer.
        schema_name (str): Optional. The name of the schema to use.

    Returns:
        dict or str: A dictionary containing the appointment details or a prompt asking the customer for missing information. 
        If all information is confirmed, summarizes the appointment details for customer approval.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Check if the conversation is within operation hours.
        3. Gather missing personal information if `name`, `mobile` and `nric` is missing.
        4. Validate the NRIC in correct format.
        5. Gather appointment types.
        6. Gather Branch.
        7. Gather Doctor.
        8. Gather Date.
        9. Gather Time.
        10. Gather Appointment Request.
        11. Summarize the details for customer confirmation.
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool gather_new_appointment_information is called with conversation_id: {conversation_id}", extra={
                "conversation_id_": conversation_id,
                "name_": name,
                "mobile_": mobile,
                "nric_": nric,
                "appointment_types_": appointment_types,
                "branch_": branch,
                "doctor_": doctor,
                "date_": date,
                "time_": time,
                "appointment_request_": appointment_request,
                "incoming_message_": incoming_message,
                "schema_name_": schema_name
            }
        )

        if str(mobile).lower() == "none" or not mobile:
            # Proceed to check the database if it is a whatsapp conversation
            mobile = get_contact_number_from_whatsapp(
                conversation_id=conversation_id, schema_name=schema_name)

        # <---------------------------- Step 1: Gather missing personal information ---------------------------->
        personal_info_status = collect_personal_information(name, mobile, nric, schema_name, incoming_message)
        if personal_info_status:
            return personal_info_status
        
        # Logging the output
        logger.info(f"Collected personal information: Name: {name}, Mobile: {mobile}, NRIC: {nric}")

        # <---------------------------- Step 2: Validate NRIC ---------------------------->
        check_nric_format(nric=nric, schema_name=schema_name, incoming_message=incoming_message)
        
        # Logging the output
        logger.info(f"Validated NRIC: {nric}")

        # <---------------------------- Step 3: Gather appointment types ---------------------------->
        appointment_types_status = collect_appointment_types(appointment_types, schema_name, incoming_message)
        if appointment_types_status:
            return appointment_types_status
        
        # Logging the output
        logger.info(f"Selected appointment types: {appointment_types}")

        # <---------------------------- Step 4: Gather branch ---------------------------->
        branch_status = collect_branch(branch, appointment_types, schema_name, incoming_message)
        if branch_status:
            return branch_status
        
        # Logging the output
        logger.info(f"Selected branch: {branch}")

        # <---------------------------- Step 5: Gather doctor ---------------------------->
        doctor_status = collect_doctor(doctor, appointment_types, branch)
        
        # Logging the output
        logger.info(f"Selected doctor: {doctor_status}")

        # <---------------------------- Step 6: Gather date ---------------------------->
        date_status = collect_date(date, schema_name, incoming_message)
        if date_status:
            return date_status
        
        # Convert date by using gpt
        date = convert_date_by_using_gpt(date)
        
        # Logging the output
        logger.info(f"Selected date: {date}")
        
        # <---------------------------- Step 7: Gather time ---------------------------->
        # Calculate total consultation time using case-insensitive matching
        total_consultation_time = calculate_total_consultation_time(appointment_types)
        
        # Logging the output
        logger.info(f"Total consultation time: {total_consultation_time}")
        
        # Gather time
        time_status = collect_time(time, date, doctor_status, appointment_types, total_consultation_time, schema_name, incoming_message, conversation_id)
        if time_status:
            return time_status

        # Logging the output
        logger.info(f"Selected time: {time}")
        
        # <---------------------------- Step 8: Convert date time ---------------------------->
        date_time_status = convert_date_time_by_using_gpt(date, time)
        date = date_time_status["date"]
        time = date_time_status["time"]
        
        # Logging the output
        logger.info(f"Finalized date and time: {date} {time}")
        
        # TODO: need create a new function to check the date time have crash with the database availability

        # <---------------------------- Step 9: Gather appointment request ---------------------------->
        appointment_request_status = collect_appointment_request(appointment_request, schema_name, incoming_message)
        if appointment_request_status:
            return appointment_request_status
        
        # Logging the output
        logger.info(f"Selected appointment request: {appointment_request}")
        
        # Get Doctor ID and Doctor Name for save appointment use
        with schema_context(schema_name):
            conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
            
            conversation_metadata = conversation_instance.metadata
            
            doctor_id = conversation_metadata.get("doctor_id")
            doctor_name = conversation_metadata.get("doctor_name")

        # <---------------------------- Step 10: Save appointment to google calendar ---------------------------->
        google_calendar = GoogleCalendar()
        google_calendar.book(
            summary=f"Appointment for {name} with {doctor_name}",
            date_str=date,
            time_str=time,
            full_address=f"{branch} Branch",
            remarks=appointment_request,
            color="1",
            duration=total_consultation_time
        )
        
        # <---------------------------- Step 10.1: Save appointment to database ---------------------------->
        # Get Branch ID
        if branch == "Healthwerkz Medical Centre @ Choa Chu Kang":
            branch_id = "CCK"
        elif branch == "Healthwerkz Medical Centre @ Bedok":
            branch_id = "KCS"
        elif branch == "Healthwerkz Medical Centre @ Centrium":
            branch_id = "OR"
        elif branch == "Healthwerkz Medical Centre @ Sembawang Blk 355":
            branch_id = "SBW"
        else:
            branch_id = "None"
            
        # Get Patient ID
        user_information = api_with_data(f"https://api.hb.sgimed.com/openapi/patient?nric={nric}", schema_name, incoming_message)
        if user_information:
            patient_id = user_information[0]['id']
        else:
            # Create a new patient
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {os.getenv('BEARER_TOKEN')}"
            }
            payload = {'name': name, 'gender': 'Unknown', 'nric': nric, 'ic_type': 'PINK IC', 'branches': [branch_id], 'created_by': 'UtterUnicorn'}
            # Logging the output
            logger.info(f"Patient payload: {payload}")
            
            url = "https://api.hb.sgimed.com/openapi/patient"
            response = requests.post(url, data=json.dumps(payload), headers=headers)
            
            # Logging the output
            logger.info(f"Patient response: {response}")
            
            # Get Patient ID
            patient_id = response.json()['id']
        
        # Get Appointment Types String
        appointment_types_str = ', '.join(appointment_types)
        
        # Get Start Date and Time
        start_date = date + "T00:00:00+08:00"
        start_time = time.split('+')[0] if '+' in time else time
        
        # Get End Date and Time
        end_date = date + "T00:00:00+08:00"
        end_time = (datetime.strptime(start_time, "%H:%M:%S") + timedelta(minutes=total_consultation_time)).strftime("%H:%M:%S")
        
        # Get Calendar ID
        url_calendar = "https://api.hb.sgimed.com/openapi/calendar"
        response_calendar = api_with_data(url_calendar, schema_name, incoming_message)
        doctor_calendar_id = [calendar['id'] for calendar in response_calendar if calendar['name'].lower().replace('.','').replace('_','') == doctor_name.lower().replace('.','').replace('_','') and calendar['branch_id'] == branch_id][0]
        
        url = "https://api.hb.sgimed.com/openapi/appointment/by_patient"
            
        payload = {
            "patient_id": patient_id,
            "branch_id": branch_id,
            "doctor_id": doctor_id,
            "appointment_type_id": f"{branch_id}-Consultation",
            "subject": appointment_types_str,
            "description": appointment_request,
            "start_date": start_date,
            "end_date": end_date,
            "start_time": start_time,
            "end_time": end_time,
            "calendar_ids": [
                doctor_calendar_id
            ],
        }
        
        # Logging the output
        logger.info(f"Appointment payload: {payload}")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {os.getenv('BEARER_TOKEN')}"
        }
    
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        
        # Logging the output
        logger.info(f"Appointment response: {response}")    
        # <---------------------------- Step 11: Generate response ---------------------------->
        response_to_customer = f"""
        Thanks for your patience. Your appointment has been successfully booked to {date} at {time}. 
        """
        return response_to_customer
    except Exception as e:
        logger.error(f"Tool gather_event_information failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "name_": name,
            "mobile_": mobile,
            "nric_": nric,
            "appointment_types_": appointment_types,
            "incoming_message_": incoming_message,
            "schema_name_": schema_name
        })
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
