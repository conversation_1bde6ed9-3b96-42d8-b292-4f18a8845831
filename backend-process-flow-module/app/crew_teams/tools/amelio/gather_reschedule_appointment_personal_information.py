import logging
import re
import traceback
from typing import Optional

from crewai.tools import tool
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.tools.amelio.helper_functions.check_operation_hours import is_operation_hours
from app.crew_teams.tools.amelio.helper_functions.api_with_data import api_with_data
from app.crew_teams.tools.amelio.helper_functions.api_without_data import api_without_data
from app.crew_teams.tools.amelio.helper_functions.date_converter import convert_date_format
from datetime import datetime
from dateutil import parser


logger = logging.getLogger(__name__)

# Function to collect personal information
def collect_personal_information(nric: Optional[str], dob: Optional[str], schema_name: str, incoming_message: str) -> str: 
    """
    Checks for missing personal details and returns a concise prompt requesting only the required information.
    
    Args:
        nric (str): The customer's nric.
        dob (str): The customer's date of birth.
        schema_name (str): The name of the schema.
        incoming_message (str): The latest message from the customer

    Returns:
        str: A message requesting the missing details. If all details are provided, returns an empty string.
    """
    missing_info = []
    if not nric:
        missing_info.append("nric")
    if not dob:
        missing_info.append("date of birth")

    if missing_info:
        response_to_customer = f"""To process with your reschedule appointment, we just need a few more details: {', '.join(missing_info)}. Could you please share these with us? Your information will be kept confidential and used only for your appointment. Thanks!"""
        return response_to_customer
    
    if nric:
        nric_pattern = re.compile(r"^[STFG]\d{7}[A-Z]$")
        if not nric_pattern.match(nric):
            response_to_customer = f"""The NRIC you provided is not in the correct format. Please provide a valid NRIC."""
            return response_to_customer
        
    if dob:
        # Convert DOB to standard format
        formatted_dob = convert_date_format(dob)
        if not formatted_dob:
            response_to_customer = """The date of birth format is not valid. Please provide your date of birth in a format like DD/MM/YYYY (e.g., 18/03/1999) or YYYY-MM-DD (e.g., 1999-03-18)."""
            return response_to_customer
        
        # Call API
        url = f"https://api.hb.sgimed.com/openapi/appointment?nric={nric}"
        appointment_info = api_with_data(url, schema_name, incoming_message)
        
        if not appointment_info:
            response_to_customer = f"""No appointments found for the provided NRIC. Please provide a valid NRIC."""
            return response_to_customer
        
        appointment_info.sort(key=lambda x: parser.parse(x['start_date']), reverse=True)
        latest_appointment = appointment_info[0]
        
        url2 = f"https://api.hb.sgimed.com/openapi/patient/{latest_appointment['patient']['id']}"
        patient_info = api_without_data(url2, schema_name, incoming_message)
        
        api_dob = datetime.strptime(patient_info['date_of_birth'], '%Y-%m-%d').date()
        provided_dob = datetime.strptime(formatted_dob, '%Y-%m-%d').date()
        
        if api_dob != provided_dob:
            response_to_customer = f"""The date of birth you provided does not match the one in our records. Please provide the correct date of birth."""
            return response_to_customer
        else:
            response_to_customer = f"""
            Hi {latest_appointment['patient']['name']}, Thank you for your patience! We've successfully found your appointment details. Could you please confirm your appointment on {latest_appointment['start_date']} at {latest_appointment['start_time']} with Dr. {latest_appointment['doctor']['name']}? 
            
            If this is your appointment, please let us know your new appointment date and time, we will assistant you to reschedule your appointment!
            """
            return response_to_customer

    return ""

@tool("gather_reschedule_appointment_personal_information")
def gather_reschedule_appointment_personal_information(
    conversation_id: int,
    nric: str = None,
    dob: str = None,
    incoming_message: str = None,
    schema_name: str = None,
):
    """
    Main function to orchestrate gathering all necessary appointment information from the customer to reschedule appointment.

    This function collects and validates appointment-related details provided by the customer, identifies any missing 
    information, recommends suitable services, and generates a proposal if all details are confirmed. It ensures 
    a seamless flow in gathering customer information and handles missing or incomplete inputs by returning prompts.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        nric (str): Optional. The customer's NRIC.
        dob (str): Optional. The customer's date of birth.
        incoming_message (str): Optional. The latest message from the customer.
        schema_name (str): Optional. The name of the schema to use.

    Returns:
        dict or str: A dictionary containing the appointment details or a prompt asking the customer for missing information. 
        If all information is confirmed, summarizes the appointment details for customer approval.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Check if the conversation is within operation hours.
        3. Gather missing personal information if `nric` and `dob` is missing.
        4. Validate the NRIC in correct format.
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool gather_reschedule_appointment_personal_information is called with conversation_id: {conversation_id}", extra={
                "conversation_id_": conversation_id,
                "nric_": nric,
                "dob_": dob,
                "incoming_message_": incoming_message,
                "schema_name_": schema_name
            }
        )

        # Step 1: Check if the conversation is in operation hours
        if not is_operation_hours():
            response_to_customer = "Sorry, we are currently operating between 5am-11:59pm only. Please try again during operation hours."
            
            return response_to_customer

        # Step 2: Gather missing personal information, Step 3: Validate NRIC, Step 4: Validate DOB
        personal_info_status = collect_personal_information(nric, dob, schema_name, incoming_message)
        if personal_info_status:
            return personal_info_status

    except Exception as e:
        logger.error(f"Tool gather_event_information failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "nric_": nric,
            "dob_": dob,
            "incoming_message_": incoming_message,
            "schema_name_": schema_name
        })
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
