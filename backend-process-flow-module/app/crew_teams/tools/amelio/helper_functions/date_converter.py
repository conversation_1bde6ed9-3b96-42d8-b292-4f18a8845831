from typing import Optional
from dateutil import parser
from datetime import datetime

def convert_date_format(dob: str) -> Optional[str]:
    """
    Convert various date formats to YYYY-MM-DD format.
    Handles common formats like:
    - DD/MM/YYYY
    - DD-MM-YYYY
    - DD.MM.YYYY
    - YYYY/MM/DD
    - YYYY-MM-DD
    
    Args:
        dob (str): Date of birth in various formats
        
    Returns:
        str: Date in YYYY-MM-DD format if valid, None if invalid
    """
    try:
        # Parse the date string
        parsed_date = parser.parse(dob, dayfirst=True)  # Assume day comes first in ambiguous dates
        # Format to YYYY-MM-DD
        formatted_date = parsed_date.strftime('%Y-%m-%d')
        
        # Validate year is reasonable (between 1900 and current year)
        year = parsed_date.year
        current_year = datetime.now().year
        if year < 1900 or year > current_year:
            return None
            
        return formatted_date
    except (ValueError, TypeError):
        return None