import re
from app.crew_teams.tools.happier.formulate_response import formulate_response

def check_nric_format(nric: str, schema_name: str, incoming_message: dict) -> bool:
    # check nric format
    nric_pattern = re.compile(r"^[STFG]\d{7}[A-Z]$")
    if not nric_pattern.match(nric):
        response_to_customer = f"""The NRIC you provided is not in the correct format. Please provide a valid NRIC in the format *********."""
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message)
    
    return ""
    