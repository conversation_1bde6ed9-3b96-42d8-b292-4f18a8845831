import pytz
from datetime import datetime
from django_tenants.utils import schema_context
from app.chats.chat_action_tracker.models import MessageToSend
from app.chats.chat.models import Conversation
from app.utility import models as utility_models
from django.utils import timezone
from app.crew_teams.utils.gpt_integration import get_gpt_response
from openai import OpenAI
import app.utility.chat_constant as chat_constant


def send_message_to_amelio_customer_support(
    conversation_id: int,
    schema_name: str,
    customer_message: str = "",
    customer_name: str = "Customer",
    customer_contact: str = "",
    conversation_context: str = ""
):
    """
    Send a customer support request message to the hardcoded support contact.

    Args:
        conversation_id (int): The conversation ID where the request came from
        schema_name (str): The schema name (e.g., "amelio")
        customer_message (str): The original customer message requesting support
        customer_name (str): Customer's name if available
        customer_contact (str): Customer's contact if available
        conversation_context (str): Recent conversation history for context

    Returns:
        dict: Status and bot response message
    """

    # Hardcoded customer support contact number
    CUSTOMER_SUPPORT_CONTACT = "+6588870118"

    try:
        with schema_context(schema_name):
            # Get the conversation details
            conversation = Conversation.objects.filter(
                id=conversation_id).first()

            if not conversation:
                return {
                    "status": "error",
                    "message": "Conversation not found"
                }

            customer_third_party_id = conversation.third_party_id

            # Use AI to summarize the conversation context for customer support
            openai_client = OpenAI()

            summary_prompt = f"""
            Summarize the following customer conversation for customer support staff. Focus on:
            1. Customer's main concern or request
            2. What they've tried or discussed so far
            3. Current status/stage of the conversation
            4. What specific help they need

            Customer Name: {customer_name if customer_name else 'Not provided'}
            Latest Message: {customer_message}
            Conversation Context: {conversation_context}

            Provide a clear, concise summary that helps customer support understand the situation quickly:
            """

            ai_summary = get_gpt_response(
                openai_client,
                [{"role": "user", "content": summary_prompt}],
                model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0]
            )

            # Format the support request message
            support_message = f"""🆘 CUSTOMER SUPPORT REQUEST 🆘

Customer Details:
• Name: {customer_name if customer_name else 'Not provided'}
• Contact: {customer_third_party_id}
• Conversation ID: {conversation_id}

Summary:
{ai_summary}

Latest Customer Message:
"{customer_message}"

Timestamp: {datetime.now(pytz.timezone('Asia/Singapore')).strftime('%Y-%m-%d %H:%M:%S SGT')}

Please assist this customer as soon as possible."""

            # Create message to send to customer support
            MessageToSend.objects.create(
                contact=CUSTOMER_SUPPORT_CONTACT,
                message=support_message,
                status=utility_models.PENDING,
                message_type=utility_models.MACHINE_TEXT,
                target_source=utility_models.WHATSAPP_QR
            )

            # Turn the auto reply to false
            Conversation.objects.filter(
                id=conversation_id).update(is_auto_reply=False)

            return {
                "status": "success",
                "bot_response": "Thank you for your request! We have contacted our customer support team and they will get in touch with you shortly to assist you. Please wait for a moment."
            }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to send customer support request: {str(e)}",
            "bot_response": "We apologize, but there was an issue processing your support request. Please try again later or contact us directly."
        }
