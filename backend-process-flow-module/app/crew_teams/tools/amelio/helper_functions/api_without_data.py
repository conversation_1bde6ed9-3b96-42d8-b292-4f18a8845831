import os
import requests
from app.crew_teams.tools.happier.formulate_response import formulate_response

def api_without_data(endpoint: str, schema_name: str, incoming_message: dict) -> dict:
    bearer_token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiJ9.******************************************************************************************************************************************************************************************.VvDdCHIXGF1_UFzJDKE8DpNT7HMhespCgZCR3-lT4JFUVZmkoRQVqugmutLQm_69chKmNGccM_RbFlCQsXNV_A'
    if not bearer_token:
        response_to_customer = "Something when wrong. Please ask for help."
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message)
    
    headers = {"Authorization": f"Bearer {bearer_token}"}
    response = requests.get(endpoint, headers=headers)
    if response.status_code == 200:
        return response.json()
    elif response.status_code == 401:
        response_to_customer = "Something when wrong. Please ask for help."
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message)
    else:
        response_to_customer = "Something when wrong. Please ask for help."
        return formulate_response(schema_name=schema_name, response_to_customer=response_to_customer, incoming_message=incoming_message)