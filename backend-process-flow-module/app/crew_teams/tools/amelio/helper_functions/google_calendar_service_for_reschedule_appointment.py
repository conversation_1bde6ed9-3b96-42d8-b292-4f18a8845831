from app.crew_teams.tools.amelio.helper_functions import google_calendar_helper

import pytz
from datetime import datetime, timedelta
import re

class GoogleCalendar:
    def __init__(self):
        self.SG_TIMEZONE = pytz.timezone("Asia/Singapore")
        self.service_account_key_json = ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        self.credentials = google_calendar_helper.get_credentials(self.service_account_key_json)
        self.service = google_calendar_helper.get_service(self.credentials)
        self.unassigned_calendar_id = "<EMAIL>"
        self.unassigned_calendar = google_calendar_helper.Calendar(
            self.unassigned_calendar_id, "Appointment Schedule", self.service
        )
        self.regex_team = re.compile(r"^\[T\d+\]")

    def book(self, summary, date_str, time_str, full_address, remarks="", color=""):
        start_time = self.SG_TIMEZONE.localize(datetime.strptime(f"{date_str} {time_str}", "%Y-%m-%d %H:%M"))
        end_time = start_time + timedelta(minutes=40)
        try:
            event = self.unassigned_calendar.events.create(
                summary=summary,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                location=full_address,
                description=remarks,
                colorId=google_calendar_helper.EVENT_COLOR_VALUE_TO_ID_MAPPING.get(color, "1"),
            )
            return event
        except Exception as e:
            raise ValueError(f"Failed to book the servicing: {e}")