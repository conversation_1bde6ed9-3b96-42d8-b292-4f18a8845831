from datetime import datetime

import google.auth.exceptions
import googleapiclient.errors
import pytz
from django.utils import timezone
from google.oauth2 import service_account
from googleapiclient.discovery import build

CALENDAR_COLOR_VALUE_TO_ID_MAPPING = {
    "brown": 1,  # ac725e
    "light red": 2,  # d06b64
    "red": 3,  # f83a22
    "dark red": 4,  # fa573c
    "orange": 5,  # ff7537
    "light orange": 6,  # ffad46
    "green": 7,  # 42d692
    "dark green": 8,  # 16a765
    "light green": 9,  # 7bd148
    "lime": 10,  # b3dc6c
    "yellow": 11,  # fbe983
    "mustard": 12,  # fad165
    "light turquoise": 13,  # 92e1c0
    "cyan": 14,  # 9fe1e7
    "sky blue": 15,  # 9fc6e7
    "blue": 16,  # 4986e7
    "purple-blue": 17,  # 9a9cff
    "light purple": 18,  # b99aff
    "grey": 19,  # c2c2c2
    "light pink": 20,  # cabdbf
    "pink": 21,  # cca6ac
    "rose": 22,  # f691b2
    "violet": 23,  # cd74e6
    "purple": 24,  # a47ae2
}

EVENT_COLOR_VALUE_TO_ID_MAPPING = {
    "light blue": 1,  # a4bdfc
    "light green": 2,  # 7ae7bf
    "light purple": 3,  # dbadff
    "light red": 4,  # ff887c
    "yellow": 5,  # fbd75b
    "orange": 6,  # ffb878
    "turquoise": 7,  # 46d6db
    "grey": 8,  # e1e1e1
    "blue": 9,  # 5484ed
    "green": 10,  # 51b749
    "red": 11,  # dc2127
}


class CalendarCollection:
    def __init__(self, service):
        self.service = service

    def list(self):
        calendars = (
            self.service.calendarList()
            .list(
                minAccessRole="owner",
                maxResults=250,
            )
            .execute()["items"]
        )
        return [
            Calendar(
                calendar_json["id"], calendar_json["summary"], service=self.service
            )
            for calendar_json in calendars
        ]

    def get(self, id=None, summary=None):
        """
        summary: title of the calendar
        """
        assert (id is None) ^ (
            summary is None
        ), f"Either calendar id only or summary only must be provided. id={id}, summary={summary}"
        if id is not None:
            calendar_json = self.service.calendars().get(calendarId=id).execute()
            return Calendar(
                calendar_json["id"], calendar_json["summary"], service=self.service
            )
        if summary is not None:
            calendars = self.list()
            for calendar in calendars:
                if calendar.summary == summary:
                    return calendar
            raise ValueError(f"Calendar with summary {summary} not found.")
        raise ValueError("Either calendar id or summary must be provided.")

    def create(self, summary):
        calendar = {
            "summary": summary,
        }
        calendar_json = self.service.calendars().insert(body=calendar).execute()
        return Calendar(
            calendar_json["id"], calendar_json["summary"], service=self.service
        )

    def delete(self, id=None, summary=None):
        assert (id is None) ^ (
            summary is None
        ), f"Either calendar id only or summary only must be provided. id={id}, summary={summary}"
        if id is not None:
            return Calendar(id, service=self.service).delete()
        if summary is not None:
            return self.get(summary=summary).delete()

    def update(self, id, summary):
        return Calendar(id, service=self.service).update(summary)


class EventCollection:
    def __init__(self, calendar_id, service):
        self.calendar_id = calendar_id
        self._service = service

    @property
    def service(self):
        if self._service is None:
            raise ValueError(
                "Event must be initialized with service in order to update or delete."
            )
        return self._service

    def list(
        self, start_after=None, end_before=None, query=None, is_single_event=False
    ):
        events_json = (
            self.service.events()
            .list(
                calendarId=self.calendar_id,
                maxResults=1000,  # max is 2500
                timeMin=start_after or None,
                timeMax=end_before or None,
                q=query or None,
                orderBy="startTime" if is_single_event else None,
                singleEvents=is_single_event,
            )
            .execute()["items"]
        )
        return [
            Event(
                event_json["id"],
                calendar_id=self.calendar_id,
                service=self.service,
                summary=event_json.get("summary"),
                start_time=event_json["start"]["dateTime"],
                end_time=event_json["end"]["dateTime"],
                location=event_json.get("location"),
                description=event_json.get("description"),
                status=event_json.get("status"),
                attendees=event_json.get("attendees", []),
                color_id=event_json.get("colorId"),
                attachments=event_json.get("attachments", []),
            )
            for event_json in events_json
        ]

    def get(self, id=None, summary=None):
        assert (id is None) ^ (
            summary is None
        ), f"Either event id only or summary only must be provided. id={id}, summary={summary}"

        try:
            if id is not None:
                event_json = (
                    self.service.events()
                    .get(calendarId=self.calendar_id, eventId=id)
                    .execute()
                )
                event = Event(
                    event_json["id"],
                    calendar_id=self.calendar_id,
                    service=self.service,
                    summary=event_json.get("summary"),
                    start_time=event_json["start"]["dateTime"],
                    end_time=event_json["end"]["dateTime"],
                    location=event_json.get("location"),
                    description=event_json.get("description"),
                    status=event_json.get("status"),
                    attendees=event_json.get("attendees", []),
                    color_id=event_json.get("colorId"),
                    attachments=event_json.get("attachments", []),
                )
                return event
            if summary is not None:
                events = (
                    self.service.events()
                    .list(calendarId=self.calendar_id, q=summary)
                    .execute()["items"]
                )
                for event in events:
                    if event.summary == summary:
                        event = Event(
                            event_json["id"],
                            calendar_id=self.calendar_id,
                            service=self.service,
                            summary=event_json.get("summary"),
                            start_time=event_json["start"]["dateTime"],
                            end_time=event_json["end"]["dateTime"],
                            location=event_json.get("location"),
                            description=event_json.get("description"),
                            status=event_json.get("status"),
                            attendees=event_json.get("attendees", []),
                            color_id=event_json.get("colorId"),
                            attachments=event_json.get("attachments", []),
                        )
                        return event
                raise ValueError(f"Event with summary {summary} not found.")
        except Exception as e:
            print("Error in get event: ", e)
            return None

    def create(
        self,
        summary,
        start_time,
        end_time,
        status="confirmed",
        location=None,
        description=None,
        attendees=None,  # list
        colorId=None,
        attachments=None,
        sendUpdates="none",
        guestsCanModify=None,
    ):
        is_supports_attachments = False
        if attachments:
            is_supports_attachments = True
        payload = Event.build_payload(
            summary,
            start_time,
            end_time,
            location,
            description,
            status,
            attendees,
            colorId,
            attachments,
            guestsCanModify,
        )
        event_json = (
            self.service.events()
            .insert(
                calendarId=self.calendar_id,
                body=payload,
                supportsAttachments=is_supports_attachments,
                sendUpdates=sendUpdates,
            )
            .execute()
        )
        print("=" * 20)
        print(event_json)
        print("=" * 20)
        return Event(
            event_json["id"],
            summary=event_json["summary"],
            start_time=event_json["start"]["dateTime"],
            end_time=event_json["end"]["dateTime"],
            status=event_json["status"],
            location=event_json.get("location", ""),
            description=event_json.get("description", ""),
            attendees=event_json.get("attendees", []),
            color_id=event_json.get("colorId"),
            calendar_id=self.calendar_id,
            service=self.service,
            attachments=event_json.get("attachments", []),
            html_link=event_json.get("htmlLink"),
        )

    def delete(self, id=None, summary=None):
        assert (id is None) ^ (
            summary is None
        ), f"Either event id only or summary only must be provided. id={id}, summary={summary}"
        if id is not None:
            return Event(
                id, calendar_id=self.calendar_id, service=self.service
            ).delete()
        if summary is not None:
            return self.get(summary=summary).delete()

    def update(
        self,
        id,
        summary=None,
        start_time=None,
        end_time=None,
        location=None,
        description=None,
        status=None,
        attendees=None,
    ):
        return Event(id, calendar_id=self.calendar_id, service=self.service).update(
            summary, start_time, end_time, location, description, status, attendees
        )

    def bulk_update(self, events):
        batch = self.service.new_batch_http_request()
        for event in events:
            batch.add(
                self.service.events().update(
                    calendarId=self.calendar_id, eventId=event.id, body=event.to_json()
                )
            )
        resp = batch.execute()
        return resp


class AccessCollection:
    def __init__(self, calendar_id, service=None):
        self.calendar_id = calendar_id
        self._service = service

    @property
    def service(self):
        if self._service is None:
            raise ValueError(
                "AccessCollection must be initialized with service in order to update or delete."
            )
        return self._service

    def list(self):
        acl = self.service.acl().list(calendarId=self.calendar_id).execute()
        return [acl_item["scope"]["value"] for acl_item in acl["items"]]

    def add(self, *emails):
        if len(emails) == 0:
            return []
        batch = self.service.new_batch_http_request()
        for email in emails:
            batch.add(
                self.service.acl().insert(
                    calendarId=self.calendar_id,
                    body={"role": "writer", "scope": {"type": "user", "value": email}},
                )
            )
        resp = batch.execute()
        return resp


class Calendar:
    def __init__(self, id, summary=None, service=None):
        """allow service to be uninitialized, but have to use .set_service(service) later"""
        self.id = id
        if summary is not None:
            self.summary = summary
        self._service = service
        self.events = EventCollection(self.id, self.service)
        self.access = AccessCollection(self.id, self.service)

    @property
    def service(self):
        if self._service is None:
            raise ValueError(
                "Calendar must be initialized with service in order to update or delete."
            )
        return self._service

    def delete(self):
        try:
            self.service.calendars().delete(calendarId=self.id).execute()
            return True
        except Exception as e:
            raise e

    def update(self, summary):
        payload = {
            "summary": summary,
        }
        calendar_json = (
            self.service.calendars().update(calendarId=self.id, body=payload).execute()
        )
        self.summary = calendar_json["summary"]
        return self

    def move_events(self, from_calendar_id, to_calendar_id, event_id):
        try:
            # Step 1: Retrieve the event from the source calendar
            event = (
                self.service.events()
                .get(calendarId=from_calendar_id, eventId=event_id)
                .execute()
            )

            # Remove 'id' and 'status' fields to avoid conflicts during insertion
            event.pop("id", None)
            event.pop("status", None)

            # Step 2: Insert the event into the destination calendar
            inserted_event = (
                self.service.events()
                .insert(calendarId=to_calendar_id, body=event)
                .execute()
            )
            print(
                f"Event inserted into calendar B with new event ID: {inserted_event['id']}"
            )

            # Step 3: Delete the event from the source calendar
            self.service.events().delete(
                calendarId=from_calendar_id, eventId=event_id
            ).execute()
            print(f"Event deleted from calendar A with event ID: {event_id}")

        except google.auth.exceptions.GoogleAuthError as auth_error:
            print(f"Authentication error: {auth_error}")
        except googleapiclient.errors.HttpError as http_error:
            print(f"HTTP error: {http_error}")
        except Exception as e:
            print(f"An error occurred: {e}")

    def __str__(self):
        return f"<Calendar ({self.id[:10]}...): {self.summary}>"

    __repr__ = __str__


class Event:

    def __init__(
        self,
        id,
        calendar_id=None,
        service=None,
        summary=None,
        start_time=None,
        end_time=None,
        location=None,
        description=None,
        status=None,
        attendees=None,
        attachments=None,
        color_id=None,
        html_link=None,
    ):
        self.id = id
        self.calendar_id = calendar_id
        self._service = service
        self.summary = summary
        self.start_time = start_time
        self.end_time = end_time
        self.location = location
        self.description = description
        self.status = status
        self.attendees = attendees
        self.color_id = color_id
        self.attachments = attachments
        self.html_link = html_link

    @property
    def service(self):
        if self._service is None:
            raise ValueError(
                "Event must be initialized with service in order to update or delete."
            )
        return self._service

    def delete(
        self,
        sendUpdates="none",
    ):
        try:
            self.service.events().delete(
                calendarId=self.calendar_id, eventId=self.id, sendUpdates=sendUpdates
            ).execute()
            return True
        except Exception as e:
            raise e

    def update(
        self,
        summary=None,
        start_time=None,
        end_time=None,
        location=None,
        description=None,
        status=None,
        attendees=None,
        attachments=None,
        colorId=None,
    ):
        if summary is None:
            summary = self.summary
        if start_time is None:
            start_time = self.start_time
        if end_time is None:
            end_time = self.end_time
        if location is None:
            location = self.location
        if description is None:
            description = self.description
        if status is None:
            status = self.status
        if attendees is None:
            attendees = self.attendees
        if attachments is None:
            attachments = self.attachments
        if colorId is None:
            colorId = self.color_id
        payload = Event.build_payload(
            summary=summary,
            start_time=start_time,
            end_time=end_time,
            location=location,
            description=description,
            status=status,
            attendees=attendees,
            attachments=attachments,
            colorId=colorId,
        )
        if len(payload) == 0:
            return self
        event_json = (
            self.service.events()
            .update(calendarId=self.calendar_id, eventId=self.id, body=payload)
            .execute()
        )  # This method does not support patch semantics and always updates the entire event resource. https://developers.google.com/calendar/api/v3/reference/events/update
        self.summary = event_json.get("summary")
        self.start_time = event_json["start"]["dateTime"]
        self.end_time = event_json["end"]["dateTime"]
        return self

    def to_json(self):
        return Event.build_payload(
            summary=self.summary,
            start_time=self.start_time,
            end_time=self.end_time,
            location=self.location,
            description=self.description,
            status=self.status,
            attendees=self.attendees,
            colorId=self.color_id,
        )

    __dict__ = to_json

    @staticmethod
    def build_payload(
        summary=None,
        start_time=None,
        end_time=None,
        location=None,
        description=None,
        status=None,
        attendees=None,
        colorId=None,
        attachments=None,
        guestsCanModify=None,
    ):
        payload = {}
        if summary is not None:
            payload["summary"] = summary
        if start_time is not None:
            payload["start"] = {"dateTime": start_time}
        if end_time is not None:
            payload["end"] = {"dateTime": end_time}
        if location is not None:
            payload["location"] = location
        if description is not None:
            payload["description"] = description
        if status is not None:
            payload["status"] = status
        if attendees is not None:
            payload["attendees"] = attendees
        if colorId is not None:
            payload["colorId"] = colorId
        if attachments is not None:
            payload["attachments"] = attachments
        if guestsCanModify is not None:
            payload["guestsCanModify"] = guestsCanModify
        return payload

    @staticmethod
    def convert_utc_to_django_date(utc_datetime_str):
        """
        Converts a UTC datetime string to a date in Django's current timezone.

        Args:
            utc_datetime_str (str): The UTC datetime string (e.g., '2024-06-01T01:40:00Z').

        Returns:
            date: The date object in Django's current timezone.
        """
        # Parse the datetime string to a naive datetime object (without timezone)
        utc_naive = datetime.strptime(utc_datetime_str, "%Y-%m-%dT%H:%M:%SZ")

        # Make the naive datetime object aware of the UTC timezone
        utc_aware = pytz.utc.localize(utc_naive)

        # Convert to Django's current timezone
        django_aware = timezone.localtime(utc_aware)

        return django_aware.date()

    @staticmethod
    def convert_utc_to_django_slot(
        utc_datetime_str,
        morning_slot_start=10,
        morning_slot_end=14,
        afternoon_slot_start=14,
        afternoon_slot_end=18,
    ):
        """
        Converts a UTC datetime string to a slot (morning or afternoon) in Django's current timezone.

        Args:
            utc_datetime_str (str): The UTC datetime string (e.g., '2024-06-01T01:40:00Z').

        Returns:
            str: The slot ('morning' or 'afternoon') in Django's current timezone.
        """
        # Parse the datetime string to a naive datetime object (without timezone)
        utc_naive = datetime.strptime(utc_datetime_str, "%Y-%m-%dT%H:%M:%SZ")

        # Make the naive datetime object aware of the UTC timezone
        utc_aware = pytz.utc.localize(utc_naive)

        # Convert to Django's current timezone
        django_aware = timezone.localtime(utc_aware)

        # Determine the slot based on the hour
        if morning_slot_start <= django_aware.hour < morning_slot_end:
            return "morning"
        elif afternoon_slot_start <= django_aware.hour < afternoon_slot_end:
            return "afternoon"
        else:
            return "morning/afternoon"

    @staticmethod
    def get_datetime_obj_from_gcalendar_event_time(event_time: str):
        event_time_obj = datetime.strptime(event_time, "%Y-%m-%dT%H:%M:%SZ")
        # Make the time_obj timezone-aware assuming it's in UTC
        event_time_obj = timezone.make_aware(event_time_obj, pytz.utc)
        event_time_obj = event_time_obj.astimezone(timezone.get_current_timezone())
        return event_time_obj

    def __str__(self):
        return f"<Event ({self.id[:10]}...): {self.summary}>"

    __repr__ = __str__


SCOPES = [
    "https://www.googleapis.com/auth/calendar",
    "https://www.googleapis.com/auth/calendar.events",
]


def get_credentials(service_account_data, scopes=SCOPES):
    """Can take in the service account data as path to file or dict"""
    if isinstance(service_account_data, dict):
        return service_account.Credentials.from_service_account_info(
            service_account_data, scopes=scopes
        )
    try:
        return service_account.Credentials.from_service_account_file(
            service_account_data, scopes=scopes
        )
    except Exception as e:
        raise e


def get_service(credentials):
    return build("calendar", "v3", credentials=credentials)
