from datetime import time

consultation_types = {
    "General Practice": 15,
    "Allergy": 30,
    "Aesthetics": 45,
    "Sports Medicine": 30,
    "Mental Health": 30,
    "Health Screening": 15,
    "Others": 15
}

branch_details = [
    {"id": "CCK", "name": "healthwerkz medical centre @ choa chu kang"},
    {"id": "KCS", "name": "healthwerkz medical centre @ bedok"},
    {"id": "OR", "name": "healthwerkz medical centre @ centrium"},
    {"id": "SBW", "name": "healthwerkz medical centre @ sembawang blk 355"}
]

doctor_with_branch_with_specialty = {
    "Dr. <PERSON><PERSON>": {
        "CCK": {
            "id": "DT-CCK000004",
            "specialties": ["General Practice", "Allergy", "Health Screening", "Others"],
            "schedule": {
                "monday": [{"time": (time(8, 30), time(13, 0))}],
                "wednesday": [{"time": (time(18, 0), time(21, 0))}],
                "thursday": [{"time": (time(8, 30), time(13, 0))}]
            }
        }
    },
    "Dr. <PERSON>": {
        "CCK": {
            "id": "DT-CCK000003",
            "specialties": ["General Practice", "Allergy", "Health Screening", "Others"],
            "schedule": {
                "monday": [{"time": (time(18, 0), time(21, 0))}],
                "tuesday": [{"time": (time(8, 30), time(13, 0)), "time": (time(18, 0), time(21, 0))}],
                "thursday": [{"time": (time(18, 0), time(21, 0))}],
                "friday": [{"time": (time(18, 0), time(21, 0))}],
                "saturday": [{"time": (time(8, 30), time(12, 30))}]
            }
        }
    },
    "Dr. Goh Hsin Kai": {
        "CCK": {
            "id": "DT-CCK000002",
            "specialties": ["General Practice", "Allergy", "Health Screening", "Others"],
            "schedule": {
                "wednesday": [{"time": (time(8, 30), time(13, 0))}],
                "saturday": [{"time": (time(8, 30), time(12, 30))}]
            }
        }
    },
    "Dr. Erwin Kay": {
        "CCK": {
            "id": "DT-CCK000001",
            "specialties": ["General Practice", "Allergy", "Aesthetics", "Sports Medicine", "Mental Health", "Health Screening", "Others"],
            "schedule": {
                "friday": [{"time": (time(8, 30), time(13, 0))}]
            }
        },
        "KCS": {
            "id": "DT000002",
            "specialties": ["General Practice", "Allergy", "Aesthetics", "Sports Medicine", "Mental Health", "Health Screening", "Others"],
            "schedule": {
                "tuesday": [{"time": (time(19, 0), time(21, 0))}],
                "thursday": [{"time": (time(19, 0), time(21, 0))}],
                "saturday": [{"time": (time(8, 30), time(12, 30))}]
            }
        },
        "OR": {
            "id": "DT-OR000003",
            "specialties": ["General Practice", "Allergy", "Aesthetics", "Sports Medicine", "Mental Health", "Health Screening", "Others"],
            "schedule": {
                "monday": [{"time": (time(9, 0), time(12, 0)), "time": (time(13, 0), time(18, 0))}],
                "tuesday": [{"time": (time(9, 0), time(12, 0)), "time": (time(13, 0), time(18, 0))}],
                "wednesday": [{"time": (time(9, 0), time(12, 0)), "time": (time(13, 0), time(18, 0))}],
                "thursday": [{"time": (time(9, 0), time(12, 0)), "time": (time(13, 0), time(18, 0))}],
                "friday": [{"time": (time(9, 0), time(12, 0)), "time": (time(13, 0), time(18, 0))}],
                "saturday": [{"time": (time(9, 0), time(12, 0))}]
            }
        },
        "SBW": {
            "id": "DT-SBW000003",
            "specialties": ["General Practice", "Allergy", "Aesthetics", "Sports Medicine", "Mental Health", "Health Screening", "Others"],
            "schedule": {
                "friday": [{"time": (time(8, 0), time(14, 0)), "exclude_first_last": True}]  # Added exclude flag
            }
        }
    },
    "Dr. Chong Swee Long": {
        "KCS": {
            "id": "DT000001",
            "specialties": ["General Practice", "Allergy", "Health Screening", "Others"],
            "schedule": {
                "monday": [{"time": (time(8, 30), time(14, 0)), "time": (time(19, 0), time(21, 0))}],
                "tuesday": [{"time": (time(8, 30), time(14, 0))}],
                "wednesday": [{"time": (time(8, 30), time(14, 0)), "time": (time(19, 0), time(21, 0))}],
                "thursday": [{"time": (time(8, 30), time(14, 0))}],
                "friday": [{"time": (time(8, 30), time(14, 0)), "time": (time(19, 0), time(21, 0))}],
                "saturday": [{"time": (time(8, 30), time(12, 30))}]
            }
        }
    },
    "Dr. Liau Kah Han": {
        "SBW": {
            "id": "DT-SBW000001",
            "specialties": ["General Practice", "Allergy", "Sports Medicine", "Health Screening", "Others"],
            "schedule": {
                "monday": [{"time": (time(8, 0), time(14, 0))}],
                "tuesday": [{"time": (time(8, 0), time(14, 0))}],
                "wednesday": [{"time": (time(8, 0), time(14, 0))}],
                "thursday": [{"time": (time(8, 0), time(14, 0))}],
                "friday": [{"time": (time(8, 0), time(14, 0))}],
                "sunday": [{"time": (time(8, 0), time(15, 0))}]
            }
        }
    },
    "Dr. J Susanna Wong": {
        "SBW": {
            "id": "DT-SBW000004",
            "specialties": ["General Practice", "Allergy", "Health Screening", "Others"],
            "schedule": {
                "monday": [{"time": (time(14, 0), time(21, 0))}],
                "tuesday": [{"time": (time(14, 0), time(21, 0))}],
                "wednesday": [{"time": (time(14, 0), time(21, 0))}],
                "thursday": [{"time": (time(14, 0), time(21, 0))}],
                "friday": [{"time": (time(14, 0), time(21, 0))}],
                "saturday": [{"time": (time(8, 0), time(15, 0))}]
            }
        }
    }
}