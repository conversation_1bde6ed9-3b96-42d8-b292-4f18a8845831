import pytz
from datetime import datetime

def is_operation_hours() -> bool:
    """
    Check if current time is within operation hours (5am-11:59pm)
    
    Returns:
        bool: True if current time is within operation hours, False otherwise
    """
    # Get Singapore timezone
    sg_tz = pytz.timezone('Asia/Singapore')
    
    # Get current time in Singapore
    sg_time = datetime.now(sg_tz)
    
    # Get current hour (0-23)
    current_hour = sg_time.hour
    
    # Operation hours are 5am-11:59pm
    return 5 <= current_hour <= 23