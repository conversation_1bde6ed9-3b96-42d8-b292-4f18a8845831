def get_client_context():
    # Connect to chromadb
    # Pull out the document
    # Ask CHATGPT to summarize into the following
    # - company metadata, services and additional details
    return [
        {
            "company_metadata": 
            {
                "Company": 
                {
                    "Name": "Amelio Medical Clinic",
                    "Description": "Amelio Medical Clinic is a healthcare provider located in Singapore, specializing in general medical services, preventive health care, and wellness programs. The clinic is committed to offering personalized patient care, utilizing modern medical practices and technologies to enhance patient outcomes. With a focus on holistic health, Amelio Medical Clinic aims to support patients in achieving optimal wellness through comprehensive medical assessments, treatment plans, and health supplements."
                },
                "Services": 
                {
                    "General Medical Consultations": "Comprehensive consultations for various health concerns.",
                    "Preventive Health Screenings": "Health screenings to detect and prevent potential health issues.",
                    "Wellness Programs": "Personalized programs aimed at promoting overall well-being.",
                    "Mental Health Support": "Services addressing mental health needs.",
                    "Dermatological Services": "Skin-related consultations and treatments.",
                    "Child Health Services": "Medical services tailored for children's health."
                },
                "Additional Details": 
                {
                    "Contact Information": 
                    {
                        "Address": "290 Orchard Road, Paragon #06-21, Singapore 238859",
                        "Phone": "+65 6836 3367"
                    },
                    "Operating Hours": 
                    {
                        "Weekdays": "8:30 AM - 12:00 PM, 7:00 PM - 9:00 PM",
                        "Saturday": "8:30 AM - 12:30 PM"
                    }
                }
            }
        }
    ]