import logging
import traceback
import app.utility.chat_constant as chat_constant
from app.crew_teams.tools.chromadb_tools import initialize_chroma_and_retrieve
import app.utility.general_helper as utility_general_helper

from crewai.tools import tool
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.tools.amelio.helper_functions.check_operation_hours import is_operation_hours
from app.crew_teams.utils.gpt_integration import get_gpt_response
from openai import OpenAI
from tenant_schemas.utils import schema_context
from langchain.embeddings.openai import OpenAIEmbeddings

logger = logging.getLogger(__name__)


@tool("answer_beauty_service_questions")
def answer_beauty_service_questions(
    conversation_id: int,
    incoming_message: str = None,
    schema_name: str = None,
):
    """
    Main function to answer beauty service questions.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        incoming_message (str): Optional. The latest message from the customer.
        schema_name (str): Optional. The name of the schema to use.

    Returns:
        dict or str: A dictionary containing the appointment details or a prompt asking the customer for missing information. 
        If all information is confirmed, summarizes the appointment details for customer approval.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Check if the conversation is within operation hours.
        3. Answer the beauty service questions
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool answer_beauty_service_questions is called with conversation_id: {conversation_id}", extra={
                "conversation_id_": conversation_id,
                "incoming_message_": incoming_message,
                "schema_name_": schema_name
            }
        )

        # Step 1: Check if the conversation is in operation hours
        if not is_operation_hours():
            response_to_customer = "Sorry, we are currently operating between 5am-11:59pm only. Please try again during operation hours."

            return response_to_customer

        openai_client = OpenAI()

        # First, analyze the complexity of the beauty question
        complexity_prompt = f"""
        Analyze the following beauty service question and determine if it's simple or complex:

        Question: {incoming_message}

        Classification criteria:
        SIMPLE: General service information, pricing, availability, basic treatment descriptions, general skincare tips
        Examples: "What facials do you offer?", "How much does laser treatment cost?", "What are your operating hours?"

        COMPLEX: Specific skin conditions, personalized treatment recommendations, medical-grade procedures, specific skin concerns requiring assessment
        Examples: "What treatment is best for my acne scars?", "I have sensitive skin, what should I do?", "Is this treatment safe for my skin condition?"

        Respond with only: SIMPLE or COMPLEX
        """

        complexity_response = get_gpt_response(openai_client, [
                                               {"role": "user", "content": complexity_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

        if "COMPLEX" in complexity_response.upper():
            # Answer complex questions but provide customer support fallback
            retriever_content = initialize_chroma_and_retrieve.run(
                incoming_message, 'external_' + schema_name)

            complex_prompt = f"""
            You are a professional beauty service assistant. Answer the customer's beauty question to the best of your ability using available information, but be cautious about giving specific medical or personalized advice.

            Customer's question:
            {incoming_message}
            
            Relevant beauty service information:
            {retriever_content}
            
            Instructions:
            1. Provide helpful general information based on the context
            2. Be clear about limitations - avoid specific medical advice or diagnosis
            3. Keep your response informative but general
            4. If the context doesn't contain enough information, acknowledge this
            5. Focus on general guidance rather than specific treatment recommendations

            Please provide your response:
            """

            complex_response = get_gpt_response(openai_client, [
                                                {"role": "user", "content": complex_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

            # Add customer support fallback line
            return f"{complex_response}\n\nIf the above answer doesn't fully address your question or you need personalized consultation, simply send the message 'I want to speak to your staff' and we'll connect you with our beauty specialists who can assist you personally."

        # Handle simple beauty questions
        retriever_content = initialize_chroma_and_retrieve.run(
            incoming_message, 'external_' + schema_name)

        prompt = f"""
        You are a professional beauty service assistant. Your task is to provide general information about beauty services for simple, non-personalized questions.

        Customer's question:
        {incoming_message}
        
        Relevant beauty service information:
        {retriever_content}
        
        Instructions:
        1. Provide general service information only - no personalized treatment recommendations
        2. Use the provided context to answer the customer's question about services, pricing, and availability
        3. If the context doesn't contain enough information, politely state that you don't have sufficient information to answer fully
        4. Keep your response concise and directly relevant to the question
        5. For specific skin concerns or personalized recommendations, suggest consulting with beauty specialists
        6. If appropriate, suggest follow-up questions the customer might want to ask

        Please provide your response:
        """

        response = get_gpt_response(openai_client, [
                                    {"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

        return response
    except Exception as e:
        logger.error(f"Tool gather_event_information failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "incoming_message_": incoming_message,
            "schema_name_": schema_name
        })
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
