import logging
import traceback
import app.utility.chat_constant as chat_constant

from crewai.tools import tool
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.tools.amelio.helper_functions.check_operation_hours import is_operation_hours
from app.crew_teams.utils.gpt_integration import get_gpt_response
from openai import OpenAI
from app.crew_teams.tools.chromadb_tools import initialize_chroma_and_retrieve

logger = logging.getLogger(__name__)


@tool("answer_health_questions")
def answer_health_questions(
    conversation_id: int,
    incoming_message: str = None,
    schema_name: str = None,
):
    """
    Main function to answer health questions.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        incoming_message (str): Optional. The latest message from the customer.
        schema_name (str): Optional. The name of the schema to use.

    Returns:
        dict or str: A dictionary containing the appointment details or a prompt asking the customer for missing information. 
        If all information is confirmed, summarizes the appointment details for customer approval.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Check if the conversation is within operation hours.
        3. Answer the health questions
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool answer_health_questions is called with conversation_id: {conversation_id}", extra={
                "conversation_id_": conversation_id,
                "incoming_message_": incoming_message,
                "schema_name_": schema_name
            }
        )

        # Step 1: Check if the conversation is in operation hours
        if not is_operation_hours():
            response_to_customer = "Sorry, we are currently operating between 5am-11:59pm only. Please try again during operation hours."

            return response_to_customer

        openai_client = OpenAI()

        # First, analyze the complexity of the health question
        complexity_prompt = f"""
        Analyze the following health question and determine if it's simple or complex:

        Question: {incoming_message}

        Classification criteria:
        SIMPLE: General health information, basic symptoms, common conditions, preventive care tips
        Examples: "What are symptoms of common cold?", "How to prevent flu?", "What is normal blood pressure?"

        COMPLEX: Specific medical diagnosis, treatment recommendations, medication advice, serious symptoms, personal medical situations
        Examples: "What medicine should I take for my chest pain?", "Is this rash serious?", "Should I stop my medication?"

        Respond with only: SIMPLE or COMPLEX
        """

        complexity_response = get_gpt_response(openai_client, [
                                               {"role": "user", "content": complexity_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

        if "COMPLEX" in complexity_response.upper():
            # Answer complex questions but provide customer support fallback
            retriever_content = initialize_chroma_and_retrieve.run(
                incoming_message, 'external_' + schema_name)

            complex_prompt = f"""
            You are a professional healthcare information assistant. Answer the customer's health question to the best of your ability using available information, but be very cautious about giving specific medical advice or diagnosis.

            Customer's question:
            {incoming_message}
            
            Relevant health information:
            {retriever_content}
            
            Instructions:
            1. Provide helpful general health information based on the context
            2. Be very clear about limitations - avoid specific medical advice, diagnosis, or treatment recommendations
            3. Keep your response informative but general
            4. Always include appropriate disclaimers about consulting healthcare professionals
            5. If the context doesn't contain enough information, acknowledge this clearly
            6. Focus on general health education rather than specific medical guidance

            Please provide your response:
            """

            complex_response = get_gpt_response(openai_client, [
                                                {"role": "user", "content": complex_prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

            # Add customer support fallback line
            return f"{complex_response}\n\nIf the above answer doesn't fully address your question or you need specific medical advice, simply send the message 'I want to speak to your staff' and we'll connect you with our healthcare professionals who can assist you personally."

        # Handle simple health questions
        retriever_content = initialize_chroma_and_retrieve.run(
            incoming_message, 'external_' + schema_name)

        prompt = f"""
        You are a professional healthcare information assistant. Your task is to provide general health information for simple, non-diagnostic questions.

        Customer's question:
        {incoming_message}
        
        Relevant health information:
        {retriever_content}
        
        Instructions:
        1. Provide general health information only - no specific medical advice or diagnosis
        2. Use the provided context to answer the customer's question
        3. If the context doesn't contain enough information, provide general guidance and suggest consulting healthcare professionals
        4. Keep your response concise and directly relevant to the question
        5. Always include a disclaimer that this is general information and not medical advice
        6. For any specific medical concerns, recommend consulting with healthcare professionals

        Please provide your response:
        """

        response = get_gpt_response(openai_client, [
                                    {"role": "user", "content": prompt}], model=chat_constant.GPT_4_MINI_MODEL_CHOICES[0])

        return response
    except Exception as e:
        logger.error(f"Tool gather_event_information failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "incoming_message_": incoming_message,
            "schema_name_": schema_name
        })
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
