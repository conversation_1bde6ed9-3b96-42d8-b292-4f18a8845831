import logging
import traceback

from typing import Optional
from crewai.tools import tool
from app.crew_teams.tools.happier.formulate_response import formulate_response
from app.crew_teams.tools.amelio.helper_functions.check_operation_hours import is_operation_hours
from app.crew_teams.tools.amelio.helper_functions.google_calendar_service_for_reschedule_appointment import GoogleCalendar

logger = logging.getLogger(__name__)

# Function to collect reschedule appointment information
def collect_reschedule_appointment_information(date: Optional[str], time: Optional[str], schema_name: str, incoming_message: str) -> str:
    """
    Checks for missing reschedule appointment information and returns a concise prompt requesting only the required information.
    
    Args:
        date (str): The date of the reschedule appointment.
        time (str): The time of the reschedule appointment.
        schema_name (str): The name of the schema.
        incoming_message (str): The latest message from the customer

    Returns:
        str: A message requesting the missing details. If all details are provided, returns an empty string.
    """
    missing_info = []
    if not date:
        missing_info.append("date")
    if not time:
        missing_info.append("time")

    if missing_info:
        response_to_customer = f"""Thanks for confirming your appointment. Please let us know the date and time of your reschedule appointment."""
        return response_to_customer

    return ""

@tool("gather_reschedule_appointment_date_time_information")
def gather_reschedule_appointment_date_time_information(
    conversation_id: int,
    name: str = None,
    doctor: str = None,
    branch: str = None,
    date: str = None,
    time: str = None,
    incoming_message: str = None,
    schema_name: str = None,
):
    """
    After customer provided their all information and confirm this is his/her appointment will continue with this collect date and time function

    This function collects and validates appointment-related details provided by the customer, identifies any missing 
    information, recommends suitable services, and generates a proposal if all details are confirmed. It ensures 
    a seamless flow in gathering customer information and handles missing or incomplete inputs by returning prompts.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        
        name (str): Optional. The name of the customer.
        doctor (str): Optional. The name of the doctor.
        branch (str): Optional. The branch of the doctor.
        date (str): Optional. The date of the appointment.
        time (str): Optional. The time of the appointment.
        incoming_message (str): Optional. The latest message from the customer.
        schema_name (str): Optional. The name of the schema to use.

    Returns:
        dict or str: A dictionary containing the appointment details or a prompt asking the customer for missing information. 
        If all information is confirmed, summarizes the appointment details for customer approval.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Check if the conversation is within operation hours.
        3. Gather missing reschedule appointment information if `date` and `time` is missing.
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool gather_reschedule_appointment_date_time_information is called with conversation_id: {conversation_id}", extra={
                "conversation_id_": conversation_id,
                "name_": name,
                "doctor_": doctor,
                "branch_": branch,
                "date_": date,
                "time_": time,
                "incoming_message_": incoming_message,
                "schema_name_": schema_name
            }
        )

        # Step 1: Check if the conversation is in operation hours
        if not is_operation_hours():
            response_to_customer = "Sorry, we are currently operating between 5am-11:59pm only. Please try again during operation hours."
            
            return response_to_customer
        
        # Step 2: Gather missing reschedule appointment information if `date` and `time` is missing.
        reschedule_info_status = collect_reschedule_appointment_information(date, time, schema_name, incoming_message)
        if reschedule_info_status:
            return reschedule_info_status
        
        # Step 3: Save reschedule appointment to google calendar
        google_calendar = GoogleCalendar()
        google_calendar.book(
            summary=f"Appointment for {name} with Dr {doctor}",
            date_str=date,
            time_str=time,
            full_address=f"{branch}",
            remarks="",
            color="1",
        )
        response_to_customer = f"Thanks for your patience. Your appointment has been successfully rescheduled to {date} at {time}. Please check your calendar for the updated appointment details."     
        return response_to_customer

    except Exception as e:
        logger.error(f"Tool gather_event_information failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "name_": name,
            "doctor_": doctor,
            "branch_": branch,
            "date_": date,
            "time_": time,
            "incoming_message_": incoming_message,
            "schema_name_": schema_name
        })
        logger.error(traceback.format_exc())
        return f"""Tool execution failed! Need to rerun the tool again. Error: {e}"""
