import logging
import traceback
from crewai.tools import tool
from django_tenants.utils import schema_context
from app.crew_teams.tools.amelio.helper_functions.send_message_to_amelio_customer_support import send_message_to_amelio_customer_support
from app.chats.chat.models import Conversation

logger = logging.getLogger(__name__)


@tool("handle_customer_support_request")
def handle_customer_support_request(
    conversation_id: int,
    incoming_message: str = None,
    schema_name: str = None,
    customer_name: str = "",
    customer_contact: str = ""
):
    """
    Handle customer support requests when users want to speak to staff.
    This tool detects phrases like 'I want to speak to your staff' and forwards
    the request to customer support.

    Args:
        conversation_id (int): Required. The unique identifier for the conversation.
        incoming_message (str): Optional. The latest message from the customer.
        schema_name (str): Optional. The name of the schema to use.
        customer_name (str): Optional. Customer's name if available.
        customer_contact (str): Optional. Customer's contact if available.

    Returns:
        str: A response message confirming the support request has been forwarded.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Detect if the message is a customer support request.
        3. Send the request to customer support via WhatsApp.
        4. Return confirmation message to the customer.
    """
    try:
        if not conversation_id:
            raise Exception("Missing conversation_id")

        logger.info(
            f"Tool handle_customer_support_request is called with conversation_id: {conversation_id}",
            extra={
                "conversation_id_": conversation_id,
                "incoming_message_": incoming_message,
                "schema_name_": schema_name,
                "customer_name_": customer_name,
                "customer_contact_": customer_contact
            }
        )

        # Common phrases that indicate customer wants to speak to support
        support_request_phrases = [
            "i want to speak to your staff",
            "speak to your staff",
            "talk to your staff",
            "contact your staff",
            "speak to a human",
            "talk to a human",
            "speak to someone",
            "talk to someone",
            "customer support",
            "customer service",
            "human support",
            "live support",
            "speak to agent",
            "talk to agent",
            "real person",
            "human agent",
            "staff",
            "representative",
            "speak to staff",
            "talk to staff",
            "human help",
            "live chat",
            "live person"
        ]

        # Check if the incoming message contains any support request phrases
        message_lower = incoming_message.lower() if incoming_message else ""
        is_support_request = any(
            phrase in message_lower for phrase in support_request_phrases)

        # Always handle customer support requests, regardless of other logic
        if is_support_request:
            logger.info(
                f"Customer support request detected in message: {incoming_message}")

            # Get customer contact from conversation if not provided
            if not customer_contact:
                try:
                    with schema_context(schema_name):
                        conversation = Conversation.objects.filter(
                            id=conversation_id).first()
                        if conversation:
                            customer_contact = conversation.third_party_id
                except Exception as e:
                    logger.warning(f"Could not retrieve customer contact: {e}")

            # Get recent conversation context for better support understanding
            conversation_context = ""
            try:
                with schema_context(schema_name):
                    conversation = Conversation.objects.filter(
                        id=conversation_id).first()
                    if conversation:
                        # Get recent messages for context (last 10 messages)
                        recent_messages = conversation.messages.order_by(
                            '-timestamp')[:10]
                        context_parts = []
                        # Reverse to get chronological order
                        for msg in reversed(recent_messages):
                            sender = "Customer" if msg.sender == "user" else "Bot"
                            context_parts.append(f"{sender}: {msg.message}")
                        conversation_context = "\n".join(context_parts)
            except Exception as e:
                logger.warning(f"Could not retrieve conversation context: {e}")
                conversation_context = "Context unavailable"

            # Send the support request
            result = send_message_to_amelio_customer_support(
                conversation_id=conversation_id,
                schema_name=schema_name,
                customer_message=incoming_message or "",
                customer_name=customer_name or "Customer",
                customer_contact=customer_contact or "",
                conversation_context=conversation_context
            )

            if result["status"] == "success":
                logger.info(f"Customer support request sent successfully")
                return result["bot_response"]
            else:
                logger.error(
                    f"Customer support request failed: {result.get('message', 'Unknown error')}")
                return result.get("bot_response", "We apologize, but there was an issue processing your support request. Please try again later.")

        # If it's not a support request, we shouldn't be here - this tool should only be called for support requests
        logger.warning(
            f"handle_customer_support_request tool called without customer support request phrase in message: {incoming_message}")
        return "I understand you may need additional help. If you'd like to speak to our customer support team, please send the message 'I want to speak to your staff' and we'll connect you immediately."

    except Exception as e:
        logger.error(f"Tool handle_customer_support_request failed with error: {e}", extra={
            "conversation_id_": conversation_id,
            "incoming_message_": incoming_message,
            "schema_name_": schema_name
        })
        logger.error(traceback.format_exc())
        return f"We apologize, but there was an issue processing your support request. Please try again later or contact us directly."
