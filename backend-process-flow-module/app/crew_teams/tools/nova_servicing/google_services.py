import re
import json
import holidays
from datetime import datetime, timedelta, date

import gspread
import pytz
from google.oauth2.service_account import Credentials

from app.crew_teams.tools.nova_servicing import google_calendar_helper
from app.integrations.integration.models import TenantCredentials
from app.chats.customer.models import Customer
from app.chats.customer.serializers import CustomerSerializer

SG_HOLIDAYS = holidays.SG()


class GoogleCalendar:
    def __init__(self):
        self.MANUAL_BLOCK_DATES = [
            date(2024, 10, 28),  # Manual block
            date(2024, 10, 29),  # Manual block
            date(2024, 10, 30),  # Manual block
            date(2024, 11, 1),  # Manual block
            date(2024, 11, 2),  # Manual block
            date(2024, 11, 4),  # Manual block
            date(2024, 11, 5),  # Manual block
        ]
        self.SG_TIMEZONE = pytz.timezone("Asia/Singapore")
        self.EVENT_START_TIME = 10  # 10am
        self.MIN_BUFFER_DAYS = 5  # Minimum 5 days buffer
        self.BUFFER_DAYS = self.get_buffer_days(self.MIN_BUFFER_DAYS)
        self.EVENT_DURATION_MINS = 40  # 40 mins

        self.NUM_SERVICING_PER_TEAM_PER_BLOCK = 5  # 5 appt per day

        self.service_account_key_json = json.loads(
            TenantCredentials.objects.get(
                key="GCALENDAR_SERVICE_ACCOUNT_KEY_JSON"
            ).value
        )
        self.credentials = google_calendar_helper.get_credentials(
            self.service_account_key_json
        )
        self.service = google_calendar_helper.get_service(self.credentials)
        self.unassigned_calendar_id = TenantCredentials.objects.get(
            key="UNASSIGNED_CALENDAR_ID"
        ).value
        self.t1_calendar_id = TenantCredentials.objects.get(key="T1_CALENDAR_ID").value
        self.t2_calendar_id = TenantCredentials.objects.get(key="T2_CALENDAR_ID").value
        self.calendar_id = self.unassigned_calendar_id
        self.calendar = google_calendar_helper.Calendar(
            self.calendar_id, "Service Schedule", self.service
        )
        self.regex_team = re.compile(r"^\[T\d+\]")

    def get_buffer_days(self, min_days):
        """
        Calculate the number of buffer days needed to ensure at least min_days working day(s) (excluding today).
        """
        start_date = datetime.now(self.SG_TIMEZONE).date() + timedelta(days=1)
        buffer_days = 0
        working_days = 0

        while working_days < min_days:
            current_date = start_date + timedelta(days=buffer_days)
            is_weekday = current_date.weekday() < 5
            is_not_blocked = current_date not in self.MANUAL_BLOCK_DATES
            is_not_holiday = current_date not in SG_HOLIDAYS

            if is_weekday and is_not_blocked and is_not_holiday:
                working_days += 1

            buffer_days += 1

        return buffer_days

    def get_latest_available_date(self, date):
        """
        Adjust the given date backward by MIN_BUFFER_DAYS and ensure it lands on a valid working day.
        If the resulting date is on a weekend, holiday, or blocked date, move forward to the next valid working day.
        """
        target_date = (
            datetime.strptime(date, "%Y-%m-%d") - timedelta(days=self.MIN_BUFFER_DAYS)
        ).date()

        while (
            target_date.weekday() >= 5
            or target_date in self.MANUAL_BLOCK_DATES
            or target_date in SG_HOLIDAYS
        ):
            target_date += timedelta(days=1)

        return target_date

    def get_remaining_slot(self):
        today = datetime.now(self.SG_TIMEZONE)
        today = today.replace(hour=0, minute=0, second=0, microsecond=0)
        search_start_date = today + timedelta(days=self.BUFFER_DAYS)
        search_end_date = search_start_date + timedelta(days=60)  # Adjust as needed
        events = self.calendar.events.list(
            start_after=search_start_date.isoformat(),
            end_before=search_end_date.isoformat(),
        )
        available_slots = {}

        for days in range((search_end_date - search_start_date).days):
            check_date = (search_start_date + timedelta(days=days)).date()
            if (
                check_date.weekday() == 5
                or check_date.weekday() == 6
                or check_date in self.MANUAL_BLOCK_DATES
                or check_date in SG_HOLIDAYS
            ):
                continue
            available_slots[check_date] = self.NUM_SERVICING_PER_TEAM_PER_BLOCK

        for event in events:
            event_date = event.get_datetime_obj_from_gcalendar_event_time(
                event.start_time
            ).date()
            if not event.summary:
                continue
            if "block" in event.summary.lower():
                available_slots.pop(event_date, None)
                continue
            if event_date in available_slots:
                available_slots[event_date] -= 1

        remaining_slots = {k: v for k, v in available_slots.items() if v > 0}
        return remaining_slots

    def check_general_availability(self):
        """
        postal_code: str, postal code of the service location
        invoice_number: str, invoice number of the serviceP
        """
        remaining_slots = self.get_remaining_slot()
        # group by date, combine slots, {date} {slots}, no need for count
        grouped_remaining_slots = set()
        for date, count in remaining_slots.items():
            grouped_remaining_slots.add(date)
        grouped_remaining_slots = sorted(list(grouped_remaining_slots))
        # chr(10) is newline, chr(47) is forward slash

        # available_slot_str = f"""Available slots for servicing:
        #     {chr(10).join([f'{date.strftime("%A, %d %B %Y")} {chr(47).join(slots)}' for date, slots in grouped_remaining_slots.items()])}"""
        available_slot_str = f"""Available slots for servicing:
                {chr(10).join([f'{date.strftime("%A, %d %B %Y")}' for date in grouped_remaining_slots])}"""
        return available_slot_str

    def get_earliest_five_available_dates(self):
        remaining_slots = self.get_remaining_slot()
        earliest_five_dates = list(remaining_slots.keys())[:5]
        earliest_five_dates_str = ", ".join(
            [date.strftime("%A, %d %B %Y") for date in earliest_five_dates]
        )
        return earliest_five_dates_str

    def check_slot_availability(self, check_date):
        """
        check_date: datetime.date, the date to check for availability
        """
        remaining_slots = self.get_remaining_slot()
        # Check if the specific slot on the given date is available
        if check_date in remaining_slots and remaining_slots[check_date] > 0:
            return True
        return False

    def book(
        self,
        summary,
        date_str,
        full_address,
        attachments=[],
        remarks="",
        color="",
    ):
        """
        date_str: str, date in YYYY-MM-DD format
        slot: str, one of 'morning' or 'afternoon'
        postal_code: str, postal code of the service location
        full_address: str, full address of the service location
        invoice_number: str, invoice number of the service
        remarks: str, optional, additional details for the service
        """

        try:
            date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError(f"Invalid date: {date_str}, require YYYY-MM-DD format")
        earliest_booking_date = datetime.now(self.SG_TIMEZONE).date() + timedelta(
            days=self.BUFFER_DAYS
        )
        if date < earliest_booking_date:
            raise ValueError(
                f"Date must be at least {earliest_booking_date} ({self.BUFFER_DAYS} days from today)"
            )

        start_time = self.SG_TIMEZONE.localize(
            datetime(date.year, date.month, date.day, self.EVENT_START_TIME)
        )
        end_time = start_time + timedelta(minutes=self.EVENT_DURATION_MINS)
        try:
            event = self.calendar.events.create(
                summary=summary,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                location=full_address,  # TODO: cast to actual postal code
                description=remarks,  # TODO: add details like issue, preferences here?
                attachments=attachments,
                colorId=google_calendar_helper.EVENT_COLOR_VALUE_TO_ID_MAPPING.get(
                    color, "1"
                ),
            )
            print(
                f"The servicing is booked (between {self.convert24to12(self.EVENT_START_TIME)}-{self.convert24to12(self.EVENT_START_TIME + self.EVENT_DURATION_MINS)}) on {date_str}."
            )
            return event
        except Exception as e:
            raise ValueError(f"Failed to book the servicing: {e}")

    def get_event_booking_event(self, so_number, contact_number):
        """[{so_number}|{contact_number}|WA:{third_party_contact}] Service@{postal_code} [{status}]"""
        today = datetime.now(self.SG_TIMEZONE)
        today = today.replace(hour=0, minute=0, second=0, microsecond=0)
        events = self.calendar.events.list(
            query=f"[{so_number}|{contact_number}",
            start_after=today.isoformat(),
        )
        if not events:
            return None
        event = events[0]
        return event

    def get_event_from_available_calendars(
        self, event_id
    ) -> None | google_calendar_helper.Event:
        event = self.calendar.events.get(id=event_id)
        if not event:
            print(f"Event with id {event_id} not found in any calendar.")

        return event

    def get_approved_event_booking_event(
        self, so_number, contact_number
    ) -> None | google_calendar_helper.Event:
        """[{so_number}|{contact_number}|WA:{third_party_contact}] Service@{postal_code} [{status}]"""

        today = datetime.now(self.SG_TIMEZONE)
        today = today.replace(hour=0, minute=0, second=0, microsecond=0)
        events = self.calendar.events.list(
            query=f"[{so_number}|{contact_number}",
            start_after=today.isoformat(),
        )
        approved_events: list[google_calendar_helper.Event] = []
        for event in events:
            if "Approved" in str(event.summary):
                approved_events.append(event)

        if not approved_events:
            return None
        approved_event = approved_events[0]
        return approved_event

    @staticmethod
    def convert24to12(hr):
        return f'{hr % 12 or 12}{"am" if hr < 12 else "pm"}'


def get_customer_info(so_number, contact_number):
    # extract only the number from so_number

    # Clean up "-"
    so_number = re.sub(r"-", "", so_number)
    # Convert inputs to match the longest number
    matches = re.findall(r"\d+", so_number)
    if matches:
        so_number = max(matches, key=len)

    contact_number = contact_number.replace(" ", "").lstrip("+65")
    contact_numbers = contact_number.split("/")
    customers = []
    for contact_number in contact_numbers:
        customers.extend(
            Customer.objects.filter(
                so_number__contains=so_number, contact_number__contains=contact_number
            )
        )

    if not customers:
        return None

    customer = customers[0]
    customer_data = CustomerSerializer(customer).data
    for i in range(1, len(customers)):
        customer_data[
            "stock_description"
        ] += f",{CustomerSerializer(customers[i]).data['stock_description']}"
        customer_data[
            "stock_code"
        ] += f",{CustomerSerializer(customers[i]).data['stock_code']}"
    return customer_data


class GoogleSheetHelper:
    def __init__(self, spreadsheet_url, is_globe_3=False):
        self.spreadsheet_url = spreadsheet_url
        # Dictionary containing your service account credentials
        credentials_dict = json.loads(
            TenantCredentials.objects.get(key="GSHEET_SERVICE_ACCOUNT_KEY_JSON").value
        )

        # Scope for Google Sheets API
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/spreadsheets",
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/drive",
        ]

        # Authenticate using the credentials dictionary
        creds = Credentials.from_service_account_info(credentials_dict, scopes=scope)
        client = gspread.authorize(creds)

        # Open the Google Sheet by URL
        spreadsheet = client.open_by_url(self.spreadsheet_url)
        self.worksheet = spreadsheet.get_worksheet(0)
        self.last_index = len(self.worksheet.get_all_values())
        self.lookup_dict = self.create_customer_lookup_dict()
        # # Print out last 10 lookup_dict
        # print(
        #     "lookup dict",
        #     json.dumps(list(self.lookup_dict.items())[-10:], indent=4),
        # )

    def get_opened_spreadsheet(self):
        return self.client.open_by_url(self.spreadsheet_url)

    def get_worksheet(self, spreadsheet):
        return spreadsheet.get_worksheet(0)

    # ----------------------------------- Nova Servicing Google Sheet -----------------------------------
    def append_new_row(self, new_row_values):
        self.worksheet.append_row(new_row_values)
        print("New row added successfully.")

    def get_so_number_count(self, so_number):
        counter = 1
        records = self.worksheet.get_all_records()
        for record in records:
            existed_so = record["SO number"]
            if str(so_number).upper() == str(existed_so).upper():
                counter += 1
        return counter

    # ----------------------------------- GLOBE 3 -----------------------------------
    # Function to create a lookup dictionary in globe3
    def create_customer_lookup_dict(self):
        records = self.worksheet.get_all_records()
        lookup_dict = {}

        for record in records:
            full_so_number = str(record["SO Number"]).strip().upper()
            extracted_so_number = str(record["SO Number"]).strip().upper()
            # Replace out any non-alphanumeric characters
            extracted_so_number = re.sub(r"[^a-zA-Z0-9]", "", extracted_so_number)
            # Grab the longest series of number groups can be found
            matches = re.findall(r"\d+", extracted_so_number)
            if matches:
                extracted_so_number = max(matches, key=len)
            contact_number = (
                str(record["Contact Number"]).replace(" ", "").lstrip("+65")
            )

            if lookup_dict.get((extracted_so_number, contact_number)):
                lookup_dict[(extracted_so_number, contact_number)][
                    "Stock Description"
                ] += f',{record["Stock Description"]}'
                lookup_dict[(extracted_so_number, contact_number)][
                    "Stock Code"
                ] += f',{record["Stock Code"]}'
            else:
                lookup_dict[(extracted_so_number, contact_number)] = record
                lookup_dict[(extracted_so_number, contact_number)][
                    "Stock Description"
                ] = str(
                    lookup_dict[(extracted_so_number, contact_number)][
                        "Stock Description"
                    ]
                )
                lookup_dict[(extracted_so_number, contact_number)]["Stock Code"] = str(
                    lookup_dict[(extracted_so_number, contact_number)]["Stock Code"]
                )
                lookup_dict[(extracted_so_number, contact_number)][
                    "Full SO Number"
                ] = full_so_number

        return lookup_dict

    # Function to check SO number and Contact number using the lookup dictionary in globe3
    def get_customer_info(self, so_number, contact_number):
        # extract only the number from so_number

        # Clean up "-"
        so_number = re.sub(r"-", "", so_number)
        # Convert inputs to match the longest number
        matches = re.findall(r"\d+", so_number)
        if matches:
            so_number = max(matches, key=len)

        contact_number = contact_number.replace(" ", "").lstrip("+65")
        for key in self.lookup_dict.keys():
            if so_number in key[0] and contact_number == key[1]:
                so_number = key[0]
                break

        return self.lookup_dict.get((so_number, contact_number), {})
