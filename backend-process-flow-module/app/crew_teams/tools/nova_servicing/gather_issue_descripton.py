from crewai.tools import tool

from app.chats.chat.models import Conversation
from app.crew_teams.tools.nova_servicing.gather_customer_information import (
    ConfirmPersonalInformationResponse,
    confirm_personal_information,
)
from app.crew_teams.tools.nova_servicing.google_services import GoogleCalendar
from app.documents.document.models import KnowledgeImage
from app.utility.models import MACHINE_TEXT
from backend.settings import AWS_STORAGE_BUCKET_NAME


def collect_problem_description(
    customer_contact_number: str = "",
    sales_order_number: str = "",
    product: str = "",
    issue_description: str = "",
    is_address_confirmed_or_provided: str = "",
    attachment_ids: list[int] = [],
    how_many_times_images_were_asked: str = "0",
    conversation_id: str = "",
) -> ConfirmPersonalInformationResponse:
    """
    1. Check and validate the contact number and sales order number
    2. Check if the product and issue description are provided
    3. Check if the photos or videos are shared
    4. Check if the address is confirmed

    Return prompt with success=False if any of the above checks fail
    Return the customer information if all checks pass with success=True
    """
    personal_information_verification = confirm_personal_information(
        customer_contact_number=customer_contact_number,
        sales_order_number=sales_order_number,
        conversation_id=conversation_id,
    )
    if not personal_information_verification.success:
        return personal_information_verification
    missing_info = []

    if not product:
        missing_info.append("the product")
    if not issue_description:
        missing_info.append("the description of the issue")

    missing_info = "\n".join([f"- {info}" for info in missing_info])
    if missing_info:
        return ConfirmPersonalInformationResponse(
            message=f"""I understand you're looking to schedule a service appointment. To help you better, I'll need to gather some additional information:
            {missing_info}
            Could you please provide these details so I can proceed with scheduling your appointment?""",
            success=False,
        )

    # TODO: Add this back in after implementing the file transfer with websocket

    try:
        times_images_were_asked = int(how_many_times_images_were_asked)
    except ValueError:
        times_images_were_asked = 0
    if not attachment_ids and times_images_were_asked < 2:
        photo_guide_image_markdown_prefix = "![Photo Guide Image]"
        photo_guide_image_markdown_str = ""
        conversation_instance = Conversation.objects.get(id=conversation_id)
        chatbot_reply_messages = conversation_instance.messages.filter(
            message_type=MACHINE_TEXT
        )
        if not any(
            [
                photo_guide_image_markdown_prefix in message.message
                for message in chatbot_reply_messages
            ]
        ):
            photo_guide_image_instance = KnowledgeImage.objects.filter(
                file__icontains="photo_guide"
            ).first()
            if photo_guide_image_instance:
                s3_url = photo_guide_image_instance.file.url

                if str(s3_url).startswith("/"):
                    # If the S3 URL starts with a slash, remove it
                    s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/{str(s3_url)[1:]}"
                elif str(s3_url).startswith("https://"):
                    # If the S3 URL starts with "https://", keep it as is
                    pass
                else:
                    # If the S3 URL does not start with "https://", prepend it
                    s3_url = (
                        f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/{s3_url}"
                    )
                photo_guide_image_markdown_str = f"Use this image guide: {photo_guide_image_markdown_prefix}]({s3_url})"
        return ConfirmPersonalInformationResponse(
            message=f"You need to ask the customer to share at least 2 images or 1 video of the defected product.{photo_guide_image_markdown_str}",
            success=False,
        )

    is_address_confirmed_or_provided = (
        str(is_address_confirmed_or_provided).lower() == "true"
    )
    if not is_address_confirmed_or_provided:
        address = personal_information_verification.message["customer_address"]
        if address and address != "":

            return ConfirmPersonalInformationResponse(
                message=f"""You are to confirm that the address is correct. Here is the customer address: {address}. Ask the customer to confirm the address.""",
                success=False,
            )
    return ConfirmPersonalInformationResponse(
        message=personal_information_verification.message,
        success=True,
    )


@tool("gather_issue_description")
def gather_issue_description(
    conversation_id: str,
    customer_contact_number: str = "",
    sales_order_number: str = "",
    product: str = "",
    issue_description: str = "",
    is_address_confirmed_or_provided: str = "",
    attachment_ids: list[int] = [],
    how_many_times_images_were_asked: str = "0",
) -> str:
    """
    Main function to collect ONLY the three required fields if missing:
    1. customer_contact_number
    2. sales_order_number
    3. issue_description

    This tool should ONLY be used to collect these three fields. All other validations
    (like service date, address, etc.) should be handled by other tools.
    """
    if not conversation_id:
        raise Exception("Missing conversation_id")

    problem_description_missing_info = collect_problem_description(
        customer_contact_number=customer_contact_number,
        sales_order_number=sales_order_number,
        product=product,
        issue_description=issue_description,
        is_address_confirmed_or_provided=is_address_confirmed_or_provided,
        attachment_ids=attachment_ids,
        how_many_times_images_were_asked=how_many_times_images_were_asked,
        conversation_id=conversation_id,
    )
    if not problem_description_missing_info.success:
        return problem_description_missing_info.message

    google_calendar = GoogleCalendar()
    available_dates = google_calendar.get_earliest_five_available_dates()

    return f"Tool execution completed! Ask customer for which date they would like to schedule the appointment. These are the earliest available dates: {available_dates}"
