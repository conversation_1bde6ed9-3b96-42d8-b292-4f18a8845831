import json
from datetime import datetime

from crewai.tools import tool
from pydantic import BaseModel

from app.chats.chat.models import Conversation
from app.crew_teams.tools.nova_servicing.google_services import get_customer_info
from app.documents.document.models import KnowledgeImage
from app.utility.models import MACHINE_TEXT
from backend.settings import AWS_STORAGE_BUCKET_NAME


class ConfirmPersonalInformationResponse(BaseModel):
    message: str | dict[str, str | bool]
    success: bool


# check if the contact number and sales order number are provided, and check against globe 3 database
def confirm_personal_information(
    customer_contact_number: str, sales_order_number: str, conversation_id: str
) -> ConfirmPersonalInformationResponse:
    """
    1. Check if the contact number and sales order number are provided
    2. Check against globe 3 database

    Return prompt with success=False if any of the above checks fail
    Return the customer information if all checks pass with success=True
    """

    conversation_instance = Conversation.objects.get(id=conversation_id)
    so_guide_image_markdown_str = ""
    image_markdown_prefix = "![Sales Order Number Search Place Guide]"
    missing_info = []
    if not customer_contact_number:
        missing_info.append("the customer's contact number")
    if not sales_order_number:
        missing_info.append("the sales order number")
        chatbot_reply_messages = conversation_instance.messages.filter(
            message_type=MACHINE_TEXT
        )
        if not any(
            [
                image_markdown_prefix in message.message
                for message in chatbot_reply_messages
            ]
        ):
            so_number_guide_instance = KnowledgeImage.objects.filter(
                file__icontains="so_number_guide"
            ).first()
            if so_number_guide_instance:
                s3_url = so_number_guide_instance.file.url

                if str(s3_url).startswith("/"):
                    # If the S3 URL starts with a slash, remove it
                    s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/{str(s3_url)[1:]}"
                elif str(s3_url).startswith("https://"):
                    # If the S3 URL starts with "https://", keep it as is
                    pass
                else:
                    # If the S3 URL does not start with "https://", prepend it
                    s3_url = (
                        f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/{s3_url}"
                    )
                so_guide_image_markdown_str = f"the sales order number is on the upper right corner of the invoice.\n{image_markdown_prefix}({s3_url}), make sure the image is provided to the customer."

    # if contant number missing, ask for contact number without so_guide_image_markdown_str
    if not customer_contact_number and sales_order_number:
        return ConfirmPersonalInformationResponse(
            message=f"Tool execution completed! This is the message from the tool back to the customer: You should ask the customer to provide the customer's contact number as it is required to proceed with the appointment request.",
            success=False,
        )
    # if only sales order number is missing
    elif customer_contact_number and not sales_order_number:
        return ConfirmPersonalInformationResponse(
            message=f"Tool execution completed! This is the message from the tool back to the customer: You should ask the customer to provide the sales order number. {so_guide_image_markdown_str}.",
            success=False,
        )
    # both missing
    elif missing_info:
        return ConfirmPersonalInformationResponse(
            message=f"Tool execution completed! This is the message from the tool back to the customer: You should ask the customer to provide {', '.join(missing_info)} information as it is required to proceed with the appointment request. {so_guide_image_markdown_str}",
            success=False,
        )

    # check if the contact number and sales order number are valid
    customer_info = get_customer_info(sales_order_number, customer_contact_number)
    if not customer_info:  # try flipping
        customer_info = get_customer_info(customer_contact_number, sales_order_number)
    if not customer_info:
        # verfication failed
        try:
            number_of_failed_verification = int(
                conversation_instance.metadata.get("number_of_failed_verification", 0)
            )
        except:
            number_of_failed_verification = 0
        number_of_failed_verification += 1

        if number_of_failed_verification >= 3:
            # number_of_failed_verification = 0
            return ConfirmPersonalInformationResponse(
                message={
                    "customer_contact_number": str(customer_contact_number).replace(
                        " ", ""
                    ),
                    "full_sales_order_number": "",
                    "customer_address": "",
                    "customer_name": "",
                    "sales_order_number": sales_order_number,
                    "stock_code": "",
                    "stock_description": "",
                    "verification_success": False,
                },
                success=True,
            )
        conversation_instance.metadata.update(
            {"number_of_failed_verification": number_of_failed_verification}
        )
        conversation_instance.save()

        return ConfirmPersonalInformationResponse(
            message=f"Tool execution completed! This is the message from the tool back to the customer: The sales order number:{sales_order_number} cannot be matched with the contact number:{customer_contact_number}. You should ask the customer to provide the correct information.",
            success=False,
        )

    return_value = {
        "customer_contact_number": str(customer_info["contact_number"]).replace(
            " ", ""
        ),
        "full_sales_order_number": customer_info["full_so_number"],
        "customer_address": customer_info["ship_to_address"],
        "customer_name": str(customer_info["attention"]).title(),
        "sales_order_number": sales_order_number,
        "stock_code": customer_info["stock_code"],
        "stock_description": customer_info["stock_description"],
        "verification_success": True,
    }
    return ConfirmPersonalInformationResponse(
        message=return_value,
        success=True,
    )


# @tool("gather_customer_information")
def gather_customer_information(
    conversation_id: str,
    customer_contact_number: str = "",
    sales_order_number: str = "",
    intent: str = "create_appointment",
) -> str:
    """
    Main function to collect the personal information and confirm the personal information.

    This function collects the personal information from the customer and confirms the personal information.
    It ensures a seamless flow in gathering customer information and confirming the personal information.

    If customer provided an issue description, trigger gather_issue_description instead.

    Args:
        conversation_id (str): Required. The unique identifier for the conversation.
        contact_number (str): Optional. The customer's contact number.
        sales_order_number (str): Optional. The sales order number.
        intent (str): Optional. The intent of the customer. It is either "create_appointment", "reschedule_appointment","query_appointment" or "general_inquiry". Default is "create_appointment"
    Returns:
        str: A prompt asking for missing information if the personal information is not matched with the sales order number in the database.

    Process:
        1. Validate that `conversation_id` is provided.
        2. Gather the missing personal information if `contact_number` or `sales_order_number` is missing.
        3. Confirm the personal information against the database.
    """

    if not conversation_id:
        raise Exception("Missing conversation_id")

    personal_information_verification = confirm_personal_information(
        customer_contact_number=customer_contact_number,
        sales_order_number=sales_order_number,
        conversation_id=conversation_id,
    )
    if not personal_information_verification.success:
        return personal_information_verification.message

    if intent == "create_appointment":
        return f"This is the message for the next agent. All the necessary information has been collected. Here is the customer information: {str(personal_information_verification.message)}. Use the information inside the json to greet the customer and ask for the issue description to create an appointment."
    elif intent == "reschedule_appointment":
        return f"This is the message for the next agent. All the necessary information has been collected. Here is the customer information: {str(personal_information_verification.message)}. You can now proceed to reschedule the appointment."
    elif intent == "query_appointment":
        return f"This is the message for the next agent. All the necessary information has been collected. Here is the customer information: {str(personal_information_verification.message)}. You can now proceed to query the appointment."
    else:
        return f"This is the message for the next agent. All the necessary information has been collected. Here is the customer information: {str(personal_information_verification.message)}. Use the information inside the json to greet the customer and ask for the issue description to create an appointment."
