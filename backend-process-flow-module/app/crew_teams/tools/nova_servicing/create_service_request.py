import json
from datetime import datetime, timedelta
import traceback

from backend.settings import AWS_STORAGE_BUCKET_NAME
from crewai.tools import tool
from django.utils import timezone
from app.utility.models import PENDING

from app.chats.chat.models import Conversation, Attachment
from app.chats.chat_action.models import CustomChatActionSetting
from app.chats.chat_action_tracker.models import CustomChatActionTracker
from app.crew_teams.tools.nova_servicing.gather_issue_descripton import (
    collect_problem_description,
)
from app.crew_teams.tools.nova_servicing.google_services import GoogleCalendar
from app.crew_teams.tools.nova_servicing.approve_service_request import (
    APPROVE_APPOINTMENT_FUNCTION,
    REJECT_APPOINTMENT_FUNCTION,
)


@tool("create_service_request")
def create_service_request(
    conversation_id: str,
    customer_contact_number: str = "",
    sales_order_number: str = "",
    product: str = "",
    issue_description: str = "",
    is_address_confirmed_or_provided: str = "",
    customer_postal_code: str = "",
    attachment_ids: list[int] = [],
    how_many_times_images_were_asked: str = "0",
    requested_service_date: str = "",
) -> str:
    """
    Main function to create a service request.

    This function is to be called after the customer contact number and sales order number are verified and the issue description is collected.
    This function also check if the images, requested service date and address are provided by customer. If the requested service date is not provided, it will suggest five earliest available dates.
    This function can check if the requested service date is available for booking.
    It ensures a seamless flow in creating a service request.

    Args:
        conversation_id (str): Required. The unique identifier for the conversation.
        customer_contact_number (str): Optional. The customer's contact number.
        sales_order_number (str): Optional. The sales order number.
        product (str): Optional. The specific furniture product that requires servicing. For example, sofa, dining table, bed frame, wardrobe, etc.
        issue_description (str): The customer's complaint and description of issues with the furniture product, including any specific problems, damages, defects or malfunctions that need to be addressed during servicing
        is_address_confirmed_or_provided (str): Optional. Set to `True` if the customer confirms the address is correct or the address is already provided by the customer, else `False`.
        customer_postal_code (str): Optional. The customer's postal code.
        attachment_ids (list[int]): Optional. A list of attachment ids shared by the customer.
        how_many_times_images_were_asked (str): Optional. The number of times the bot has asked the customer to share images or videos.
        requested_service_date (str): Optional. The date the customer want to schedule the appointment. It must be in the format of "YYYY-MM-DD".
    """
    # check if there any missing information
    try:
        personal_information_verification = collect_problem_description(
            customer_contact_number=customer_contact_number,
            sales_order_number=sales_order_number,
            product=product,
            issue_description=issue_description,
            is_address_confirmed_or_provided=is_address_confirmed_or_provided,
            attachment_ids=attachment_ids,
            how_many_times_images_were_asked=how_many_times_images_were_asked,
            conversation_id=conversation_id,
        )
        if not personal_information_verification.success:
            return personal_information_verification.message

        google_calendar = GoogleCalendar()

        # check if the requested service date is provided, if not, get the earliest five available dates
        if not requested_service_date:
            available_dates = google_calendar.get_earliest_five_available_dates()
            return f"Tool execution completed! Ask customer for which date they would like to schedule the appointment. These are the earliest available dates: {available_dates}."

        # check if the requested service date is available in google calendar
        is_available_in_google_calendar = google_calendar.check_slot_availability(
            datetime.strptime(requested_service_date, "%Y-%m-%d").date()
        )
        if not is_available_in_google_calendar:
            available_dates = google_calendar.get_earliest_five_available_dates()
            return f"Tool execution completed! The requested service date {requested_service_date} is not available. Please ask the customer to choose another date. Provide these earliest available dates in point form for the customer to choose: {available_dates}"

        # create the service request in CustomChatActionTracker
        conversation_instance = Conversation.objects.get(id=conversation_id)

        now = timezone.localtime()
        today_date = now.strftime("%d %B %Y")
        current_time = now.strftime("%H%M")

        full_sales_order_number = personal_information_verification.message[
            "full_sales_order_number"
        ]
        customer_name = personal_information_verification.message["customer_name"]
        customer_stock_code_and_description_pair_str = ""
        attachment_instances = Attachment.objects.filter(id__in=attachment_ids)
        photo_repository_comma_separated_str = ", ".join(
            [
                f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com{attachment_instance.file.url}"
                for attachment_instance in attachment_instances
            ]
        )

        for stock_code, stock_description in zip(
            personal_information_verification.message.get("stock_code Code", "").split(
                ","
            ),
            personal_information_verification.message.get(
                "Stock stock_description", ""
            ).split(","),
        ):
            customer_stock_code_and_description_pair_str += (
                f"{stock_code} - {stock_description}\n"
            )

        customer_full_address = personal_information_verification.message[
            "customer_address"
        ]

        verification_success = personal_information_verification.message[
            "verification_success"
        ]

        if not verification_success:
            # verfication failed, create unverified chat action tracker
            unverified_custom_chat_action_setting_instance, _ = (
                CustomChatActionSetting.objects.get_or_create(
                    name="Unverified Service Request",
                    defaults={
                        "description": "Unverified Service Request Log",
                        "is_activated": True,
                        "icon_svg": "report_gmailerrorred",
                        "on_approve": r"""def on_approve(custom_chat_action_tracker_id):
            print("On approved function to be updated.")
            """,
                        "on_reject": r"""def on_reject(custom_chat_action_tracker_id):
            print("On reject function to be updated.")""",
                    },
                )
            )
            unverified_additional_fields = {
                "enquiry_date": today_date,
                "enquiry_time": current_time,
                "customer_name": "",
                "contact_number": customer_contact_number,
                "so_number": sales_order_number,
                "product": product,
                "issue_description": issue_description,
                "photo_repository": photo_repository_comma_separated_str,
            }
            (
                unverified_chat_action_tracker_instance,
                is_unverified_chat_action_tracker_instance_created,
            ) = CustomChatActionTracker.objects.get_or_create(
                conversation=conversation_instance,
                status=PENDING,
                custom_chat_action_setting=unverified_custom_chat_action_setting_instance,
                remarks="Unverified Service Request",
                defaults={
                    "message": conversation_instance.messages.first(),
                    "additional_fields": json.dumps(unverified_additional_fields),
                },
            )
            return f"""Tool execution completed! Inform the customer that appointment request has been recorded and the servicing team will get back to them in 5 working days."""
        # create custom chat action setting if not exists
        custom_chat_action_setting_instance, _ = (
            CustomChatActionSetting.objects.get_or_create(
                name="Service Request",
                defaults={
                    "description": "Service Request Log",
                    "is_activated": True,
                    "icon_svg": "",
                    "on_approve": APPROVE_APPOINTMENT_FUNCTION,
                    "on_reject": REJECT_APPOINTMENT_FUNCTION,
                },
            )
        )

        # check if the service request already exists

        current_chat_action_tracker_instance = None
        custom_chat_action_tracker_instances = CustomChatActionTracker.objects.filter(
            conversation__id=conversation_id,
            status=PENDING,
        )
        for custom_chat_action_tracker_instance in custom_chat_action_tracker_instances:
            custom_chat_action_tracker_instance_af = json.loads(
                custom_chat_action_tracker_instance.additional_fields
            )
            if (
                custom_chat_action_tracker_instance_af.get("so_number")
                == full_sales_order_number
                and custom_chat_action_tracker_instance_af.get("contact_number")
                == customer_contact_number
            ):
                current_chat_action_tracker_instance = (
                    custom_chat_action_tracker_instance
                )
                break

        if not current_chat_action_tracker_instance:
            # get counter for the case number
            latest_custom_chat_action_tracker_instance_list = CustomChatActionTracker.objects.filter(
                additional_fields__contains=f'"so_number": "{full_sales_order_number}"'
            ).all()

            counter = 1
            if latest_custom_chat_action_tracker_instance_list:
                try:
                    case_number_list = [
                        json.loads(
                            latest_custom_chat_action_tracker_instance.additional_fields
                        ).get("case_number", "")
                        for latest_custom_chat_action_tracker_instance in latest_custom_chat_action_tracker_instance_list
                    ]
                    # sort desc
                    case_number_list.sort(reverse=True)
                    counter = int(str(case_number_list[0]).rsplit("-", 1)[1]) + 1
                except Exception as e:
                    # error getting the case number, use default counter 1
                    print(
                        "Error in getting the case number:",
                        str(e).replace("\n", ";;"),
                        "hence using default counter 1",
                    )

            # create a new service request in CustomChatActionTracker

            case_number = f"{full_sales_order_number}-{counter}"
            additional_fields = {
                "enquiry_date": today_date,
                "enquiry_time": current_time,
                "customer_name": customer_name,
                "contact_number": customer_contact_number,
                "so_number": full_sales_order_number,
                "product": product,
                "issue_description": issue_description,
                "photo_repository": photo_repository_comma_separated_str,
                "case_number": case_number,
                "assigned_date": "",
                "assigned_time": "",
                "assigned_team": "",
                "reminded": "",
                "confirmed": "",
                "full_address": customer_full_address,
                "postal_code": customer_postal_code,
                "item_purchased": customer_stock_code_and_description_pair_str,
                "calendar_event_id": "",
            }
            current_chat_action_tracker_instance = (
                CustomChatActionTracker.objects.create(
                    conversation=conversation_instance,
                    message=conversation_instance.messages.first(),
                    status=PENDING,
                    remarks="Created automatically via SR through chat",
                    additional_fields=json.dumps(additional_fields),
                    custom_chat_action_setting=custom_chat_action_setting_instance,
                )
            )
        case_number = json.loads(
            current_chat_action_tracker_instance.additional_fields
        ).get("case_number")
        if not case_number:
            case_number = full_sales_order_number

        current_chat_action_tracker_instance_af = json.loads(
            current_chat_action_tracker_instance.additional_fields
        )

        # if the service request is already made tentatively, return the message
        if (
            current_chat_action_tracker_instance_af.get("assigned_date")
            == requested_service_date
            and current_chat_action_tracker_instance_af.get("assigned_time")
            == "morning_afternoon"
        ):
            return f"""Tool execution completed! Now, reply to the customer accordingly and do not make any promises to the customer. Keep in mind that the service request is already made tentatively in the previous conversation.
            
    Do note that:
    - **Do not promise, mention, or agree on any specific timing for the service request date, including morning or afternoon.**
            """

        # add in preferred data and slot
        current_chat_action_tracker_instance_af.update(
            {
                "assigned_date": requested_service_date,
                "assigned_time": "morning_afternoon",
            }
        )
        current_chat_action_tracker_instance.additional_fields = json.dumps(
            current_chat_action_tracker_instance_af
        )
        current_chat_action_tracker_instance.save()

        # create event in google calendar
        so_number = current_chat_action_tracker_instance_af.get("so_number", "")
        contact_number = current_chat_action_tracker_instance_af.get(
            "contact_number", ""
        )
        postal_code = current_chat_action_tracker_instance_af.get("postal_code", "")
        third_party_id = (
            conversation_instance.third_party_id
            if conversation_instance.third_party_id
            else contact_number
        )
        summary = f"[{so_number}|{contact_number}|WA:{third_party_id}] Service@{postal_code} [Pending]"
        remarks = f'Product with issue: {current_chat_action_tracker_instance_af.get("product", "")}\n\nDescription: {current_chat_action_tracker_instance_af.get("issue_description", "")}\n\nPrevious Purchased Items:\n{current_chat_action_tracker_instance_af.get("item_purchased", "")}'

        attachments_for_calendar = [
            {"fileUrl": pr, "title": f"Photo {ranking}"}
            for ranking, pr in enumerate(
                current_chat_action_tracker_instance_af.get(
                    "photo_repository", ""
                ).split(","),
                start=1,
            )
            if pr
        ]

        event = google_calendar.book(
            summary=summary,
            date_str=current_chat_action_tracker_instance_af.get("assigned_date", ""),
            full_address=current_chat_action_tracker_instance_af.get(
                "full_address", ""
            ),
            attachments=attachments_for_calendar,
            remarks=remarks,
            color="orange",
        )
        if event:
            current_chat_action_tracker_instance_af.update(
                {"calendar_event_id": event.id}
            )

        current_chat_action_tracker_instance.additional_fields = json.dumps(
            current_chat_action_tracker_instance_af
        )
        current_chat_action_tracker_instance.save()

        latest_available_date = google_calendar.get_latest_available_date(
            requested_service_date
        )

        return f"""Tool execution completed! The service request has been made tentatively in the calendar. Tell the customer we will get back to them latest by {latest_available_date} to confirm the service request.

    Do note that:
    - We do not confirm specific timings until 1 day before the service date.
    - If the customer requests a specific timing, politely inform them that timings will only be advised 1 day before the service date.
    - **Do not promise, mention, or agree on any specific timing for the service request date, including morning or afternoon.**

    Here is the summary of the service request:
    - **Service Request Date:** {requested_service_date}
    - **Product with Issue:** {product}
    """
    except Exception as e:
        traceback.print_exc()
        raise e
