import json
import re
from datetime import datetime
import traceback

from crewai.tools import tool
from django.db.models import Q
from django.utils import timezone

import app.utility.models as utility_models
from app.chats.chat.models import Conversation
from app.chats.chat_action.models import CustomChatActionSetting
from app.chats.chat_action_tracker.models import CustomChatActionTracker
from app.crew_teams.tools.nova_servicing.gather_customer_information import (
    confirm_personal_information,
)
from app.crew_teams.tools.nova_servicing.google_services import GoogleCalendar


@tool("reschedule_service_request")
def reschedule_service_request(
    conversation_id: str,
    customer_contact_number: str = "",
    sales_order_number: str = "",
    requested_service_date: str = "",
):
    """
    Main function to reschedule a service request

    This function is to be called after the customer information is verified.
    It ensures a seamless flow in rescheduling a service request.

    Args:
        conversation_id (str): Required. The unique identifier for the conversation.
        customer_contact_number (str): Optional. The customer's contact number.
        sales_order_number (str): Optional. The sales order number.
        requested_service_date (str): Optional. The date the customer want to schedule the appointment. It must be in the format of "YYYY-MM-DD".

    Process:
        1. Validate that `conversation_id` is provided.
        2. Validate that `customer_contact_number` and `sales_order_number` are provided.
        3. Validate that `requested_service_date` is provided.
        4. Reschedule the service request.
    """
    try:
        if not conversation_id:
            raise ValueError("conversation_id is required")

        conversation_instance = Conversation.objects.get(id=conversation_id)

        personal_information_verification = confirm_personal_information(
            customer_contact_number=customer_contact_number,
            sales_order_number=sales_order_number,
            conversation_id=conversation_id,
        )
        if not personal_information_verification.success:
            return personal_information_verification.message

        customer_name = personal_information_verification.message.get(
            "customer_name", ""
        )
        full_so_number = personal_information_verification.message.get(
            "full_sales_order_number", ""
        )

        custom_chat_action_tracker_instances = (
            CustomChatActionTracker.objects.filter(
                Q(additional_fields__contains=f'"so_number": "{full_so_number}"')
                & Q(
                    additional_fields__contains=f'"contact_number": "{customer_contact_number}"'
                )
                & ~Q(status=utility_models.REJECTED)
                & ~Q(status=utility_models.CONFIRMED)
            )
            .order_by("-id")
            .all()
        )

        selected_custom_chat_action_tracker_instance = None
        if custom_chat_action_tracker_instances:
            selected_custom_chat_action_tracker_instance = (
                custom_chat_action_tracker_instances[0]
            )

        google_calendar = GoogleCalendar()

        if not selected_custom_chat_action_tracker_instance:

            return f"""
                Tool execution completed. Greet the customer, his/her name is {customer_name}. Inform him/her that there is no active service request with the provided Sales Order Number and Contact Number.
            """

        if not requested_service_date:
            return "Tool execution completed. This is the message to be sent to the customer. Can you please provide the new date that you want to reschedule the appointment to?"

        is_available_in_google_calendar = google_calendar.check_slot_availability(
            datetime.strptime(requested_service_date, "%Y-%m-%d").date()
        )

        if not is_available_in_google_calendar:
            return f"Tool execution completed! The requested service date {requested_service_date} is not available. Please ask the customer to choose another date."

        # update the custom chat action tracker instance
        selected_custom_chat_action_tracker_instance_additional_fields = {
            **json.loads(
                selected_custom_chat_action_tracker_instance.additional_fields
            ),
            "assigned_date": requested_service_date,
        }

        calendar_event_id = (
            selected_custom_chat_action_tracker_instance_additional_fields.get(
                "calendar_event_id", ""
            )
        )
        event = google_calendar.calendar.events.get(id=calendar_event_id)
        initial_service_request_date = event.convert_utc_to_django_date(
            event.start_time
        )

        # Check if the new date and slot is the same as the initial date and slot
        if initial_service_request_date.strftime("%Y-%m-%d") == requested_service_date:
            return f"Tool execution completed. Inform the customer that the new date is the same as the initial date."

        # create a new one and delete the previous one
        # book a new one in google calendar
        attachments = [
            {"fileUrl": pr, "title": f"Photo {ranking}"}
            for ranking, pr in enumerate(
                selected_custom_chat_action_tracker_instance_additional_fields.get(
                    "photo_repository", ""
                ).split(","),
                start=1,
            )
            if pr
        ]
        so_number = selected_custom_chat_action_tracker_instance_additional_fields.get(
            "so_number", ""
        )
        contact_number = (
            selected_custom_chat_action_tracker_instance_additional_fields.get(
                "contact_number", ""
            )
        )
        postal_code = (
            selected_custom_chat_action_tracker_instance_additional_fields.get(
                "postal_code", ""
            )
        )
        third_party_id = (
            conversation_instance.third_party_id
            if conversation_instance.third_party_id
            else contact_number
        )
        summary = f"[{so_number}|{contact_number}|WA:{third_party_id}] Service@{postal_code} [Pending]"

        remarks = f'Product with issue: {selected_custom_chat_action_tracker_instance_additional_fields.get("product", "")}\n\nDescription: {selected_custom_chat_action_tracker_instance_additional_fields.get("issue_description", "")}\n\nPrevious Purchased Items:\n{selected_custom_chat_action_tracker_instance_additional_fields.get("item_purchased", "")}'

        new_event = google_calendar.book(
            summary,
            requested_service_date,
            selected_custom_chat_action_tracker_instance_additional_fields.get(
                "full_address", ""
            ),
            attachments=attachments,
            remarks=remarks,
            color="orange",
        )
        selected_custom_chat_action_tracker_instance_additional_fields[
            "calendar_event_id"
        ] = new_event.id

        CustomChatActionTracker.objects.create(
            conversation=conversation_instance,
            message=conversation_instance.messages.first(),
            status=utility_models.PENDING,
            remarks="Created automatically via SR reschedule through chat",
            additional_fields=json.dumps(
                selected_custom_chat_action_tracker_instance_additional_fields
            ),
            custom_chat_action_setting=selected_custom_chat_action_tracker_instance.custom_chat_action_setting,
        )

        selected_custom_chat_action_tracker_instance.status = utility_models.REJECTED
        selected_custom_chat_action_tracker_instance.remarks = (
            "Rejected due to rescheduled"
        )
        selected_custom_chat_action_tracker_instance.save()

        try:
            if event:
                event.delete()
                print("Trigger event delete upon reschedule!")
        except Exception as e:
            print("Error in deleting event:", str(e).replace("\n", ";"))

        return f"""Tool execution completed. Inform the customer that A Service Request Date has been Change tentatively. We will review and get back to you soon. Note that we do not confirm any timing until the service date. If the customer asked for a timing (e.g. before 3pm, morning, afternoon), tell them that timing will be advised 1 day before the service date. DO NOT PROMISE OR AGREE ON ANY TIMING INCLUDING MORNING OR AFTERNOON.
            Here is the summary of the service request:
            Initial Service Request Date: {initial_service_request_date}
            New Service Request Date:{requested_service_date}"""
    except Exception as e:
        traceback.print_exc()
        raise e
