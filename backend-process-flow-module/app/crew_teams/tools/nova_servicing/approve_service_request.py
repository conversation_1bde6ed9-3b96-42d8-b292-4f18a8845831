APPROVE_APPOINTMENT_FUNCTION = '''def on_approve(
    custom_chat_action_tracker_id,
):  # fixed, custom chat action tracker id must pass into this custom function

    import chat_action_tracker.models as chat_action_tracker_models
    import chat_action_tracker.nova_servicing_tasks as chat_action_tracker_nova_servicing_tasks
    import utility.gcalendar_helper as utility_gcalendar_helper
    import pytz
    import re
    import json

    from datetime import datetime, timedelta
    from datetime import date as datetime_date

    import utility.models as utility_models

    from collections import defaultdict

    # Google Calendar Start
    class GoogleCalendar:
        def __init__(self):
            self.SG_PUBLIC_HOLIDAY_DATE_OBJECT = [
                datetime_date(2024, 8, 9),  # National Day
                datetime_date(2024, 10, 31),  # Deepavali
                datetime_date(2024, 12, 25),  # Christmas
            ]
            self.SG_TIMEZONE = pytz.timezone("Asia/Singapore")
            # self.SLOT_TIMES = {
            #     "morning": (10, 14),
            #     "afternoon": (14, 18),
            # } # Legacy, the team don't want timing, to solve the SR location far from each other, but in the same slot problem
            self.SLOT_TIMES = {
                "morning_afternoon": (10, 18),
            }  # We use this, and the timing is internal only.
            self.MIN_BUFFER_DAYS = 3
            self.EVENT_DURATION_MINS = 40

            self.NUM_TEAMS = 2
            self.NUM_SERVICING_PER_TEAM_PER_BLOCK = (
                6 * 2
            )  # Multiply by 2 due to combination of morning and afternoon time slots
            self.service_account_key_json = {
                "type": "service_account",
                "project_id": "calendarapi-423901",
                "private_key_id": "fcb8d147547974c8a5ce11803facd428a485e9cd",
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                "client_email": "<EMAIL>",
                "client_id": "103498307354969785142",
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/serviceaccount%40calendarapi-423901.iam.gserviceaccount.com",
                "universe_domain": "googleapis.com",
            }
            self.credentials = utility_gcalendar_helper.get_credentials(
                self.service_account_key_json
            )
            self.service = utility_gcalendar_helper.get_service(self.credentials)
            self.unassigned_calendar_id = "<EMAIL>"
            self.t1_calendar_id = "<EMAIL>"
            self.t2_calendar_id = "<EMAIL>"
            self.unassigned_calendar = utility_gcalendar_helper.Calendar(
                self.unassigned_calendar_id, "Service Schedule", self.service
            )
            self.t1_calendar = utility_gcalendar_helper.Calendar(
                self.t1_calendar_id, "Service Schedule T1", self.service
            )
            self.t2_calendar = utility_gcalendar_helper.Calendar(
                self.t2_calendar_id, "Service Schedule T2", self.service
            )
            self.regex_team = re.compile(r"^\[T\d+\]")

        def get_remaining_slot(self, postal_code, invoice_number):
            """
            postal_code: str, postal code of the service location
            invoice_number: str, invoice number of the service
            """
            today = datetime.now(self.SG_TIMEZONE)
            search_start_date = today + timedelta(days=self.MIN_BUFFER_DAYS)
            search_end_date = search_start_date + timedelta(days=30)  # Adjust as needed
            events = self.unassigned_calendar.events.list(
                start_after=search_start_date.isoformat(),
                end_before=search_end_date.isoformat(),
            )
            available_slots = {}
            # loop through all dates between search_start_date and search_end_date
            timelines = {}
            for day in range((search_end_date - search_start_date).days):
                check_date = (search_start_date + timedelta(days=day)).date()
                if (
                    check_date.weekday() == 6
                    or check_date in self.SG_PUBLIC_HOLIDAY_DATE_OBJECT
                ):  # Skip Sundays (0 = Monday, ..., 6 = Sunday)
                    continue
                timelines[check_date] = []
                for slot, (start_hour, end_hour) in self.SLOT_TIMES.items():
                    available_slots[(check_date, slot)] = (
                        self.NUM_TEAMS * self.NUM_SERVICING_PER_TEAM_PER_BLOCK
                    )
                    timelines[check_date].append((start_hour, slot))
            # loop through all events and decrement available slots
            for event in events:
                start_time = event.get_datetime_obj_from_gcalendar_event_time(
                    event.start_time
                )
                # start_time = datetime.fromisoformat(event.start_time)
                event_date = start_time.date()
                if (
                    event_date.weekday() == 6
                    or event_date in self.SG_PUBLIC_HOLIDAY_DATE_OBJECT
                ):  # Skip Sundays
                    continue
                timelines[event_date].append(
                    (start_time.hour, "zz")
                )  # "zz" so that the events is always after the existing 'morning' and 'afternoon' slot, that are pre-defined above .

            for date, servicings in timelines.items():
                # First few items in servicings, will be the
                servicings.sort(key=lambda x: (x[0], x[1]))
                current_slot = None  #'morning' and 'afternoon' slot need to be selected as current_slot before any of the events, such that it can proceed to deduce the available slot
                for hour, slot in servicings:
                    if slot != "zz":
                        current_slot = slot
                    elif current_slot is not None:
                        if (date, current_slot) in available_slots:
                            available_slots[(date, current_slot)] -= 1
            remaining_slots = {k: v for k, v in available_slots.items() if v > 0}
            return remaining_slots

        def check_general_availability(self, postal_code, invoice_number):
            """
            postal_code: str, postal code of the service location
            invoice_number: str, invoice number of the serviceP
            """
            remaining_slots = self.get_remaining_slot(postal_code, invoice_number)
            # group by date, combine slots, {date} {slots}, no need for count
            grouped_remaining_slots = defaultdict(list)
            for (date, slot), count in remaining_slots.items():
                grouped_remaining_slots[date].append(slot)
            # chr(10) is newline, chr(47) is forward slash

            # available_slot_str = f"""Available slots for servicing:
            #     {chr(10).join([f'{date.strftime("%A, %d %B %Y")} {chr(47).join(slots)}' for date, slots in grouped_remaining_slots.items()])}"""
            available_slot_str = f"""Available slots for servicing:
                {chr(10).join([f'{date.strftime("%A, %d %B %Y")}' for date, slots in grouped_remaining_slots.items()])}"""
            return available_slot_str

        def check_slot_availability(
            self, postal_code, invoice_number, check_date, check_slot
        ):
            """
            postal_code: str, postal code of the service location
            invoice_number: str, invoice number of the service
            check_date: datetime.date, the date to check for availability
            check_slot: str, the time slot to check for availability ("morning", "afternoon")
            """
            remaining_slots = self.get_remaining_slot(postal_code, invoice_number)
            # Check if the specific slot on the given date is available
            if (check_date, check_slot) in remaining_slots and remaining_slots[
                (check_date, check_slot)
            ] > 0:
                return True
            return False

        def book(
            self,
            summary,
            date_str,
            slot,
            full_address,
            attachments=[],
            remarks="",
            color="",
        ):
            """
            date_str: str, date in YYYY-MM-DD format
            slot: str, one of 'morning' or 'afternoon'
            postal_code: str, postal code of the service location
            full_address: str, full address of the service location
            invoice_number: str, invoice number of the service
            remarks: str, optional, additional details for the service
            """

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                raise ValueError(f"Invalid date: {date_str}, require YYYY-MM-DD format")
            earliest_booking_date = datetime.now(self.SG_TIMEZONE).date() + timedelta(
                days=self.MIN_BUFFER_DAYS
            )
            if date < earliest_booking_date:
                raise ValueError(
                    f"Date must be at least {earliest_booking_date} ({self.MIN_BUFFER_DAYS} days from today)"
                )
            if slot not in self.SLOT_TIMES:
                raise ValueError(
                    f'Invalid slot: {slot}. Available slots: {", ".join(self.SLOT_TIMES.keys())}'
                )
            start_hour, end_hour = self.SLOT_TIMES[slot]
            start_time = self.SG_TIMEZONE.localize(
                datetime(date.year, date.month, date.day, start_hour)
            )
            end_time = start_time + timedelta(minutes=self.EVENT_DURATION_MINS)
            try:
                event = self.unassigned_calendar.events.create(
                    summary=summary,
                    start_time=start_time.isoformat(),
                    end_time=end_time.isoformat(),
                    location=full_address,  # TODO: cast to actual postal code
                    description=remarks,  # TODO: add details like issue, preferences here?
                    attachments=attachments,
                    colorId=utility_gcalendar_helper.EVENT_COLOR_VALUE_TO_ID_MAPPING.get(
                        color, "1"
                    ),
                )
                print(
                    f"The servicing is booked for the {slot} slot (between {self.convert24to12(start_hour)}-{self.convert24to12(end_hour)}) on {date_str}."
                )
                return event
            except Exception as e:
                raise ValueError(f"Failed to book the servicing: {e}")

        def get_event_booking_event(self, so_number, contact_number):
            """[{so_number}|{contact_number}|WA:{third_party_contact}] Service@{postal_code} [{status}]"""
            today = datetime.now(self.SG_TIMEZONE)
            events = self.unassigned_calendar.events.list(
                query=f"[{so_number}|{contact_number}",
                start_after=today.isoformat(),
            )
            if not events:
                return None
            event = events[0]
            return event

        def get_event_from_available_calendars(
            self, event_id
        ) -> None | utility_gcalendar_helper.Event:
            event = self.unassigned_calendar.events.get(id=event_id)
            if not event:
                event = self.t1_calendar.events.get(id=event_id)

            if not event:
                event = self.t2_calendar.events.get(id=event_id)

            if not event:
                print(f"Event with id {event_id} not found in any calendar.")

            return event

        def get_approved_event_booking_event(
            self, so_number, contact_number
        ) -> None | utility_gcalendar_helper.Event:
            """[{so_number}|{contact_number}|WA:{third_party_contact}] Service@{postal_code} [{status}]"""

            today = datetime.now(self.SG_TIMEZONE)
            events = self.unassigned_calendar.events.list(
                query=f"[{so_number}|{contact_number}",
                start_after=today.isoformat(),
            )
            approved_events: list[utility_gcalendar_helper.Event] = []
            for event in events:
                if utility_models.APPROVED in str(event.summary):
                    approved_events.append(event)

            if not approved_events:
                return None
            approved_event = approved_events[0]
            return approved_event

        @staticmethod
        def convert24to12(hr):
            return f'{hr % 12 or 12}{"am" if hr < 12 else "pm"}'

    google_calendar = GoogleCalendar()
    # Google Calendar End

    custom_chat_action_tracker_instance = (
        chat_action_tracker_models.CustomChatActionTracker.objects.get(
            id=custom_chat_action_tracker_id
        )
    )

    custom_chat_action_tracker_instance_af = json.loads(
        custom_chat_action_tracker_instance.additional_fields
    )

    calendar_event_id = custom_chat_action_tracker_instance_af.get("calendar_event_id")

    if calendar_event_id:
        event = google_calendar.unassigned_calendar.events.get(id=calendar_event_id)
        updated_summary = event.summary.replace(
            chat_action_tracker_nova_servicing_tasks.GCALENDAR_EVENT_PENDING,
            chat_action_tracker_nova_servicing_tasks.GCALENDAR_EVENT_APPROVED,
        )
        event.update(
            summary=updated_summary,
            colorId=utility_gcalendar_helper.EVENT_COLOR_VALUE_TO_ID_MAPPING["green"],
        )
        return

    # else create
    attachments = [
        {"fileUrl": pr, "title": f"Photo {ranking}"}
        for ranking, pr in enumerate(
            custom_chat_action_tracker_instance_af.get("photo_repository", "").split(
                ","
            ),
            start=1,
        )
        if pr
    ]
    so_number = custom_chat_action_tracker_instance_af.get("so_number", "")
    contact_number = custom_chat_action_tracker_instance_af.get("contact_number", "")
    postal_code = custom_chat_action_tracker_instance_af.get("postal_code", "")

    conversation_instance = custom_chat_action_tracker_instance.conversation
    third_party_id = (
        conversation_instance.third_party_id
        if conversation_instance.third_party_id
        else contact_number
    )
    summary = f"[{so_number}|{contact_number}|WA:{third_party_id}] Service@{postal_code} [Pending]"
    remarks = f'Product with issue: {custom_chat_action_tracker_instance_af.get("product", "")}\n\nDescription: {custom_chat_action_tracker_instance_af.get("issue_description", "")}\n\nPrevious Purchased Items:\n{custom_chat_action_tracker_instance_af.get("item_purchased", "")}'

    event = google_calendar.book(
        summary,
        custom_chat_action_tracker_instance_af.get("assigned_date", ""),
        custom_chat_action_tracker_instance_af.get("assigned_time", ""),
        custom_chat_action_tracker_instance_af.get("full_address", ""),
        attachments=attachments,
        remarks=remarks,
        color="green",
    )
    if event:
        custom_chat_action_tracker_instance_af.update({"calendar_event_id": event.id})
        custom_chat_action_tracker_instance.additional_fields = json.dumps(
            custom_chat_action_tracker_instance_af
        )
        custom_chat_action_tracker_instance.save()
'''

REJECT_APPOINTMENT_FUNCTION = '''
def on_reject(custom_chat_action_tracker_id):
    import chat_action_tracker.models as chat_action_tracker_models
    import utility.gcalendar_helper as utility_gcalendar_helper
    import utility.models as utility_models
    import json

    import pytz
    import re
    import json
    from datetime import datetime, timedelta
    from datetime import date as datetime_date

    from collections import defaultdict

    custom_chat_action_tracker_instance = (
        chat_action_tracker_models.CustomChatActionTracker.objects.get(
            id=custom_chat_action_tracker_id
        )
    )

    # Google Calendar Start
    class GoogleCalendar:
        def __init__(self):
            self.SG_PUBLIC_HOLIDAY_DATE_OBJECT = [
                datetime_date(2024, 8, 9),  # National Day
                datetime_date(2024, 10, 31),  # Deepavali
                datetime_date(2024, 12, 25),  # Christmas
            ]
            self.SG_TIMEZONE = pytz.timezone("Asia/Singapore")
            # self.SLOT_TIMES = {
            #     "morning": (10, 14),
            #     "afternoon": (14, 18),
            # } # Legacy, the team don't want timing, to solve the SR location far from each other, but in the same slot problem
            self.SLOT_TIMES = {
                "morning_afternoon": (10, 18),
            } # We use this, and the timing is internal only.
            self.MIN_BUFFER_DAYS = 3
            self.EVENT_DURATION_MINS = 40

            self.NUM_TEAMS = 2
            self.NUM_SERVICING_PER_TEAM_PER_BLOCK = 6 * 2 # Multiply by 2 due to combination of morning and afternoon time slots
            self.service_account_key_json = {
                "type": "service_account",
                "project_id": "calendarapi-423901",
                "private_key_id": "fcb8d147547974c8a5ce11803facd428a485e9cd",
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                "client_email": "<EMAIL>",
                "client_id": "103498307354969785142",
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/serviceaccount%40calendarapi-423901.iam.gserviceaccount.com",
                "universe_domain": "googleapis.com",
            }
            self.credentials = utility_gcalendar_helper.get_credentials(
                self.service_account_key_json
            )
            self.service = utility_gcalendar_helper.get_service(self.credentials)
            self.unassigned_calendar_id = "<EMAIL>"
            self.t1_calendar_id = "<EMAIL>"
            self.t2_calendar_id = "<EMAIL>"
            self.unassigned_calendar = utility_gcalendar_helper.Calendar(
                self.unassigned_calendar_id, "Service Schedule", self.service
            )
            self.t1_calendar = utility_gcalendar_helper.Calendar(
                self.t1_calendar_id, "Service Schedule T1", self.service
            )
            self.t2_calendar = utility_gcalendar_helper.Calendar(
                self.t2_calendar_id, "Service Schedule T2", self.service
            )
            self.regex_team = re.compile(r"^\[T\d+\]")

        def get_remaining_slot(self, postal_code, invoice_number):
            """
            postal_code: str, postal code of the service location
            invoice_number: str, invoice number of the service
            """
            today = datetime.now(self.SG_TIMEZONE)
            search_start_date = today + timedelta(days=self.MIN_BUFFER_DAYS)
            search_end_date = search_start_date + timedelta(days=30)  # Adjust as needed
            events = self.unassigned_calendar.events.list(
                start_after=search_start_date.isoformat(),
                end_before=search_end_date.isoformat(),
            )
            available_slots = {}
            # loop through all dates between search_start_date and search_end_date
            timelines = {}
            for day in range((search_end_date - search_start_date).days):
                check_date = (search_start_date + timedelta(days=day)).date()
                if (
                    check_date.weekday() == 6
                    or check_date in self.SG_PUBLIC_HOLIDAY_DATE_OBJECT
                ):  # Skip Sundays (0 = Monday, ..., 6 = Sunday)
                    continue
                timelines[check_date] = []
                for slot, (start_hour, end_hour) in self.SLOT_TIMES.items():
                    available_slots[(check_date, slot)] = (
                        self.NUM_TEAMS * self.NUM_SERVICING_PER_TEAM_PER_BLOCK
                    )
                    timelines[check_date].append((start_hour, slot))
            # loop through all events and decrement available slots
            for event in events:
                start_time = event.get_datetime_obj_from_gcalendar_event_time(
                    event.start_time
                )
                # start_time = datetime.fromisoformat(event.start_time)
                event_date = start_time.date()
                if (
                    event_date.weekday() == 6
                    or event_date in self.SG_PUBLIC_HOLIDAY_DATE_OBJECT
                ):  # Skip Sundays
                    continue
                timelines[event_date].append(
                    (start_time.hour, "zz")
                )  # "zz" so that the events is always after the existing 'morning' and 'afternoon' slot, that are pre-defined above . 

            for date, servicings in timelines.items():
                # First few items in servicings, will be the 
                servicings.sort(key=lambda x: (x[0], x[1]))
                current_slot = None #'morning' and 'afternoon' slot need to be selected as current_slot before any of the events, such that it can proceed to deduce the available slot
                for hour, slot in servicings:
                    if slot != "zz":
                        current_slot = slot
                    elif current_slot is not None:
                        if (date, current_slot) in available_slots:
                            available_slots[(date, current_slot)] -= 1
            remaining_slots = {k: v for k, v in available_slots.items() if v > 0}
            return remaining_slots

        def check_general_availability(self, postal_code, invoice_number):
            """
            postal_code: str, postal code of the service location
            invoice_number: str, invoice number of the serviceP
            """
            remaining_slots = self.get_remaining_slot(postal_code, invoice_number)
            # group by date, combine slots, {date} {slots}, no need for count
            grouped_remaining_slots = defaultdict(list)
            for (date, slot), count in remaining_slots.items():
                grouped_remaining_slots[date].append(slot)
            # chr(10) is newline, chr(47) is forward slash

            # available_slot_str = f"""Available slots for servicing:
            #     {chr(10).join([f'{date.strftime("%A, %d %B %Y")} {chr(47).join(slots)}' for date, slots in grouped_remaining_slots.items()])}"""
            available_slot_str = f"""Available slots for servicing:
                {chr(10).join([f'{date.strftime("%A, %d %B %Y")}' for date, slots in grouped_remaining_slots.items()])}"""
            return available_slot_str

        def check_slot_availability(
            self, postal_code, invoice_number, check_date, check_slot
        ):
            """
            postal_code: str, postal code of the service location
            invoice_number: str, invoice number of the service
            check_date: datetime.date, the date to check for availability
            check_slot: str, the time slot to check for availability ("morning", "afternoon")
            """
            remaining_slots = self.get_remaining_slot(postal_code, invoice_number)
            # Check if the specific slot on the given date is available
            if (check_date, check_slot) in remaining_slots and remaining_slots[
                (check_date, check_slot)
            ] > 0:
                return True
            return False

        def book(
            self,
            summary,
            date_str,
            slot,
            full_address,
            attachments=[],
            remarks="",
            color="",
        ):
            """
            date_str: str, date in YYYY-MM-DD format
            slot: str, one of 'morning' or 'afternoon'
            postal_code: str, postal code of the service location
            full_address: str, full address of the service location
            invoice_number: str, invoice number of the service
            remarks: str, optional, additional details for the service
            """

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                raise ValueError(f"Invalid date: {date_str}, require YYYY-MM-DD format")
            earliest_booking_date = datetime.now(self.SG_TIMEZONE).date() + timedelta(
                days=self.MIN_BUFFER_DAYS
            )
            if date < earliest_booking_date:
                raise ValueError(
                    f"Date must be at least {earliest_booking_date} ({self.MIN_BUFFER_DAYS} days from today)"
                )
            if slot not in self.SLOT_TIMES:
                raise ValueError(
                    f'Invalid slot: {slot}. Available slots: {", ".join(self.SLOT_TIMES.keys())}'
                )
            start_hour, end_hour = self.SLOT_TIMES[slot]
            start_time = self.SG_TIMEZONE.localize(
                datetime(date.year, date.month, date.day, start_hour)
            )
            end_time = start_time + timedelta(minutes=self.EVENT_DURATION_MINS)
            try:
                event = self.unassigned_calendar.events.create(
                    summary=summary,
                    start_time=start_time.isoformat(),
                    end_time=end_time.isoformat(),
                    location=full_address,  # TODO: cast to actual postal code
                    description=remarks,  # TODO: add details like issue, preferences here?
                    attachments=attachments,
                    colorId=utility_gcalendar_helper.EVENT_COLOR_VALUE_TO_ID_MAPPING.get(
                        color, "1"
                    ),
                )
                print(
                    f"The servicing is booked for the {slot} slot (between {self.convert24to12(start_hour)}-{self.convert24to12(end_hour)}) on {date_str}."
                )
                return event
            except Exception as e:
                raise ValueError(f"Failed to book the servicing: {e}")

        def get_event_booking_event(self, so_number, contact_number):
            """[{so_number}|{contact_number}|WA:{third_party_contact}] Service@{postal_code} [{status}]"""
            today = datetime.now(self.SG_TIMEZONE)
            events = self.unassigned_calendar.events.list(
                query=f"[{so_number}|{contact_number}",
                start_after=today.isoformat(),
            )
            if not events:
                return None
            event = events[0]
            return event

        def get_event_from_available_calendars(self, event_id) -> None | utility_gcalendar_helper.Event:
                event = self.unassigned_calendar.events.get(id=event_id)
                if not event:
                    event = self.t1_calendar.events.get(id=event_id)

                if not event:
                    event = self.t2_calendar.events.get(id=event_id)

                if not event:
                    print(f"Event with id {event_id} not found in any calendar.")

                return event
        
        def get_approved_event_booking_event(
            self, so_number, contact_number
        ) -> None | utility_gcalendar_helper.Event:
            """[{so_number}|{contact_number}|WA:{third_party_contact}] Service@{postal_code} [{status}]"""

            today = datetime.now(self.SG_TIMEZONE)
            events = self.unassigned_calendar.events.list(
                query=f"[{so_number}|{contact_number}",
                start_after=today.isoformat(),
            )
            approved_events: list[utility_gcalendar_helper.Event] = []
            for event in events:
                if utility_models.APPROVED in str(event.summary):
                    approved_events.append(event)

            if not approved_events:
                return None
            approved_event = approved_events[0]
            return approved_event


        @staticmethod
        def convert24to12(hr):
            return f'{hr % 12 or 12}{"am" if hr < 12 else "pm"}'

    google_calendar = GoogleCalendar()
    # Google Calendar End

    custom_chat_action_tracker_instance_af = json.loads(
        custom_chat_action_tracker_instance.additional_fields
    )
    calendar_event_id = custom_chat_action_tracker_instance_af.get("calendar_event_id")
    print("calendar_event_id on reject: ", calendar_event_id)
    if calendar_event_id:
        print(f"calendar event id found for reject: {calendar_event_id}")
        try:
            google_calendar.unassigned_calendar.events.delete(id=calendar_event_id)
        except Exception as e:
            print("Error when deleting calendar event: ", e)

        custom_chat_action_tracker_instance_af.update({"calendar_event_id": ""})
        custom_chat_action_tracker_instance.additional_fields = json.dumps(
            custom_chat_action_tracker_instance_af
        )

    custom_chat_action_tracker_instance.status = utility_models.REJECTED
    custom_chat_action_tracker_instance.save()

    print("Updated on reject!")

'''
