from crewai.tools import tool
from openai import OpenAI
import hashlib

from app.crew_teams.tools.mj_wines.google_services import GoogleSheetHelper

# Simple cache to store previous recommendations
_recommendation_cache = {}


def check_required_fields(event, pairing_food, price_per_bottle, wine_variety):
    missing_info = []
    if not event:
        missing_info.append("event")
    if not pairing_food:
        missing_info.append("pairing food")
    if not price_per_bottle:
        missing_info.append("price per bottle")
    if not wine_variety:
        missing_info.append("wine variety")
    return missing_info


def get_gpt_reponse(prompt):
    client = OpenAI()
    gpt_response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=prompt,
        temperature=0,
    )
    return gpt_response.choices[0].message.content


@tool("recommend_product")
def recommend_product(
    event: str,
    pairing_food: str,
    price_per_bottle: float,
    wine_variety: str | list[str],
):
    """
    Main function to recommend a product based on customer preferences.

    This function takes in customer preferences such as event, pairing food, price per bottle, and variety and returns a recommended product.

    Args:
        event (str): Optional. event the customer is interested in.
        pairing_food (str): Optional. The food the customer is pairing with.
        price_per_bottle (float): Optional. The price per bottle of the wine.
        wine_variety (str | list[str]): Optional. The variety of the wine.

    Returns:
        str: The name of the recommended product.
    """
    
    # Check for missing information first
    missing_info = check_required_fields(
        event, pairing_food, price_per_bottle, wine_variety
    )
    if missing_info:
        return f"Tool execution completed! This is the message from the tool back to the customer: Could you please provide the following information: {', '.join(missing_info)}. We will recommend a product based on the information you provided."
    
    # Create a cache key from the input parameters
    wine_variety_str = wine_variety if isinstance(wine_variety, str) else ",".join(sorted(wine_variety))
    cache_key = hashlib.md5(f"{event}|{pairing_food}|{price_per_bottle}|{wine_variety_str}".encode()).hexdigest()
    
    # Check if we already have a recommendation for this exact query
    if cache_key in _recommendation_cache:
        print("Using cached recommendation for identical query")
        return _recommendation_cache[cache_key]

    if type(wine_variety) == list:
        wine_variety = ", ".join(wine_variety)
    gpt_prompt = [
        {
            "role": "system",
            "content": f"Based on the event: {event}, pairing food: {pairing_food}, price per bottle: {price_per_bottle}, and variety: {wine_variety}, what wine types or categories do you recommend? RESPONSE IN JSON FORMAT, e.g. [{{'red wine':{{'wine_name':{{'region, ...}}}}, }}]",
        }
    ]

    recommend_wine_type = get_gpt_reponse(gpt_prompt)

    spreadsheet_url = "https://docs.google.com/spreadsheets/d/1_l2YBRZxIsziAEe3DTrVMoBzFI3_XCTlyOv6-s_6e3k/edit?gid=0"
    google_sheet = GoogleSheetHelper(spreadsheet_url=spreadsheet_url)
    wine_df = google_sheet.get_dataframe()
    wine_df = wine_df[wine_df["Quantity"] > 0]  # stock check

    # Filter wines by price range (within $5 of the specified price_per_bottle)
    min_price = float(price_per_bottle) - 5
    max_price = float(price_per_bottle) + 5
    # Convert price from "$30" format to float by removing $ and converting to float
    wine_df[" Retail price "] = (
        wine_df[" Retail price "].astype(str).str.replace("$", "").astype(float)
    )
    wine_df = wine_df[
        (wine_df[" Retail price "] >= min_price)
        & (wine_df[" Retail price "] <= max_price)
    ]

    wines = []

    for _, row in wine_df.iterrows():
        wines.append(
            f"{row['Marketing name']} | {row['Tasting Notes']} | {row['Food Pairing']} | ${row[' Retail price ']} | ${row['Country of Origin']} | {row['Alc (%)']}"
        )
    wines_str = "\n".join(wines)

    system_msg = f"List of wines:\n=====\nMarketing name | Tasting Notes | Food Pairing | Retail price | Country of Origin | Alcohol Percentage\n{wines_str}\n=====\nNarrow down the list of wines based on the recommendation. recommended_wine_type: {recommend_wine_type}. Your response will be used to recommend a wine to the customer. INCLUDE RETAIL PRICE IN YOU ANSWER."
    # beefy_recommendation = get_response("query_inventory", system_msg=system_msg)

    recommendation_prompt = [
        {"role": "system", "content": system_msg},
        {"role": "user", "content": "query_inventory"},
    ]
    final_recommendation = get_gpt_reponse(recommendation_prompt)
    print("final_recommendation: ", final_recommendation)
    
    # Store the recommendation in the cache for future identical requests
    _recommendation_cache[cache_key] = final_recommendation
    
    return final_recommendation
