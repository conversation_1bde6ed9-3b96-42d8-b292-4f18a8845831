from crewai.tools import tool
import requests
from fuzzywuzzy import process

from app.crew_teams.tools.mj_wines.google_services import GoogleSheetHelper

# Constants for Shopify
SHOPIFY_URL = "https://mjwines.myshopify.com"
SHOPIFY_ADMIN_API_ACCESS_TOKEN = "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"


def admin_graphql_query(query, variables=None):
    """Execute a GraphQL query against the Shopify Admin API."""
    url = f"{SHOPIFY_URL}/admin/api/2023-04/graphql.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_ADMIN_API_ACCESS_TOKEN,
    }
    payload = {"query": query, "variables": variables or {}}
    response = requests.post(url, json=payload, headers=headers)
    if response.status_code != 200:
        raise Exception(
            f"GraphQL query failed with status {response.status_code}: {response.text}"
        )
    return response.json()


def find_variant_by_sku(skus: list):
    """Find product variants by SKU."""
    query = """
    query ($sku: String!) {
    productVariants(query: $sku, first: 10) {
        edges {
        node {
            id
            title
            product {
            id
            title
            }
            sku
        }
        }
    }
    }
    """
    variables = {"sku": " OR ".join([f"sku:{x}" for x in skus])}
    result = admin_graphql_query(query, variables)

    variants = result.get("data", {}).get("productVariants", {}).get("edges", [])
    if not variants:
        return None

    variant_ids = {}
    for variant in variants:
        variant_node = variant["node"]
        if variant_node["sku"] in skus:
            variant_id = variant_node["id"]
            variant_ids[variant_node["sku"]] = variant_id.split("/")[-1]
    return variant_ids


def create_cart_url(variant_ids_quantities):
    """Create a cart URL from variant IDs and quantities."""
    url = f"{SHOPIFY_URL}/cart/"
    cart_url_strings = []
    for variant_id, quantity in variant_ids_quantities.items():
        cart_url_strings.append(f"{variant_id}:{quantity}")
    url = url + ",".join(cart_url_strings) + "?storefront=true"
    return url


@tool("purchase_product")
def purchase_product(
    purchase_details: list[dict],
    add_to_existing_cart: str = "false",
    existing_items: list[dict] = None,
):
    """
    This tool manages the purchase process, including validating products, checking stock,
    and creating a cart URL. It supports adding items to an existing cart.

    Args:
        purchase_details (list[dict]): A list of dictionaries containing wine_name and quantity.
        add_to_existing_cart (str): Whether to add to an existing cart or create a new one.
        existing_items (list[dict]): List of items already in the cart (if add_to_existing_cart is True).

    Returns:
        dict: A response containing cart URL, available items, and any unavailable or out-of-stock items.
    """
    add_to_existing_cart = str(add_to_existing_cart).lower() == "true"

    # Initialize existing_items if None
    if existing_items is None:
        existing_items = []

    if not purchase_details:
        return (
            "I'd be happy to help with your purchase! Could you please let me know which wine you're interested in? I'd be delighted to recommend some excellent options if you'd like.",
        )

    # Validate quantities
    for wine in purchase_details:
        if "quantity" not in wine or int(wine.get("quantity", 0)) <= 0:
            return {
                "status": "error",
                "message": f"Please specify how many bottles you want to purchase for {wine.get('wine_name', 'this wine')}.",
            }

    # Get wine inventory data
    spreadsheet_url = "https://docs.google.com/spreadsheets/d/1_l2YBRZxIsziAEe3DTrVMoBzFI3_XCTlyOv6-s_6e3k/edit?gid=0"
    google_sheet = GoogleSheetHelper(spreadsheet_url=spreadsheet_url)
    df = google_sheet.get_dataframe()
    wine_names = [name.strip().lower() for name in df["Marketing name"].tolist()]

    # Combine existing items with new items if adding to cart
    if add_to_existing_cart and existing_items:
        all_items = existing_items.copy()

        # Add new items or update quantities for existing items
        for new_item in purchase_details:
            new_wine_name = new_item.get("wine_name", "").strip().title()
            new_quantity = int(new_item.get("quantity", 0))

            # Check if this wine is already in the cart
            existing_item = next(
                (
                    item
                    for item in all_items
                    if item.get("wine_name", "").strip().title() == new_wine_name
                ),
                None,
            )

            if existing_item:
                # Update quantity if wine already exists in cart
                existing_item["quantity"] = (
                    int(existing_item.get("quantity", 0)) + new_quantity
                )
            else:
                # Add as new item if not already in cart
                all_items.append(new_item)

        purchase_details = all_items

    # Process all items
    sku_quantity_map = {}
    cart_items = []
    not_available_wines = []
    not_enough_stock = []
    adjusted_quantities = {}

    for wine in purchase_details:
        wine_name = str(wine.get("wine_name", "")).strip().title()
        quantity = int(wine.get("quantity", 0))

        closest_match = process.extractOne(wine_name, wine_names)
        matched_wine = None

        if closest_match and closest_match[1] > 80:
            matched_wine = df[
                df["Marketing name"].str.lower().str.strip() == closest_match[0]
            ]
            if not matched_wine.empty:
                sku = matched_wine.iloc[0]["SKU"]
                stock_quantity = int(matched_wine.iloc[0]["Quantity"])

                if stock_quantity <= 0:
                    not_available_wines.append(wine_name)
                elif stock_quantity < quantity:
                    # Add available quantity to cart instead of marking as not enough stock
                    sku_quantity_map[sku] = stock_quantity
                    cart_items.append(wine_name)
                    adjusted_quantities[wine_name] = stock_quantity
                else:
                    sku_quantity_map[sku] = quantity
                    cart_items.append(wine_name)
        else:
            not_available_wines.append(wine_name)

    # If no valid items to purchase
    if not sku_quantity_map:
        message = "I couldn't find any valid wines to add to your cart."
        if not_available_wines:
            message += f" These wines are not available in our shop: {', '.join(not_available_wines)}."
        return {"status": "error", "message": message}

    # Create cart URL with valid items
    variant_ids = find_variant_by_sku(list(sku_quantity_map.keys()))
    if not variant_ids:
        return {
            "status": "error",
            "message": "I couldn't find these wines in our online shop. Please try different wines.",
        }

    variant_id_quantity_map = {
        variant_ids[sku]: qty
        for sku, qty in sku_quantity_map.items()
        if sku in variant_ids
    }
    cart_url = create_cart_url(variant_id_quantity_map)

    # Build response
    response = {
        "status": "success",
        "cart_url": cart_url,
        "cart_items": cart_items,
        "not_available": not_available_wines,
        "adjusted_quantities": adjusted_quantities,
        "message": f"I've created a cart with {', '.join(cart_items)}. You can complete your purchase using this link: {cart_url}. Let me know if you need anything else!",
    }

    if not_available_wines:
        response[
            "message"
        ] += f" Note that these wines are not available in our shop: {', '.join(not_available_wines)}."

    # Add information about adjusted quantities
    if adjusted_quantities:
        adjustment_messages = []
        for wine_name, available_qty in adjusted_quantities.items():
            requested_qty = next(
                (
                    item["quantity"]
                    for item in purchase_details
                    if item.get("wine_name", "").strip().title() == wine_name
                ),
                0,
            )
            adjustment_messages.append(
                f"At the moment, we only have {available_qty} bottles of {wine_name} available (you requested {requested_qty}), so I've added that quantity to the cart."
            )

        response["message"] = ". ".join(adjustment_messages) + " " + response["message"]

    # Add cart_items_details to the response for future reference
    cart_items_details = []
    for wine_name in cart_items:
        # Use adjusted quantity if available, otherwise use the original quantity
        if wine_name in adjusted_quantities:
            quantity = adjusted_quantities[wine_name]
        else:
            quantity = next(
                (
                    item["quantity"]
                    for item in purchase_details
                    if item.get("wine_name", "").strip().title() == wine_name
                ),
                1,
            )

        cart_items_details.append(
            {
                "wine_name": wine_name,
                "quantity": quantity,
            }
        )
    response["cart_items_details"] = cart_items_details

    return response
