from google.oauth2.service_account import Credentials
import gspread
import pandas as pd


class GoogleSheetHelper:
    def __init__(self, spreadsheet_url):
        self.spreadsheet_url = spreadsheet_url
        # Dictionary containing your service account credentials
        credentials_dict = {
            "type": "service_account",
            "project_id": "utterunicorn",
            "private_key_id": "9c69f16b135b33b6722564289e9c0d77527d35a3",
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            "client_email": "<EMAIL>",
            "client_id": "117371264294234618212",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/mjwines%40utterunicorn.iam.gserviceaccount.com",
            "universe_domain": "googleapis.com",
        }

        # Scope for Google Sheets API
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/spreadsheets",
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/drive",
        ]

        # Authenticate using the credentials dictionary
        creds = Credentials.from_service_account_info(credentials_dict, scopes=scope)
        client = gspread.authorize(creds)

        # Open the Google Sheet by URL
        spreadsheet = client.open_by_url(self.spreadsheet_url)
        self.worksheet = spreadsheet.get_worksheet(0)

    def get_opened_spreadsheet(self):
        return self.client.open_by_url(self.spreadsheet_url)

    def get_worksheet(self, spreadsheet):
        return spreadsheet.get_worksheet(0)

    def get_dataframe(self):
        records = self.worksheet.get_all_records()
        return pd.DataFrame(records)
