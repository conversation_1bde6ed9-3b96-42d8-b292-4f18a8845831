from crewai.tools import tool
from fuzzywuzzy import process

from app.crew_teams.tools.mj_wines.google_services import GoogleSheetHelper


def check_required_fields(wine_name: str) -> tuple[bool, str]:
    if not wine_name:
        return (False, "Could you please provide the wine name?")
    return (True, "")


@tool("specific_product_query")
def specific_product_query(wine_name: str):
    """
    This tool retrieves information about a specific product based on its name.

    Args:
        wine_name (str): The name of the wine to retrieve information for.

    Returns:
        str: A JSON string representing the information about the product.
    """

    missing_info, message = check_required_fields(wine_name)
    if not missing_info:
        return f"Tool execution completed! This is the message from the tool back to the customer: {message}"

    spreadsheet_url = "https://docs.google.com/spreadsheets/d/1_l2YBRZxIsziAEe3DTrVMoBzFI3_XCTlyOv6-s_6e3k/edit?gid=0"
    google_sheet = GoogleSheetHelper(spreadsheet_url=spreadsheet_url)
    df = google_sheet.get_dataframe()
    df = df[df["Quantity"] > 0]

    wine_names = [name.strip().lower() for name in df["Marketing name"].tolist()]

    wine_name = str(wine_name).strip().title()
    closest_match = process.extractOne(wine_name, wine_names)
    matched_wine = None
    if closest_match and closest_match[1] > 80:
        # get back the original wine row from wine_names
        matched_wine = df[
            df["Marketing name"].str.lower().str.strip() == closest_match[0]
        ]
        print(f"{matched_wine=}")

    if matched_wine is None or matched_wine.empty:
        return "Tool execution completed! This is the message from the tool back to the customer: We do not have this wine in our stock."

    if matched_wine["Quantity"].values[0] == 0:
        return "Tool execution completed! This is the message from the tool back to the customer: We have this wine in our stock but it is currently out of stock."

    wine_info = matched_wine.iloc[0]
    print(f"{matched_wine.columns=}")
    formatted_message = (
        f"Here is the wine information:\n"
        f"- Name: {wine_info['Marketing name']}\n"
        f"- Price: ${wine_info[' Retail price ']}\n"
        f"- Tasting notes: {wine_info['Tasting Notes'] if 'Tasting Notes' in wine_info else 'Not available'}\n"
        f"- Country of origin: {wine_info['Country of Origin'] if 'Country of Origin' in wine_info else 'Not available'}\n"
        f"- Alcohol percentage: {wine_info['Alc (%)'] if 'Alc (%)' in wine_info else 'Not available'}\n"
    )

    return f"Tool execution completed! This is the message from the tool back to the customer: {formatted_message}"
