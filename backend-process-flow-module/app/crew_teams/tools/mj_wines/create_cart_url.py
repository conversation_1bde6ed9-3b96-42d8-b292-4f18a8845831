import requests
from crewai.tools import tool
from fuzzywuzzy import process
import traceback

from app.crew_teams.tools.mj_wines.google_services import GoogleSheetHelper

# from app.chats.llm_action.models import CustomLLMActionCredentials

# SHOPIFY_URL = (
#     CustomLLMActionCredentials.objects.filter(name="Shopify_Url").first().credential_str
# )
# SHOPIFY_ADMIN_API_ACCESS_TOKEN = (
#     CustomLLMActionCredentials.objects.filter(name="Shopify_Admin_Api_Access_Token")
#     .first()
#     .credential_str
# )

SHOPIFY_URL = "https://mjwines.myshopify.com"
SHOPIFY_ADMIN_API_ACCESS_TOKEN = "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"


def admin_graphql_query(query, variables=None):
    url = f"{SHOPIFY_URL}/admin/api/2023-04/graphql.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_ADMIN_API_ACCESS_TOKEN,
    }
    payload = {"query": query, "variables": variables or {}}
    response = requests.post(url, json=payload, headers=headers)
    if response.status_code != 200:
        raise Exception(
            f"GraphQL query failed with status {response.status_code}: {response.text}"
        )
    return response.json()


def find_variant_by_sku(skus: list):
    # This query searches products with a query string that targets the sku.
    query = """
    query ($sku: String!) {
    productVariants(query: $sku, first: 10) {
        edges {
        node {
            id
            title
            product {
            id
            title
            }
            sku
        }
        }
    }
    }

    """
    variables = {"sku": " OR ".join([f"sku:{x}" for x in skus])}
    result = admin_graphql_query(query, variables)

    variants = result.get("data", {}).get("productVariants", {}).get("edges", [])
    if not variants:
        return None

    variant_ids = {}

    for variant in variants:
        variant_node = variant["node"]
        if variant_node["sku"] in skus:
            # return variant_node
            variant_id = variant_node["id"]
            variant_ids[variant_node["sku"]] = variant_id.split("/")[-1]
    return variant_ids


def get_cart_url(variant_id: str, quantity: int):
    url = f"{SHOPIFY_URL}/cart/{variant_id}:{quantity}?storefront=true"
    return url


# @tool("create_cart_url")
def create_cart_url(purchase_details: list[dict]):
    """
    This tool will create a URL for a cart for the customer to purchase the product.

    Args:
        purchase_details (list[dict]): A list of dictionaries containing wine_name and quantity.

    Returns:
        str: The URL for a cart.
    """
    # type check
    for wines in purchase_details:
        if "quantity" not in wines or int(wines["quantity"]) <= 0:
            return f"Tool execution completed! This is the message from the tool back to the customer: How many bottles do you want to purchase for {wines['wine_name']}?"

    spreadsheet_url = "https://docs.google.com/spreadsheets/d/1_l2YBRZxIsziAEe3DTrVMoBzFI3_XCTlyOv6-s_6e3k/edit?gid=0"
    google_sheet = GoogleSheetHelper(spreadsheet_url=spreadsheet_url)
    df = google_sheet.get_dataframe()

    wine_names = [name.strip().lower() for name in df["Marketing name"].tolist()]

    sku_quantity_map = {}
    cart_items = []
    not_available_wines = []
    not_enough_stock = []

    for wine in purchase_details:
        wine_name = str(wine.get("wine_name", "")).strip().title()
        quantity = int(wine.get("quantity", 0))

        closest_match = process.extractOne(wine_name, wine_names)
        matched_wine = None

        if closest_match and closest_match[1] > 80:
            matched_wine = df[
                df["Marketing name"].str.lower().str.strip() == closest_match[0]
            ]
            if not matched_wine.empty:
                sku = matched_wine.iloc[0]["SKU"]
                stock_quantity = matched_wine.iloc[0]["Quantity"]
                if int(stock_quantity) < quantity:
                    not_enough_stock.append(wine_name)
                else:
                    sku_quantity_map[sku] = quantity
                    cart_items.append(wine_name)
        else:
            not_available_wines.append(wine_name)
    variant_ids = find_variant_by_sku(sku_quantity_map.keys())
    url = f"{SHOPIFY_URL}/cart/"

    cart_url_strings = []
    for sku, quantity in sku_quantity_map.items():
        cart_url_strings.append(f"{variant_ids[sku]}:{quantity}")
    url = url + ",".join(cart_url_strings) + "?storefront=true"

    print("=" * 20)
    print(f"{not_available_wines=}")
    print(f"{not_enough_stock=}")
    print("=" * 20)

    result = f"Tool execution completed! Ask customer to follow this url {url} to complete the purchase for {', '.join(cart_items)}."
    if not_available_wines:
        result += f"Let the customer knows that these wines are not listed on our shop: {', '.join(not_available_wines)}"

    if not_enough_stock:
        result += f"Let the customer knows that these wines are out of stock: {', '.join(not_enough_stock)}"

    return result
