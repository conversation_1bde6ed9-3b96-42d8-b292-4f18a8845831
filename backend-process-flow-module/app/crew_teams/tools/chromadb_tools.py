from typing import List

import chromadb
from chromadb.config import Settings
from crewai.tools import tool
from langchain_community.embeddings import OpenAIEmbeddings
from langchain_core.documents import Document as langchain_Document

from backend.settings import CHROMA_SERVER_IP, CHROMA_SERVER_PORT, OPENAI_API_KEY


def get_chromadb_client():
    client = chromadb.HttpClient(
        host=CHROMA_SERVER_IP,
        port=CHROMA_SERVER_PORT,
        settings=Settings(allow_reset=True),
    )
    return client


@tool("This tool retrieves documents from the ChromaDB collection based on the query.")
def initialize_chroma_and_retrieve(query: str, collection_name: str = "default_collection", num_results: str = "4") -> List[langchain_Document]:
    """
    Initializes Chroma connection, loads the collection, and retrieves documents based on the query.

    Parameters:
        query (str): The search query to match documents.
        collection_name (str): Name of the Chroma collection. Defaults to "default_collection".
        num_results (str): Number of results to retrieve. Defaults to 4.

    Returns:
        List[langchain_Document]: List of documents matching the query.
    """
    print(f"Query: {query}")
    print(f"Collection name: {collection_name}")
    print(f"Number of results: {num_results}")
    # Initialize ChromaDB client
    client = get_chromadb_client()
    
    # Load or create the specified collection
    collection = client.get_collection(collection_name)
    
    # Embed the query for matching
    embeddings = OpenAIEmbeddings(api_key=OPENAI_API_KEY)
    query_embedding = embeddings.embed_query(query)
    
    # Search for matching documents
    search_results = collection.query(
        query_embeddings=[query_embedding],
        n_results=int(num_results)  # Adjust the number of results based on your need
    )
    
    # Retrieve and format documents from search results
    retrieved_documents = []
    documents_length = len(search_results['documents'][0])
    for index in range(documents_length):
        retrieved_documents.append({
            "content": search_results['documents'][0][index],
            "metadata": search_results['metadatas'][0][index]
        })

    return retrieved_documents


@tool("This tool runs the Chroma Retrieval Crew to extract knowledge base of the company information.")
def run_chroma_retrieval_crew(conversation_id: str, query: str, collection_name: str = "default_collection", num_results: str = "4", crew_variables: dict = {}):
    """
    This tool runs the Chroma Retrieval Crew to extract knowledge base of the company information.
    """
    from app.chats.chat.chat_engine.chat_engine import (
        run_crew_team,  # TODO: Fix circular import
    )
    yaml_path = f"app/crew_teams/agents_and_tasks/common/chroma_retriever_crew.yml"
    chroma_retrieval_crew_output, _, _, _ = run_crew_team(crew_variables, yaml_path, seed_number=conversation_id)

    return chroma_retrieval_crew_output


def get_all_documents(collection_name: str) -> List[langchain_Document]:
    """
    Retrieves all documents from a ChromaDB collection for the specified schema.

    Parameters:
        collection_name (str): Name of the Chroma collection. Defaults to "default_collection"

    Returns:
        List[langchain_Document]: List of all documents in the collection
    """
    print(f"Getting all documents for collection: {collection_name}")

    # Initialize ChromaDB client
    client = get_chromadb_client()
    
    # Load the specified collection
    collection = client.get_collection(collection_name)
    
    # Get all documents from collection
    results = collection.get()
    
    # Convert to langchain Document format
    documents = []
    if results and len(results['documents']) > 0:
        for i in range(len(results['documents'])):
            doc = langchain_Document(
                page_content=results['documents'][i],
                metadata=results['metadatas'][i] if results['metadatas'] else {}
            )
            # Only include documents matching the schema
            documents.append(doc)
    
    return documents
