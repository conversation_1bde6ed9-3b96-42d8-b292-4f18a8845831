import json


def format_content(content):
    """Helper function to format content consistently."""
    try:
        if isinstance(content, str):
            # Try to parse string as JSON
            json_content = json.loads(content)
            return f"<pre>{json.dumps(json_content, indent=2)}</pre>"
        elif isinstance(content, (dict, list)):
            return f"<pre>{json.dumps(content, indent=2)}</pre>"
        else:
            return str(content)
    except (json.JSONDecodeError, TypeError):
        return str(content)


def generate_html_header(config=None):
    """Generates the HTML header with CSS styles and configuration metadata at the top of the page."""
    metadata_html = ""
    if config:
        # Create a metadata section at the top of the page
        metadata_html = "<div class='metadata'><h3>Conversation Metadata</h3><ul>"
        for key, value in config.items():
            metadata_html += f"<li><strong>{key}:</strong> {value}</li>"
        metadata_html += "</ul></div><hr>"

    return f'''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Conversation Log</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f0f2f5;
            }}
            .metadata {{
                font-size: 0.9em;
                color: #333;
                background-color: #f7f9fc;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }}
            .metadata ul {{
                list-style: none;
                padding: 0;
            }}
            .metadata li {{
                margin-bottom: 5px;
            }}
            .chat-container {{
                width: 90%;
                max-width: 1200px;
                margin: 20px auto;
                padding: 20px;
                background-color: #ffffff;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }}
            h2 {{
                text-align: center;
                color: #333;
                margin-bottom: 20px;
            }}
            /* Message bubble styling */
            .message-container {{
                display: flex;
                margin: 10px 0;
                padding: 5px;
                align-items: flex-start;
            }}
            .user-message-container {{
                justify-content: flex-start;
            }}
            .cs-message-container {{
                justify-content: flex-end;
            }}
            .message {{
                max-width: 70%;
                padding: 10px 15px;
                border-radius: 15px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                position: relative;
            }}
            .cs-message {{
                background-color: #e6f7ff;
                color: #00529B;
                border-top-right-radius: 0;
                text-align: left;
            }}
            .user-message {{
                background-color: #d9fdd3;
                color: #4F8A10;
                border-top-left-radius: 0;
                text-align: left;
            }}
            .error-message {{
                background-color: #ffdddd;
                color: #d8000c;
                border-radius: 15px;
                text-align: center;
                width: 100%;
                box-shadow: none;
            }}
            .input-message {{
                background-color: #f8f9fa;  /* Light gray background */
                color: #495057;  /* Darker text for better contrast */
                border-radius: 15px;
                text-align: left;
                width: 100%;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                border-left: 4px solid #6c757d;  /* Left border accent */
                padding: 15px;
                margin: 10px 0;
            }}
            .input-message strong {{
                color: #343a40;  /* Darker color for the label */
                display: block;  /* Make the label appear on its own line */
                margin-bottom: 5px;
                font-size: 0.9em;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            .timestamp {{
                font-size: 0.75em;
                color: #888;
                margin-top: 5px;
                display: block;
                text-align: right;
            }}
            .cs-input-message {{
                background-color: #fff5e6;  /* Warm light orange background */
                color: #663c00;  /* Dark orange text */
                border-top-right-radius: 0;
                text-align: left;
                border-left: 4px solid #ff9933;  /* Orange left border */
                padding: 15px;
            }}
            .cs-input-message strong {{
                color: #994d00;  /* Darker orange for the label */
                display: block;
                margin-bottom: 5px;
                font-size: 0.9em;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            .cs-input-message pre {{
                background: rgba(255, 255, 255, 0.5);
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                margin: 5px 0;
            }}
            .cs-thinking-message {{
                background-color: #f0f7ff;  /* Light blue background */
                color: #2c5282;  /* Dark blue text */
                border-top-right-radius: 0;
                text-align: left;
                border-left: 4px solid #4299e1;  /* Blue left border */
                padding: 15px;
                font-family: 'Courier New', monospace;
            }}
            .cs-thinking-message strong {{
                color: #2b6cb0;  /* Darker blue for headers */
                display: block;
                margin-bottom: 8px;
                font-size: 0.9em;
                font-family: Arial, sans-serif;
            }}
            .cs-thinking-message .key {{
                color: #4a5568;
                font-weight: bold;
                display: inline-block;
                min-width: 120px;
                margin-right: 10px;
            }}
            .cs-thinking-message .value {{
                color: #2d3748;
                display: inline-block;
            }}
            .cs-thinking-message .nested {{
                margin-left: 20px;
                border-left: 2px solid #e2e8f0;
                padding-left: 10px;
                margin-top: 5px;
                margin-bottom: 5px;
            }}
            .cs-thinking-message pre {{
                background: rgba(255, 255, 255, 0.5);
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                margin: 5px 0;
                font-size: 0.9em;
            }}
            .content {{
                margin: 10px 0;
                line-height: 1.4;
            }}
            .content pre {{
                background: rgba(255, 255, 255, 0.5);
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                margin: 5px 0;
                font-family: 'Courier New', monospace;
                font-size: 0.9em;
                white-space: pre-wrap;
            }}
            .message strong {{
                display: block;
                margin-bottom: 8px;
                font-size: 0.95em;
                color: inherit;
            }}
            .cs-message .content pre {{
                background: rgba(255, 255, 255, 0.7);
                border: 1px solid rgba(0, 82, 155, 0.2);
            }}
            .user-message .content pre {{
                background: rgba(255, 255, 255, 0.7);
                border: 1px solid rgba(79, 138, 16, 0.2);
            }}
            .input-message .content pre {{
                background: rgba(255, 255, 255, 0.7);
                border: 1px solid rgba(108, 117, 125, 0.2);
            }}
            .avatar {{
                width: 35px;
                height: 35px;
                border-radius: 50%;
                margin: 0 10px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                color: white;
            }}
            .user-avatar {{
                background-color: #4F8A10;
            }}
            .bot-avatar {{
                background-color: #00529B;
            }}
            /* Collapsible styling */
            .collapsible {{
                cursor: pointer;
                user-select: none;
            }}
            .collapsible:hover {{
                opacity: 0.9;
            }}
            .collapsible::after {{
                content: '▼';
                float: right;
                font-size: 0.8em;
                margin-left: 5px;
            }}
            .collapsible.collapsed::after {{
                content: '▶';
            }}
            .collapsible-content {{
                max-height: 1000px;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }}
            .collapsible-content.collapsed {{
                max-height: 0;
            }}
        </style>
        <script>
            function toggleCollapsible(element) {{
                const content = element.nextElementSibling;
                element.classList.toggle('collapsed');
                content.classList.toggle('collapsed');
            }}
        </script>
    </head>
    <body>

    {metadata_html}

    <div class="chat-container">
        <h2>Conversation Log</h2>
    '''
        

def generate_user_message(content, timestamp):
    """Generates HTML for a user message aligned to the left."""
    formatted_content = format_content(content)
    return f'''
        <div class="message-container user-message-container">
            <div class="avatar user-avatar">👤</div>
            <div class="message user-message">
                <strong>User:</strong>
                <div class="content">{formatted_content}</div>
                <span class="timestamp">{timestamp}</span>
            </div>
        </div>
    '''

def generate_thought_process(content, timestamp):
    """Generates HTML for each thought process entry in the chat log with improved key-value formatting."""
    def format_value(value):
        if isinstance(value, (dict, list)):
            return f"<pre>{json.dumps(value, indent=2)}</pre>"
        return str(value)

    def format_dict(d, nested=False):
        html = "<div class='content'>"
        for key, value in d.items():
            if isinstance(value, dict):
                html += f"<div><span class='key'>{key}:</span></div>"
                html += f"<div class='nested'>{format_dict(value, nested=True)}</div>"
            else:
                formatted_value = format_value(value)
                html += f"<div><span class='key'>{key}:</span><span class='value'>{formatted_value}</span></div>"
        html += "</div>"
        return html

    html_output = ""
    
    # Handle list of entries
    if isinstance(content, list):
        for entry in content:
            if isinstance(entry, dict):
                formatted_content = format_dict(entry)
            else:
                formatted_content = format_value(entry)
            
            html_output += f'''
                <div class="message-container cs-message-container">
                    <div class="message cs-thinking-message">
                        <strong class="collapsible" onclick="toggleCollapsible(this)">Bot is thinking:</strong>
                        <div class="collapsible-content">
                            {formatted_content}
                            <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
                        </div>
                    </div>
                    <div class="avatar bot-avatar">🤖</div>
                </div>
            '''
    # Handle single entry
    else:
        if isinstance(content, dict):
            formatted_content = format_dict(content)
        else:
            formatted_content = format_value(content)
            
        html_output += f'''
            <div class="message-container cs-message-container">
                <div class="message cs-thinking-message">
                    <strong class="collapsible" onclick="toggleCollapsible(this)">Bot is thinking:</strong>
                    <div class="collapsible-content">
                        {formatted_content}
                        <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
                    </div>
                </div>
                <div class="avatar bot-avatar">🤖</div>
            </div>
        '''
    
    return html_output


def generate_error_message(content, timestamp):
    """Generates HTML for an error message centered in the chat log."""
    return f'''
        <div class="message-container error-message-container">
            <div class="message error-message">
                <strong>Error:</strong> {content}
                <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
            </div>
        </div>
    '''

def generate_input_message(input_name, content, timestamp):
    """Generates HTML for an input message with distinct styling."""
    # Format content based on type
    if isinstance(content, (dict, list)):
        formatted_content = "<pre>" + json.dumps(content, indent=2) + "</pre>"
    else:
        formatted_content = str(content)
        
    return f'''
        <div class="message-container cs-message-container">
            <div class="message cs-input-message">
                <strong class="collapsible" onclick="toggleCollapsible(this)">{input_name}</strong>
                <div class="collapsible-content">
                    <div class="content">{formatted_content}</div>
                    <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
                </div>
            </div>
            <div class="avatar bot-avatar">🤖</div>
        </div>
    '''

def generate_crew_output(content, timestamp):
    """Generates HTML for the bot's thought process in the chat log."""
    formatted_content = format_content(content)
    return f'''
        <div class="message-container cs-message-container">
            <div class="message input-message">
                <strong>Thought Process:</strong>
                <div class="content">{formatted_content}</div>
                <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
            </div>
        </div>
    '''

def generate_final_bot_response(content, timestamp):
    """Generates HTML for the final response from the bot, aligned to the right."""
    formatted_content = format_content(content)
    return f'''
        <div class="message-container cs-message-container">
            <div class="message cs-message">
                <strong>Bot Response:</strong>
                <div class="content">{formatted_content}</div>
                <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
            </div>
            <div class="avatar bot-avatar">🤖</div>
        </div>
    '''

def generate_final_bot_response_trimmed(content, timestamp):
    """Generates HTML for the final response from the bot, aligned to the right."""
    content_html = ""
    if isinstance(content, list):
        for message in content:
            content_html += f'<div class="content">{message}</div>'
    else:
        content_html = f'<div class="content">{content}</div>'
        
    return f'''
        <div class="message-container cs-message-container">
            <div class="message cs-message">
                <strong>Bot Response:</strong>
                {content_html}
                <span class="timestamp">{timestamp.strftime('%I:%M %p')}</span>
            </div>
            <div class="avatar bot-avatar">🤖</div>
        </div>
    '''

def generate_html_footer():
    """Generates the closing HTML tags."""
    return '''
    </div>
    </body>
    </html>
    '''

def write_to_html_file(html_content, filename="conversation_log.html"):
    """Writes the given HTML content to an HTML file."""
    try:
        with open(filename, "w") as file:
            file.write(html_content)
        print(f"HTML content successfully written to {filename}")
    except Exception as e:
        print(f"An error occurred while writing to the file: {e}")


def remove_html_footer(filename):
    """Removes the closing HTML tags from the end of an HTML file."""
    try:
        with open(filename, 'r') as file:
            content = file.read()
            
        # Remove the footer HTML
        footer = '''
    </div>
    </body>
    </html>
    '''
        if content.endswith(footer):
            content = content[:-len(footer)]
            
        return content
        
    except Exception as e:
        print(f"An error occurred while removing HTML footer: {e}")
