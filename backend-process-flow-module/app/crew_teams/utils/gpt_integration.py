from pydantic import BaseModel
from app.utility import chat_constant


def get_gpt_response(client, 
                      prompt: str, 
                      response_format: BaseModel = None, 
                      model: str = chat_constant.GPT_4_MODEL_CHOICES[0]) -> dict:
    """
    Gets a response from the GPT model and processes it into a structured format.

    Args:
        client: The OpenAI client instance
        prompt (str): The input prompt to send to the GPT model
        response_format (BaseModel, optional): Pydantic model for response validation. Defaults to None.
        model (str, optional): The GPT model to use. Defaults to GPT-4.

    Returns:
        dict: The processed and optionally validated response from GPT.
            If response_format is provided, returns a validated dictionary matching the Pydantic model.
            If no response_format, returns the raw GPT response.

    Raises:
        ValueError: If the GPT response fails validation against the provided Pydantic model.
    """
    if response_format:
        chat_completion_response = client.beta.chat.completions.parse(
            model=model,
            messages=prompt,
            temperature=0,
            response_format=response_format  # Enforce JSON output
        )
    else:
        chat_completion_response = client.chat.completions.create(
            model=model,
            messages=prompt,
            temperature=0,
        )
    chat_gpt_response = chat_completion_response.choices[0].message.content
    return chat_gpt_response


def get_groq_response(client, 
                      prompt: str, 
                      response_format: BaseModel = None, 
                      model: str = "llama-3.3-70b-versatile") -> dict:
    """
    Gets a response from the GROQ model and processes it into a structured format.

    Args:
        client: The GROQ client instance
        prompt (str): The input prompt to send to the GROQ model
        response_format (BaseModel, optional): Pydantic model for response validation. Defaults to None.
        model (str, optional): The GROQ model to use. Defaults to GROQ.

    Returns:
        dict: The processed and optionally validated response from GROQ.
            If response_format is provided, returns a validated dictionary matching the Pydantic model.
            If no response_format, returns the raw GROQ response.
            If response_format is provided, returns a validated dictionary matching the Pydantic model.
            If no response_format, returns the raw GPT response.

    Raises:
        ValueError: If the GPT response fails validation against the provided Pydantic model.
    """
    chat_completion_response = client.chat.completions.create(
        model=model,
        messages=prompt,
        temperature=0,
    )
    chat_gpt_response = chat_completion_response.choices[0].message.content
    return chat_gpt_response