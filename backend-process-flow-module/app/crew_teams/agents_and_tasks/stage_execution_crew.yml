crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  node_analyzer:
    role: "Node Purpose Analyzer"
    goal: "Understand and analyze the purpose and requirements of the current node"
    backstory: "An expert in analyzing conversation flow nodes and their objectives"
    model: "gpt-4o-mini"
    verbose: False

  response_validator:
    role: "User Response Validator"
    goal: "Evaluate if user responses meet the node requirements"
    backstory: "A specialist in analyzing user responses against defined requirements"
    model: "gpt-4o-mini"
    verbose: False

  state_determiner:
    role: "Node State Determiner"
    goal: "Determine the current state of the node based on user interaction"
    backstory: "An expert in evaluating conversation progress and completion states"
    model: "gpt-4o-mini"
    verbose: False

  document_extractor:
    role: "Document Extraction Necessity Analyzer"
    goal: "Determine if document extraction from ChromaDB is needed based on the node analysis"
    backstory: "An expert in identifying whether documents are required for the current conversation context"
    model: "gpt-4o-mini"
    verbose: False

  chromadb_query_constructor:
    role: "ChromaDB Query Constructor"
    goal: "Construct a query to retrieve documents from ChromaDB if document extraction is necessary"
    backstory: "A specialist in forming precise queries to extract relevant documents from ChromaDB"
    model: "gpt-4o-mini"
    verbose: False

  document_retriever:
    role: "Document Retriever"
    goal: "Use the constructed query to retrieve documents from ChromaDB, if needed"
    backstory: "An expert in executing ChromaDB queries to retrieve documents"
    model: "gpt-4o-mini"
    verbose: False
    tools:
      - initialize_chroma_and_retrieve

  response_generator:
    role: "Bot Response Generator"
    goal: "Generate appropriate responses based on node purpose and user interaction"
    backstory: "A communication specialist crafting contextual and purposeful responses"
    model: "gpt-4o-mini"
    verbose: False

  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and correctness of the generated outputs"
    backstory: "A quality assurance specialist ensuring response accuracy and relevance"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the overall execution quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to conversation outputs"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  node_analysis_task:
    agent: node_analyzer
    description: |
      Analyze the current node to understand its purpose, requirements, and expected outcomes.

      Node information:
      {stage_node}

      Current context: 
      {recent_messages}

    expected_output: |
      {
          "node_purpose": "Description of what the node aims to achieve",
          "requirements": ["List of specific requirements"],
          "expected_outcomes": ["Expected results or responses"],
          "success_criteria": ["Criteria for successful completion"]
      }
    arguments:
      - stage_node
      - recent_messages

  response_validation_task:
    agent: response_validator
    description: |
      Evaluate if user responses fulfill the node requirements.

      User messages: 
      {unread_messages}

      Previous feedback:
      {previous_feedback}

    expected_output: |
      {
          "requirements_met": ["List of met requirements"],
          "requirements_missing": ["List of unmet requirements"],
          "validation_opinion": "Detailed opinion on response adequacy",
          "confidence_score": 0.0
      }
    arguments:
      - unread_messages
      - previous_feedback
    context: [node_analysis_task]

  state_determination_task:
    agent: state_determiner
    description: |
      Determine the current state of the node execution.
    expected_output: |
      {
          "completion_status": "completed/pending/missing_info",
          "reason": "Detailed explanation for the status",
          "missing_elements": ["List of missing elements if any"],
          "next_steps": ["Recommended next steps"]
      }
    context: [response_validation_task]

  document_extraction_task:
    agent: document_extractor
    description: |
      Based on the node analysis, determine if there is a need to extract documents from ChromaDB.

      Node information:
      {stage_node}

    expected_output: |
      {
          "extraction_needed": true/false,
          "extraction_reason": "Explanation of why extraction is necessary or unnecessary"
      }
    arguments:
      - stage_node
    context: [node_analysis_task]

  chromadb_query_construction_task:
    agent: chromadb_query_constructor
    description: |
      Construct a concise and meaningful phrase for ChromaDB that summarizes the user's main request or intent based on the provided user messages.
      The query should be a brief, clear sentence or phrase that captures the essence of what the user is asking for.
      Do not write a SQL query or include references to `extraction_needed` in the query string.
      If no extraction is necessary, skip the query construction entirely.

      User messages: 
      {unread_messages}

    expected_output: |
      A single, concise sentence or phrase summarizing the user's main request or intent based on the user messages. If extraction is not required, skip query construction.

    arguments:
      - unread_messages
    context: [document_extraction_task]

  document_retrieval_task:
    agent: document_retriever
    description: |
      If a query has been constructed and `extraction_needed` is true, use the `initialize_chroma_and_retrieve` tool to retrieve documents from ChromaDB. 
      Pass the constructed query as the `query` argument, and use `external_{schema_name}` as the `collection_name`.

      If there is no query or `extraction_needed` is false, skip the document retrieval process.

      For retrieval, the tool usage should look like:
        initialize_chroma_and_retrieve(query="<constructed_query>", collection_name="external_{schema_name}")

    expected_output: |
      Return the retrieved documents as a list. If retrieval is skipped, indicate no retrieval was performed.

    arguments:
      - schema_name
    context: [document_extraction_task, chromadb_query_construction_task]

  response_generation_task:
    agent: response_generator
    description: |
      Generate an appropriate customer service response that is professional yet conversational, as if responding in a social messaging app. You are also given the retrieved documents from ChromaDB if any.

      Consider:
      - Use a friendly but professional tone
      - Be clear and concise while maintaining warmth
      - Show empathy and understanding
      - Provide helpful guidance and next steps
      - Match the user's communication style appropriately
      - Maintain brand voice and professionalism

      Previous feedback:
      {previous_feedback}
    expected_output: |
      {
          "bot_response": "Generated response message",
          "response_type": "inform/request/confirm",
          "context_references": ["Referenced context elements"],
          "response_purpose": "Purpose of the generated response"
      }
    context: [state_determination_task, document_retrieval_task]
    arguments:
      - previous_feedback

  output_analysis_task:
    agent: output_analyzer
    description: |
      Analyze the quality and correctness of the generated outputs.
    expected_output: |
      {
          "analysis_result": "Analysis of outputs",
          "correctness_score": 0.0,
          "improvement_suggestions": ["Suggested improvements"],
          "critical_issues": ["Any critical issues found"]
      }
    context:
      [node_analysis_task, response_validation_task, response_generation_task]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Score the execution based on defined quality metrics.

      #### **1. Understanding Score (0-10)**

      This evaluates the bot’s comprehension of the provided context, user intent, and its ability to identify the correct node or stage within a conversation flow.

      - **0-3: Below Standard**
        - **Description**: The bot’s understanding is superficial or inaccurate. Key intents are missed, or the incorrect node/stage is identified, indicating limited comprehension.
        - **Example**: The bot incorrectly identifies a node that doesn’t align with the user’s intent, suggesting an unrelated flow. Discrepancies are frequent and indicate poor comprehension.

      - **4-6: Basic Understanding**
        - **Description**: The bot demonstrates a basic understanding, identifying an approximate node or stage but lacking depth. Some important details are missed or misinterpreted.
        - **Example**: The bot selects a plausible node but fails to capture key nuances in the user’s intent, leading to minor misalignments with the expected flow.

      - **7-8: Good Understanding**
        - **Description**: The bot accurately identifies the correct node and stage, with minor omissions. Most details align well with the user’s intent, but a few finer points could be clarified.
        - **Example**: The bot correctly identifies the flow and stage and includes most relevant details but could improve in addressing subtle aspects of user context.

      - **9-10: Professional Understanding**
        - **Description**: The bot demonstrates comprehensive, insightful understanding. It identifies the correct node and stage with precision, showing clear comprehension of the user’s intent.
        - **Example**: The bot not only identifies the correct node but provides a nuanced understanding of user intent, capturing all relevant details accurately.

      #### **2. Validation Score (0-10)**

      This measures the quality and thoroughness of the validation checks applied by the bot, such as required fields, data types, and conditional requirements.

      - **0-3: Poor Validation**
        - **Description**: Validation is incomplete or inaccurate. Important checks are missed, and errors in field types or missing fields are not identified.
        - **Example**: The bot fails to validate that required fields are present or incorrectly identifies data types, leading to potential errors in the workflow.

      - **4-6: Basic Validation**
        - **Description**: The bot performs basic validation, checking for some required fields but missing key aspects, such as conditional requirements or correct data types.
        - **Example**: The bot verifies primary fields but overlooks conditional fields or misinterprets data type specifications, resulting in incomplete validation.

      - **7-8: Good Validation**
        - **Description**: The bot’s validation is generally accurate, covering most required fields and data types with only minor omissions in conditional checks.
        - **Example**: The bot performs thorough validation, correctly identifying most required fields and data types, with minor issues in specific conditional checks.

      - **9-10: Excellent Validation**
        - **Description**: The bot performs comprehensive validation, accurately identifying all required fields, data types, and conditional requirements with no errors.
        - **Example**: The bot meticulously validates every required field and condition, ensuring accuracy and completeness in every aspect of validation.

      #### **3. Response Score (0-10)**

      This assesses the overall quality of the bot’s response in terms of clarity, detail, and relevance to the user's query.

      - **0-3: Inadequate Response**
        - **Description**: The response is vague, unclear, or irrelevant to the user’s intent, lacking key details and leading to confusion.
        - **Example**: The bot provides a generic response without specific information relevant to the user’s query, making it difficult for the user to understand or act on.

      - **4-6: Basic Response**
        - **Description**: The response addresses the user’s query in basic terms, with some relevant information but lacking depth or clarity in places.
        - **Example**: The bot’s response contains correct information but could be more detailed or clearer, leading to partial understanding by the user.

      - **7-8: Good Response**
        - **Description**: The response is clear and mostly detailed, covering the main points of the user’s query with minimal extraneous information.
        - **Example**: The bot provides a relevant response that includes most necessary details and is easy to understand, with only minor room for improvement.

      - **9-10: Professional Response**
        - **Description**: The response is highly detailed, precise, and directly relevant, providing all necessary information concisely and effectively.
        - **Example**: The bot delivers a well-structured response that thoroughly addresses the user’s query with no extraneous information, leaving no room for misunderstanding.

    expected_output: |
      {
          "analysis_quality": {
              "score": X,
              "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
              "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
          },
          "conciseness": {
              "score": X,
              "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
              "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
          },
          "relevance_of_information": {
              "score": X,
              "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
              "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."
          }
      }

    context: [output_analysis_task]
    json_output: RubricScore
