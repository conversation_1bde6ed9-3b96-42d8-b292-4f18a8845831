crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  company_profile_builder:
    role: "Company Profile Builder"
    goal: "Create a detailed profile of the company based on metadata and provided knowledge."
    backstory: "An expert in analyzing company operations and customer demographics."
    model: "gpt-4o-mini"
    verbose: False

  customer_scenario_generator:
    role: "Customer Scenario Generator"
    goal: "Generate realistic scenarios of customer interactions based on the company profile."
    backstory: "A creative specialist in constructing plausible customer use cases."
    model: "gpt-4o-mini"
    verbose: False

  customer_personality_creator:
    role: "Customer Personality Creator"
    goal: "Define the personality traits, temperament, and emotional state for each scenario."
    backstory: "A psychologist specializing in creating relatable customer profiles."
    model: "gpt-4o-mini"
    verbose: False

  full_scenario_builder:
    role: "Full Scenario Builder"
    goal: "Combine all details into a complete customer scenario, including background, context, and objectives."
    backstory: "A skilled storyteller synthesizing customer interaction scenarios."
    model: "gpt-4o-mini"
    verbose: False

  scenario_personality_analyzer:
    role: "Scenario and Personality Analyzer"
    goal: "Evaluate the correctness and detail of customer scenarios and personalities."
    backstory: "A quality analyst focusing on verifying scenario accuracy and depth."
    model: "gpt-4o-mini"
    verbose: False

  scenario_scorer:
    role: "Scenario Scorer"
    goal: "Score scenarios using a given rubric for quality and detail."
    backstory: "An evaluation expert applying scoring metrics to assess scenario quality."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  build_company_profile:
    agent: company_profile_builder
    description: |
      Use the provided metadata, knowledge, and existing customer profiles (if any) to create a detailed and realistic profile of the company, including {num_of_customer_profiles} target customer profiles.

      - **If existing customer profiles are provided**, prioritize using them to determine the company's target customers and offerings. Refine these profiles for realism and alignment with the company's business nature and products/services.
      - **If the number of existing profiles is fewer than {num_of_customer_profiles}**, generate additional realistic profiles based on the company metadata and knowledge to meet the total of {num_of_customer_profiles} profiles.
      - **If no existing customer profiles are provided**, create {num_of_customer_profiles} realistic profiles from scratch using the company metadata and knowledge.

      Ensure that the profiles are well-aligned with the company’s operations, include plausible demographics, and reflect likely customer interactions with the company's products/services.

      Company metadata:
      {company_metadata}

      Company knowledge:
      {company_knowledge}

      Existing customer profiles (if any):
      {existing_customer_profiles}

      Previous feedback:
      {previous_feedback}

    expected_output: |
      {
          "company_name": "Name of the company",
          "business_nature": "Description of what the company does",
          "products_services": ["List of products and services offered"],
          "target_customers": [
              {
                  "profile_id": "Unique identifier for the customer profile",
                  "description": "Detailed description of the target customer, including demographics, interests, and potential needs"
              }
          ]
      }
    arguments:
      - company_metadata
      - company_knowledge
      - existing_customer_profiles
      - previous_feedback
      - num_of_customer_profiles

  generate_customer_scenarios:
    agent: customer_scenario_generator
    description: |
      Generate {num_of_customer_profiles} realistic scenarios where customers interact with the company's event planning chatbot. 
      The scenarios should follow a customer journey through different stages of event planning:

      Stage 1: Initial Enquiry
      - Customer inquires about company's event/party planning services
      - Basic questions about services, packages, and availability

      Stage 2: Event Planning Details
      - Customer proceeds to engage the company for event planning
      - Scenario should include the following event information:
        1. Name of the customer
        2. Contact number of the customer
        3. Email of the customer
        4. Event date
        5. Event start time
        6. Event end time
        7. Event location
        8. Number of adults attending the event
        9. Number of kids and age range of kids attending the event
        10. Occasion type (Either private or corporate)
        11. Occasion
        12. Theme
        13. Budget

      Stage 3: Proposal Request
      - Customer finalizes event details and requests for a proposal

      Company knowledge:
      {company_knowledge}
    expected_output: |
      [
          {
              "customer_intent": "What the customer wants to achieve",
              "query_type": "Type of query (e.g., complaint, inquiry)",
              "customer_background": "Short description of customer background",
              "short_scenario": "Short description of the scenario"
          }
      ]
    arguments:
      - num_of_customer_profiles
      - company_knowledge
    context: [build_company_profile]

  define_customer_personalities:
    agent: customer_personality_creator
    description: |
      Define the personality and temperament of customers for each scenario. There are {num_of_customer_profiles} scenarios.
      Include traits like communication style, attitude, and emotional state.
    expected_output: |
      [
          {
              "personality_traits": {
                  "temperament": "Temperament description",
                  "communication_style": "Description of how the customer communicates",
                  "emotional_state": "Current emotional state"
              }
          }
      ]
    arguments:
      - num_of_customer_profiles
    context: [generate_customer_scenarios]

  build_full_scenarios:
    agent: full_scenario_builder
    description: |
      Combine customer context, background, personality, and objectives into full, detailed scenarios for event planning interactions. Each scenario should progress through the customer journey stages and include all necessary event details.

      - **Customer Profile Information**:
        Create detailed customer profiles including:
        - Demographics (age, occupation, income level)
        - Location in Singapore
        - Previous event planning experience
        - Cultural background and preferences
        - Social status and lifestyle

      - **Event Requirements**:
        Detail the complete event specifications:
        1. Event Information:
           - Event date and timing (start/end time)
           - Venue preferences or specific location
           - Number of attendees (adults and children with age ranges)
           - Occasion type (private/corporate)
           - Specific occasion (birthday, wedding, corporate event, etc.)
           - Theme preferences
           - Budget range and constraints
        2. Special Requirements:
           - Dietary restrictions
           - Cultural considerations
           - Special needs accommodations
           - Entertainment preferences
           - Decoration preferences

      - **Customer Journey Stage**:
        Define which stage the customer is at:
        1. Initial Enquiry:
           - Questions about services and packages
           - Preliminary budget discussions
           - Date availability checks
        2. Event Planning:
           - Detailed requirements gathering
           - Venue selection
           - Theme discussion
           - Budget refinement
        3. Proposal Request:
           - Final requirements confirmation
           - Timeline expectations
           - Budget confirmation

      - **Personality Traits**:
        Detail the customer's interaction style:
        - Communication preferences (email, phone, in-person)
        - Decision-making style (quick vs. deliberate)
        - Temperament and emotional state
        - Cultural communication nuances
        - Level of involvement desired in planning

      - **Scenario Context**:
        Create a realistic narrative including:
        - Motivation for the event
        - Time constraints or urgency
        - Key concerns or priorities
        - Desired outcomes
        - Previous experiences with event planning

      **Guidelines**:
      - Ensure scenarios reflect Singapore's cultural context
      - Include realistic budget ranges for the local market
      - Consider seasonal events and weather conditions
      - Account for local venue options and restrictions
      - Incorporate common local event preferences and customs

      There are {num_of_customer_profiles} scenarios. Return in an array of dictionaries.
    expected_output: |
      [
          {
              "customer_profile": {
                  "name": "Customer's full name",
                  "contact": {
                      "phone": "Contact number",
                      "email": "Email address"
                  },
                  "demographics": {
                      "age": "Age range",
                      "occupation": "Professional background",
                      "location": "Area in Singapore",
                      "cultural_background": "Cultural context"
                  }
              },
              "event_details": {
                  "date": "Event date",
                  "timing": {
                      "start_time": "Event start time",
                      "end_time": "Event end time"
                  },
                  "location": "Event venue/location",
                  "attendees": {
                      "adults": "Number of adults",
                      "children": {
                          "count": "Number of children",
                          "age_range": "Age range of children"
                      }
                  },
                  "occasion": {
                      "type": "Private or Corporate",
                      "specific_occasion": "Type of celebration/event",
                      "theme": "Event theme",
                      "budget": "Budget range"
                  },
                  "special_requirements": {
                      "dietary": "Dietary restrictions",
                      "cultural": "Cultural considerations",
                      "other": "Other special needs"
                  }
              },
              "journey_stage": {
                  "current_stage": "Current stage in customer journey",
                  "next_steps": "Expected next steps",
                  "timeline": "Expected timeline for decision-making"
              },
              "personality": {
                  "temperament": "Customer's temperament",
                  "communication_style": "Preferred communication method and style",
                  "decision_making": "Decision-making approach",
                  "emotional_state": "Current emotional state"
              },
              "scenario_context": {
                  "motivation": "Reason for planning this event",
                  "concerns": "Key concerns or priorities",
                  "expectations": "Expected outcomes and service level",
                  "previous_experience": "Past experience with event planning"
              }
          }
      ]
    arguments:
      - num_of_customer_profiles
    context: [generate_customer_scenarios, define_customer_personalities]
    json_output: CustomerScenarioList

  # analyze_scenarios:
  #   agent: scenario_personality_analyzer
  #   description: |
  #     Evaluate the accuracy, detail, and correctness of the customer scenarios and personalities. There are {num_of_customer_profiles} scenarios.
  #   expected_output: |
  #     {
  #         "analysis": [
  #             {
  #                 "scenario_id": "Unique ID matching the scenario",
  #                 "accuracy": "Analysis of accuracy",
  #                 "detail": "Analysis of scenario detail",
  #                 "issues": ["List of identified issues"]
  #             }
  #         ]
  #     }
  #   arguments:
  #     - num_of_customer_profiles
  #   context: [build_full_scenarios, build_company_profile]

  # score_evaluation_task:
  #   agent: scenario_scorer
  #   description: |
  #     Score each scenario based on a defined rubric.

  #     ### **Rubric Metrics for Customer Full Scenario Evaluation**

  #     #### **1. Accuracy Score (0-10)**

  #     This evaluates how well the scenario aligns with the provided inputs, including customer profiles, personalities, and company context.

  #     - **0-3: Below Standard**
  #       - **Description**: The scenario contains significant inaccuracies or contradictions with inputs. Key elements like intent, personality, or context are misrepresented.
  #       - **Example**: Scenario describes a calm customer when the profile indicates an irate personality, and the intent does not match the customer’s stated goal.

  #     - **4-6: Basic Accuracy**
  #       - **Description**: The scenario captures main elements but includes minor inconsistencies or omissions.
  #       - **Example**: Scenario aligns with the customer’s intent but misrepresents their emotional state as neutral instead of frustrated.

  #     - **7-8: Good Accuracy**
  #       - **Description**: The scenario accurately reflects inputs, with only minor discrepancies or omissions.
  #       - **Example**: Scenario captures the customer’s intent and personality well but omits some context, such as their background or demographic relevance.

  #     - **9-10: Excellent Accuracy**
  #       - **Description**: The scenario is fully accurate, with no discrepancies. All elements (intent, personality, and context) align perfectly with the provided inputs.
  #       - **Example**: Scenario captures an irate customer seeking a refund for damaged goods, integrating personality traits, intent, and background seamlessly.

  #     ---

  #     #### **2. Level of Detail Score (0-10)**

  #     This evaluates how comprehensive and descriptive the scenario is, ensuring clarity and depth in customer context and persona.

  #     - **0-3: Insufficient Detail**
  #       - **Description**: The scenario is vague and lacks key descriptive elements. Background, intent, and personality traits are poorly articulated or missing.
  #       - **Example**: Scenario states, “The customer is frustrated,” but does not explain why or how their background affects their attitude.

  #     - **4-6: Basic Detail**
  #       - **Description**: The scenario includes some context but misses opportunities to enrich the narrative with vivid details.
  #       - **Example**: Scenario mentions the customer’s intent and query type but lacks depth in describing their motivations or situation.

  #     - **7-8: Good Detail**
  #       - **Description**: The scenario is detailed and engaging, with clear descriptions of most elements, though minor aspects might be omitted.
  #       - **Example**: Scenario describes the customer’s intent and emotional state but could add more nuance to their communication style or motivations.

  #     - **9-10: Exceptional Detail**
  #       - **Description**: The scenario is fully descriptive and vivid, painting a clear, realistic picture of the customer’s persona and context.
  #       - **Example**: Scenario describes an irate small business owner frustrated with late deliveries, outlining their communication style and specific motivations.

  #     ---

  #     #### **3. Relevance to Inputs Score (0-10)**

  #     This assesses how well the scenario integrates and builds on the provided context, including customer profiles, company metadata, and personalities.

  #     - **0-3: Poor Relevance**
  #       - **Description**: The scenario is disconnected from the inputs, ignoring key aspects like customer profile, personality, or intent.
  #       - **Example**: Scenario describes a customer asking for technical support when the profile specifies a retail customer seeking product information.

  #     - **4-6: Moderate Relevance**
  #       - **Description**: The scenario uses some inputs but lacks full integration or consistency.
  #       - **Example**: Scenario focuses on the customer’s intent but ignores personality traits, resulting in a generic narrative.

  #     - **7-8: Strong Relevance**
  #       - **Description**: The scenario effectively integrates most inputs, with minor gaps or inconsistencies.
  #       - **Example**: Scenario aligns well with the customer’s profile and intent but misses a detail about their demographic relevance.

  #     - **9-10: Full Relevance**
  #       - **Description**: The scenario seamlessly integrates all inputs, clearly reflecting the customer profile, intent, and company context.
  #       - **Example**: Scenario describes a busy professional seeking time-saving solutions, integrating their profile, intent, and personality traits perfectly.

  #     ---

  #     #### **4. Realism and Plausibility Score (0-10)**

  #     This determines how realistic and plausible the scenario is, based on customer behavior and the company context.

  #     - **0-3: Unrealistic**
  #       - **Description**: The scenario is implausible and does not reflect real-world behavior or interactions.
  #       - **Example**: Scenario describes a polite and patient customer filing a complaint after receiving an incomplete order, which contradicts typical behavior.

  #     - **4-6: Basic Realism**
  #       - **Description**: The scenario is somewhat plausible but includes unlikely or exaggerated elements.
  #       - **Example**: Scenario describes a customer sending multiple follow-up emails in a short span, which feels exaggerated.

  #     - **7-8: Strong Realism**
  #       - **Description**: The scenario is realistic and aligns with typical customer behavior, with only minor issues.
  #       - **Example**: Scenario depicts a frustrated customer reaching out after multiple unresolved complaints, aligning with plausible behavior.

  #     - **9-10: Fully Realistic**
  #       - **Description**: The scenario is highly plausible and reflects a deep understanding of customer behavior and context.
  #       - **Example**: Scenario describes a customer initially calm but growing irate after unresolved issues, capturing realistic escalation patterns.
  #     ---

  #   expected_output: |
  #     {
  #         "accuracy": {
  #             "score": X,
  #             "rating": "Below Standard/Basic Accuracy/Good Accuracy/Excellent Accuracy",
  #             "reason": "Scenario aligns perfectly with customer intent, personality, and company context."
  #         },
  #         "detail": {
  #             "score": X,
  #             "rating": "Insufficient/Basic Detail/Good Detail/Exceptional Detail",
  #             "reason": "Scenario provides vivid descriptions of background and intent but could elaborate on emotional state."
  #         },
  #         "relevance": {
  #             "score": X,
  #             "rating": "Poor/Moderate/Strong/Full",
  #             "reason": "All inputs are seamlessly integrated into the scenario."
  #         },
  #         "realism": {
  #             "score": X,
  #             "rating": "Unrealistic/Basic Realism/Strong Realism/Fully Realistic",
  #             "reason": "Scenario captures real-world customer behavior and escalation patterns."
  #         }
  #     }
  #   context: [analyze_scenarios]
  #   json_output: ScenarioScoreList
