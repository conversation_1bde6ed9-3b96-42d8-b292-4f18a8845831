crew_config:
  process: sequential
  verbose: False
  content_task_output: 3
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:

  context_analyzer:
    role: "Context Analyzer"
    goal: "Analyze the context and extract crucial information"
    backstory: "An expert in analyzing conversation context and extracting relevant information"
    model: "gpt-4o-mini"
    verbose: True

  reset_context_decision_maker:
    role: "Reset Context Decision Maker"
    goal: "Determine if the conversation should be reset based on unread messages, historical messages, current state, and defined flows."
    backstory: "An expert in identifying conversation flow stages based on historical and unread messages."
    model: "gpt-4o-mini"
    verbose: True

  context_resetter:
    role: "Context Resetter"
    goal: "Reset the context information based on the decision made by the reset context decision maker."
    backstory: "An expert in resetting conversation context based on the decision made by the reset context decision maker."
    model: "gpt-4o-mini"
    verbose: True
    

  flow_stage_identifier:
    role: "Flow and Stage Identifier"
    goal: "Determine the flow and stage to focus on based on unread messages, historical messages, current state, and defined flows."
    backstory: "An expert in identifying conversation flow stages based on historical and unread messages."
    model: "gpt-4o-mini"
    verbose: True

  historical_intent_summarizer:
    role: "Historical Intent Summarizer"
    goal: "Summarize the intent of the historical messages to provide context."
    backstory: "A summarization specialist skilled in distilling key intents from historical conversation data."
    model: "gpt-4o-mini"
    verbose: False

  unread_intent_analyzer:
    role: "Unread Message Intent Analyzer"
    goal: "Identify the intent within unread messages to clarify user's current objective."
    backstory: "An expert intent analyzer that specializes in interpreting recent user messages to understand immediate objectives."
    model: "gpt-4o-mini"
    verbose: False

  discussion_continuity_analyzer:
    role: "Discussion Continuity Analyzer"
    goal: "Analyze the relationship between unread messages and recent messages to identify if the conversation is continuing or has been interrupted, and explain why."
    backstory: "An advanced conversational context analyzer with expertise in detecting discussion flow and continuity across multiple message sets."
    model: "gpt-4o-mini"
    verbose: False

  flow_stage_analyzer:
    role: "Flow and Stage Analyzer"
    goal: "Analyze the current flow and stage against the focus determined in point 1."
    backstory: "An analytical agent focused on comparing current and recommended conversation flow stages for continuity."
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the analysis quality based on rubric scoring metrics."
    backstory: "An evaluation agent specialized in applying rubric metrics to ensure analysis quality and relevance."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  context_analysis_task:
    agent: context_analyzer
    description: |
      Analyze the conversation context to extract key information.

      Unread messages:
      {unread_messages}

      Recent messages:
      {recent_messages}
    expected_output: |
      {
          "event": <Type of event or occasion (e.g., wedding, dinner party, lunch). ONLY infer if explicitly mentioned (e.g., "dinner", "lunch"). If no event is mentioned, leave this field empty.>,
          "pairing_food": <Type of food to pair with wine. ONLY infer from explicit food mentions. If no food is mentioned, leave this field empty.>,
          "price_per_bottle": <Preferred price range per bottle. ONLY use when a price is explicitly mentioned. If no price is mentioned, leave this field empty.>,
          "wine_variety": <Type of wine (e.g., red wine, white wine). Use exactly what is mentioned. Do not invent varieties not in the message.>,
          "purchase_details": [
              {
                  "wine_name": <Name of wine>,
                  "quantity": <Number of bottles to purchase, default 0>
              },...
          ] <IMPORTANT: ONLY populate this list when the customer EXPLICITLY states they want to PURCHASE a specific wine (e.g., "I want to buy 2 bottles of X"). Do NOT include wines that are merely recommended by the bot or wines that the customer is only asking about. The customer must clearly express purchase intent. Otherwise, leave as an empty list.>,
          "add_to_existing_cart": <Boolean indicating whether to add to an existing cart. Set to true when: 1) customer explicitly mentions adding to an existing cart or order, OR 2) customer has already added items to the cart in previous messages and is now adding more items. Default is false.>,
          "existing_items": <List of items already in the cart, in the same format as purchase_details. This should be populated from the cart_items field in the previous purchase_product tool response if available. Only include when add_to_existing_cart is true.>
          "should_reset_context": <Boolean indicating whether to reset the context. Set to true when: 1) the user explicitly mentions wanting to "start over", "restart", "new purchase", "new order", or similar phrases, OR 2) the user indicates they want to buy something different or new after completing a previous purchase, OR 3) the conversation has been inactive for a long time (more than 30 minutes), OR 4) the user's new query is completely unrelated to the previous conversation context, OR 5) the user has completed a purchase (cart URL was created) and is now starting a new conversation. Default is false.>,
      }
    arguments:
      - unread_messages
      - recent_messages

  reset_context_decision_maker_task:
    agent: reset_context_decision_maker
    description: |
      Determine if the conversation should be reset based on unread messages, current state, and defined flows.
      
      IMPORTANT: Reset the context when:
      1. The user explicitly mentions wanting to "start over", "restart", "new purchase", "new order", or similar phrases
      2. The user indicates they want to buy something different or new after completing a previous purchase
      3. The conversation has been inactive for a long time (more than 30 minutes)
      4. The user's new query is completely unrelated to the previous conversation context
      5. The user has completed a purchase (cart URL was created) and is now starting a new conversation

      Unread messages:
      {unread_messages}

    expected_output: |
      {
          "should_reset_context": boolean,
          "reason": "Brief explanation of why the context should or should not be reset"
      }
    arguments:
      - unread_messages

  context_resetter_task:
    agent: context_resetter
    description: |
      Examine the decision made by the reset context decision maker and act accordingly:
      
      1. If the reset context decision maker determined that the context should be reset (should_reset_context is TRUE):
         Reset ALL context information to None or empty values as appropriate.
      
      2. If the reset context decision maker determined that the context should NOT be reset (should_reset_context is FALSE):
         Return the context from the context analysis task WITHOUT MODIFICATION.
      
      IMPORTANT: When resetting context, ensure ALL purchase-related fields are completely cleared:
      1. Set all fields to None or empty lists as appropriate
      2. Especially ensure purchase_details is set to an empty list
      3. Set add_to_existing_cart to false
      4. Clear existing_items completely
      
      This ensures that when a user starts a new purchase, no information from previous purchases is retained.

    expected_output: |
      # If the context should be reset:
      {
          "event": None,
          "pairing_food": None,
          "price_per_bottle": None,
          "wine_variety": None,
          "purchase_details": [],
          "add_to_existing_cart": false,
          "existing_items": [],
          "should_reset_context": true
      }
      
      # If the context should NOT be reset:
      # Return the context analysis output without modification
    context: [reset_context_decision_maker_task, context_analysis_task]

  flow_stage_identification_task:
    agent: flow_stage_identifier
    description: |
      Given the unread messages, historical messages, current state, and defined flows,
      identify and output the specific node and stage that we should focus on.

      Your primary goal is to locate the exact `node_id` within the flow that aligns with the user's intent and current state.
      In this case, the `node_id` will refer to nodes like "start-1", which can be identified based on flow conditions, 
      actions, or other node data points that match the user's intent.

      IMPORTANT ROUTING RULES:
      1. Route to "flow1-start-product-query" ONLY when the user mentions a SPECIFIC wine name (e.g., "Chateau Margaux", "Dom Perignon")
      2. Route to "flow2-start-product-recommendation" when the user mentions general wine types (e.g., "red", "white") 
         or preferences (event, food pairing, price) WITHOUT specifying an exact wine name
      3. Route to "flow3-start-purchase" when the user explicitly indicates they want to purchase a specific wine they've already discussed

      CRITICAL: You MUST use the context provided by context_resetter_task. If context_resetter_task has reset the context (cleared wine_variety, purchase_details, etc.), you MUST respect this reset and NOT re-extract this information from the messages. The reset context takes precedence over any information you might extract from the messages.

      Key information provided:
      Unread messages:
      {unread_messages}

      Recent messages:
      {recent_messages}

      Defined flows:
      {active_flow_mapping}

      Previous feedback:
      {previous_feedback}

      Your output MUST be in a JSON format.
    expected_output: |
      {
          "node_id": "The ID of the identified node in the defined flow (e.g., 'start-1')",
          "state_id": "The ID of the state that is relevant to the user's intent",
          "context": context_resetter_task
      }

    arguments:
      - unread_messages
      - recent_messages
      - active_flow_mapping
      - previous_feedback
    context: [context_resetter_task]
    json_output: MJWinesFlowStageIdentification

  historical_intent_summary_task:
    agent: historical_intent_summarizer
    description: |
      Summarize the intent of the historical messages to capture context for the conversation.

      Recent messages:
      {recent_messages}
    expected_output: |
      {
          "historical_intent_summary": "Summary of intent based on historical messages"
      }
    arguments:
      - recent_messages

  unread_intent_analysis_task:
    agent: unread_intent_analyzer
    description: |
      Identify the intent within unread messages to understand the user’s current objective.

      Unread messages:
      {unread_messages}
    expected_output: |
      {
          "unread_intent": "Identified intent within unread messages"
      }
    arguments:
      - unread_messages

  analyze_discussion_continuity_task:
    agent: discussion_continuity_analyzer
    description: |
      Analyze the continuity between unread messages and recent messages to determine if the discussion is ongoing or has been interrupted. Provide reasoning for the analysis.

      Inputs:
      - Unread messages: {unread_messages}
      - Recent messages: {recent_messages}
    expected_output: |
      {
          "is_continuing": true/false,
          "reason": "Detailed analysis explaining whether the discussion is continuing or has been interrupted, and why."
      }
    arguments:
      - unread_messages
      - recent_messages

  flow_stage_analysis_task:
    agent: flow_stage_analyzer
    description: |
      Compare the current flow and stage with the focus flow and stage identified in point 1.

      Defined flows:
      {active_flow_mapping}
    expected_output: |
      {
          "current_flow": "Current flow ID or name",
          "current_stage": "Current stage ID or name",
          "focus_match": "Boolean (true/false) indicating if current flow/stage matches focus flow/stage",
          "discrepancy": "Description of any discrepancy found (if applicable)"
      }
    arguments:
      - active_flow_mapping
    context:
      [
        flow_stage_identification_task,
        historical_intent_summary_task,
        unread_intent_analysis_task,
        analyze_discussion_continuity_task,
      ]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Apply rubric scoring to evaluate the quality of the intent analysis process.

      #### **1. Analysis Quality (0-10)**

      This measures the depth, accuracy, and clarity of the analysis performed in determining the intent and appropriate stage.

      - **0-3: Below Standard**
        - **Description**: The analysis is superficial or inaccurate, missing key intents or failing to identify the correct flow and stage. It shows limited understanding and often contains errors.
        - **Example**: The analysis incorrectly identifies the user's intent, suggesting an unrelated flow/stage. There are significant discrepancies between the analyzed and expected stages.

      - **4-6: Basic Quality**
        - **Description**: The analysis covers basic aspects of the intent and flow but lacks depth. Some key details may be missed, or the logic used may be partially unclear or inconsistent.
        - **Example**: The analysis identifies a plausible flow but misses nuances in the user's intent, leading to minor discrepancies. The focus stage is partially correct, but some elements are misinterpreted.

      - **7-8: Good Quality**
        - **Description**: The analysis is accurate, covering key aspects of the intent with minor omissions. Most details align with the expected flow/stage, though a few could be clarified or expanded.
        - **Example**: The analysis correctly identifies the flow and stage and includes most intent details, but it could have slightly improved on clarity or depth in capturing finer points of user context.

      - **9-10: Professional Quality**
        - **Description**: The analysis is comprehensive, accurate, and insightful, with a clear understanding of the user's intent. There are no discrepancies, and the flow/stage identification is spot-on.
        - **Example**: The analysis not only correctly identifies the flow and stage but also provides context that captures the nuances of user intent and context, with no missing or extraneous details.

      #### **2. Conciseness (0-10)**

      This assesses the ability to keep the analysis brief and to the point, focusing on essential details without redundancy.

      - **0-3: Excessively Detailed**
        - **Description**: The analysis is overly wordy, with much irrelevant or repetitive information that distracts from the key points. Clarity is lost in unnecessary details.
        - **Example**: The analysis includes detailed explanations for each minor point, creating a lengthy output with irrelevant historical context that could have been omitted.

      - **4-6: Moderately Concise**
        - **Description**: The analysis includes some extraneous information and could be more focused. Key points are present but buried under minor or less relevant details.
        - **Example**: While the main points are addressed, the analysis contains explanations that, while tangentially related, are not essential. The user intent is clear but could be presented more succinctly.

      - **7-8: Concise and Focused**
        - **Description**: The analysis is mostly concise, covering essential details with only minor instances of unnecessary information or repetition.
        - **Example**: The analysis focuses on the main points, only slightly veering off into minor details. It is easy to follow but could be refined by trimming a few non-essential elements.

      - **9-10: Highly Concise**
        - **Description**: The analysis is exceptionally concise, with a sharp focus on only the essential details. No redundancies or off-topic information are present.
        - **Example**: The analysis covers the key points succinctly without any superfluous information, providing a clear understanding of intent and flow in just a few focused statements.

      #### **3. Relevance of Information (0-10)**

      This category evaluates whether all provided information is directly relevant to the task's objective and intent analysis.

      - **0-3: Irrelevant or Off-Topic Content**
        - **Description**: The analysis includes off-topic information that distracts from the primary purpose. Much of the content does not align with the user's intent or relevant stages.
        - **Example**: The analysis includes unrelated details about previous conversations or hypothetical intents that are not relevant to the current stage or flow.

      - **4-6: Partially Relevant**
        - **Description**: The analysis contains some off-topic points, though it addresses the main purpose. There is room for refinement to focus more closely on the meeting's objectives.
        - **Example**: The analysis contains a few tangential points, such as minor background details that don't contribute to understanding the current intent. The key points are present but could be clearer with tighter focus.

      - **7-8: Mostly Relevant**
        - **Description**: The analysis is mostly on-topic, with minor deviations. It aligns well with the user's main purpose and captures the key aspects of intent.
        - **Example**: The analysis provides a relevant overview with only a minor digression into secondary details. It focuses on the main intent but could slightly improve by omitting one or two tangential references.

      - **9-10: Fully Relevant**
        - **Description**: The analysis is completely relevant, containing only information that directly aligns with the user's intent and the current stage requirements.
        - **Example**: Every detail in the analysis directly relates to the user's intent and current stage, with no extraneous or off-topic points. The analysis is sharply focused on fulfilling the task objective.
    expected_output: |
      {
            "analysis_quality": {
                "score": X,
                "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
                "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
            },
            "conciseness": {
                "score": X,
                "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
                "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
            },
            "relevance_of_information": {
                "score": X,
                "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
                "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."

            }
        }
    context: [flow_stage_analysis_task]
    json_output: RubricScore
