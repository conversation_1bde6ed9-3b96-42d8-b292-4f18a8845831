crew_config:
  process: sequential
  verbose: False
  content_task_output: 4
  content_task_output_json_flag: False
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  node_analyzer:
    role: "Node Purpose Analyzer"
    goal: "Understand and analyze the purpose and requirements of the current node"
    backstory: "An expert in analyzing conversation flow nodes and their objectives"
    model: "gpt-4o-mini"
    verbose: False

  response_validator:
    role: "User Response Validator"
    goal: "Evaluate if user responses meet the node requirements"
    backstory: "A specialist in analyzing user responses against defined requirements"
    model: "gpt-4o-mini"
    verbose: False

  state_determiner:
    role: "Node State Determiner"
    goal: "Determine the current state of the node based on user interaction"
    backstory: "An expert in evaluating conversation progress and completion states"
    model: "gpt-4o-mini"
    verbose: True

  tool_decider:
    role: "Tool Selection and Query Specialist"
    goal: "Analyze the node context to determine the required tool and construct the arguments for the tool."
    backstory: "An expert in identifying the most appropriate tool for the current context and formulating queries to retrieve the necessary data or documents."
    model: "gpt-4o-mini"
    verbose: True

  tool_executor:
    role: "Tool Execution Specialist"
    goal: "Execute the tool selected by the Tool Selection and Query Specialist using a key-value pair dictionary as the input and return the retrieved data in a structured format. If the value for the key is missing, use the default value of the argument type. (For example, if the value is a type string, use an empty string as the default value.)"
    backstory: "A specialist in executing tools with precision, ensuring the input adheres to the required format (key-value pair dictionary) and retrieving results based on the constructed query."
    model: "gpt-4o-mini"
    verbose: True
    tools:
      - recommend_product
      - specific_product_query
      - purchase_product
  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and correctness of the generated outputs"
    backstory: "A quality assurance specialist ensuring response accuracy and relevance"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the overall execution quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to conversation outputs"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  node_analysis_task:
    agent: node_analyzer
    description: |
      Analyze the current node to understand its purpose, requirements, and expected outcomes.

      Node information:
      {stage_node}

      Recent messages:
      {recent_messages}

      Current context: 
      {unread_messages}

    expected_output: |
      {
          "node_purpose": "Description of what the node aims to achieve",
          "requirements": ["List of specific requirements"],
          "expected_outcomes": ["Expected results or responses"],
          "success_criteria": ["Criteria for successful completion"]
      }
    arguments:
      - stage_node
      - recent_messages
      - unread_messages

  response_validation_task:
    agent: response_validator
    description: |
      Evaluate if user responses fulfill the node requirements.

      User messages: 
      {unread_messages}

      Recent messages:
      {recent_messages}
    expected_output: |
      {
          "requirements_met": ["List of met requirements"],
          "requirements_missing": ["List of unmet requirements"],
          "validation_opinion": "Detailed opinion on response adequacy",
          "confidence_score": 0.0
      }
    arguments:
      - unread_messages
      - recent_messages
    context: [node_analysis_task]

  state_determination_task:
    agent: state_determiner
    description: |
      Determine the current state of the node execution.

      Detailed flow mapping:
      {active_flow_mapping}
    expected_output: |
      {
          "completion_status": "completed/pending/missing_info",
          "reason": "Detailed explanation for the status",
          "missing_elements": ["List of missing elements if any"],
          "next_steps": ["Recommended next steps"]
      }
    arguments:
      - active_flow_mapping
    context: [response_validation_task]

  tool_selection_task:
    agent: tool_decider
    description: |
      Analyze the context to decide if a tool is needed. If a tool is needed:
      - Determine the appropriate tool to use.
      - Construct a query for the selected tool.

      Tools:
      1. specific_product_query: This function retrieves information about a specific product based on its name.
      2. recommend_product: This function suggests a product based on the current conversation state.
      3. purchase_product: This function manages the purchase process, including validating products, checking stock, and creating a cart URL. It supports adding items to an existing cart. Only select this if the user mention the product and explicitly state they want to purchase it.

      IMPORTANT - CONTEXT RESET DETECTION:
      Before selecting a tool, check if the user is attempting to:
      1. Start a new conversation (phrases like "new order", "start over", "different wines", and more)
      2. Change the topic completely from the previous conversation
      3. Begin a fresh inquiry after completing a previous purchase
      
      If any of these are detected, reset the context and DO NOT automatically use purchase_product with previous context.
      Instead, either:
      - Use recommend_product to suggest new options based on the current message
      - Use specific_product_query if they're asking about specific wines
      - Only use purchase_product if they explicitly state what they want to purchase in the current message

      Node information:
      {stage_node}

      User messages: 
      {recent_messages}

      Conversation ID:
      {conversation_id}

      Incoming message:
      {unread_messages}

      Contextual information:
      {context}
    expected_output: |
      {
          "selected_tool": "Name of the tool to use, or null if no tool is required",
          "constructed_query": "Query string for the selected tool, or null if no tool is required",
          "selection_reason": "Explanation of why the tool was selected or why no tool is needed"
      }
    arguments:
      - stage_node
      - recent_messages
      - conversation_id
      - unread_messages
      - context
    context: [node_analysis_task, state_determination_task]

  tool_execution_task:
    agent: tool_executor
    description: |
      Execute the tool selected by the Tool Selection and Query Specialist and retrieve the output.

      IMPORTANT: You must use the exact tool that was selected by the tool_selection_task.
      Do not switch to a different tool. The selected tool will be one of:
      - specific_product_query: Used when some information is missing
      - recommend_product: This function suggests a product based on the current conversation state.
      - purchase_product: This function manages the purchase process, validating products, checking stock, and creating a cart URL. It can add items to an existing cart.

      For tools, use these arguments from contextual information as needed:
      - event: Type of event or occasion (e.g., wedding, dinner party)
      - pairing_food: Type of food to pair with wine
      - price_per_bottle: Preferred price range per bottle
      - wine_variety: Type of wine (e.g., red wine, white wine)
      - purchase_details: Names of the wines and number of quantity customer wants to purchase, only populated when the customer explicitly states they want to purchase a specific wine (a list of dictionary containing zero or more items)
      - add_to_existing_cart: Boolean indicating whether to add to an existing cart (for purchase_product tool). Set to true when: 1) customer explicitly mentions adding to an existing cart or order, OR 2) customer has already added items to the cart in previous messages and is now adding more items.
      - existing_items: List of items already in the cart (for purchase_product tool when add_to_existing_cart is True). This should be populated from the cart_items field in the previous purchase_product tool response if available.

      CRITICAL - NEW PURCHASE DETECTION:
      When using the purchase_product tool, ALWAYS check if the user is starting a new purchase by looking for:
      1. Explicit mentions of "new purchase", "start over", "restart", etc.
      2. A significant change in the wines being requested compared to previous purchases
      3. A completed previous purchase followed by new product inquiries
      
      If ANY of these conditions are met, set add_to_existing_cart to FALSE and do NOT include existing_items, 
      even if they exist in the context. This ensures a fresh cart is created for the new purchase.

      Guidelines for tool execution:
      1. Check which tool was selected by tool_selection_task
      2. Extract only the arguments needed for that specific tool
      3. Only use arguments that are explicitly provided - do not invent or hallucinate values
      4. Ensure the conversation_id matches the one provided in context
      5. Pass the extracted arguments to the selected tool according to its requirements
      6. Return the raw tool output without modification

      Node information:
      {stage_node}

      Conversation ID:
      {conversation_id}

      Contextual Information:
      {context}
    expected_output: |
      Return the output of the executed tool. Do not hallucinate or input your own feedback or opinion. The tools will determine what needs to be included in the response to the customer.
    arguments:
      - stage_node
      - conversation_id
      - context
    context: [tool_selection_task]
