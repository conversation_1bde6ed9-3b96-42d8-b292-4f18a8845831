crew_config:
  process: sequential
  verbose: True
  content_task_output: 1
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  flow_state_analyzer:
    role: "Flow State Analyzer"
    goal: "Decipher the current stage in the customer service flow based on the persona, scenarios, chat history, and flow mapping. Determine the next stage if the current stage is complete."
    backstory: "An expert in understanding customer interactions and guiding the flow of conversations."
    model: "gpt-4o-mini"
    verbose: False

  response_formulator:
    role: "Response Formulator"
    goal: "Craft appropriate and conversational questions or messages for a customer to ask a customer service agent, based on the flow state, customer persona, scenarios, and chat history. Supports generating multiple questions or messages as an array"
    backstory: "An expert in creating smooth and engaging customer service interactions tailored to various scenarios and personas."
    model: "gpt-4o-mini"
    verbose: False

  message_content_analyzer:
    role: "Message Content Analyzer"
    goal: "Analyze the generated messages to ensure they are aligned with the flow mapping, customer persona, and scenarios. Evaluate their correctness, completeness, and clarity."
    backstory: "A quality control agent ensuring that generated messages are accurate and effective for customer service interactions."
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Score the messages against a predefined rubric to determine if they meet the quality threshold for content. Flag any messages that do not pass the threshold."
    backstory: "An evaluation expert ensuring that the generated messages meet the required standards."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  analyze_flow_state:
    agent: flow_state_analyzer
    description: |
      Determine the current stage in the customer service flow based on the persona, scenarios, chat history, and flow mapping. If the current stage is complete, provide the next stage.

      Customer persona:
      {customer_persona}

      Chat history:
      {chat_history}

      Flow mapping:
      {flow_mapping}
    expected_output: |
      Return the next stage in the flow mapping if the current stage is complete, else return the current stage.
    arguments:
      - customer_persona
      - chat_history
      - flow_mapping

  formulate_response:
    agent: response_formulator
    description: |
      Act as a customer chatting casually on a social messaging app. Your responses should be guided by the follow-up intent and use information from your customer persona.

      Customer persona (YOUR IDENTITY - USE THIS FOR ANSWERS):
      {customer_persona}

      Chat history (Reference context):
      {chat_history}

      Follow up intent (IMPORTANT - FOLLOW THIS GUIDE):
      {conversation_analysis}

      **Response Priority**:
      1. FOLLOW THE FOLLOW-UP INTENT PRECISELY - This is your main guide for what to say next
      2. USE YOUR PERSONA INFO to answer any questions:
         - Check your persona details for specific preferences
         - Use event details, budget, and requirements from your profile
         - Stay consistent with your personality traits
      3. Keep responses natural and chat-like

      **Message Guidelines**:
      - ONE purpose per message - never combine multiple topics
      - Keep each message under 15 words
      - Write like you're texting a friend
      - Use natural pauses between messages
      - Add emojis that match your persona 😊

      **Language Style**:
      - Use chat abbreviations (btw, thx, etc)
      - Keep tone casual and friendly
      - Write in lowercase unless emphasizing
      - Match your persona's communication style

      **Examples of Good Messages**:
      ❌ "Hi, I need AC servicing next week and also want to know the price and duration"
      ✅ "hey!"
      ✅ "need AC servicing next week"
      ✅ "btw what's the price?"
      ✅ "how long will it take?"

      Return in a JSON format with a single message that follows the follow-up intent.

    expected_output: |
      {
          "formulated_messages": [
              "Message following the follow-up intent and using persona information"
          ]
      }
    arguments:
      - customer_persona
      - chat_history
      - conversation_analysis
    context: [analyze_flow_state]
    json_output: FormulatedMessagesModel

  analyze_messages:
    agent: message_content_analyzer
    description: |
      Analyze the generated messages to ensure they align with the flow mapping, customer persona, and scenarios. Evaluate their correctness, completeness, and clarity.

      Chat history:
      {chat_history}

      Flow mapping:
      {flow_mapping}
    expected_output: |
      {
          "analysis": [
              {
                  "message": "Message content",
                  "correctness": "Correct/Incorrect",
                  "completeness": "Complete/Incomplete",
                  "clarity": "Clear/Unclear",
                  "issues": ["List of identified issues"]
              }
          ]
      }
    arguments:
      - chat_history
      - flow_mapping
    context: [formulate_response]

  score_evaluation_task:
    agent: rubric_scorer
    description: |
      Score the messages based on the predefined rubric. Evaluate correctness, completeness, clarity, and alignment with customer intent. Determine if the messages pass the quality threshold.

      # **Rubric Metrics for Customer Full Scenario Evaluation**

      ## **1. Accuracy Score (0-10)**
      This evaluates how well the scenario aligns with the provided inputs, including customer profiles, personalities, and company context.

      | **Score Range**         | **Description**                                                                                                        | **Example**                                                                                   |
      |--------------------------|------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Below Standard)** | The scenario contains significant inaccuracies or contradictions with inputs. Key elements like intent, personality, or context are misrepresented. | Scenario describes a calm customer when the profile indicates an irate personality, and the intent does not match the customer’s stated goal. |
      | **4-6 (Basic Accuracy)** | The scenario captures main elements but includes minor inconsistencies or omissions.                                   | Scenario aligns with the customer’s intent but misrepresents their emotional state as neutral instead of frustrated. |
      | **7-8 (Good Accuracy)**  | The scenario accurately reflects inputs, with only minor discrepancies or omissions.                                   | Scenario captures the customer’s intent and personality well but omits some context, such as their background or demographic relevance. |
      | **9-10 (Excellent Accuracy)** | The scenario is fully accurate, with no discrepancies. All elements (intent, personality, and context) align perfectly with the provided inputs. | Scenario captures an irate customer seeking a refund for damaged goods, integrating personality traits, intent, and background seamlessly. |

      ---

      ## **2. Level of Detail Score (0-10)**
      This evaluates how comprehensive and descriptive the scenario is, ensuring clarity and depth in customer context and persona.

      | **Score Range**         | **Description**                                                                                                        | **Example**                                                                                   |
      |--------------------------|------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Insufficient Detail)** | The scenario is vague and lacks key descriptive elements. Background, intent, and personality traits are poorly articulated or missing. | Scenario states, “The customer is frustrated,” but does not explain why or how their background affects their attitude. |
      | **4-6 (Basic Detail)**   | The scenario includes some context but misses opportunities to enrich the narrative with vivid details.                | Scenario mentions the customer’s intent and query type but lacks depth in describing their motivations or situation. |
      | **7-8 (Good Detail)**    | The scenario is detailed and engaging, with clear descriptions of most elements, though minor aspects might be omitted. | Scenario describes the customer’s intent and emotional state but could add more nuance to their communication style or motivations. |
      | **9-10 (Exceptional Detail)** | The scenario is fully descriptive and vivid, painting a clear, realistic picture of the customer’s persona and context. | Scenario describes an irate small business owner frustrated with late deliveries, outlining their communication style and specific motivations. |

      ---

      ## **3. Relevance to Inputs Score (0-10)**
      This assesses how well the scenario integrates and builds on the provided context, including customer profiles, company metadata, and personalities.

      | **Score Range**         | **Description**                                                                                                        | **Example**                                                                                   |
      |--------------------------|------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Poor Relevance)** | The scenario is disconnected from the inputs, ignoring key aspects like customer profile, personality, or intent.       | Scenario describes a customer asking for technical support when the profile specifies a retail customer seeking product information. |
      | **4-6 (Moderate Relevance)** | The scenario uses some inputs but lacks full integration or consistency.                                            | Scenario focuses on the customer’s intent but ignores personality traits, resulting in a generic narrative. |
      | **7-8 (Strong Relevance)** | The scenario effectively integrates most inputs, with minor gaps or inconsistencies.                                  | Scenario aligns well with the customer’s profile and intent but misses a detail about their demographic relevance. |
      | **9-10 (Full Relevance)** | The scenario seamlessly integrates all inputs, clearly reflecting the customer profile, intent, and company context.    | Scenario describes a busy professional seeking time-saving solutions, integrating their profile, intent, and personality traits perfectly. |

      ---

      ## **4. Realism and Plausibility Score (0-10)**
      This determines how realistic and plausible the scenario is, based on customer behavior and the company context.

      | **Score Range**         | **Description**                                                                                                        | **Example**                                                                                   |
      |--------------------------|------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Unrealistic)**    | The scenario is implausible and does not reflect real-world behavior or interactions.                                   | Scenario describes a polite and patient customer filing a complaint after receiving an incomplete order, which contradicts typical behavior. |
      | **4-6 (Basic Realism)**  | The scenario is somewhat plausible but includes unlikely or exaggerated elements.                                       | Scenario describes a customer sending multiple follow-up emails in a short span, which feels exaggerated. |
      | **7-8 (Strong Realism)** | The scenario is realistic and aligns with typical customer behavior, with only minor issues.                           | Scenario depicts a frustrated customer reaching out after multiple unresolved complaints, aligning with plausible behavior. |
      | **9-10 (Fully Realistic)** | The scenario is highly plausible and reflects a deep understanding of customer behavior and context.                   | Scenario describes a customer initially calm but growing irate after unresolved issues, capturing realistic escalation patterns. |

      Return the scoring in JSON format.
    expected_output: |
      {
          "accuracy": {
              "score": X,
              "rating": "Below Standard/Basic Accuracy/Good Accuracy/Excellent Accuracy",
              "reason": "Explanation of how well the scenario aligns with the provided inputs, including customer profiles, personalities, and company context."
          },
          "detail": {
              "score": X,
              "rating": "Insufficient/Basic Detail/Good Detail/Exceptional Detail",
              "reason": "Explanation of how comprehensive and descriptive the scenario is, ensuring clarity and depth in customer context and persona."
          },
          "relevance": {
              "score": X,
              "rating": "Poor/Moderate/Strong/Full",
              "reason": "Explanation of how well the scenario integrates and builds on the provided context, including customer profiles, company metadata, and personalities."
          },
          "realism": {
              "score": X,
              "rating": "Unrealistic/Basic Realism/Strong Realism/Fully Realistic",
              "reason": "Explanation of how realistic and plausible the scenario is, based on customer behavior and the company context."
          }
      }
    context: [analyze_messages]
    json_output: ConversationQuestionScoreModel
