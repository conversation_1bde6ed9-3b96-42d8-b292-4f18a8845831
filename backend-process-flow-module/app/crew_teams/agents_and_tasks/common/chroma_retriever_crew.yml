crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  document_extractor:
    role: "Document Extraction Necessity Analyzer"
    goal: "Determine if document extraction is required based on the node analysis and specify the type of extraction needed (ChromaDB, service extraction, or other)."
    backstory: "An expert in identifying document or data retrieval needs based on the node context."
    model: "gpt-4o-mini"
    verbose: False

  query_constructor:
    role: "Dynamic Query Constructor"
    goal: "Construct a query for the specified type of extraction based on node requirements (e.g., ChromaDB or service extraction)."
    backstory: "A specialist in forming precise queries tailored to the node's context."
    model: "gpt-4o-mini"
    verbose: False

  retriever:
    role: "Data Retriever"
    goal: "Execute the appropriate tool based on the specified extraction type (ChromaDB or service extraction) and return the retrieved data."
    backstory: "An expert in executing various retrieval tools to fetch relevant data or documents."
    model: "gpt-4o-mini"
    verbose: False
    tools:
      - initialize_chroma_and_retrieve

  data_summarizer:
    role: "Data Summarization Specialist"
    goal: "Summarize the retrieved data into a concise and meaningful output that highlights key information."
    backstory: "An expert in extracting and summarizing essential details from complex data for clarity and actionable insights."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  document_extraction_task:
    agent: document_extractor
    description: |
      Based on the node analysis, determine if there is a need to extract documents or data. Specify the type of extraction required, such as querying ChromaDB or retrieving service information.

      Node information:
      {stage_node}

    expected_output: |
      {
          "extraction_needed": true/false,
          "extraction_type": "chroma_db/service/other",
          "extraction_reason": "Explanation of why extraction is necessary or unnecessary"
      }
    arguments:
      - stage_node
    context: [node_analysis_task]

  query_construction_task:
    agent: query_constructor
    description: |
      Construct a concise query based on the specified type of extraction (e.g., ChromaDB or service extraction). The query should summarize the user's main request or the context provided in the node.

      If the extraction type is ChromaDB:
        - Construct a query to retrieve documents from ChromaDB.

      If the extraction type is service extraction:
        - Construct a query to retrieve service-related information.

      If extraction is not required, skip query construction.

      Node information:
      {stage_node}
      User messages: 
      {unread_messages}

    expected_output: |
      {
          "constructed_query": "Constructed query string based on the specified extraction type. Skip if no extraction is required."
      }
    arguments:
      - unread_messages
      - stage_node
    context: [document_extraction_task]

  data_retrieval_task:
    agent: retriever
    description: |
      Based on the constructed query and specified extraction type, use the appropriate tool to retrieve data.

      - For ChromaDB queries:
        Use the `initialize_chroma_and_retrieve` tool with the constructed query from query_construction_task.

      - For service extraction:
        Use the `about_services_or_decors_v2` tool with the constructed query.

      If no query or extraction is needed, skip the retrieval process.

    expected_output: |
      {
          "retrieved_data": "List of retrieved documents or information. Indicate if retrieval was skipped."
      }
    context: [document_extraction_task, query_construction_task]

  data_summarization_task:
    agent: data_summarizer
    description: |
      Summarize the information retrieved from the data_retrieval_task. Extract and present the key points in a detailed, comprehensive, and user-friendly format.

      The summary should be tailored to the node purpose.

      Node information:
      {stage_node}

    expected_output: |
      {
          "summary": "A detailed and comprehensive summary of the retrieved data, highlighting the main points or relevant information to the node purpose."
      }
    arguments:
      - stage_node
    context: [data_retrieval_task]
