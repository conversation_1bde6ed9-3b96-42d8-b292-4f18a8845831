crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  metadata_handler:
    role: "Chat Metadata Handler"
    goal: "Extract relevant metadata action based on the latest conversation."
    backstory: "Specializes in extracting actionable insights and updating metadata from customer conversations."
    model: "gpt-4o-mini"
    verbose: True

tasks:
  metadata_extraction_task:
    agent: metadata_handler
    description: |
      Extract concise and relevant customer metadata such as name, contact number, email, appointment details (date, time, location, reason), and other insights for analytics. If information is missing in the conversation, leave fields blank. Avoid adding unverified or conversational text.

      Unread messages:
      {unread_messages}

      Recent messages:
      {recent_messages}

      Bot planned response:
      {response_output}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
        "metadata": {
            <Key Value Pairs>,
            ...
          }
      }
    arguments:
      - unread_messages
      - recent_messages
      - response_output
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: MetadataModel
