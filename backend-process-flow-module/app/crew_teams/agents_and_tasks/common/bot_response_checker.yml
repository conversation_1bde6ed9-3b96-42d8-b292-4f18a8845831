crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  response_analyzer:
    role: "Response Analyzer"
    goal: "Determine if the bot's planned reply is required."
    backstory: "An expert in analyzing historical messages and determining the necessity of replies ."
    model: "gpt-4o-mini"
    verbose: True

tasks:
  response_analysis_task:
    agent: response_analyzer
    description: |
      Your task is to determine whether a reply is required to the conversation, especially to the latest customer message. Consider the following guidelines:
        1. If the conversation is in the middle of a process to perform an action, and the reply is to collect more information, then is_reply_required is True.
        2. If the reply adds value to the customer, then is_reply_required is True.
        3. If the last message in the conversation is only expressing gratitude and the reply does not add any value to the customer, then is_reply_required is False.
        4. Unless the customer's messages or past messages clearly indicate an intention to end the chat (e.g., "bye" or "thanks!"), we should continue to respond, and is_reply_required is True.

      Historical Messages between customer (user) and customer service representative (assistant):
      {recent_messages}

      Planned reply to send to the customer:
      {bot_response}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "is_reply_required": "<True/False>",
        "reason": "<Reason for the reply to be required or not>",
      }
    arguments:
      - recent_messages
      - bot_response
    guardrails:
      - validate_pydantic_output
      - validate_json_output
    max_retries: 5
    output_pydantic: BotReplyRequiredResponse
