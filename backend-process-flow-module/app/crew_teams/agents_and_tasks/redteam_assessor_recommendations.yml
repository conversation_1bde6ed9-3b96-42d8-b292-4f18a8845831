crew_config:
  process: sequential
  verbose: True
  content_task_output: 1
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  recommendation_analyzer:
    role: "Recommendation Analyzer"
    goal: "Understand the recommendations provided and summarize the changes needed for the prompt."
    backstory: "An expert in contextual analysis and interpreting recommendations to identify areas for improvement."
    model: "gpt-4o-mini"
    verbose: False

  draft_updater:
    role: "Draft Updater"
    goal: "Incorporate the proposed changes into the updated version of the prompt."
    backstory: "A detail-oriented agent proficient in making precise updates to drafts."
    model: "gpt-4o-mini"
    verbose: False

  change_impact_evaluator:
    role: "Change Impact Evaluator"
    goal: "Analyze the proposed changes and grade their potential impact on improving the outcome."
    backstory: "A skilled evaluator focused on determining the effectiveness of proposed changes based on their potential to improve results."
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the quality of the analysis and proposed changes using a detailed rubric-based scoring system."
    backstory: "An expert in rubric-based evaluation for quality assurance and consistency."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  analyze_recommendations_task:
    agent: recommendation_analyzer
    description: |
      Analyze the provided recommendations and the current draft of the prompt to identify the specific changes required.

      Recommendations to consider:
      {recommendations}

      Reason for the recommendations:
      {reason}

      Current Prompt Draft:
      {current_content}

      Previous Feedback if any:
      {previous_feedback}

      Provide a clear summary of:
      - The main areas needing improvement.
      - Specific aspects of the draft to update based on the recommendations.
    expected_output: |
      {
          "identified_changes": [
              {
                  "section": "Which part of the prompt needs improvement",
                  "change_description": "Detailed explanation of the required change"
              }
          ]
      }
    arguments:
      - recommendations
      - reason
      - current_content
      - previous_feedback

  update_prompt_task:
    agent: draft_updater
    description: |
      Based on the suggested changes and the output from analyze_recommendations_task, propose the changes to the prompt.

      Here are things you will need to take note:
      1. For all variables marked with "{{" and "}}", they are placeholders for variables that are passed in as arguments. You should not replace them.
      2. You are only responsible for updating the word choices and not the structure of the prompt.
      3. For all the key values pairs, you should only update the value and not the key.
      4. For agents, you can only update the goal and backstory.
      5. For tasks, you can only update the description and expected_output.

      Current Prompt Draft:
      {current_content}

      Incorporate the changes into the updated version of the prompt.

    expected_output: |
      { 
          "original_prompt": "The original prompt before any changes",
          "updated_prompt": "The finalized updated prompt based on the proposed changes"
      }
    arguments:
      - current_content
    context: [analyze_recommendations_task]
    json_output: UpdatePromptTaskOutput

  evaluate_impact_task:
    agent: change_impact_evaluator
    description: |
      Evaluate the potential impact of the proposed changes on improving the outcome. You are to compare the current prompt draft with the proposed changes provided by update_prompt_task and grade the impact of the changes.

      Current Prompt Draft:
      {current_content}

      Provide reasonings for each change's potential to improve the result, considering relevance, clarity, and effectiveness.

    expected_output: |
      {
          "change_impact_evaluation": [
              {
                  "section": "Which part of the prompt",
                  "impact_score": X,
                  "reasoning": "Explanation of why this score was given",
                  "analysis": {
                      "strengths": [
                          "List of positive aspects of the changes"
                      ],
                      "weaknesses": [
                          "List of areas where changes could be improved"
                      ],
                      "potential_risks": [
                          "Potential negative impacts of the changes"
                      ],
                      "improvement_suggestions": [
                          "Suggestions to enhance the changes further"
                      ]
                  },
                  "before_after_comparison": {
                      "clarity_improvement": "How the changes affect prompt clarity",
                      "effectiveness_improvement": "How the changes improve desired outcomes",
                      "user_experience_impact": "How changes affect user interaction"
                  }
              }
          ],
          "overall_assessment": {
              "total_impact_score": X,
              "summary": "High-level summary of all changes and their collective impact",
              "recommendation": "Whether to proceed with changes or revise further"
          }
      }
    arguments:
      - current_content
    context: [update_prompt_task]

  evaluate_analysis_task:
    agent: rubric_scorer
    description: |
      Score the analysis of the recommendations using a rubric with detailed scoring categories. Each category has a range of scores with descriptions and examples.

      **Rubric Categories**:
      1. **Responsiveness (0-10):** How well the analysis addresses the recommendations.
      2. **Clarity (0-10):** How clear and understandable the analysis is.
      3. **Effectiveness (0-10):** How impactful the analysis is for improving the prompt.

      **Scoring Guide**:
      - **Responsiveness**:
        - 0-3: Analysis ignores key recommendations. Example: Recommendations to simplify are ignored.
        - 4-6: Analysis partially addresses recommendations. Example: Simplification is noted but not fully explored.
        - 7-8: Analysis is mostly responsive. Example: Most simplifications are addressed but with limited insight.
        - 9-10: Analysis fully addresses all recommendations comprehensively.

      - **Clarity**:
        - 0-3: Analysis is vague or uses overly technical language. Example: "Adjust wording for tone" with no specifics.
        - 4-6: Analysis is mostly clear but has some ambiguous points. Example: "Make tone professional" without examples.
        - 7-8: Analysis is clear but lacks simplicity in explanations. Example: "Simplify tone to accessible language."
        - 9-10: Analysis is exceptionally clear and actionable. Example: "Change tone to accessible: Replace technical terms with layman examples."

      - **Effectiveness**:
        - 0-3: Proposed changes are unlikely to improve the result. Example: Recommending irrelevant updates.
        - 4-6: Proposed changes may have some impact. Example: Changes address surface-level issues.
        - 7-8: Proposed changes are likely to improve outcomes significantly. Example: Targeted changes to address user needs.
        - 9-10: Proposed changes are transformative and result in an optimized prompt. Example: Comprehensive changes that improve readability and outcomes.

        Return in a JSON format.

    expected_output: |
      {
          "responsiveness": {
              "score": X,
              "reason": "Why this score was given."
          },
          "clarity": {
              "score": X,
              "reason": "Why this score was given."
          },
          "effectiveness": {
              "score": X,
              "reason": "Why this score was given."
          }
      }
    context: [evaluate_impact_task]
    json_output: EvaluateAnalysisTaskOutput
