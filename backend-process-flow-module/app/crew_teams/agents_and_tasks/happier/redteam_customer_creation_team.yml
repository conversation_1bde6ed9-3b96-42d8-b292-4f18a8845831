crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.7

default_model: "groq/llama-3.3-70b-versatile"

agents:
  company_profile_builder:
    role: "Company Profile Builder"
    goal: "Create a detailed profile of the company based on metadata and provided knowledge."
    backstory: "An expert in analyzing company operations and customer demographics."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  customer_scenario_generator:
    role: "Customer Scenario Generator"
    goal: "Generate realistic scenarios of customer interactions based on company profiles, incorporating real customer messaging behaviors and tone."
    backstory: "A creative specialist in mimicking actual customer conversations and constructing plausible use cases."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  customer_personality_creator:
    role: "Customer Personality Creator"
    goal: "Define customer traits that reflect real-world behaviors, tone, and word choice in inquiries, making interactions sound authentic and relatable."
    backstory: "A psychologist with expertise in analyzing customer messaging and emotional states to replicate natural communication styles."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  full_scenario_builder:
    role: "Full Scenario Builder"
    goal: "Create complete, customer-centric scenarios using conversational language that aligns with real-life customer behaviors."
    backstory: "An expert storyteller who captures authentic customer voices, including tone, style, word choice, and messaging brevity."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  scenario_personality_analyzer:
    role: "Scenario and Personality Analyzer"
    goal: "Evaluate the correctness and detail of customer scenarios and personalities."
    backstory: "A quality analyst focusing on verifying scenario accuracy and depth."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  scenario_scorer:
    role: "Scenario Scorer"
    goal: "Score scenarios using a given rubric for quality and detail."
    backstory: "An evaluation expert applying scoring metrics to assess scenario quality."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

tasks:
  build_company_profile:
    agent: company_profile_builder
    description: |
      Use the provided metadata, knowledge, and existing customer profiles (if any) to create a detailed and realistic profile of the company, including {num_of_customer_profiles} target customer profiles.

      - **If existing customer profiles are provided**, prioritize using them to determine the company's target customers and offerings. Refine these profiles for realism and alignment with the company's business nature and products/services.
      - **If the number of existing profiles is fewer than {num_of_customer_profiles}**, generate additional realistic profiles based on the company metadata and knowledge to meet the total of {num_of_customer_profiles} profiles.
      - **If no existing customer profiles are provided**, create {num_of_customer_profiles} realistic profiles from scratch using the company metadata and knowledge.

      Ensure that the profiles are well-aligned with the company’s operations, include plausible demographics, and reflect likely customer interactions with the company's products/services.

      Company metadata:
      {company_metadata}

      Company knowledge:
      {company_knowledge}

      Existing customer profiles (if any):
      {existing_customer_profiles}

      Previous feedback:
      {previous_feedback}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "company_name": "Name of the company",
          "business_nature": "Description of what the company does",
          "products_services": ["List of products and services offered"],
          "target_customers": [
              {
                  "profile_id": "Unique identifier for the customer profile",
                  "description": "Detailed description of the target customer, including demographics, interests, and potential needs"
              }
          ]
      }
    arguments:
      - company_metadata
      - company_knowledge
      - existing_customer_profiles
      - previous_feedback
      - num_of_customer_profiles

  generate_customer_scenarios:
    agent: customer_scenario_generator
    description: |
      Generate {num_of_customer_profiles} realistic scenarios where customers interact with the company's chatbot. The scenarios must replicate actual customer messaging behaviors:

      1. Use **short, casual messaging styles** common in chat platforms.
      2. Include realistic tones like:
        - Friendly and polite ("Hi there, how much for face painting?")
        - Direct and concise ("How much for 1 hour ice cream cart?")
        - Urgent requests ("Need balloon sculpting for tomorrow, available?")
        - Enthusiastic and detailed ("Hi, planning a party for my 10-year-old. Would love face painting, magic show, and balloons!")
      3. Ensure queries align with **typical customer motivations**:
        - Event details: type, theme, date, number of attendees
        - Budget constraints: realistic ranges (SGD 100 to SGD 1500)
        - Preferences: decorations, entertainment, food stands, etc.
      4. Incorporate **emotional states** like excitement, stress (urgent), curiosity, or indecisiveness.

      Ensure the scenarios flow logically through:
      - Initial inquiry  
      - Event details confirmation  
      - Proposal request

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      [
          {
              "customer_intent": "What the customer wants to achieve",
              "query_type": "Inquiry, request, or urgent need",
              "customer_background": "Short background context",
              "short_scenario": "Realistic, conversational style interaction"
          }
      ]
    arguments:
      - num_of_customer_profiles
      - company_knowledge
    context: [build_company_profile]

  define_customer_personalities:
    agent: customer_personality_creator
    description: |
      Define customer personalities that encompass a wide range of real-world tones and messaging behaviors:
      - **Temperament:** Can vary from negative traits like anger, rudeness, and uncooperativeness to positive traits such as friendliness, calmness, and cooperation.
      - **Communication style:**  
        - May range from dismissive and curt responses ("Whatever, just do it.") to friendly and engaging queries ("Could you please help me with this?")
        - The tone can be from casual to demanding.
      - **Emotional states:** Span from negative (frustrated, annoyed) to neutral (indecisive, curious) to positive (enthusiastic, eager).

      Ensure that the personality traits align with the context of queries provided. Each personality should naturally align with the type of inquiry and the anticipated emotional context.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      [
          {
              "personality_traits": {
                  "temperament": "Temperament description",
                  "communication_style": "Real-world tone and phrasing style",
                  "emotional_state": "Emotional context for the inquiry"
              }
          }
      ]
    arguments:
      - num_of_customer_profiles
    context: [generate_customer_scenarios]

  build_full_scenarios:
    agent: full_scenario_builder
    description: |
      Create customer scenarios that use short, casual messaging style typical of social apps. Messages should be brief, informal, and straight to the point.

      **Primary Objective**:
      - MAIN GOAL: Enquire about the event that you are planning and ultimately, getting the Proposal A and Proposal B from customer service
      - Reach out to the customer officer to start the conversation to plan your event.
      - CONTINUE conversation until BOTH EVENT PROPOSALS are explicitly provided
      - Can accept or decline recommended services along the way

      **Interaction Strategy**:
      - Start with initial inquiry about event planning
      - Provide necessary details for proposal generation
      - When recommendations are made:
        * Review and respond to service suggestions
        * Make selections based on persona preferences
        * Continue asking about proposals if not yet received
      - ONLY conclude when both Proposal A and B are explicitly provided

      - **Customer Profile Information**:
        Create detailed customer profiles including:
        - Demographics (age, occupation, income level)
        - Location in Singapore
        - Previous event planning experience
        - Cultural background and preferences
        - Social status and lifestyle
        - Conversation style and information sharing pattern

      - **Event Requirements**:
        Detail the complete event specifications that will be revealed through conversation:
        1. Event Information:
           - Event date and timing (start/end time) (MUST BE AT LEAST 3 DAYS FROM {date_today})
           - Venue preferences or specific location
           - Number of attendees (adults and children with age ranges)
           - Occasion type (private/corporate)
           - Specific occasion (birthday, wedding, corporate event, etc.)
           - Theme preferences
           - Budget range and constraints
        2. Special Requirements:
           - Dietary restrictions
           - Cultural considerations
           - Special needs accommodations
           - Entertainment preferences
           - Decoration preferences

      - **Personality Traits**:
        Define diverse customer interaction styles:
        - Communication style: 
          Range from terse and abrupt to overly verbose and friendly
        - Decision-making approach: 
          Span from impulsive to overly analytical
        - Temperament: 
          Vary from calm and cooperative to irritable and demanding
        - Cultural sensitivity: 
          Include both culturally aware and insensitive personas
        - Involvement level: 
          Range from hands-off to micromanaging
        - Information sharing: 
          Vary from withholding details to oversharing
        - Flexibility: 
          Span from rigid and unyielding to overly accommodating
        - Time management: 
          Range from punctual and organized to chronically late
        - Cooperation level:
          Include uncooperative personas who avoid answering questions and ask irrelevant ones

      - **Scenario Context**:
        Create a realistic narrative including:
        - Motivation for the event
        - Time constraints or urgency
        - Key concerns or priorities
        - Desired outcomes
        - Previous experiences with event planning
        - Specific triggers for providing information

      **Guidelines**:
      - Ensure scenarios reflect Singapore's cultural context
      - Include realistic budget ranges for the local market
      - Consider seasonal events and weather conditions
      - Account for local venue options and restrictions
      - Incorporate common local event preferences and customs
      - Define clear patterns for information sharing

      **Success Criteria**:
      - Conversation MUST continue until BOTH Proposal A and B are explicitly mentioned in customer officer's response

      There are {num_of_customer_profiles} scenarios. Return in an array of dictionaries.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
        "scenarios": [
          {
                "customer_profile": {
                    "name": "Customer's full name",
                    "contact": {
                        "phone": "Contact number",
                        "email": "Email address"
                    },
                    "demographics": {
                        "age": "Age range",
                        "occupation": "Professional background",
                        "location": "Area in Singapore",
                        "cultural_background": "Cultural context"
                    }
                },
                "additional_details": {
                    "date": "Event date",
                    "timing": {
                        "start_time": "Event start time",
                        "end_time": "Event end time"
                    },
                    "location": "Event venue/location",
                    "attendees": {
                        "adults": "Number of adults",
                        "children": {
                            "count": "Number of children",
                            "age_range": "Age range of children"
                        }
                    },
                    "occasion": {
                        "type": "Private or Corporate",
                        "specific_occasion": "Type of celebration/event",
                        "theme": "Event theme",
                        "budget": "Budget range"
                    },
                    "special_requirements": {
                        "dietary": "Dietary restrictions",
                        "cultural": "Cultural considerations",
                        "other": "Other special needs"
                    }
                },
                "journey_stage": {
                    "current_stage": "Current stage in customer journey",
                    "next_steps": "Expected next steps",
                    "timeline": "Expected timeline for decision-making"
                },
                "personality": {
                    "temperament": "Customer's temperament",
                    "communication_style": "Preferred communication method and style",
                    "decision_making": "Decision-making approach",
                    "emotional_state": "Current emotional state"
                },
                "scenario_context": {
                    "motivation": "Reason for planning this event",
                    "concerns": "Key concerns or priorities",
                    "expectations": "Expected outcomes and service level",
                    "previous_experience": "Past experience with event planning"
                },
                "objectives": {
                    <Success Criteria and Objectives>
                }
            }
        ]
      }
    arguments:
      - num_of_customer_profiles
      - date_today
    context: [generate_customer_scenarios, define_customer_personalities]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: CustomerScenarioList
