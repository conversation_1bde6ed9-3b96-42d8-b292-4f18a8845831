crew_config:
  process: sequential
  verbose: True
  content_task_output: 1
  content_task_output_json_flag: False

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  response_reasonability_validator:
    role: "WhatsApp Response Reasonability Validator"
    goal: "Analyze the updated_bot_response to identify any piece of information that are unreasonable or inconsistent with the context."
    backstory: "A specialist in message context evaluation, ensuring all response details align with the conversation and are appropriate."
    model: "gpt-4o-mini"
    verbose: False

  response_adjuster:
    role: "WhatsApp Response Adjuster"
    goal: "Modify the updated_bot_response based on the identified issues, ensuring minimal changes while preserving the original intent."
    backstory: "An expert in precise text adjustment, committed to making only the necessary corrections to align with conversation context."
    model: "gpt-4o-mini"
    verbose: False

  response_json_formulator:
    role: "WhatsApp Message Formatter"
    goal: "Transform bot responses into concise WhatsApp message bubbles"
    backstory: "An expert in crafting engaging WhatsApp conversations that maintain message clarity while optimizing for mobile reading"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  response_reasonability_task:
    agent: response_reasonability_validator
    description: |
      Review the Updated Bot Response solely for its phrasing and word choice.
      
      Ensure that the response does not address or refer to the customer by name or any personal identifier. Evaluate the response within its context to confirm that the information is consistently presented, without any misalignment. Disregard issues related to missing services, proposals, or broader conversation context—only the phrasing matters.

      Updated Bot Response:
      {updated_bot_response}

      Context:
      {context}

    expected_output: |
      {
        "is_reasonable": true,
        "issues": [
          // List issues here, e.g., "Greeting is redundant", "Name mismatch: expected Jon, found Saori"
        ],
        "suggestions": "Detailed suggestions for corrections if any issues are found. If no issues, state that the response is reasonable."
      }
    arguments:
      - updated_bot_response
      - context

  # response_adjustment_task:
  #   agent: response_adjuster
  #   description: |
  #     Using the output from response_reasonability_task, adjust the updated_bot_response by:
  #     - Making only the minimal necessary changes to address identified issues.
  #     - Ensuring consistency with the conversation context.
  #     - Preserving the original intent and overall message structure.
  #     Return the updated response with corrections applied.

  #     Updated Bot Response:
  #     {updated_bot_response}

  #   expected_output: |
  #     Return the updated_bot_response with minimal changes applied based on the suggestions from response_reasonability_task. You must keep the original data structure and format.
  #   arguments:
  #     - updated_bot_response
  #   context:
  #     - response_reasonability_task

  response_json_formulation_task:
    agent: response_json_formulator
    description: |
      Transform Bot Response into concise WhatsApp message bubbles:

      Bot Response:
      {updated_bot_response}

      Guidelines:
      1. Each message fits naturally in a WhatsApp bubble.
      2. Keep messages short and scannable (2-3 lines max)
      3. Remove any blank lines
      4. Limit to less than 20 words per message
      5. Include emoji VERY SPARINGLY, only when ABSOLUTELY NECESSARY
      6. Ensure concise and user-friendly readability
      7. Maintain the original message intent
      8. Use markdown for formatting (bullet points, images, emphasis)
      9. [!IMPORTANT] YOU MUST NOT Change the Original Bot Response. Only do very minor fix if necessary.
      10. [!IMPORTANT] YOU MUST NOT HAVE MORE THAN 3 MESSAGES.

      Return an array of message bubbles, each in markdown.
      One array item = one WhatsApp message bubble. DO NOT spam the user with too many messages.

    expected_output: |
      [
          "<Message bubble 1 in markdown>",
          "<Message bubble 2 in markdown>"
      ]
    arguments:
      - updated_bot_response
    context:
      - response_reasonability_task
