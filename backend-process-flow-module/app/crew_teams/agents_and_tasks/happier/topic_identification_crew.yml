crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  topic_extractor:
    role: "Topic Extractor"
    goal: "Extract and summarize conversation topics from {conversation_history} including event planning, business enquiry, escalation to manager, revised proposal, and confirmation of proposal."
    backstory: "An expert in parsing conversation histories to identify multiple topics, even when contexts overlap or recur, ensuring that each distinct event is analyzed in detail."
    model: "groq/llama-3.3-70b-versatile"
    verbose: true

tasks:
  extract_topics_task:
    agent: topic_extractor
    description: |
      Your task is to identify the different topics discussed. These topics can include planning of events, business enquiry, escalation to manager, revised proposal, or confirmation of proposal. For each topic, provide a detailed description of what was discussed.
      
      Additionally, include for each topic the date and time it was discussed.

      Past Conversation:
      {conversation_history}

      YOU MUST NOT RETURN FALSE INFORMATION IN THE TOPICS. IF THERE ARE NO TOPICS, YOU CAN RETURN AN EMPTY ARRAY.
    expected_output: |
      {
          "topics": [
              {
                  "topic_type": "string",
                  "context_description": "string",
                  "date_time_discussed": "string"
              }
          ]
      }
    arguments:
      - conversation_history
