crew_config:
  process: sequential
  verbose: True
  content_task_output: 1
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  message_formatter:
    role: "Message Formatter"
    goal: "Combine multiple draft responses into a concise, conversational sequence formatted for WhatsApp"
    backstory: "An expert chatbot copywriter who adapts AI output into short, friendly chat messages"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  json_formatter:
    role: "JSON Response Formatter"
    goal: "Format the response into the required JSON structure"
    backstory: "A specialist in structuring responses according to predefined schemas"
    model: "gpt-4o-mini"
    verbose: False
    tools:
      - generate_final_response

  context_analyzer:
    role: "Context Analyzer"
    goal: "Analyze the context and extract crucial information"
    backstory: "An expert in analyzing conversation context and extracting relevant information"
    model: "gpt-4o-mini"
    verbose: False

  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and appropriateness of the formatted response"
    backstory: "A quality assurance specialist ensuring response meets requirements"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the response quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to responses"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

tasks:
  format_for_whatsapp:
    agent: message_formatter
    description: |
      Given a list of individual response segments, merge and rewrite them into a single smooth, casual WhatsApp‑style chat thread.
      There might be multiple messages in the list, so you need to merge them into a single message where it replies appropriately to the customer's message.
      
      Preserve all factual content and user‑facing tone but make it feel like a real text conversation.

      Here are the set of individual response segments:
      {stage_execution_output}

      This is customer's message for your context ONLY (do not include this in the response):
      {unread_messages}
    expected_output: |
      A response to the user's message(s) in a casual WhatsApp-style chat thread.
    arguments:
      - stage_execution_output   # list of raw message strings to combine
      - unread_messages         # list of messages received from the user

  json_formatting_task:
    agent: json_formatter
    description: |
      Call the generate_final_response tool with the following parameters exactly as provided:
      - schema_name: {schema_name}
      - bot_name: {bot_name}
      - bot_settings: {bot_settings}
      - stage_execution_output: < Provided by format_for_whatsapp >
      - unread_messages: {unread_messages}
      - recent_messages: {recent_messages}
      - following_nodes: {following_nodes}
      - context: {context}

      DO NOT modify any of these values. Pass them directly to the tool.
      DO NOT CHANGE THE VALUE OF THE TOOL OUTPUT and DO NOT ADD ANY EMOJI if it is not part of the tool output.
      You CAN ONLY return what the tool output is.

    expected_output: |
      {
          "bot_response": [
              "<Message 1 in Markdown format>",
              "<Message 2 in Markdown format>",
          ]
      }
    arguments:
      - schema_name
      - bot_name
      - bot_settings
      - stage_execution_output
      - unread_messages
      - recent_messages
      - following_nodes
      - context
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: HappierCrewResponseOutput
    context:
      - format_for_whatsapp

  formulate_json_reply_task:
    agent: context_analyzer
    description: |
      The purpose of this task is to take the response formulated in the `response_formulation_task` and place it under `formulated_response`. Additionally, the agent should analyze the provided context to extract all available information from the unread messages and past messages.
      Additionally, if the "Bot planned response" contains instructions to store information as message context, you should extract the information as added it under additional_context.

      Your output should be a JSON object containing the following:
      1. `context_summary`: A concise summary of the current conversation context and state
      2. `decision_rationale`: A detailed explanation of why this response was chosen
      3. `additional_context`:
        - `user_intent`: The detected intent of the user
        - `confidence_score`: Confidence level of the response
        - `previous_states`: A list of previous conversation states leading to this point
        - `relevant_entities`: Any entities extracted from the conversation
      4. `tone_analysis`:
        - `formality`: Whether the tone is formal, informal, or casual
        - `sentiment`: Whether the sentiment is positive, negative, or neutral
        - `urgency`: Whether the urgency is high, medium, or low
      5. `is_completed`: true/false, depending on whether all necessary information has been extracted and the response is finalized

      The agent must ensure that all information is extracted accurately and presented in a structured format for further processing.

      Bot planned response:
      <Provided by json_formatting_task>

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}

      Context:
      {context}
    expected_output: |
      {
          "context_summary": "A concise summary of the current conversation context and state",
          "decision_rationale": "Detailed explanation of why this response was chosen",
          "additional_context": {
              "user_intent": "The detected intent/purpose of the user's messages",
              "confidence_score": 0.95,
              "previous_states": ["List of conversation states that led to this point"],
              "relevant_entities": {
                  "entity_type": "Specific entities extracted from the conversation"
              },
              "proposals": [
                  The array of "Proposal information" extracted by response_formulation_task.
            ]
          },
          "tone_analysis": {
              "formality": "formal/informal/casual",
              "sentiment": "positive/negative/neutral",
              "urgency": "high/medium/low"
          },
          "is_completed": true
      }
    arguments:
      - stage_execution_output
      - recent_messages
      - unread_messages
      - context
    context:
      - json_formatting_task
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: HappierResponseOutput

  output_analysis_task:
    agent: output_analyzer
    description: |
      Analyze the quality and appropriateness of the formatted response.
    expected_output: |
      {
          "quality_assessment": {
              "clarity_score": 0.0,
              "relevance_score": 0.0,
              "context_alignment": 0.0
          },
          "improvement_suggestions": ["List of suggested improvements"],
          "critical_issues": ["Any critical issues found"],
          "overall_quality_score": 0.0
      }
    context: [json_formatting_task]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Score the response based on defined quality metrics.

      #### **1. Analysis Quality (0-10)**

      This evaluates the bot's comprehension of the provided context, user intent, and its ability to identify the correct node or stage within a conversation flow.

      - **0-3: Below Standard**
        - **Description**: The bot's understanding is superficial or inaccurate. Key intents are missed, or the incorrect node/stage is identified, indicating limited comprehension.
        - **Example**: The bot incorrectly identifies a node that doesn't align with the user's intent, suggesting an unrelated flow. Discrepancies are frequent and indicate poor comprehension.

      - **4-6: Basic Understanding** 
        - **Description**: The bot demonstrates a basic understanding, identifying an approximate node or stage but lacking depth. Some important details are missed or misinterpreted.
        - **Example**: The bot selects a plausible node but fails to capture key nuances in the user's intent, leading to minor misalignments with the expected flow.

      - **7-8: Good Understanding**
        - **Description**: The bot accurately identifies the correct node and stage, with minor omissions. Most details align well with the user's intent, but a few finer points could be clarified.
        - **Example**: The bot correctly identifies the flow and stage and includes most relevant details but could improve in addressing subtle aspects of user context.

      - **9-10: Professional Understanding**
        - **Description**: The bot demonstrates comprehensive, insightful understanding. It identifies the correct node and stage with precision, showing clear comprehension of the user's intent.
        - **Example**: The bot not only identifies the correct node but provides a nuanced understanding of user intent, capturing all relevant details accurately.

      #### **2. Conciseness (0-10)**

      This measures how well the response balances completeness with brevity.

      - **0-3: Excessively Detailed**
        - **Description**: Response includes unnecessary information and tangents that detract from the core message.
        - **Example**: The bot provides extensive background information and multiple examples when a simple direct answer would suffice.

      - **4-6: Moderately Concise**
        - **Description**: Response contains mostly relevant information but includes some unnecessary details.
        - **Example**: The bot gives a generally focused response but includes a few extraneous details that could be omitted.

      - **7-8: Concise and Focused**
        - **Description**: Response is clear and to-the-point while maintaining necessary context.
        - **Example**: The bot provides all essential information without unnecessary elaboration.

      - **9-10: Highly Concise**
        - **Description**: Response achieves perfect balance of brevity and completeness.
        - **Example**: The bot delivers exactly what's needed with no wasted words while maintaining clarity.

      #### **3. Relevance of Information (0-10)**

      This assesses how well the information aligns with the user's needs and query context.

      - **0-3: Irrelevant**
        - **Description**: Response contains mostly or completely irrelevant information.
        - **Example**: The bot provides information about an unrelated topic or misses the core of the user's query.

      - **4-6: Partially Relevant**
        - **Description**: Some relevant information is present but mixed with irrelevant details.
        - **Example**: The bot addresses part of the query correctly but includes unrelated tangents.

      - **7-8: Mostly Relevant**
        - **Description**: Most information is relevant with minor deviations.
        - **Example**: The bot provides mostly pertinent information with only slight divergence from the core query.

      - **9-10: Fully Relevant**
        - **Description**: All information directly addresses the user's query and context.
        - **Example**: The bot's response is perfectly aligned with the user's needs and context.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
          "analysis_quality": {
              "score": X,
              "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
              "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
          },
          "conciseness": {
              "score": X,
              "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
              "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
          },
          "relevance_of_information": {
              "score": X,
              "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
              "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."
          }
      }
    context: [output_analysis_task]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RubricScore
