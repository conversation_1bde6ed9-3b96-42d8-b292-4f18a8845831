crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  contact_info_extractor:
    role: "Contact Info Extractor"
    goal: "Extract only the customer name, email, and phone from the provided context and set all other fields to None."
    backstory: "This agent is specialized in isolating essential contact information while discarding any extraneous event details, ensuring a clean and focused output."
    model: "groq/llama-3.3-70b-versatile"
    verbose: true

tasks:
  extract_contact_info:
    agent: contact_info_extractor
    description: |
      Given the 'context' that may include various pieces of customer and event-related information, extract only the following details:
      - Customer name
      - Email
      - Phone (contact number)
      - customer_agreed_to_collect_information (Only if it is True as user has already agreed to collect information previously)
      
      All other event-related fields should be set to None unless additional information can be extracted from the incoming messages.

      Incoming messages:
      {incoming_message}

      Context:
      {context}
    expected_output: |
      {
        "name": "string",
        "contact_number": "string",
        "email": "string",
        "event_date": "string",
        "event_start_time": "string",
        "event_end_time": "string",
        "event_location": "string",
        "number_of_adults": "string",
        "number_of_kids": "string",
        "age_range_of_kids": "string",
        "occasion_type": "string",
        "occasion": "string",
        "theme": "string",
        "budget": "string",
        "event_additional_details": "string",
        "services_list": "string",
        "customer_agreed_to_collect_information": "string",
        "billing_address": "string",
        "selected_proposal": "string"
      }
    arguments:
      - context
      - incoming_message
    output_pydantic: HappierContext
    guardrails:
      - validate_pydantic_output
