crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  context_identifier:
    role: "Context Identifier"
    goal: "Execute the tool 'identify_context' to extract the most relevant context from the provided inputs."
    backstory: "An expert in discerning and isolating the most relevant context from conversation history, topics, and incoming messages to support decision-making."
    model: "gpt-4o-mini"
    verbose: False
    tools:
      - identify_context

  flow_stage_analyzer:
    role: "Flow and Stage Analyzer"
    goal: "Analyze the current flow and stage against the focus determined in point 1."
    backstory: "An analytical agent focused on comparing current and recommended conversation flow stages for continuity."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the analysis quality based on rubric scoring metrics."
    backstory: "An evaluation agent specialized in applying rubric metrics to ensure analysis quality and relevance."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

tasks:
  identify_context_task:
    agent: context_identifier
    description: |
      Given the following inputs:
        - conversation_id: {conversation_id}
        - schema_name: {schema_name}
        - incoming_message: {unread_messages}
        - conversation_third_party_id: {conversation_third_party_id}
        - conversation_name: {conversation_name}
      
      Execute the tool 'identify_context' to analyze these inputs and extract the most relevant context that aligns with the intent of the incoming message. The output may contain multiple intents from the identify_context tool. You MUST output the same number of intents as the identify_context tool.
    expected_output: |
      {
        "intents":[
          {
            "intent": "<Detailed description of intent>",
            "confidence": "<High/Medium/Low>",
            "reasoning": "<Detailed reasoning for the intent and confidence level>",
            "course_of_action": "<Event Proposal Generation/Proposal Revision/Business Enquiry/Proposal Confirmation/Manager Escalation/Unknown Intent>",
            "reason_for_course_of_action": "<Detailed reasoning for the course of action>",
            "node_id": "<The selected node's id from Defined flows>",
            "next_node_id": "<The next node's id from Defined flows>",
            "context": {
              "name": "string",
              "contact_number": "string",
              "email": "string",
              "event_date": "string",
              "event_start_time": "string",
              "event_end_time": "string",
              "event_location": "string",
              "number_of_adults": "string",
              "number_of_kids": "string",
              "age_range_of_kids": "string",
              "occasion_type": "string",
              "occasion": "string",
              "theme": "string",
              "budget": "string",
              "event_additional_details": "string",
              "services_list": "string",
              "customer_agreed_to_collect_information": "string",
              "billing_address": "string",
              "selected_proposal": "string"
            }
          }
        ]
      }
      
    arguments:
      - conversation_id
      - schema_name
      - unread_messages
      - conversation_third_party_id
      - conversation_name
    output_pydantic: IntentAnalysisOutputList
    guardrails:
      - validate_pydantic_output

  flow_stage_analysis_task:
    agent: flow_stage_analyzer
    description: |
      Compare the current flow and stage with the focus flow and stage identified in point 1.

      Defined flows:
      {active_flow_mapping}
    expected_output: |
      {
          "current_flow": "Current flow ID or name",
          "current_stage": "Current stage ID or name",
          "focus_match": "Boolean indicating if current flow/stage matches focus flow/stage",
          "discrepancy": "Description of any discrepancy found (if applicable)"
      }
    arguments:
      - active_flow_mapping
    context:
      [
        identify_context_task
      ]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Apply rubric scoring to evaluate the quality of the intent analysis process.

      #### **1. Analysis Quality (0-10)**

      This measures the depth, accuracy, and clarity of the analysis performed in determining the intent and appropriate stage.

      - **0-3: Below Standard**
        - **Description**: The analysis is superficial or inaccurate, missing key intents or failing to identify the correct flow and stage. It shows limited understanding and often contains errors.
        - **Example**: The analysis incorrectly identifies the user's intent, suggesting an unrelated flow/stage. There are significant discrepancies between the analyzed and expected stages.

      - **4-6: Basic Quality**
        - **Description**: The analysis covers basic aspects of the intent and flow but lacks depth. Some key details may be missed, or the logic used may be partially unclear or inconsistent.
        - **Example**: The analysis identifies a plausible flow but misses nuances in the user's intent, leading to minor discrepancies. The focus stage is partially correct, but some elements are misinterpreted.

      - **7-8: Good Quality**
        - **Description**: The analysis is accurate, covering key aspects of the intent with minor omissions. Most details align with the expected flow/stage, though a few could be clarified or expanded.
        - **Example**: The analysis correctly identifies the flow and stage and includes most intent details, but it could have slightly improved on clarity or depth in capturing finer points of user context.

      - **9-10: Professional Quality**
        - **Description**: The analysis is comprehensive, accurate, and insightful, with a clear understanding of the user's intent. There are no discrepancies, and the flow/stage identification is spot-on.
        - **Example**: The analysis not only correctly identifies the flow and stage but also provides context that captures the nuances of user intent and context, with no missing or extraneous details.

      #### **2. Conciseness (0-10)**

      This assesses the ability to keep the analysis brief and to the point, focusing on essential details without redundancy.

      - **0-3: Excessively Detailed**
        - **Description**: The analysis is overly wordy, with much irrelevant or repetitive information that distracts from the key points. Clarity is lost in unnecessary details.
        - **Example**: The analysis includes detailed explanations for each minor point, creating a lengthy output with irrelevant historical context that could have been omitted.

      - **4-6: Moderately Concise**
        - **Description**: The analysis includes some extraneous information and could be more focused. Key points are present but buried under minor or less relevant details.
        - **Example**: While the main points are addressed, the analysis contains explanations that, while tangentially related, are not essential. The user intent is clear but could be presented more succinctly.

      - **7-8: Concise and Focused**
        - **Description**: The analysis is mostly concise, covering essential details with only minor instances of unnecessary information or repetition.
        - **Example**: The analysis focuses on the main points, only slightly veering off into minor details. It is easy to follow but could be refined by trimming a few non-essential elements.

      - **9-10: Highly Concise**
        - **Description**: The analysis is exceptionally concise, with a sharp focus on only the essential details. No redundancies or off-topic information are present.
        - **Example**: The analysis covers the key points succinctly without any superfluous information, providing a clear understanding of intent and flow in just a few focused statements.

      #### **3. Relevance of Information (0-10)**

      This category evaluates whether all provided information is directly relevant to the task's objective and intent analysis.

      - **0-3: Irrelevant or Off-Topic Content**
        - **Description**: The analysis includes off-topic information that distracts from the primary purpose. Much of the content does not align with the user's intent or relevant stages.
        - **Example**: The analysis includes unrelated details about previous conversations or hypothetical intents that are not relevant to the current stage or flow.

      - **4-6: Partially Relevant**
        - **Description**: The analysis contains some off-topic points, though it addresses the main purpose. There is room for refinement to focus more closely on the meeting's objectives.
        - **Example**: The analysis contains a few tangential points, such as minor background details that don't contribute to understanding the current intent. The key points are present but could be clearer with tighter focus.

      - **7-8: Mostly Relevant**
        - **Description**: The analysis is mostly on-topic, with minor deviations. It aligns well with the user's main purpose and captures the key aspects of intent.
        - **Example**: The analysis provides a relevant overview with only a minor digression into secondary details. It focuses on the main intent but could slightly improve by omitting one or two tangential references.

      - **9-10: Fully Relevant**
        - **Description**: The analysis is completely relevant, containing only information that directly aligns with the user's intent and the current stage requirements.
        - **Example**: Every detail in the analysis directly relates to the user's intent and current stage, with no extraneous or off-topic points. The analysis is sharply focused on fulfilling the task objective.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
          "analysis_quality": {
              "score": X,
              "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
              "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
          },
          "conciseness": {
              "score": X,
              "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
              "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
          },
          "relevance_of_information": {
              "score": X,
              "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
              "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."

          }
      }
    context: [flow_stage_analysis_task]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RubricScore
