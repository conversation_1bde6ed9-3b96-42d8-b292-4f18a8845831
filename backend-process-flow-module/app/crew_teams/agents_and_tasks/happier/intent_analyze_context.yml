crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  agreed_to_collect_analyzer:
    role: "Consent Verification Specialist"
    goal: "Identify the intent within unread messages to clarify user's current objective."
    backstory: "A 10-year veteran in data privacy and consent management, expert in interpreting user responses for clear agreement or refusal."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  selected_services_validator:
    role: "Service Selection Validator"
    goal: "Determine if the client has explicitly specified which services to include in their proposal."
    backstory: "A seasoned consultant with 15 years of experience in customer engagement and service tailoring, focused on ensuring that proposals only incorporate services explicitly approved by the client."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  manager_escalation_context_collector:
    role: "Context Retrieval Specialist"
    goal: "Retrieve and prioritize historical context from past messages for manager escalation, ensuring that the most recent and relevant messages are highlighted."
    backstory: "A veteran in data analysis and contextual retrieval, specializing in weighting historical messages based on their recency and priority to provide the most applicable context for escalation situations."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  flow_stage_identifier:
    role: "Conversation Flow Architect"
    goal: "Determine the flow and stage to focus on based on unread messages, historical messages, current state, and defined flows."
    backstory: "A seasoned professional with 10 years in conversation design, expert in mapping and navigating intricate dialogue structures."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False


tasks:
  check_agreed_task:
    agent: agreed_to_collect_analyzer
    description: |
      The objective of this task is to determine if the user has provided the consent to the information collection. Here's the step-by-step:

      1. Look at the past messages and determine if we just asked the user for permission to collect the information.
      2. If yes, interpret the unread message to see whether the user has provided consent or not. True or False should overwrite None.
      3. If no, search for context within the past messages to see whether the user previously provided consent or not. True or False should overwrite None.

      ### Guidelines for Checking Agreement

      **Customer agreed to collect information**  
        - If the unread message indicates consent:
          - **True**: Customer agreed to collect information or indicated general acceptance to proceed.
        - If the unread message indicates no consent:
          - **False**: Customer has clearly disagreed to collect information or to proceed.
        - Otherwise:
          - **None**: Consent status is not determined.

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}

      Do note that "package" and "proposal" can be used interchangeably in this context.

    expected_output: |
      {
          "Customer agreed to collect information": "<True/False/None>",
      }
    arguments:
      - recent_messages
      - unread_messages

  check_selected_services_task:
    agent: selected_services_validator
    description: |
      The objective of this task is to analyze both recent and historical messages to verify if the client has clearly and explicitly indicated the services they want to include. The process includes the following steps:
      
      1. Review recent messages to detect any direct statements from the client requesting the inclusion of specific services.
      2. Cross-reference these with historical context to ensure that the selections are truly client-driven and not merely system suggestions.
      3. Ensure that any internal recommendations or general mentions are not mistaken for explicit service selections.
      4. Use state indicators attached to previous messages to track when a service has been explicitly confirmed by the client.
      
      **Guidelines for Service Selection:**
        - **Explicit Selection:** Only services directly stated by the client (e.g., "I want service X included") should be considered.
        - **Exclusion of Suggestions:** Suggestions or discussions that do not include a clear directive from the client should be ignored.
        - **State Tracking:** Maintain a clear state across historical messages to reflect when a service has been positively identified.

      These are the services we offer.
        Balloon Sculpting
        Face Painting for Kids
        Birthday Party Games
        Birthday Party Magic Show
        Corporate Family Magic Show
        Birthday Party Magic Show with Live Animals
        Corporate Family Magic Show with Live Animals
        Birthday Party Bubble Show
        Corporate Family Bubble Show
        Birthday Party Circus Show
        Corporate Family Circus Show
        Birthday Party Science Show
        Corporate Family Science Show
        Balloon Sculpting and Magic Package
        Glitter Tattoo
        Name Art Calligraphy
        Airbrush Tattoo
        Best of Both Worlds Package (BBWP)
        Photo Booth
        Birthday Party Photography
        Corporate Event Photography
        Kids Party Backdrop with Character Standees (Cake Cutting / Photo Taking Area)
        Kids Party Backdrop with Balloons (Cake Cutting / Photo Taking Area)
        Adult Party Backdrop without Balloons (Cake Cutting / Photo Taking Area)
        Adult Party Backdrop with Balloons (Cake Cutting / Photo Taking Area)
        Corporate Draped Backdrop with Modern Column Pair (Photo Taking / Small Stage Area)
        Corporate Draped Backdrop with Modern Arch (Photo Taking / Small Stage Area)
        Petite Corporate Custom Backdrop (Photo Taking / Small Stage Area)
        Classic Corporate Custom Backdrop (Photo Taking / Small Stage Area)
        1 Hour Popcorn and Candyfloss Package
        2 Hour Popcorn and Candyfloss Package
        3 Hour Popcorn and Candyfloss Package
        4 Hour Popcorn and Candyfloss Package
        Popcorn Machine Rental Live Station
        Candy Floss Machine Rental Live Station
        Kachang Puteh and Malt Candy Package
        Kachang Puteh Rental Live Station
        Malt Candy Rental Live Station
        Scooped Traditional Ice Cream Live Station
        Traditional Ice Cream Cart Rental Live Station
        UV or Glow-in-the-Dark Tattoo
        Caricature Drawing
        Digital Caricature Drawing (Soft Copy Artwork via Email)
        Digital Caricature Drawing (Printed 4R Hard Copy Artwork)
        Caricature Drawing on Shrink Art Keychain
        Wire Name Art Calligraphy Keychain or Bracelet
        Silhouette Shadow Portrait Cutting
        Rainbow Glow Slime Making
        Free-Hand Tote Bag Painting
        3D Sculpting Keychain Making
        Shrink Art Keychain Making
        Badge Making
        Sand Art Making on Table Top
        Sand Art Making on Twin Racks
        Window Art
        Face Painting for Adults
        UV Glow Face Painting for Adults
        Face Painting for Halloween Makeup Appointment
        Face Painting for Dinner Makeup Appointment
        Special Effects / Movie Makeup Appointment
        Belly Painting for Maternity Photoshoot
        Full Body Painting (Appointment)
        UV Glow Face Painting for Kids
        Petite Rectangle Backdrop with Modern Column Pair (Photo Taking / Small Stage Area)
        Petite Rectangle Backdrop with Star Column Pair (Photo Taking / Small Stage Area)
        Classic Rectangle Backdrop with Modern Column Pair (Photo Taking / Small Stage Area)
        Classic Rectangle Backdrop with Star Column Pair (Photo Taking / Small Stage Area)
        Corporate Draped Backdrop with Star Column Pair (Photo Taking / Small Stage Area)
        Corporate Draped Backdrop with Modern Column Pair (Photo Taking / Stage Area) + Star Column Pair (Entrance / Aisle Area)
        Festive Office Party Decor Package
        Corporate Draped Backdrop with Stacked Cubes (Photo Taking) + Banner/Standee Pair (Entrance / Aisle)
        Classic LED Corporate Custom Backdrop (Up to 4 Jumbo Fonts)
        Grande LED Corporate Custom Backdrop (Up to 6 Jumbo Fonts)
        Stage Backdrop with Modern Balloons (Large Stage Area)
        Stage Backdrop with Chevron Landscape (Large Stage Area)
        Stage Backdrop with Themed Landscape (Large Stage Area)
        Petite Stage Landscape with Spiral Balloons (Small Stage Area)
        Classic Stage Landscape with Spiral Balloons (Medium Stage Area)
        Grande Stage Landscape with Spiral Balloons (Large Stage Area)
        Petite Stage Landscape with Spiral Balloons and Balloon Fonts (Small Stage Area)
        Classic Stage Landscape with Spiral Balloons and Balloon Fonts (Medium Stage Area)
        Grande Stage Landscape with Spiral Balloons and Balloon Fonts (Large Stage Area)
        Petite Stage Landscape with Spiral Balloons and LED Fonts (Small Stage Area)
        Classic Stage Landscape with Spiral Balloons and LED Fonts (Medium Stage Area)
        Grande Stage Landscape with Spiral Balloons and LED Fonts (Large Stage Area)
        Rainbow Glow Tattoo (Face & Body)
        UV Glow Balloon Sculpting by 1 Artist
        Free-Hand Fairy Jar Making
        Faux Test Tube Terrarium
        4 Hour Rental of 3 Carnival Game Booths
        4 Hour Rental of 4 Carnival Game Booths
        4 Hour Rental of 5 Carnival Game Booths
        Customised Tote Bag Painting
        Customised Fairy Jar Making
        Jar Terrarium
        Jewel Glitter Makeup
        Rainbow Hair Tattoo
        Rainbow Hair Tattoo with LED
        Hair Styling
        Standard Express Manicure
        Gel Express Manicure
        Standard Express Pedicure
        Gel Express Pedicure
        UV Glow Jewel Glitter Makeup
      
      DO NOT include any other services that are not explicitly provided by us.

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}
    
    expected_output: |
      {
          "Selected Services": ["service_name1", "service_name2", ...] // list all explicitly approved services
      }
    arguments:
      - recent_messages
      - unread_messages

  collect_manager_escalation_context_task:
    agent: manager_escalation_context_collector
    description: |
      The objective of this task is to extract the event information from the chat messages if available:
      1. Name of the customer
      2. Contact number of the customer
      3. Email of the customer
      4. Event date
      5. Event start time
      6. Event end time
      7. Event location
      8. Number of adults attending the event
      9. Number of kids and age range of kids attending the event
      10. Occasion type
      11. Occasion
      12. Theme
      13. Budget
      14. Event additional details
      15. Services list
      16. Customer agreed to collect information
      17. billing_address (Can be None if not provided)
      18. selected_proposal (Can be None if not provided)

      Here are the steps to determine the values:

      1. Look at the past messages and determine if we just asked the user for the relevant information.
      2. If user has previously answered, use the values. Make sure the latest information takes priority.

      ### Guidelines for Extracting Event Information

      1. **Name of the customer**  
        - It should not be the user's son. It needs to be the proper customer name.  
        - If the name is not provided, leave it as an empty string (`""`).

      2. **Contact number of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      3. **Email of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      4. **Event date**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      5. **Event start time**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      6. **Event end time**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      7. **Event location**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      8. **Number of adults attending the event**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      9. **Number of kids and age range of kids attending the event**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      10. **Occasion type**  
          - It is either **private** or **corporate**.  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      11. **Occasion**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`. This is the name of the occasion.

      12. **Theme**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      13. **Budget**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      14. **Event additional details**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      15. **Services list**  
          - List of services that the customer has chosen, proposed in past messages.  
          - If it has been asked previously and user is unclear or not providing the information, leave as an empty array (`[]`).
          - Only when the user explicitly indicates that they want the service. Else, you can treat it as not confirmed which should not be included in the list.
          - Only refers to check_selected_services_task output

      16. **Customer agreed to collect information**  
          - Referencing to the unread messages and/or past messages if the user replied to your question of collecting information.
            - **True**: Customer has agreed to collect information.  
            - **False**: Customer has not agreed to collect information.  
            - **None**: Customer has not yet been asked.
        
      17. **billing_address**  
          - Default to None.
        
      18. **selected_proposal**  
          - Default to None.
      ---

      ### Additional Rules:
      1. **If the question has been asked before, and the user does not want to provide the information, the value will be `None`.**  
      2. **If the question has been asked before, but the user is unclear, unsure, or does not reply with the information, the value will be `"Not given"`.**
      3. **If the customer has answered, use those values, ensuring that the latest information (based on message priority) takes precedence.**
      ---

      Your output should be a JSON object.

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}

      You can extract the number/ contact number from here if exists. But you must use the contact number in the message context if it exists as customer might provide a different phone number.
      Conversation Contact Number: {conversation_third_party_id}
      Conversation Name: {conversation_name}

    expected_output: |
      {
        "name": "string",
        "contact_number": "string",
        "email": "string",
        "event_date": "string",
        "event_start_time": "string",
        "event_end_time": "string",
        "event_location": "string",
        "number_of_adults": "string",
        "number_of_kids": "string",
        "age_range_of_kids": "string",
        "occasion_type": "string",
        "occasion": "string",
        "theme": "string",
        "budget": "string",
        "event_additional_details": "string",
        "services_list": "string",
        "customer_agreed_to_collect_information": "string",
        "billing_address": "string",
        "selected_proposal": "string"
      }
    arguments:
      - recent_messages
      - unread_messages
      - conversation_third_party_id
      - conversation_name
  
  flow_stage_identification_task:
    agent: flow_stage_identifier
    description: |
      Given the unread messages, historical messages, current state, and defined flows, determine the specific node and stage to focus on within the business flows. This decision should be based on the following steps:

      Do note that "package" and "proposal" can be used interchangeably in this context.

      Node Selection Rules:
      1. Node selection based on context:
         - The final output should include the unique node ID and state ID that best correspond to the customer’s current intent, along with the relevant context.

      Defined flows:
      {active_flow_mapping}

      Previous feedback:
      {previous_feedback}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "node_id": "<The selected node's id from Defined flows (e.g., 'flow1-start-event-proposal')>",
          "context": {
            "name": <Name of the customer>,
            "contact_number": <Contact number of the customer>,
            "email": <Email of the customer>,
            "event_date": <Event date>,
            "event_start_time": <Event start time>,
            "event_end_time": <Event end time>,
            "event_location": <Event location>,
            "number_of_adults_attending": <Number of adults attending the event in string>,
            "number_of_kids_attending": <Number of kids and age range of kids attending the event in string>,
            "occasion_type": <Occasion type>,
            "occasion": <Occasion>,
            "theme": <Theme>,
            "budget": <Budget in string>,
            "event_additional_details": <Event additional details>,
            "services_list": <Services list. This is an array>,
            "customer_agreed_to_collect_information": <Customer agreed to collect information>,
            "billing_address": <Billing address>,
            "selected_proposal": <Selected proposal>
          }
      }

    arguments:
      - active_flow_mapping
      - previous_feedback
    context:
      [
        collect_manager_escalation_context_task
      ]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: FlowStageIdentification