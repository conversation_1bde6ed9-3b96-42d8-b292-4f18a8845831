crew_config:
  process: sequential
  verbose: True
  content_task_output: 4
  content_task_output_json_flag: False
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  node_analyzer:
    role: "Node Purpose Analyzer"
    goal: "Understand and analyze the purpose and requirements of the current node"
    backstory: "An expert in analyzing conversation flow nodes and their objectives"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  response_validator:
    role: "User Response Validator"
    goal: "Evaluate if user responses meet the node requirements"
    backstory: "A specialist in analyzing user responses against defined requirements"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  state_determiner:
    role: "Node State Determiner"
    goal: "Determine the current state of the node based on user interaction"
    backstory: "An expert in evaluating conversation progress and completion states"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  tool_decider:
    role: "Tool Selection and Query Specialist"
    goal: "Analyze the node context to determine the required tool and construct the arguments for the tool."
    backstory: "An expert in identifying the most appropriate tool for the current context and formulating queries to retrieve the necessary data or documents."
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  tool_executor:
    role: "Tool Execution Specialist"
    goal: "Execute the tool selected by the Tool Selection and Query Specialist using a key-value pair dictionary as the input and return the retrieved data in a structured format. If the value for the key is missing, use the default value of the argument type. (For example, if the value is a type string, use an empty string as the default value.)"
    backstory: "A specialist in executing tools with precision, ensuring the input adheres to the required format (key-value pair dictionary) and retrieving results based on the constructed query."
    model: "gpt-4o-mini"
    verbose: False
    max_iter: 3
    max_retry_limit: 3
    tools:
      - ask_customer_permission
      - gather_event_information
      - draft_proposals
      - revise_proposal
      - confirm_proposal
      - handle_business_enquiry
      - handle_unknown_intent
      - escalation_to_human

  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and correctness of the generated outputs"
    backstory: "A quality assurance specialist ensuring response accuracy and relevance"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the overall execution quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to conversation outputs"
    model: "groq/llama-3.3-70b-versatile"
    verbose: False

tasks:
  node_analysis_task:
    agent: node_analyzer
    description: |
      Analyze the current node to understand its purpose, requirements, and expected outcomes.

      Node information:
      {stage_node}

      Current context: 
      {recent_messages}

    expected_output: |
      {
          "node_purpose": "Description of what the node aims to achieve",
          "requirements": ["List of specific requirements"],
          "expected_outcomes": ["Expected results or responses"],
          "success_criteria": ["Criteria for successful completion"]
      }
    arguments:
      - stage_node
      - recent_messages

  response_validation_task:
    agent: response_validator
    description: |
      Evaluate if user responses fulfill the node requirements.

      User messages: 
      {unread_messages}

      Previous feedback:
      {previous_feedback}

    expected_output: |
      {
          "requirements_met": ["List of met requirements"],
          "requirements_missing": ["List of unmet requirements"],
          "validation_opinion": "Detailed opinion on response adequacy",
          "confidence_score": 0.0
      }
    arguments:
      - unread_messages
      - previous_feedback
    context: [node_analysis_task]

  state_determination_task:
    agent: state_determiner
    description: |
      Determine the current state of the node execution.

      Detailed flow mapping:
      {active_flow_mapping}
    expected_output: |
      {
          "completion_status": "completed/pending/missing_info",
          "reason": "Detailed explanation for the status",
          "missing_elements": ["List of missing elements if any"],
          "next_steps": ["Recommended next steps"]
      }
    arguments:
      - active_flow_mapping
    context: [response_validation_task]

  tool_selection_task:
    agent: tool_decider
    description: |
      Analyze the context to decide if a tool is needed. If a tool is needed:
      - Determine the appropriate tool to use.
      - Construct a query for the selected tool.

      Tools:
      1. ask_customer_permission: This function checks if the customer has agreed to collect their information.
      2. gather_event_information: This function collects and validates event-related details provided by the customer, identifies any missing information, recommends suitable services, and generates a proposal if all details are confirmed. It ensures a seamless flow in gathering customer information and handles missing or incomplete inputs by returning prompts. Confirmation of event details is only needed once.
      3. draft_proposals: This function generates proposals for various event services based on the provided details, ensuring a well-structured and coherent response. DO NOT INCLUDE billing_address and selected_proposal for this tool. Use this tool when the customer expresses intent that all the information is correct and shows interest in getting the proposal, ensuring all information (excluding billing_address and selected_proposal) is populated.
      4. confirm_proposal: This function handles when the customer confirms a proposal and provides necessary information for soft booking.
      5. handle_business_enquiry: This function searches through master services and provides cost estimates for business enquiries. It is the catch all for all business enquiries that are unrelated to proposal generation.
      6. handle_unknown_intent: This function provides a catch all for all unknown intents.

      Node information:
      {stage_node}

      User messages: 
      {recent_messages}

      Conversation ID:
      {conversation_id}

      Conversation Queue ID:
      {queue_id}

      Incoming message:
      {unread_messages}

      Schema name:
      {schema_name}

      Context:
      {context}

    expected_output: |
      {
          "selected_tool": "Name of the tool to use, or null if no tool is required",
          "constructed_query": "Query string for the selected tool, or null if no tool is required",
          "selection_reason": "Explanation of why the tool was selected or why no tool is needed"
      }
    arguments:
      - stage_node
      - recent_messages
      - conversation_id
      - unread_messages
      - queue_id
      - schema_name
      - context
    context: [node_analysis_task, state_determination_task]

  tool_execution_task:
    agent: tool_executor
    description: |
      Execute the tool selected by the Tool Selection and Query Specialist and retrieve the output.

      - Use the provided tool name to execute the retrieval process.
      - Do not hallucinate or invent arguments for calling the tools. Only use the information explicitly provided in the context below to determine the arguments.
      - Ensure that the `conversation_id` used for the tool execution matches the one provided in the context.
      - Use the information extracted from the provided context to determine the arguments for the tool.

      Information required to execute the tools can be found in the state of the past messages. Use this information strictly when executing the tool:
      Schema name:
      {schema_name}

      Conversation ID:
      {conversation_id}

      Is RedTeam Run:
      {is_redteam_run}

      Conversation Queue ID:
      {queue_id}

      Context:
      {context}

    expected_output: |
      Return the output of the executed tool. Do not hallucinate or input your own feedback or opinion. The tools will determine what needs to be included in the response to the customer.
      
      MUST ONLY RETURN THE OUTPUT OF THE EXECUTED TOOL. DO NOT ADD ANY ADDITIONAL INFORMATION!
    arguments:
      - schema_name
      - conversation_id
      - is_redteam_run
      - context
      - queue_id
    context: [tool_selection_task]
