crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2


default_model: "groq/llama-3.3-70b-versatile"

agents:
  topic_matcher:
    role: "Topic Matcher"
    goal: "Determine the best matching topic from the extracted topics based on the incoming_message and today_datetime."
    backstory: "An expert in analyzing conversation history and current messages to identify if the incoming message refers to a new event, a different topic from history, a continuous topic, or a reference to a past topic (with or without sufficient context)."
    model: "groq/llama-3.3-70b-versatile"
    verbose: true

tasks:
  match_topic_task:
    agent: topic_matcher
    description: |
      Given the following inputs:
        - topics: {topics}
        - incoming_message: {incoming_message}
        - today_datetime: {today_datetime}
      
      The task is to identify which topic is the most relevant match to the incoming message intent. Consider the following scenarios:
      
      1. A different topic from the past history. (topic_type: new)
      2. A continuous topic from the historical messages. (topic_type: continuous)
      3. A referencing to a past topic based on a datetime range where the context is determinable. (topic_type: old)
      4. A referencing to a past topic based on a datetime range where the context is not determinable (will need to pull more messages). (topic_type: fetch_more_messages)
      
      The output should specify:
        - topic_type: one of [continuous, new, old, fetch_more_messages]
        - reason: the reason for the topic type.
        - context: the context that it is referencing to. If there is no context or the topic_type is new, the value should be "Not given".

      Here are all the available context:
      1. Name of the customer
      2. Contact number of the customer
      3. Email of the customer
      4. Event date
      5. Event start time
      6. Event end time
      7. Event location
      8. Number of adults attending the event
      9. Number of kids and age range of kids attending the event
      10. Occasion type
      11. Occasion
      12. Theme
      13. Budget
      14. Event additional details
      15. Services list
      16. Customer agreed to collect information
      17. billing_address (Can be None if not provided)
      18. selected_proposal (Can be None if not provided)

      ### Guidelines for Extracting Event Information

      1. **Name of the customer**  
        - It should not be the user's son. It needs to be the proper customer name.  
        - If the name is not provided, leave it as an empty string (`""`).

      2. **Contact number of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      3. **Email of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      4. **Event date**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      5. **Event start time**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      6. **Event end time**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      7. **Event location**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      8. **Number of adults attending the event**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      9. **Number of kids and age range of kids attending the event**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      10. **Occasion type**  
          - It is either **private** or **corporate**.  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      11. **Occasion**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`. This is the name of the occasion.

      12. **Theme**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      13. **Budget**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      14. **Event additional details**  
          - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      15. **Services list**  
          - List of services that the customer has chosen, proposed in past messages.  
          - If it has been asked previously and user is unclear or not providing the information, leave as an empty array (`[]`).
          - Only when the user explicitly indicates that they want the service. Else, you can treat it as not confirmed which should not be included in the list.
          - Only refers to check_selected_services_task output

      16. **Customer agreed to collect information**  
          - Referencing to the unread messages and/or past messages if the user replied to your question of collecting information.
            - **True**: Customer has agreed to collect information.  
            - **False**: Customer has not agreed to collect information.  
            - **None**: Customer has not yet been asked.
        
      17. **billing_address**  
          - Default to None.
        
      18. **selected_proposal**  
          - Default to None.
      ---

      ### Additional Rules:
      1. **If the question has been asked before, and the user does not want to provide the information, the value will be `None`.** 
      ---
    expected_output: |
      {
          "topic_type": "string (continuous/new/old/fetch_more_messages)",
          "reason": "string",
          "context": {
            "name": "string",
            "contact_number": "string",
            "email": "string",
            "event_date": "string",
            "event_start_time": "string",
            "event_end_time": "string",
            "event_location": "string",
            "number_of_adults": "string",
            "number_of_kids": "string",
            "age_range_of_kids": "string",
            "occasion_type": "string",
            "occasion": "string",
            "theme": "string",
            "budget": "string",
            "event_additional_details": "string",
            "services_list": "string",
            "customer_agreed_to_collect_information": "string",
            "billing_address": "string",
            "selected_proposal": "string"
          }
      }
    arguments:
      - topics
      - incoming_message
      - today_datetime
    output_pydantic: TopicOutput
    guardrails:
      - validate_pydantic_output
