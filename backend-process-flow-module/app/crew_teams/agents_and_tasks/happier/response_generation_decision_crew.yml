crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True

models:
  - name: "gpt-4o-mini"
    temperature: 0.2

default_model: "gpt-4o-mini"

agents:
  markdown_validator:
    role: "Markdown Validator"
    goal: "Validate if the content contains proposals or service recommendations in markdown format"
    backstory: "An expert in analyzing markdown content and identifying specific content types"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  validate_markdown_task:
    agent: markdown_validator
    description: |
      Analyze the stage_execution_output to determine if it contains:
      1. Proposals in array
      2. Service recommendations in array

      stage_execution_output
      {stage_execution_output}

      If the content is already in an array format, return true.
      If the content is not in an array format, return false.

      Return a JSON response indicating if the content meets these criteria.
      The response should include a boolean flag and a reason explaining the decision.

    expected_output: |
      {
          "proposal_or_service": "<boolean> true/false",
          "reason": "<explanation>"
      }
    arguments:
      - stage_execution_output
    guardrails:
      - validate_pydantic_output
    output_pydantic: ProposalServiceValidation
