crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  proposal_restart_detector:
    role: "Proposal Restart Detector"
    goal: "Detect if the incoming message explicitly indicates that the customer wants to restart the event proposal, meaning they wish to plan a new event or reset the current proposal planning."
    backstory: "This agent specializes in parsing customer messages to identify clear signals that a new event proposal should be initiated, ensuring that any restart request is handled as a fresh topic."
    model: "groq/llama-3.3-70b-versatile"
    verbose: true

tasks:
  detect_proposal_restart:
    agent: proposal_restart_detector
    description: |
      Analyze the incoming message (provided as 'incoming_message') to determine if the customer explicitly states they want to restart the event proposal. 
      
      This means:
      - The customer wants to restart the proposal planning process. [!IMPORTANT] Customer must show extremely clear signs of wanting to restart the proposal planning process.
      
      If such intent is detected, return true along with a clear explanation of the evidence found in the message. Otherwise, return false with a reason.

      Incoming message: {incoming_message}
    expected_output: |
      {
          "restart_proposal": "<True or False>",
          "reason": "A string explaining the detected evidence or lack thereof."
      }
    arguments:
      - incoming_message
    output_pydantic: RestartProposalOutput
    guardrails:
      - validate_pydantic_output

  
