crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  context_analyzer:
    role: "Context Analyzer"
    goal: "Analyze the context and extract crucial information"
    backstory: "An expert in analyzing conversation context and extracting relevant information"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  formulate_json_reply_task:
    agent: context_analyzer
    description: |
      The purpose of this task is to take the response formulated in the `response_formulation_task` and place it under `formulated_response`. Additionally, the agent should analyze the provided context to extract all available information from the unread messages and past messages.
      Additionally, if the "Bot planned response" contains instructions to store information as message context, you should extract the information as added it under additional_context.

      Your output should be a JSON object containing the following:
      1. `context_summary`: A concise summary of the current conversation context and state
      2. `decision_rationale`: A detailed explanation of why this response was chosen
      3. `additional_context`:
        - `user_intent`: The detected intent of the user
        - `confidence_score`: Confidence level of the response
        - `previous_states`: A list of previous conversation states leading to this point
        - `relevant_entities`: Any entities extracted from the conversation
      4. `tone_analysis`:
        - `formality`: Whether the tone is formal, informal, or casual
        - `sentiment`: Whether the sentiment is positive, negative, or neutral
        - `urgency`: Whether the urgency is high, medium, or low
      5. `is_completed`: true/false, depending on whether all necessary information has been extracted and the response is finalized

      The agent must ensure that all information is extracted accurately and presented in a structured format for further processing.

      Bot planned response:
      {stage_execution_output}

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}

      Context:
      {context}
    expected_output: |
      {
          "context_summary": "A concise summary of the current conversation context and state",
          "decision_rationale": "Detailed explanation of why this response was chosen",
          "additional_context": {
              "user_intent": "The detected intent/purpose of the user's messages",
              "confidence_score": 0.95,
              "previous_states": ["List of conversation states that led to this point"],
              "relevant_entities": {
                  "entity_type": "Specific entities extracted from the conversation"
              },
              "proposals": [
                  The array of "Proposal information" extracted by response_formulation_task.
            ]
          },
          "tone_analysis": {
              "formality": "formal/informal/casual",
              "sentiment": "positive/negative/neutral",
              "urgency": "high/medium/low"
          },
          "is_completed": true
      }
    arguments:
      - stage_execution_output
      - recent_messages
      - unread_messages
      - context
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: HappierResponseOutput
