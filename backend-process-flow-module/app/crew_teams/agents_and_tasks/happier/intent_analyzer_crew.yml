crew_config:
  process: sequential
  verbose: True
  content_task_output: 1
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.2
  - name: "groq/llama-3.3-70b-versatile"
    temperature: 0.2

default_model: "groq/llama-3.3-70b-versatile"

agents:
  unread_message_intent_analyzer:
    role: "Intent Analysis Specialist"
    goal: "Extract and prioritize the user's intent from unread messages using contextual cues and historical message priority."
    backstory: "A seasoned expert in natural language processing and behavioral analysis, skilled at deciphering nuanced user intent even when messages are sparse or contextually decayed over time."
    model: "groq/llama-3.3-70b-versatile"
    verbose: True

  course_of_action_selector:
    role: "Course of Action Specialist"
    goal: "Based on the inferred intents, determine the most appropriate course of action among the provided options using a MECE framework."
    backstory: "A strategic expert in workflow automation and customer interaction, adept at mapping user intents to optimal action paths in customer service by ensuring each outcome is mutually exclusive and collectively exhaustive."
    model: "groq/llama-3.3-70b-versatile"
    verbose: True

tasks:
  analyze_unread_intent_task:
    agent: unread_message_intent_analyzer
    description: |
      The objective of this task is to analyze the user's unread messages to extract the underlying intent. Historical messages are provided as a reference with associated priority values (time decay) so that more recent messages are weighted more heavily. Follow these steps:
      1. Review the unread messages for explicit and implicit cues indicating the user's intent.
      2. Examine the historical messages and consider the provided priority for each, using more recent messages as a stronger context.
      3. Identify and synthesize potential unique intents, ranking them according to confidence and relevance.
      4. Provide a prioritized list of inferred unique intents ONLY FOR THE UNREAD MESSAGES, with associated confidence levels for each.

      Past messages (with time decay):
      {recent_messages_only_with_time_decay}

      Unread messages:
      {incoming_message}

    expected_output: |
      {
          "inferred_intents": [
              {
                "intent": "<Detailed description of intent>",
                "confidence": "<High/Medium/Low>",
                "reasoning": "<Detailed reasoning for the intent and confidence level>"
              },
              // Additional intents as needed
          ]
      }
    arguments:
      - recent_messages_only_with_time_decay
      - incoming_message

  determine_course_of_action_task:
    agent: course_of_action_selector
    description: |
      The objective of this task is to analyze the output from the previous intent analysis task and determine the best course of action to address the user's inferred intent using the MECE (Mutually Exclusive, Collectively Exhaustive) framework. This approach guarantees that:
      
      - **Mutually Exclusive:** Each possible course of action is distinct, with no overlap in scope or criteria.
      - **Collectively Exhaustive:** All potential user intents are covered by one of the defined courses of action, ensuring that no possible intent is left unaddressed.

      The six defined courses of action are:

      1. **Event Proposal Generation:**  
         - **Criteria:** The customer clearly expresses interest in creating a tailored proposal or package for their event. This includes requests for customized event planning, expressing intent to start planning an event, or asking to begin a proposal draft.
         - **Process:** Request permission to collect detailed event information (e.g., name, contact, event date, location, number of adults/children, occasion type, theme, etc.), suggest budget and service recommendations, and generate initial and alternative proposals.
         - ⚠️ Important: Do not confuse this with general enquiries about services or pricing — those fall under Business Enquiry.

      2. **Proposal Revision:**  
         - **Criteria:** The customer gives feedback indicating that an existing proposal does not fully meet their expectations or requires changes.
         - **Process:** Gather specific revision feedback, update the existing proposals accordingly, and provide revised versions for review.

      3. **Business Enquiry:**  
         - **Criteria:** The customer is asking general questions about the business or its services. These are not specific to planning an event or creating a proposal. Examples include:
              - "Do you offer balloon decorations?"
              - "What is the pricing for slime services?"
         - **Process:** Provide clear and direct answers to the enquiry without entering the event planning workflow.
         - Do not enter the event planning or proposal generation process unless the customer explicitly requests a proposal.

      4. **Proposal Confirmation:**  
         - **Criteria:** The customer confirms they want to move forward with a specific proposal by referencing a valid Proposal ID (format: P*). An expression of interest alone is not sufficient.
         - **Process:** Verify the event details, confirm the selected proposal, and trigger follow-up actions while noting that mere expression of interest is not confirmation.

      5. **Manager Escalation:**  
         - **Criteria:** The customer requests to speak with a manager or needs additional support due to uncertainty, frustration, or complex concerns.
         - **Process:** Transfer the conversation to a human agent for personalized assistance.

      6. **Unknown Intent:** 
         - **Criteria:** The customer's intent is unclear or cannot be determined or not under any of the above categories.
         - **Process:** This will be a catch-all category for non-event-specific queries.

      **Task Steps:**
      1. Review the inferred intents provided by the previous task.
      2. Categorize each inferred intent based on the above six mutually exclusive and collectively exhaustive options.
      3. Identify the primary intent by prioritizing the inferred intents using confidence levels and relevance.
      4. Map the primary intent to its corresponding course of action from the six defined options.
      5. Output the selected course of action, ensuring that the output value is one of the following exactly: "Event Proposal Generation", "Proposal Revision", "Business Enquiry", "Proposal Confirmation", "Manager Escalation", or "Unknown Intent".
      6. You are allowed to output multiple inferred intents if there are multiple intents that are relevant and have similar confidence levels.
      
    expected_output: |
      {
          "inferred_intents": [
              {
                "intent": "<Detailed description of intent>",
                "confidence": "<High/Medium/Low>",
                "reasoning": "<Detailed reasoning for the intent and confidence level>",
                "course_of_action": "<Event Proposal Generation/Proposal Revision/Business Enquiry/Proposal Confirmation/Manager Escalation/Unknown Intent>",
                "reason_for_course_of_action": "<Detailed reasoning for the course of action>"
              },
              // Additional intents as needed
          ]
      }
    context:
      - analyze_unread_intent_task
    guardrails:
      - validate_pydantic_output
    output_pydantic: InferredIntentsOutput