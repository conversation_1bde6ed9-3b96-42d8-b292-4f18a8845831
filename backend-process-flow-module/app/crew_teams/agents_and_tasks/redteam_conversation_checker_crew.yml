crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  flow_state_analyzer:
    role: "Flow State Analyzer"
    goal: "Decipher the current stage in the customer service flow based on the persona, scenarios, chat history, and flow mapping. Determine the next stage if the current stage is complete."
    backstory: "An expert in understanding customer interactions and guiding the flow of conversations."
    model: "gpt-4o-mini"
    verbose: False

  response_formulator:
    role: "Response Formulator"
    goal: "Create appropriate messages or questions for customer service based on the flow state and the customer's persona, scenarios, and chat history."
    backstory: "A skilled conversationalist specializing in generating responses for smooth customer service interaction."
    model: "gpt-4o-mini"
    verbose: False

  endpoint_caller:
    role: "Endpoint Caller"
    goal: "Call the customer service endpoint with the formulated message and retrieve the response."
    backstory: "An automation specialist ensuring seamless communication with external systems."
    model: "gpt-4o-mini"
    verbose: False
    tools: [call_chatbot_endpoint]

  response_evaluator:
    role: "Response Evaluator"
    goal: "Analyze the customer service response to assess its quality and determine if further action is required based on the flow mapping and chat history."
    backstory: "A quality assurance agent focusing on evaluating response accuracy and relevance."
    model: "gpt-4o-mini"
    verbose: False

  chat_decision_maker:
    role: "Chat Decision Maker"
    goal: "Decide whether to respond back to customer service or conclude the chat based on the evaluation of the response and the flow state."
    backstory: "A decision-making expert skilled in identifying when to continue or end conversations effectively."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  analyze_flow_state:
    agent: flow_state_analyzer
    description: |
      Determine the current stage in the customer service flow based on the persona, scenarios, chat history, and flow mapping. If the current stage is complete, provide the next stage.

      Customer persona:
      {customer_persona}

      Scenarios:
      {scenarios}

      Chat history:
      {chat_history}

      Flow mapping:
      {flow_mapping}
    expected_output: |
      Return the next stage in the flow mapping if the current stage is complete, else return the current stage.
    arguments:
      - customer_persona
      - scenarios
      - chat_history
      - flow_mapping

  formulate_response:
    agent: response_formulator
    description: |
      Create a message or question to send to customer service based on the flow state, customer persona, scenarios, and chat history.

      Customer persona:
      {customer_persona}

      Scenarios:
      {scenarios}

      Chat history:
      {chat_history}
    expected_output: |
      {
          "formulated_message": "Message to send to customer service"
      }
    arguments:
      - customer_persona
      - scenarios
      - chat_history
    context: [analyze_flow_state]

  call_endpoint:
    agent: endpoint_caller
    description: |
      Based on the formulated message, call the customer service endpoint (call_chatbot_endpoint) using the tool provided and retrieve their response.

      You will need to pass the schema name and the payload to the tool.

      Schema name:
      {schema_name}

      Payload:
      {payload}
    expected_output: |
      {
          "bot_response": "<This is the bot response>"
      }
    context: [formulate_response]
    json_output: BotResponseModel

  evaluate_response:
    agent: response_evaluator
    description: |
      Analyze the customer service response and assess its quality based on the flow mapping, chat history, and bot response. Determine if further action is needed.

      Chat history:
      {chat_history}

      Flow mapping:
      {flow_mapping}
    expected_output: |
      {
          "evaluation": {
              "response_quality": "Good/Needs Improvement",
              "follow_up_required": true/false,
              "issues_identified": ["List of issues if any"]
          }
      }
    arguments:
      - chat_history
      - flow_mapping
    context: [call_endpoint]

  make_chat_decision:
    agent: chat_decision_maker
    description: |
      Decide whether to send a follow-up response to customer service or conclude the chat based on the response evaluation and flow state.
    expected_output: |
      {
          "decision": "Respond/End Chat",
          "next_action": "Description of the next action if responding"
      }
    context: [evaluate_response]
