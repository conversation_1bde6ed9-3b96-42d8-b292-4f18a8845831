crew_config:
  process: sequential
  verbose: True
  content_task_output: 1
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  response_formulator:
    role: "Response Formulator"
    goal: "Formulate an appropriate response based on bot's planned response and node context"
    backstory: "An expert in crafting contextual and purposeful responses while maintaining conversation flow"
    model: "gpt-4o-mini"
    verbose: False

  json_formatter:
    role: "JSON Response Formatter"
    goal: "Format the response into the required JSON structure"
    backstory: "A specialist in structuring responses according to predefined schemas"
    model: "gpt-4o-mini"
    verbose: False

  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and appropriateness of the formatted response"
    backstory: "A quality assurance specialist ensuring response meets requirements"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the response quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to responses"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  response_formulation_task:
    agent: response_formulator
    description: |
      You are {bot_name}, a customer service bot. Based on the following configuration, formulate an appropriate response based on the planned response and node context.

      Your configuration is:
      {bot_settings}

      Bot planned response:
      {stage_execution_output}

      Current node:
      {stage_node}

      Unread messages:
      {unread_messages}
    expected_output: |
      {
          "formulated_response": "The complete response message to be sent to the user",
          "context_summary": "A concise summary of the current conversation context and state",
          "decision_rationale": "Detailed explanation of why this response was chosen",
          "additional_context": {
              "user_intent": "The detected intent/purpose of the user's messages",
              "confidence_score": 0.95,
              "previous_states": ["List of conversation states that led to this point"],
              "relevant_entities": {
                  "entity_type": "Specific entities extracted from the conversation"
              }
          },
          "tone_analysis": {
              "formality": "formal/informal/casual",
              "sentiment": "positive/negative/neutral",
              "urgency": "high/medium/low"
          },
          "is_completed": true
      }
    arguments:
      - bot_name
      - bot_settings
      - stage_execution_output
      - stage_node
      - unread_messages

  json_formatting_task:
    agent: json_formatter
    description: |
      Format the formulated response into the required JSON structure.

      The additional_context should include:
      - user_intent: The detected intent of the user
      - confidence_score: How confident we are in our response
      - previous_states: List of previous conversation states
      - relevant_entities: Any entities extracted from the conversation

      The is_completed should be:
      - True if the current node's requirements are met
      - False if further interaction is needed
    expected_output: |
      {
          "bot_response": "The formatted response message",
          "context_summary": "A concise summary of the current conversation context",
          "decision_rationale": "Explanation of why this response was chosen",
          "additional_context": {
              "user_intent": "The detected user intent",
              "confidence_score": 0.0,
              "previous_states": ["List of previous states"],
              "relevant_entities": {
                  "entity_type": "entity_value"
              }
          },
          "is_completed": true/false
      }
    context: [response_formulation_task]
    json_output: ResponseOutput

  output_analysis_task:
    agent: output_analyzer
    description: |
      Analyze the quality and appropriateness of the formatted response.
    expected_output: |
      {
          "quality_assessment": {
              "clarity_score": 0.0,
              "relevance_score": 0.0,
              "context_alignment": 0.0
          },
          "improvement_suggestions": ["List of suggested improvements"],
          "critical_issues": ["Any critical issues found"],
          "overall_quality_score": 0.0
      }
    context: [json_formatting_task]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Score the response based on defined quality metrics.

      #### **1. Analysis Quality (0-10)**

      This evaluates the bot's comprehension of the provided context, user intent, and its ability to identify the correct node or stage within a conversation flow.

      - **0-3: Below Standard**
        - **Description**: The bot's understanding is superficial or inaccurate. Key intents are missed, or the incorrect node/stage is identified, indicating limited comprehension.
        - **Example**: The bot incorrectly identifies a node that doesn't align with the user's intent, suggesting an unrelated flow. Discrepancies are frequent and indicate poor comprehension.

      - **4-6: Basic Understanding** 
        - **Description**: The bot demonstrates a basic understanding, identifying an approximate node or stage but lacking depth. Some important details are missed or misinterpreted.
        - **Example**: The bot selects a plausible node but fails to capture key nuances in the user's intent, leading to minor misalignments with the expected flow.

      - **7-8: Good Understanding**
        - **Description**: The bot accurately identifies the correct node and stage, with minor omissions. Most details align well with the user's intent, but a few finer points could be clarified.
        - **Example**: The bot correctly identifies the flow and stage and includes most relevant details but could improve in addressing subtle aspects of user context.

      - **9-10: Professional Understanding**
        - **Description**: The bot demonstrates comprehensive, insightful understanding. It identifies the correct node and stage with precision, showing clear comprehension of the user's intent.
        - **Example**: The bot not only identifies the correct node but provides a nuanced understanding of user intent, capturing all relevant details accurately.

      #### **2. Conciseness (0-10)**

      This measures how well the response balances completeness with brevity.

      - **0-3: Excessively Detailed**
        - **Description**: Response includes unnecessary information and tangents that detract from the core message.
        - **Example**: The bot provides extensive background information and multiple examples when a simple direct answer would suffice.

      - **4-6: Moderately Concise**
        - **Description**: Response contains mostly relevant information but includes some unnecessary details.
        - **Example**: The bot gives a generally focused response but includes a few extraneous details that could be omitted.

      - **7-8: Concise and Focused**
        - **Description**: Response is clear and to-the-point while maintaining necessary context.
        - **Example**: The bot provides all essential information without unnecessary elaboration.

      - **9-10: Highly Concise**
        - **Description**: Response achieves perfect balance of brevity and completeness.
        - **Example**: The bot delivers exactly what's needed with no wasted words while maintaining clarity.

      #### **3. Relevance of Information (0-10)**

      This assesses how well the information aligns with the user's needs and query context.

      - **0-3: Irrelevant**
        - **Description**: Response contains mostly or completely irrelevant information.
        - **Example**: The bot provides information about an unrelated topic or misses the core of the user's query.

      - **4-6: Partially Relevant**
        - **Description**: Some relevant information is present but mixed with irrelevant details.
        - **Example**: The bot addresses part of the query correctly but includes unrelated tangents.

      - **7-8: Mostly Relevant**
        - **Description**: Most information is relevant with minor deviations.
        - **Example**: The bot provides mostly pertinent information with only slight divergence from the core query.

      - **9-10: Fully Relevant**
        - **Description**: All information directly addresses the user's query and context.
        - **Example**: The bot's response is perfectly aligned with the user's needs and context.

    expected_output: |
      {
          "analysis_quality": {
              "score": X,
              "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
              "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
          },
          "conciseness": {
              "score": X,
              "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
              "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
          },
          "relevance_of_information": {
              "score": X,
              "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
              "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."
          }
      }
    context: [output_analysis_task]
    json_output: RubricScore
