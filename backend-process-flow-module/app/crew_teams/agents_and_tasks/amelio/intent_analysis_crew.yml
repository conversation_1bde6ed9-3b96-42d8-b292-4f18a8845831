crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  context_analyzer:
    role: "Conversation Context Expert"
    goal: "Analyze the context and extract crucial information"
    backstory: "With a decade of experience in linguistic analysis, specializes in deriving key insights from complex conversational contexts."
    model: "gpt-4o-mini"
    verbose: False

  flow_stage_identifier:
    role: "Conversation Flow Architect"
    goal: "Determine the flow and stage to focus on based on unread messages, historical messages, current state, and defined flows."
    backstory: "A seasoned professional with 10 years in conversation design, expert in mapping and navigating intricate dialogue structures."
    model: "gpt-4o-mini"
    verbose: False

  historical_intent_summarizer:
    role: "Dialogue History Analyst"
    goal: "Summarize the intent of the historical messages to provide context."
    backstory: "After 10 years in conversational AI, excels at distilling critical intents from extensive historical dialogue data."
    model: "gpt-4o-mini"
    verbose: False

  unread_intent_analyzer:
    role: "Real-time Intent Interpreter"
    goal: "Identify the intent within unread messages to clarify user's current objective."
    backstory: "With a decade of experience in NLP, specializes in rapidly decoding user intents from the most recent messages."
    model: "gpt-4o-mini"
    verbose: False

  discussion_continuity_analyzer:
    role: "Discussion Continuity Analyzer"
    goal: "Analyze the relationship between unread messages and recent messages to identify if the conversation is continuing or has been interrupted, and explain why."
    backstory: "An advanced conversational context analyzer with expertise in detecting discussion flow and continuity across multiple message sets."
    model: "gpt-4o-mini"
    verbose: False

  flow_stage_analyzer:
    role: "Flow and Stage Analyzer"
    goal: "Analyze the current flow and stage against the focus determined in point 1."
    backstory: "An analytical agent focused on comparing current and recommended conversation flow stages for continuity."
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the analysis quality based on rubric scoring metrics."
    backstory: "An evaluation agent specialized in applying rubric metrics to ensure analysis quality and relevance."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  context_analysis_task:
    agent: context_analyzer
    description: |
      The objective of this task is to extract the appointment information from the chat messages if available:
      1. Name of the customer
      2. Mobile of the customer
      3. NRIC of the customer
      4. Date of Birth of the customer
      5. Appointment Type(s) of the appointment
      6. Branch of the appointment
      7. Doctor of the appointment
      8. Date of the appointment
      9. Time of the appointment
      10. Appointment Request of the customer

      Here are the steps to determine the values:

      1. Look at the past messages and determine if we just asked the user for the relevant information.
      2. If user has previously answered, use the values. Make sure the latest information takes priority.
      3. If user has provided the information whether in ALL CAPS or small letters, convert it to the correct format.

      ### Guidelines for Extracting Appointment Information

      1. **Name of the customer**  
        - It should not be the user's son. It needs to be the proper customer name.  
        - If the name is not provided, leave it as an empty string (`""`).

      2. **Mobile of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      3. **NRIC of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      4. **Date of Birth of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      5. **Appointment Type(s) of the appointment**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      6. **Branch of the appointment**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      7. **Doctor of the appointment**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      8. **Date of the appointment**
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      9. **Time of the appointment**
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      10. **Appointment Request of the customer**  
        - Default to None. If specifically asked and the user does not want to provide it, assign it as `"Not given"`.

      ---

      ### Additional Rules:
      1. **If the question has been asked before, and the user does not want to provide the information, the value will be `None`.**  
      2. **If the question has been asked before, but the user is unclear, unsure, or does not reply with the information, the value will be `"Not given"`.**
      ---

      Your output should be a JSON object.

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}

      You can extract the number/ contact number from here if exists. But you must use the contact number in the message context if it exists as customer might provide a different phone number.
      Conversation Contact Number: {conversation_third_party_id}
      Conversation Name: {conversation_name}
    expected_output: |
      A JSON format of all the information extracted from the chat messages.
    arguments:
      - recent_messages
      - unread_messages
      - conversation_third_party_id
      - conversation_name

  historical_intent_summary_task:
    agent: historical_intent_summarizer
    description: |
      Objective: Analyze the historical messages to determine the overall intent of the past discussion. You are a smart assistant that helps users with appointment-related tasks and inquiries.

      Instructions:
      - Examine the historical messages provided in historical messages to determine their intent.
      - Identify the dominant intent based on patterns in user interactions.
      - Ignore minor variations in phrasing and focus on the underlying objective.
      - Output only one intent that best represents the past conversation.
      
      SPECIAL CASE: If ANY message in the historical conversation contains customer support request phrases like "speak to staff", "customer support", "talk to human", etc., classify as Customer Support Request Intent REGARDLESS of other content.

      Intent Categories:
      1. New Appointment Booking Intent - The past conversation was primarily about scheduling a new appointment.
      Example: The user inquired about available appointment slots, requested a specific date, or asked how to book a consultation.

      2. Appointment Rescheduling Intent - The user previously discussed changing an existing appointment.
      Example: The conversation involved requests to reschedule, confirm new timings, or check available slots for adjustments.

      3. Beauty Services Inquiry Intent - The past conversation focused on beauty services, pricing, or availability.
      Example: The user asked about different treatments, service details, costs, or booking requirements.

      4. Medical Inquiry Intent - The user previously asked about symptoms, treatments, or general health concerns.
      Example: The conversation included questions about medical conditions, symptoms, possible treatments, or general health advice.

      5. Customer Support Request Intent - The user requested to speak with human staff or customer support.
      Example: The conversation included phrases like "I want to speak to your staff", "customer support", "talk to a human", or similar requests for live assistance.

      6. Out of Scope Intent - The user asked questions unrelated to healthcare appointments, beauty services, medical advice, or customer support.
      Example: Questions about weather, time, unrelated businesses, general life advice, technical issues, or topics outside healthcare/beauty services.
      Example: "What time should I take medicine?", "What's the weather today?", "Do you sell cars?", "My phone is broken", "What's your favorite color?"

      Data Input:
      - Historical messages: {recent_messages}
    expected_output: |
      {
          "historical_intent_summary": "Summary of intent based on historical messages",
          "reason": "Provide your reasoning why this intent is chosen.",
          "rejected_intent": "Provide your reasoning why the other intents are not."
      }
    arguments:
      - recent_messages

  unread_intent_analysis_task:
    agent: unread_intent_analyzer
    description: |
      Objective: Identify the user's intent by analyzing the new unread messages in combination with the recent conversation history to determine their current objective. You are a smart assistant that helps users with appointment-related tasks and inquiries.

      Instructions:
      - Use the unread messages as the primary context for intent classification.
      - Cross-check with historical messages to understand ongoing discussions or context changes.
      - If the unread message lacks enough information, infer intent from the historical context.
      - CRITICAL: If the unread message contains a NEW QUESTION (especially out-of-scope topics like medical advice, weather, unrelated subjects), classify based on the NEW question's intent, NOT the historical context.
      - EXCEPTION: Only continue historical intent if the unread message directly provides requested information without asking new questions (e.g., bot asked for "name" and user responds "John" - continue appointment booking).
      
      Intents and Examples:
      1. New Appointment Booking Intent - When the user wants to schedule a new appointment with a doctor or beauty service provider.
      Example: "I want to schedule an appointment for a consultation next Monday."
      Example: "Can I book a facial treatment for tomorrow?"
      Example with context: (If previous messages discussed symptoms, and the new message says "Can I see a doctor this week?", classify as a booking intent.)
      Example with info response: (If bot asked for "name, mobile, NRIC" and user responds "John, 012345678, *********", classify as New Appointment Booking Intent - continuing the flow.)
      
      2. Appointment Rescheduling Intent - When the user wants to change the date or time of an existing appointment.
      Example: "I need to reschedule my dental appointment to a later date."
      Example: "Can I move my consultation to next Friday instead?"
      Example with context: (If past messages confirm an existing appointment and the new message says "Can I change it to next week?", classify as rescheduling.)
      
      3. Beauty Services Inquiry Intent - When the user asks about beauty services, pricing, availability, or treatment details.
      Example: "What kind of facials do you offer?"
      Example: "How much does laser hair removal cost?"
      Example with context: (If past messages show interest in facials and the new message asks, "Do you have a package deal?", classify as a beauty services inquiry.)

      4. Medical Inquiry Intent - When the user asks health-related questions about symptoms, treatments, or preventive care.
      Example: "What should I do if I have a fever?"
      Example: "Is it normal to have a headache for three days?"
      Example with context: (If past messages mention illness and the new message asks, "Should I see a doctor?", classify as a medical inquiry.)

      5. Customer Support Request Intent - When the user wants to speak with human staff or customer support.
      Example: "I want to speak to your staff"
      Example: "Can I talk to customer support?"
      Example: "I need to speak to a human"
      Example with context: (If the user seems frustrated or needs personalized assistance and asks to "speak to someone", classify as a customer support request.)

      6. Out of Scope Intent - When the user asks questions unrelated to healthcare appointments, beauty services, medical advice, or customer support.
      
      **IN SCOPE (should NOT be classified as Out of Scope):**
      - Healthcare appointments (booking, rescheduling, canceling)
      - Beauty services questions (facials, treatments, pricing, availability)
      - Medical/health questions (symptoms, treatments, general health advice)
      - Explicit customer support requests
      
      **OUT OF SCOPE (should be classified as Out of Scope Intent):**
      - General life questions: "What time should I take medicine?", "When should I eat?"
      - Weather/time questions: "What's the weather today?", "What time is it?"
      - Unrelated businesses: "Do you sell cars?", "Do you deliver food?"
      - Random topics: "What's your favorite color?", "Tell me a joke"
      - Technical issues: "My phone is broken", "How do I use WhatsApp?"
      - Other services: "Do you provide insurance?", "Can you help with taxes?"
      - General advice: "How to lose weight?", "What should I wear?"
      
      Example: "What time should I take my vitamins?"
      Example: "What's the weather like today?"
      Example: "Do you sell skincare products online?"
      Example: "My computer is not working, can you help?"
      Example: "What's the best restaurant nearby?"
      
      IMPORTANT PRIORITY RULES:
      1. If ANY variation of "speak to staff/human/support" is explicitly stated in unread messages, classify as Customer Support Request Intent.
      2. If the historical messages show bot requested specific information (name, phone, NRIC, appointment type, date, time, etc.) AND the unread message directly provides that exact requested information without asking new questions, classify as continuing the historical intent (appointment booking, rescheduling, etc.) - NOT customer support or out of scope.
         HOWEVER: If the unread message asks a NEW question (especially out-of-scope questions like medical advice, weather, unrelated topics), classify based on the NEW question's intent, NOT the historical context.
      3. Only classify as customer support if user explicitly requests human assistance.
      4. Classify beauty services questions as Beauty Services Inquiry Intent and medical/health questions as Medical Inquiry Intent.
      5. Only classify as out-of-scope if the question is clearly unrelated to healthcare appointments, beauty services, medical advice, or customer support.
      6. CRITICAL: Questions about medicine timing, weather, unrelated topics should be classified as Out of Scope Intent regardless of historical context.

      Data Inputs:
      - Unread messages: {unread_messages}
      - Historical messages: {recent_messages}
    expected_output: |
      {
          "unread_intent": "Identified intent within unread messages",
          "reason": "Provide your reasoning why this intent is chosen.",
          "rejected_intent": "Provide your reasoning why the other intents are not."
      }
    arguments:
      - unread_messages
      - recent_messages

  flow_stage_identification_task:
    agent: flow_stage_identifier
    description: |
      Objective: Analyze the unread messages, historical intent, current state, and defined business flows to determine the specific node and stage that should be selected based on the user's intent. The goal is to identify where the user is within the flow and what action should be taken next.

      Instructions:
      1. Interpret unread messages to identify the latest user intent.
      2. Summarize historical messages to understand the user's previous context.
      3. Use Unread Message Intent and Historical Intent Summary to determine the user's progression in the business flow.
      4. Cross-reference active_flow_mapping to find the corresponding node and stage that matches the identified intent.
      5. Consider previous_feedback to check if prior interactions impact the current state selection.
      6. Ensure the output is a structured JSON object compatible with Python syntax.
      - Validate that the JSON follows proper formatting, including double quotes (") for keys and values.

      Data Inputs:
      - Unread messages: {unread_messages}
      - Historical messages: {recent_messages}
      - Defined flows: {active_flow_mapping}
      - Previous feedback: {previous_feedback}

      Flow Mapping and Selection Logic:
      PRIORITY ORDER (check in this sequence):
      1. If the Unread Message Intent suggests a customer support request AND user explicitly asked for human assistance, select node "action-7" (Handle Customer Support Request).
      2. If the user is continuing an ongoing flow (providing requested information), continue with the same node from historical context:
         - If historical intent was appointment booking and user provides personal info, use "action-1" (New Appointment)
         - If historical intent was rescheduling and user provides info, use "action-2" (Reschedule Appointment)
      3. If the Unread Message Intent suggests a new appointment booking, select the node "action-1" (New Appointment).
      4. If the Unread Message Intent suggests an appointment rescheduling, select the node "action-2" (Reschedule Appointment).
      5. If the Unread Message Intent suggests beauty services questions, select the node "action-5" (Beauty Services Questions).
      6. If the Unread Message Intent suggests medical/health questions, select the node "action-6" (Health Questions).
      7. If the flow reaches a finalization stage, map it to "end-1" (End Process).
      
      CRITICAL: 
      - Do NOT route to customer support unless user explicitly requests human assistance. 
      - Providing requested information should continue the existing flow.
      - Out-of-scope questions (weather, unrelated topics) will be handled automatically by response generation which will append customer support guidance.
      - The response generation system will detect out-of-scope intent and automatically add the customer support message.
    expected_output: |
      {
          "node_id": "The ID of the identified node in the defined flow (e.g., 'start-1')",
          "state_id": "The ID of the state that is relevant to the user's intent",
          "context": {
            "name": <Name of the customer>,
            "mobile": <Mobile of the customer>,
            "nric": <NRIC of the customer>,
            "dob": <Date of Birth of the customer>,
            "appointment_types": <List of appointment types chosen by the customer>,
            "branch": <FULL NAME Branch chosen by the customer>,
            "doctor": <Doctor chosen by the customer>,
            "date": <Date of the appointment>,
            "time": <Time of the appointment>,
            "appointment_request": <Customer's request for the appointment>
          }
      }
    arguments:
      - unread_messages
      - active_flow_mapping
      - previous_feedback
      - recent_messages
    context:
      [
        context_analysis_task,
        historical_intent_summary_task,
        unread_intent_analysis_task,
      ]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: AmelioFlowStageIdentification

  analyze_discussion_continuity_task:
    agent: discussion_continuity_analyzer
    description: |
      Objective: Determine whether the discussion between the user and the system is continuing from the previous messages or if there has been an interruption. Provide a detailed explanation based on the relationship between the unread messages and historical messages.

      Instructions:
      - Analyze semantic connections between unread messages and historical messages.
      - Identify if the unread messages logically follow the historical discussion.
      - Consider interruptions such as context shifts, long delays, or unrelated topics.
      - If the new message aligns with the historical discussion, classify it as continuing.
      - If the new message introduces a different topic or lacks continuity, classify it as interrupted.
      
      Key Indicators of Continuity:
      ✅ The unread messages directly respond to or expand upon the historical conversation.
      ✅ The intent of the unread messages aligns with the intent of historical messages.
      ✅ There is no significant time gap between the last historical message and the unread messages.
      ✅ The user uses references to the previous discussion (e.g., "Yes, I want that appointment," "As I mentioned earlier...").

      Key Indicators of Interruption:
      ❌ The unread messages introduce a new topic unrelated to the historical discussion.
      ❌ There is a long delay between the last message and the new message, making it unlikely that the conversation is continuing.
      ❌ The tone or intent of the unread messages shifts (e.g., from appointment booking to a beauty service inquiry).
      ❌ The user does not reference previous messages, and the response does not logically follow the last discussion.

      Data Inputs:
      - Unread messages: {unread_messages}
      - Historical messages: {recent_messages}
    expected_output: |
      {
          "is_continuing": true/false,
          "reason": "Detailed analysis explaining whether the discussion is continuing or has been interrupted, and why."
      }
    arguments:
      - unread_messages
      - recent_messages

  flow_stage_analysis_task:
    agent: flow_stage_analyzer
    description: |
      Objective: Compare the current flow and stage with the focus flow and stage identified in point 1. Determine if they align and highlight any discrepancies.

      Instructions:
      - Extract current flow and stage from defined flows.
      - Retrieve the focus flow and stage from the identified flow-stage mapping.
      - Compare the current vs. focus flow-stage and check for mismatches.
      - Identify potential discrepancies, such as:
        - Incorrect flow progression
        - Unexpected stage transitions
        - Missing steps before reaching the expected stage
      - Return the result in a structured JSON format.
      
      Data Inputs:
      - Defined flows: {active_flow_mapping}
    expected_output: |
      {
          "current_flow": "Current flow ID or name",
          "current_stage": "Current stage ID or name",
          "focus_match": "Boolean indicating if current flow/stage matches focus flow/stage",
          "discrepancy": "Description of any discrepancy found (if applicable)"
      }
    arguments:
      - active_flow_mapping
    context:
      [
        flow_stage_identification_task,
        historical_intent_summary_task,
        unread_intent_analysis_task,
        analyze_discussion_continuity_task,
      ]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Apply rubric scoring to evaluate the quality of the intent analysis process.

      #### **1. Analysis Quality (0-10)**

      This measures the depth, accuracy, and clarity of the analysis performed in determining the intent and appropriate stage.

      - **0-3: Below Standard**
        - **Description**: The analysis is superficial or inaccurate, missing key intents or failing to identify the correct flow and stage. It shows limited understanding and often contains errors.
        - **Example**: The analysis incorrectly identifies the user's intent, suggesting an unrelated flow/stage. There are significant discrepancies between the analyzed and expected stages.

      - **4-6: Basic Quality**
        - **Description**: The analysis covers basic aspects of the intent and flow but lacks depth. Some key details may be missed, or the logic used may be partially unclear or inconsistent.
        - **Example**: The analysis identifies a plausible flow but misses nuances in the user's intent, leading to minor discrepancies. The focus stage is partially correct, but some elements are misinterpreted.

      - **7-8: Good Quality**
        - **Description**: The analysis is accurate, covering key aspects of the intent with minor omissions. Most details align with the expected flow/stage, though a few could be clarified or expanded.
        - **Example**: The analysis correctly identifies the flow and stage and includes most intent details, but it could have slightly improved on clarity or depth in capturing finer points of user context.

      - **9-10: Professional Quality**
        - **Description**: The analysis is comprehensive, accurate, and insightful, with a clear understanding of the user's intent. There are no discrepancies, and the flow/stage identification is spot-on.
        - **Example**: The analysis not only correctly identifies the flow and stage but also provides context that captures the nuances of user intent and context, with no missing or extraneous details.

      #### **2. Conciseness (0-10)**

      This assesses the ability to keep the analysis brief and to the point, focusing on essential details without redundancy.

      - **0-3: Excessively Detailed**
        - **Description**: The analysis is overly wordy, with much irrelevant or repetitive information that distracts from the key points. Clarity is lost in unnecessary details.
        - **Example**: The analysis includes detailed explanations for each minor point, creating a lengthy output with irrelevant historical context that could have been omitted.

      - **4-6: Moderately Concise**
        - **Description**: The analysis includes some extraneous information and could be more focused. Key points are present but buried under minor or less relevant details.
        - **Example**: While the main points are addressed, the analysis contains explanations that, while tangentially related, are not essential. The user intent is clear but could be presented more succinctly.

      - **7-8: Concise and Focused**
        - **Description**: The analysis is mostly concise, covering essential details with only minor instances of unnecessary information or repetition.
        - **Example**: The analysis focuses on the main points, only slightly veering off into minor details. It is easy to follow but could be refined by trimming a few non-essential elements.

      - **9-10: Highly Concise**
        - **Description**: The analysis is exceptionally concise, with a sharp focus on only the essential details. No redundancies or off-topic information are present.
        - **Example**: The analysis covers the key points succinctly without any superfluous information, providing a clear understanding of intent and flow in just a few focused statements.

      #### **3. Relevance of Information (0-10)**

      This category evaluates whether all provided information is directly relevant to the task's objective and intent analysis.

      - **0-3: Irrelevant or Off-Topic Content**
        - **Description**: The analysis includes off-topic information that distracts from the primary purpose. Much of the content does not align with the user's intent or relevant stages.
        - **Example**: The analysis includes unrelated details about previous conversations or hypothetical intents that are not relevant to the current stage or flow.

      - **4-6: Partially Relevant**
        - **Description**: The analysis contains some off-topic points, though it addresses the main purpose. There is room for refinement to focus more closely on the meeting's objectives.
        - **Example**: The analysis contains a few tangential points, such as minor background details that don't contribute to understanding the current intent. The key points are present but could be clearer with tighter focus.

      - **7-8: Mostly Relevant**
        - **Description**: The analysis is mostly on-topic, with minor deviations. It aligns well with the user's main purpose and captures the key aspects of intent.
        - **Example**: The analysis provides a relevant overview with only a minor digression into secondary details. It focuses on the main intent but could slightly improve by omitting one or two tangential references.

      - **9-10: Fully Relevant**
        - **Description**: The analysis is completely relevant, containing only information that directly aligns with the user's intent and the current stage requirements.
        - **Example**: Every detail in the analysis directly relates to the user's intent and current stage, with no extraneous or off-topic points. The analysis is sharply focused on fulfilling the task objective.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
          "analysis_quality": {
              "score": X,
              "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
              "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
          },
          "conciseness": {
              "score": X,
              "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
              "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
          },
          "relevance_of_information": {
              "score": X,
              "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
              "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."

          }
      }
    context: [flow_stage_analysis_task]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RubricScore
