crew_config:
  process: sequential
  verbose: True
  content_task_output: 2
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  response_formulator:
    role: "Response Formulator"
    goal: "Formulate an appropriate response based on bot's planned response and node context"
    backstory: "An expert in crafting contextual and purposeful responses while maintaining conversation flow"
    model: "gpt-4o-mini"
    verbose: False

  context_analyzer:
    role: "Context Analyzer"
    goal: "Analyze the context and extract crucial information"
    backstory: "An expert in analyzing conversation context and extracting relevant information"
    model: "gpt-4o-mini"
    verbose: False

  json_formatter:
    role: "JSON Response Formatter"
    goal: "Format the response into the required JSON structure"
    backstory: "A specialist in structuring responses according to predefined schemas"
    model: "gpt-4o-mini"
    verbose: False

  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and appropriateness of the formatted response"
    backstory: "A quality assurance specialist ensuring response meets requirements"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the response quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to responses"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  response_formulation_task:
    agent: response_formulator
    description: |
      Objective: As {bot_name}, a {schema_name} salesperson, your task is to craft engaging, empathetic, and concise responses to potential customers while aligning with your company's tone ({bot_settings}). 
      
      Your response should:
      - Address customer concerns with empathy and clarity.
      - Maintain a concise, engaging WhatsApp format (split into a max of 2 messages).
      - Rephrase the provided "Bot planned response" content while preserving its meaning.
      - Subtly guide customers toward next steps without overemphasizing them.
      
      Response Formatting Guidelines:
      - Convert to Markdown for structured readability.
      - Reduce blank lines (\n) to keep it compact.
      - Ensure a messaging-friendly format with natural flow and engagement.
      - DO NOT include "Message 1:", "Message 2:" or any technical labels in the response.
      - Each array element should be a complete, natural message that users will see directly.
      
      Data Inputs:
      - Bot planned response: {stage_execution_output}
      - Customer messages: {unread_messages}
      - Follow-up steps: {following_nodes} (Use subtly to guide the conversation)

    expected_output: |
      {
          "formulated_response": [
              "Hi there! I'm sorry to hear you're not feeling well. Let's get your appointment set up. Could you please provide your name, mobile number, and NRIC?",
              "Don't worry, your information will be kept confidential and used only for this appointment. Thank you for sharing!"
          ]
      }
    arguments:
      - schema_name
      - bot_name
      - bot_settings
      - stage_execution_output
      - unread_messages
      - following_nodes

  formulate_json_reply_task:
    agent: context_analyzer
    description: |
      The purpose of this task is to take the response formulated in the `response_formulation_task` and place it under `formulated_response`. Additionally, the agent should analyze the provided context to extract all available information from the unread messages and past messages.
      Additionally, if the "Bot planned response" contains instructions to store information as message context, you should extract the information as added it under additional_context.

      CRITICAL - USE ACTUAL RESPONSE CONTENT:
      You MUST use the actual bot planned response from stage_execution_output. DO NOT use hardcoded appointment booking messages.
      If the bot planned response is about customer support, use that content exactly.
      If the bot planned response is about appointments, use that content exactly.
      The formulated_response MUST reflect the actual stage_execution_output content.

      IMPORTANT - Message Formatting:
      When formatting the formulated_response, ensure that:
      - DO NOT include "Message 1:", "Message 2:", "**Message 1:**", "**Message 2:**" or any technical labels
      - Each array element should be a complete, natural message that users will see directly
      - Remove any markdown formatting like "**Message X:**" from the beginning of messages
      - Keep the actual content but strip away technical labels

      IMPORTANT - Out of Scope Customer Support Message:
      If the user's question is identified as "Out of Scope Intent" (questions unrelated to healthcare appointments, beauty services, medical advice, or customer support) AND the user has NOT already requested customer support, you MUST append the following customer support message as an additional element in the formulated_response array:
      "If you need further assistance, simply send the message 'I want to speak to your staff' and we'll connect you with a customer support representative."
      
      EXCEPTION: Do NOT append this message if:
      - The user has already requested to speak to staff/customer support
      - The bot planned response already contains a customer support confirmation message
      - The current interaction is already handling a customer support request
      - The user's question is within scope (appointments, beauty services, medical questions)
      
      This ensures customers can easily access human support when they ask questions outside our service scope, but avoids redundant messages when they've already requested support or are asking relevant questions.

      DETECTION LOGIC:
      Check the context from intent analysis to determine if the user's intent was classified as "Out of Scope Intent". 
      If the unread_intent from the context contains "Out of Scope Intent", then append the customer support message.
      This replaces the previous confidence score logic with out-of-scope detection.

      Your output should be a JSON object containing the following:
      1. `formulated_response`: The response from the `response_formulation_task`, with customer support message appended if out of scope
      2. `context_summary`: A concise summary of the current conversation context and state
      3. `decision_rationale`: A detailed explanation of why this response was chosen
      4. `additional_context`:
        - `user_intent`: The detected intent of the user
        - `out_of_scope_intent`: Whether the user's question is outside our service scope (true/false)
        - `previous_states`: A list of previous conversation states leading to this point
        - `relevant_entities`: Any entities extracted from the conversation
      5. `tone_analysis`:
        - `formality`: Whether the tone is formal, informal, or casual
        - `sentiment`: Whether the sentiment is positive, negative, or neutral
        - `urgency`: Whether the urgency is high, medium, or low
      6. `is_completed`: true/false, depending on whether all necessary information has been extracted and the response is finalized

      The agent must ensure that all information is extracted accurately and presented in a structured format for further processing.

      Bot planned response:
      {stage_execution_output}

      Past messages:
      {recent_messages}

      Unread messages:
      {unread_messages}

      Context:
      {context}
    expected_output: |
      {
          "formulated_response": [
              "Dynamic response based on the actual bot planned response and context - DO NOT use hardcoded examples",
              "Only add customer support message if out of scope AND user has NOT already requested support: 'If you need further assistance, simply send the message I want to speak to your staff and we will connect you with a customer support representative.'"
          ],
          "context_summary": "A concise summary of the current conversation context and state",
          "decision_rationale": "Detailed explanation of why this response was chosen",
          "additional_context": {
              "user_intent": "The detected intent/purpose of the user's messages",
              "out_of_scope_intent": "True/False",
              "previous_states": ["List of conversation states that led to this point"],
              "relevant_entities": {
                  "entity_type": "Specific entities extracted from the conversation"
              }
          },
          "tone_analysis": {
              "formality": "formal/informal/casual",
              "sentiment": "positive/negative/neutral",
              "urgency": "high/medium/low"
          },
          "is_completed": true
      }
    arguments:
      - stage_execution_output
      - recent_messages
      - unread_messages
      - context

  json_formatting_task:
    agent: json_formatter
    description: |
      Format the formulated response into the required JSON structure. You MUST NOT change the format and content of the `formulated_response`.

      CRITICAL - USE ACTUAL FORMULATED RESPONSE:
      You MUST transfer the actual formulated_response from the context to bot_response. DO NOT use hardcoded appointment booking messages.
      If the formulated_response contains customer support messages, use those exactly.
      If the formulated_response contains appointment messages, use those exactly.
      The bot_response MUST be identical to the formulated_response from context.

      IMPORTANT - Message Formatting:
      When transferring formulated_response to bot_response, ensure that:
      - DO NOT include "Message 1:", "Message 2:", "**Message 1:**", "**Message 2:**" or any technical labels
      - Each array element should be a complete, natural message that users will see directly
      - Remove any markdown formatting like "**Message X:**" from the beginning of messages
      - Keep the actual content but strip away technical labels
      - Users should see clean, natural messages without any technical formatting

      CRITICAL - Out of Scope Customer Support Message:
      If the user's question is identified as "Out of Scope Intent" (questions unrelated to healthcare appointments, beauty services, medical advice, or customer support) AND the user has NOT already requested customer support, you MUST append the following customer support message as an additional element in the formulated_response array:
      "If you need further assistance, simply send the message 'I want to speak to your staff' and we'll connect you with a customer support representative."
      
      EXCEPTION: Do NOT include this message if:
      - The user has already requested to speak to staff/customer support
      - The formulated_response already contains a customer support confirmation message
      - The current interaction is already handling a customer support request
      - The user's question is within scope (appointments, beauty services, medical questions)
      
      This ensures customers can easily access human support when they ask questions outside our service scope, but avoids redundant messages when they've already requested support or are asking relevant questions.

      DETECTION LOGIC:
      Check the additional_context from the previous task to determine if out_of_scope_intent is true.
      If out_of_scope_intent is true, then append the customer support message to bot_response.
      This replaces the previous confidence score logic with out-of-scope detection.

      The additional_context should include:
      - user_intent: The detected intent of the user
      - out_of_scope_intent: Whether the user's question is outside our service scope (true/false)
      - previous_states: List of previous conversation states
      - relevant_entities: Any entities extracted from the conversation

      The is_completed should be:
      - True if the current node's requirements are met
      - False if further interaction is needed

      Conversation ID:
      {conversation_id}

      Context:
      {context}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
          "bot_response": [
              "MUST use the actual formulated_response from context - DO NOT use hardcoded appointment booking messages",
              "Only include customer support message if out of scope AND user has NOT already requested support: 'If you need further assistance, simply send the message I want to speak to your staff and we will connect you with a customer support representative.'"
          ],
          "conversation_id": "< Conversation ID >",
          "context_summary": "A concise summary of the current conversation context",
          "decision_rationale": "Explanation of why this response was chosen",
          "additional_context": {
              "user_intent": "The detected user intent",
              "out_of_scope_intent": "True/False",
              "previous_states": ["List of previous states"],
              "relevant_entities": {
                  "entity_type": "entity_value"
              }
          },
          "is_completed": true/false
      }
    context: [formulate_json_reply_task]
    arguments:
      - conversation_id
      - context
    guardrails:
      - validate_pydantic_output
      - working_hours_check
    max_retries: 5
    output_pydantic: AmelioResponseOutput

  output_analysis_task:
    agent: output_analyzer
    description: |
      Analyze the quality and appropriateness of the formatted response.
    expected_output: |
      {
          "quality_assessment": {
              "clarity_score": 0.0,
              "relevance_score": 0.0,
              "context_alignment": 0.0
          },
          "improvement_suggestions": ["List of suggested improvements"],
          "critical_issues": ["Any critical issues found"],
          "overall_quality_score": 0.0
      }
    context: [json_formatting_task]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Score the response based on defined quality metrics.

      #### **1. Analysis Quality (0-10)**

      This evaluates the bot's comprehension of the provided context, user intent, and its ability to identify the correct node or stage within a conversation flow.

      - **0-3: Below Standard**
        - **Description**: The bot's understanding is superficial or inaccurate. Key intents are missed, or the incorrect node/stage is identified, indicating limited comprehension.
        - **Example**: The bot incorrectly identifies a node that doesn't align with the user's intent, suggesting an unrelated flow. Discrepancies are frequent and indicate poor comprehension.

      - **4-6: Basic Understanding** 
        - **Description**: The bot demonstrates a basic understanding, identifying an approximate node or stage but lacking depth. Some important details are missed or misinterpreted.
        - **Example**: The bot selects a plausible node but fails to capture key nuances in the user's intent, leading to minor misalignments with the expected flow.

      - **7-8: Good Understanding**
        - **Description**: The bot accurately identifies the correct node and stage, with minor omissions. Most details align well with the user's intent, but a few finer points could be clarified.
        - **Example**: The bot correctly identifies the flow and stage and includes most relevant details but could improve in addressing subtle aspects of user context.

      - **9-10: Professional Understanding**
        - **Description**: The bot demonstrates comprehensive, insightful understanding. It identifies the correct node and stage with precision, showing clear comprehension of the user's intent.
        - **Example**: The bot not only identifies the correct node but provides a nuanced understanding of user intent, capturing all relevant details accurately.

      #### **2. Conciseness (0-10)**

      This measures how well the response balances completeness with brevity.

      - **0-3: Excessively Detailed**
        - **Description**: Response includes unnecessary information and tangents that detract from the core message.
        - **Example**: The bot provides extensive background information and multiple examples when a simple direct answer would suffice.

      - **4-6: Moderately Concise**
        - **Description**: Response contains mostly relevant information but includes some unnecessary details.
        - **Example**: The bot gives a generally focused response but includes a few extraneous details that could be omitted.

      - **7-8: Concise and Focused**
        - **Description**: Response is clear and to-the-point while maintaining necessary context.
        - **Example**: The bot provides all essential information without unnecessary elaboration.

      - **9-10: Highly Concise**
        - **Description**: Response achieves perfect balance of brevity and completeness.
        - **Example**: The bot delivers exactly what's needed with no wasted words while maintaining clarity.

      #### **3. Relevance of Information (0-10)**

      This assesses how well the information aligns with the user's needs and query context.

      - **0-3: Irrelevant**
        - **Description**: Response contains mostly or completely irrelevant information.
        - **Example**: The bot provides information about an unrelated topic or misses the core of the user's query.

      - **4-6: Partially Relevant**
        - **Description**: Some relevant information is present but mixed with irrelevant details.
        - **Example**: The bot addresses part of the query correctly but includes unrelated tangents.

      - **7-8: Mostly Relevant**
        - **Description**: Most information is relevant with minor deviations.
        - **Example**: The bot provides mostly pertinent information with only slight divergence from the core query.

      - **9-10: Fully Relevant**
        - **Description**: All information directly addresses the user's query and context.
        - **Example**: The bot's response is perfectly aligned with the user's needs and context.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
          "analysis_quality": {
              "score": X,
              "rating": "below_standard/basic/good/professional",  # Options: "below_standard", "basic", "good", "professional"
              "reason": "Explanation for the assigned score, describing strengths and areas for improvement in the analysis quality."
          },
          "conciseness": {
              "score": X,
              "rating": "excessively_detailed/moderately_concise/concise_and_focused/highly_concise",  # Options: "excessively_detailed", "moderately_concise", "concise_and_focused", "highly_concise"
              "reason": "Explanation for the assigned score, highlighting any unnecessary detail or commendable conciseness."
          },
          "relevance_of_information": {
              "score": X,
              "rating": "irrelevant/partially_relevant/mostly_relevant/fully_relevant",  # Options: "irrelevant", "partially_relevant", "mostly_relevant", "fully_relevant"
              "reason": "Explanation for the assigned score, noting alignment with task objectives and relevance to user intent."
          }
      }
    context: [output_analysis_task]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RubricScore
