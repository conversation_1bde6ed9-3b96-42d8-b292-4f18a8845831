crew_config:
  process: sequential
  verbose: True
  content_task_output: 3
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  company_profile_builder:
    role: "Company Profile Builder"
    goal: "Create a detailed profile of the company based on metadata and provided knowledge."
    backstory: "An expert in analyzing company operations and customer demographics."
    model: "gpt-4o-mini"
    verbose: False

  customer_scenario_generator:
    role: "Customer Scenario Generator"
    goal: "Generate realistic scenarios of customer interactions based on company profiles, incorporating real customer messaging behaviors and tone."
    backstory: "A creative specialist in mimicking actual customer conversations and constructing plausible use cases."
    model: "gpt-4o-mini"
    verbose: False

  customer_personality_creator:
    role: "Customer Personality Creator"
    goal: "Define customer traits that reflect real-world behaviors, tone, and word choice in inquiries, making interactions sound authentic and relatable."
    backstory: "A psychologist with expertise in analyzing customer messaging and emotional states to replicate natural communication styles."
    model: "gpt-4o-mini"
    verbose: False

  full_scenario_builder:
    role: "Full Scenario Builder"
    goal: "Create complete, customer-centric scenarios using conversational language that aligns with real-life customer behaviors."
    backstory: "An expert storyteller who captures authentic customer voices, including tone, style, word choice, and messaging brevity."
    model: "gpt-4o-mini"
    verbose: False

  scenario_personality_analyzer:
    role: "Scenario and Personality Analyzer"
    goal: "Evaluate the correctness and detail of customer scenarios and personalities."
    backstory: "A quality analyst focusing on verifying scenario accuracy and depth."
    model: "gpt-4o-mini"
    verbose: False

  scenario_scorer:
    role: "Scenario Scorer"
    goal: "Score scenarios using a given rubric for quality and detail."
    backstory: "An evaluation expert applying scoring metrics to assess scenario quality."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  build_company_profile:
    agent: company_profile_builder
    description: |
      Use the provided metadata, knowledge, and existing customer profiles (if any) to create a detailed and realistic profile of the company, including {num_of_customer_profiles} target customer profiles.

      - **If existing customer profiles are provided**, prioritize using them to determine the company's target customers and offerings. Refine these profiles for realism and alignment with the company's business nature and products/services.
      - **If the number of existing profiles is fewer than {num_of_customer_profiles}**, generate additional realistic profiles based on the company metadata and knowledge to meet the total of {num_of_customer_profiles} profiles.
      - **If no existing customer profiles are provided**, create {num_of_customer_profiles} realistic profiles from scratch using the company metadata and knowledge.

      Ensure that the profiles are well-aligned with the company’s operations, include plausible demographics, and reflect likely customer interactions with the company's products/services.

      Company metadata:
      {company_metadata}

      Company knowledge:
      {company_knowledge}

      Existing customer profiles (if any):
      {existing_customer_profiles}

      Previous feedback:
      {previous_feedback}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "company_name": "Name of the company",
          "business_nature": "Description of what the company does",
          "products_services": ["List of products and services offered"],
          "target_customers": [
              {
                  "profile_id": "Unique identifier for the customer profile",
                  "description": "Detailed description of the target customer, including demographics, interests, and potential needs"
              }
          ]
      }
    arguments:
      - company_metadata
      - company_knowledge
      - existing_customer_profiles
      - previous_feedback
      - num_of_customer_profiles

  generate_customer_scenarios:
    agent: customer_scenario_generator
    description: |
      Generate {num_of_customer_profiles} realistic scenarios where customers interact with the company's chatbot. The scenarios must replicate actual customer messaging behaviors:

      1. Use **short, professional yet friendly messaging styles** common in healthcare communication.
      2. Include realistic tones like:
        - Polite and clear
        - Direct and specific
        - Urgent medical concerns
        - Detailed health queries
      3. Ensure queries align with **typical healthcare needs**:
        - Appointment details: type of consultation, preferred doctor, timing
        - Medical concerns: symptoms, duration, severity
        - Service inquiries: treatments available, procedure information
      4. Incorporate **patient states** like concern, urgency, uncertainty, or seeking information.

      Ensure the scenarios flow logically through:
      - Initial service request
      - Information verification
      - Appointment/query resolution

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      [
          {
              "customer_intent": "What the customer wants to achieve",
              "query_type": "Inquiry, request, or urgent need",
              "customer_background": "Short background context",
              "short_scenario": "Realistic, conversational style interaction"
          }
      ]
    arguments:
      - num_of_customer_profiles
      - company_knowledge
    context: [build_company_profile]

  define_customer_personalities:
    agent: customer_personality_creator
    description: |
      Define customer personalities that encompass a wide range of real-world tones and messaging behaviors:
      - **Temperament:** Can vary from negative traits like anger, rudeness, and uncooperativeness to positive traits such as friendliness, calmness, and cooperation.
      - **Communication style:**  
        - May range from dismissive and curt responses ("Whatever, just do it.") to friendly and engaging queries ("Could you please help me with this?")
        - The tone can be from casual to demanding.
      - **Emotional states:** Span from negative (frustrated, annoyed) to neutral (indecisive, curious) to positive (enthusiastic, eager).

      Ensure that the personality traits align with the context of queries provided. Each personality should naturally align with the type of inquiry and the anticipated emotional context.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      [
          {
              "personality_traits": {
                  "temperament": "Temperament description",
                  "communication_style": "Real-world tone and phrasing style",
                  "emotional_state": "Emotional context for the inquiry"
              }
          }
      ]
    arguments:
      - num_of_customer_profiles
    context: [generate_customer_scenarios]

  build_full_scenarios:
    agent: full_scenario_builder
    description: |
      Create customer scenarios that use short, casual messaging style typical of healthcare appointment booking. Messages should be brief, clear, and professional yet friendly.

      **Primary Objective**:
      - MAIN GOAL: Successfully complete one of these service requests:
        1. Book a new appointment
        2. Reschedule an existing appointment
        3. Ask beauty service related questions
        4. Ask health related questions
      - Reach out to customer service to initiate the conversation
      - CONTINUE conversation until the service request is fully completed
      - Provide all required information based on the service type

      **Interaction Strategy**:
      - Start with clear intent about which service is needed
      - Provide necessary details based on service type:
        * For new appointments: personal info, appointment type, preferred location/doctor/time
        * For rescheduling: NRIC, DOB, preferred new time
        * For questions: clear description of beauty/health concern
      - Respond to verification requests and additional information needs
      - ONLY conclude when service request is fully completed

      **Customer Profile Information**:
      Create detailed customer profiles including:
      - Demographics (age, occupation)
      - Location in Singapore
      - Medical history relevance
      - Cultural background and preferences
      - Communication style and comfort with medical terms
      - Previous experience with healthcare services

      **Service Requirements**:
      Detail the complete service specifications based on request type:
      1. New Appointment Information:
        - Personal details (name, mobile, NRIC)
        - Appointment type (consultation types available)
        - Preferred branch location
        - Doctor preference
        - Preferred date and time (must be during operation hours)
        - Specific health concerns or requests
      
      2. Reschedule Appointment Information:
        - NRIC
        - Date of Birth
        - Original appointment details
        - Preferred new date/time
      
      3. Beauty/Health Questions:
        - Specific concern or topic
        - Relevant background information
        - Previous treatments if applicable

      **Personality Traits**:
      Define diverse customer interaction styles:
      - Communication style: 
        Range from direct and brief to detailed and explanatory
      - Health information sharing:
        Vary from hesitant to very open
      - Decision-making approach: 
        Span from quick decisions to needing detailed information
      - Temperament: 
        Vary from patient and understanding to anxious and concerned
      - Medical literacy:
        Range from basic to well-informed about medical terms
      - Time sensitivity:
        Vary from urgent needs to flexible scheduling
      - Information sharing:
        Range from minimal to comprehensive health history sharing

      **Scenario Context**:
      Create a realistic narrative including:
      - Reason for seeking healthcare service
      - Urgency level of the request
      - Key health concerns or priorities
      - Previous healthcare experiences
      - Specific triggers for seeking care
      - Time constraints or preferences

      **Guidelines**:
      - Ensure scenarios reflect Singapore's healthcare context
      - Consider operation hours for appointments
      - Account for medical privacy and data protection
      - Include realistic medical concerns and queries
      - Maintain professional yet approachable tone
      - Follow proper healthcare communication protocols

      **Success Criteria**:
      - Conversation MUST continue until service request is fully completed
      - All required information must be accurately provided
      - Proper verification steps must be completed

      There are {num_of_customer_profiles} scenarios. Return in an array of dictionaries.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "scenarios": [
          {
            "customer_profile": {
              "name": "Full name of the customer (e.g., 'Tan Mei Ling')",
              "contact": {
                "phone": "Singapore format mobile number (e.g., '91234567')",
                "email": "Valid email address (e.g., '<EMAIL>')"
              },
              "demographics": {
                "age": "Age in years (e.g., '35')",
                "occupation": "Current job role (e.g., 'Software Engineer', 'Teacher')",
                "location": "Specific area in Singapore (e.g., 'Choa Chu Kang', 'Bedok')",
                "cultural_background": "Ethnicity and cultural context (e.g., 'Chinese Singaporean', 'Malay Muslim')"
              }
            },
            "additional_details": {
              "service_type": "One of: ['New Appointment', 'Reschedule Appointment', 'Beauty Service Question', 'Health Question']",
              "personal_info": {
                "nric": "Valid NRIC format (e.g., '*********')",
                "dob": "Date in YYYY-MM-DD format"
              },
              "appointment_details": {
                "type": "One of: ['General Practice', 'Allergy', 'Aesthetics', 'Sports Medicine', 'Mental Health', 'Health Screening', 'Others']",
                "branch": "One of: ['healthwerkz medical centre @ choa chu kang', 'healthwerkz medical centre @ bedok', 'healthwerkz medical centre @ centrium', 'healthwerkz medical centre @ sembawang blk 355']",
                "doctor": "One of: ['Dr. Ong Lay Siang', 'Dr. Gerald Chong', 'Dr. Goh Hsin Kai', 'Dr. J Susanna Wong']",
                "date": "Future date in YYYY-MM-DD format",
                "time": "Time in HH:MM format within operation hours (8:30-13:00 or 18:00-21:00)"
              },
              "medical_context": {
                "concerns": "Specific health/beauty issue (e.g., 'persistent cough for 3 days', 'skin rash on arms')",
                "history": "Relevant medical background (e.g., 'allergic to penicillin', 'previous skin treatments')",
                "urgency": "One of: ['routine', 'urgent', 'follow-up']"
              }
            },
            "journey_stage": {
              "current_stage": "One of: ['initial_contact', 'information_gathering', 'verification', 'scheduling', 'confirmation']",
              "next_steps": "Required actions (e.g., 'provide NRIC', 'select preferred doctor')",
              "verification_status": "One of: ['pending', 'partially_verified', 'fully_verified']"
            },
            "personality": {
              "communication_style": "One of: ['direct', 'detailed', 'hesitant', 'formal']",
              "temperament": "One of: ['basic', 'intermediate', 'advanced']",
              "emotional_state": "One of: ['reserved', 'selective', 'open']",
              "decision_making": "One of: ['quick', 'analytical', 'needs_guidance']"
            },
            "scenario_context": {
              "motivation": "Reason for contact (e.g., 'annual check-up', 'acute symptoms', 'beauty consultation')",
              "concerns": "Specific worries (e.g., 'wait time', 'cost', 'treatment effectiveness')",
              "expectations": "Expected outcomes (e.g., 'quick relief', 'long-term treatment plan')",
              "previous_experience": "Limitations (e.g., 'only available weekends', 'needs evening slots')"
            },
            "objectives": {
              "service_completion": "Requirements based on service type (e.g., 'successful appointment booking', 'clear answer to health query')",
              "information_needs": "Required details (e.g., 'appointment confirmation details', 'treatment options')"
            }
          }
        ]
      }
    arguments:
      - num_of_customer_profiles
      - date_today
    context: [generate_customer_scenarios, define_customer_personalities]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: CustomerScenarioList
