crew_config:
  process: sequential
  verbose: True
  content_task_output: 4
  content_task_output_json_flag: False
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  node_analyzer:
    role: "Node Purpose Analyzer"
    goal: "Understand and analyze the purpose and requirements of the current node"
    backstory: "An expert in analyzing conversation flow nodes and their objectives"
    model: "gpt-4o-mini"
    verbose: False

  response_validator:
    role: "User Response Validator"
    goal: "Evaluate if user responses meet the node requirements"
    backstory: "A specialist in analyzing user responses against defined requirements"
    model: "gpt-4o-mini"
    verbose: False

  state_determiner:
    role: "Node State Determiner"
    goal: "Determine the current state of the node based on user interaction"
    backstory: "An expert in evaluating conversation progress and completion states"
    model: "gpt-4o-mini"
    verbose: False

  context_analyzer:
    role: "Context Analyzer"
    goal: "Analyze the context and extract crucial information"
    backstory: "An expert in analyzing conversation context and extracting relevant information"
    model: "gpt-4o-mini"
    verbose: False

  tool_decider:
    role: "Tool Selection and Query Specialist"
    goal: "Analyze the node context to determine the required tool and construct the arguments for the tool."
    backstory: "An expert in identifying the most appropriate tool for the current context and formulating queries to retrieve the necessary data or documents."
    model: "gpt-4o-mini"
    verbose: False

  tool_executor:
    role: "Tool Execution Specialist"
    goal: "Execute the tool selected by the Tool Selection and Query Specialist using a key-value pair dictionary as the input and return the retrieved data in a structured format. If the value for the key is missing, use the default value of the argument type. (For example, if the value is a type string, use an empty string as the default value.)"
    backstory: "A specialist in executing tools with precision, ensuring the input adheres to the required format (key-value pair dictionary) and retrieving results based on the constructed query."
    model: "gpt-4o-mini"
    verbose: False
    max_iter: 1
    max_retry_limit: 1
    tools:
      - gather_new_appointment_information
      - gather_reschedule_appointment_personal_information
      - gather_reschedule_appointment_date_time_information
      - answer_beauty_service_questions
      - answer_health_questions
      - handle_customer_support_request

  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and correctness of the generated outputs"
    backstory: "A quality assurance specialist ensuring response accuracy and relevance"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the overall execution quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to conversation outputs"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  node_analysis_task:
    agent: node_analyzer
    description: |
      Objective: Analyze the current node to determine its purpose, requirements, expected outcomes, and success criteria within the business flow.

      Instructions:
      - Extract relevant details from node information to understand the node's function.
      - Review unread messages to assess the current context.
      - Identify the purpose of this node—what action or decision it facilitates.
      - List specific requirements needed for this node to execute properly.
      - Define the expected outcomes, including possible system responses or user interactions.
      - Outline success criteria to determine if the node has been completed successfully.
      - Ensure a structured JSON output that is properly formatted and validated.
    
      Data Inputs:
      - Node Information: {stage_node}
      - Unread Messages: {recent_messages}
    expected_output: |
      {
          "node_purpose": "Description of what the node aims to achieve",
          "requirements": ["List of specific requirements"],
          "expected_outcomes": ["Expected results or responses"],
          "success_criteria": ["Criteria for successful completion"]
      }
    arguments:
      - stage_node
      - recent_messages

  response_validation_task:
    agent: response_validator
    description: |
      Objective: Assess whether the user's response fulfills the node requirements and determine any missing elements. Provide a structured evaluation along with a confidence score indicating how well the response meets the expectations.

      Instructions:
      - Analyze unread messages (latest user responses).
      - Reference previous feedback to consider past corrections or improvements.
      - Compare responses against historical messages to detect inconsistencies or missing details.
      - Identify met and missing requirements, ensuring the response aligns with the node's needs.
      - Provide a detailed validation opinion explaining response adequacy.
      - Assign a confidence score (0.0 - 1.0), where:
        - 1.0 = Fully meets requirements
        - 0.7 - 0.9 = Mostly meets requirements, minor improvements needed
        - 0.4 - 0.6 = Partially meets requirements, key details missing
        - 0.1 - 0.3 = Poor response, significant missing details
        - 0.0 = Completely irrelevant or invalid response
      - Return the results in a properly formatted JSON output.

      Data Inputs:
      - Unread messages: {unread_messages}
      - Previous feedback: {previous_feedback}
      - Historical messages: {recent_messages}
    expected_output: |
      {
          "requirements_met": ["List of met requirements"],
          "requirements_missing": ["List of unmet requirements"],
          "validation_opinion": "Detailed opinion on response adequacy",
          "confidence_score": 0.0
      }
    arguments:
      - unread_messages
      - recent_messages
      - previous_feedback
    context: [node_analysis_task]

  state_determination_task:
    agent: state_determiner
    description: |
      Objective: Assess the current execution state of the node within the business flow. Identify whether it is completed, pending, or missing information, and provide recommendations for next steps.

      Instructions:
      - Analyze the defined flows to understand the node's role.
      - Review the current context to assess progress.
      - Determine the execution status based on the following:
        - Completed → All required conditions have been met, and the node has successfully executed.
        - Pending → The node is in progress but has not reached completion.
        - Missing Info → Required elements are missing, preventing node execution.
      - Identify any missing elements needed for successful execution.
      - Provide recommended next steps to move the process forward.
      - Return the results in a structured JSON format with proper validation.

      Data Inputs:
      - Defined flows: {active_flow_mapping}
      - Current Context: {context}
    expected_output: |
      {
          "completion_status": "completed/pending/missing_info",
          "reason": "Detailed explanation for the status",
          "missing_elements": ["List of missing elements if any"],
          "next_steps": ["Recommended next steps"]
      }
    arguments:
      - active_flow_mapping
      - context
    context: [response_validation_task]

  tool_selection_task:
    agent: tool_decider
    description: |
      Objective: Analyze the conversation context to determine if a tool is required for execution. 
      
      If a tool is needed:
      - Identify the most appropriate tool based on the node requirements.
      - Construct a query string suitable for the selected tool.
      - Provide a clear explanation for why the tool was chosen or why no tool is needed.

      Available Tools and Their Functions:
      - gather_new_appointment_information → Collects necessary details for booking a new appointment, such as date, time, and appointment type.
      - gather_reschedule_appointment_personal_information → Retrieves personal details from the user to verify existing appointments before rescheduling.
      - gather_reschedule_appointment_date_time_information → Collects the new preferred date and time for rescheduling an existing appointment.
      - answer_beauty_service_questions → Processes and provides responses to user inquiries about beauty services, including pricing, availability, and treatment details.
      - answer_health_questions → Processes health-related questions and provides relevant information about symptoms, treatments, or medical advice.
      - handle_customer_support_request → Detects when users want to speak to human staff and forwards their request to customer support team.
      
      EXPLICIT NODE-TO-TOOL MAPPING (Use this priority):
      1. If node_id is "action-1" → Always use "gather_new_appointment_information" tool
      2. If node_id is "action-2" → Always use "gather_reschedule_appointment_personal_information" tool  
      3. If node_id is "action-3" → Always use "gather_reschedule_appointment_date_time_information" tool
      4. If node_id is "action-5" → Always use "answer_beauty_service_questions" tool
      5. If node_id is "action-6" → Always use "answer_health_questions" tool
      6. If node_id is "action-7" → Always use "handle_customer_support_request" tool
      
      Instructions:
      - FIRST: Check the node_id in stage_node and use the explicit mapping above
      - If no explicit mapping found, then analyze context to determine appropriate tool
      - Review historical messages to understand the conversation flow
      - Examine new unread messages to identify the latest user request
      - Consider the current execution state to ensure proper alignment with flow progression
      - Match the node requirements and user request to an appropriate tool
      - Construct a query string that passes the necessary parameters for execution
      - Return the results in structured JSON format with proper validation

      Data Inputs:
      - Node Information: {stage_node}
      - Historical Messages: {recent_messages}
      - Unread Message: {unread_messages}
      - Current Context: {context}
      - Defined Flows: {active_flow_mapping}
    expected_output: |
      {
          "selected_tool": "Name of the tool to use, or null if no tool is required",
          "constructed_query": "Query string for the selected tool, or null if no tool is required",
          "selection_reason": "Explanation of why the tool was selected or why no tool is needed"
      }
    arguments:
      - stage_node
      - recent_messages
      - unread_messages
      - context
      - active_flow_mapping
    context: [node_analysis_task, state_determination_task]

  tool_execution_task:
    agent: tool_executor
    description: |
      Execute the tool selected by the Tool Selection and Query Specialist and retrieve the output.

      IMPORTANT - Tool Success Criteria:
      - If a tool returns a STRING response (asking for more information, providing answers, or confirming actions), this is a SUCCESSFUL execution
      - Do NOT retry tool execution when tools return valid string responses asking for user information
      - Only retry if there are actual execution errors or exceptions

      - Use the provided tool name to execute the retrieval process.
      - Do not hallucinate or invent arguments for calling the tools. Only use the information explicitly provided in the context below to determine the arguments.
      - Ensure that the `conversation_id` used for the tool execution matches the one provided in the context.
      - Use the information extracted from the provided context to determine the arguments for the tool.

      Information required to execute the tools can be found in the state of the past messages. Use this information strictly when executing the tool:
      Schema name:
      {schema_name}

      Conversation ID:
      {conversation_id}

      Is RedTeam Run:
      {is_redteam_run}

      Context:
      {context}

    expected_output: |
      Return the output of the executed tool. Do not hallucinate or input your own feedback or opinion. The tools will determine what needs to be included in the response to the customer.
      
      IMPORTANT: If the tool returns a string response asking for information (e.g., "Please provide your appointment type"), return that response directly as a successful completion.
    arguments:
      - schema_name
      - conversation_id
      - is_redteam_run
      - context
    context: [tool_selection_task]
