crew_config:
  process: sequential
  verbose: False
  content_task_output: 4
  content_task_output_json_flag: False
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  node_analyzer:
    role: "Node Purpose Analyzer"
    goal: "Understand and analyze the purpose and requirements of the current node"
    backstory: "An expert in analyzing conversation flow nodes and their objectives"
    model: "gpt-4o-mini"
    verbose: False

  response_validator:
    role: "User Response Validator"
    goal: "Evaluate if user responses meet the node requirements"
    backstory: "A specialist in analyzing user responses against defined requirements"
    model: "gpt-4o-mini"
    verbose: False

  state_determiner:
    role: "Node State Determiner"
    goal: "Determine the current state of the node based on user interaction"
    backstory: "An expert in evaluating conversation progress and completion states"
    model: "gpt-4o-mini"
    verbose: True

  tool_decider:
    role: "Tool Selection and Query Specialist"
    goal: "Analyze the node context to determine the required tool and construct the arguments for the tool."
    backstory: "An expert in identifying the most appropriate tool for the current context and formulating queries to retrieve the necessary data or documents."
    model: "gpt-4o-mini"
    verbose: True

  tool_executor:
    role: "Tool Execution Specialist"
    goal: "Execute the tool selected by the Tool Selection and Query Specialist using a key-value pair dictionary as the input and return the retrieved data in a structured format. If the value for the key is missing, use the default value of the argument type. (For example, if the value is a type string, use an empty string as the default value.)"
    backstory: "A specialist in executing tools with precision, ensuring the input adheres to the required format (key-value pair dictionary) and retrieving results based on the constructed query."
    model: "gpt-4o-mini"
    verbose: True
    tools:
      - gather_issue_description
      - create_service_request
      - reschedule_service_request
  output_analyzer:
    role: "Output Quality Analyzer"
    goal: "Analyze the quality and correctness of the generated outputs"
    backstory: "A quality assurance specialist ensuring response accuracy and relevance"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the overall execution quality using defined metrics"
    backstory: "An evaluation specialist applying quality metrics to conversation outputs"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  node_analysis_task:
    agent: node_analyzer
    description: |
      Analyze the current node to understand its purpose, requirements, and expected outcomes.

      Node information:
      {stage_node}

      Recent messages:
      {recent_messages}

      Current context: 
      {unread_messages}

    expected_output: |
      {
          "node_purpose": "Description of what the node aims to achieve",
          "requirements": ["List of specific requirements"],
          "expected_outcomes": ["Expected results or responses"],
          "success_criteria": ["Criteria for successful completion"]
      }
    arguments:
      - stage_node
      - recent_messages
      - unread_messages

  response_validation_task:
    agent: response_validator
    description: |
      Evaluate if user responses fulfill the node requirements.

      User messages: 
      {unread_messages}

      Recent messages:
      {recent_messages}
    expected_output: |
      {
          "requirements_met": ["List of met requirements"],
          "requirements_missing": ["List of unmet requirements"],
          "validation_opinion": "Detailed opinion on response adequacy",
          "confidence_score": 0.0
      }
    arguments:
      - unread_messages
      - recent_messages
    context: [node_analysis_task]

  state_determination_task:
    agent: state_determiner
    description: |
      Determine the current state of the node execution.

      Detailed flow mapping:
      {active_flow_mapping}
    expected_output: |
      {
          "completion_status": "completed/pending/missing_info",
          "reason": "Detailed explanation for the status",
          "missing_elements": ["List of missing elements if any"],
          "next_steps": ["Recommended next steps"]
      }
    arguments:
      - active_flow_mapping
    context: [response_validation_task]

  tool_selection_task:
    agent: tool_decider
    description: |
      Analyze the context to decide if a tool is needed. If a tool is needed:
      - Determine the appropriate tool to use.
      - Construct a query for the selected tool.

      Tools:
      1. gather_issue_description: This function collects and validates customer contact number, sales order number and issue description.
      2. create_service_request: This function collects images, address, requested service date. If the requested service date is not provided by the customer, it suggests 5 earliest available date for booking. If all the information are collected, it create a service request in google calendar.
      3. reschedule_service_request: This function collects and validates customer info, and reschedule the service request that the customer made.

      Node information:
      {stage_node}

      User messages: 
      {recent_messages}

      Conversation ID:
      {conversation_id}

      Incoming message:
      {unread_messages}

      Contextual information:
      {context}
    expected_output: |
      {
          "selected_tool": "Name of the tool to use, or null if no tool is required",
          "constructed_query": "Query string for the selected tool, or null if no tool is required",
          "selection_reason": "Explanation of why the tool was selected or why no tool is needed"
      }
    arguments:
      - stage_node
      - recent_messages
      - conversation_id
      - unread_messages
      - context
    context: [node_analysis_task, state_determination_task]

  tool_execution_task:
    agent: tool_executor
    description: |
      Execute the tool selected by the Tool Selection and Query Specialist and retrieve the output.

      IMPORTANT: You must use the exact tool that was selected by the tool_selection_task.
      Do not switch to a different tool. The selected tool will be one of:
      - gather_issue_description: Used when some information is missing
      - create_service_request: Used when all required information is present
      - reschedule_service_request: Used for rescheduling existing appointments

      For tools, use these arguments from contextual information as needed:
      - customer_name: Customer's name
      - customer_contact_number: Customer's Singapore phone number
      - sales_order_number: 5-6 digit order identifier
      - appointment_date: Preferred date for service appointment. Must be in YYYY-MM-DD format.
      - product: The specific furniture product that requires servicing (e.g. sofa, dining table, bed frame, wardrobe, etc.). This must be a valid furniture item purchased from the company. Derive from issue description if it is not specifically mentioned.
      - issue_description: The customer's complaint and description of issues with the furniture product
      - is_address_confirmed: Whether service address is confirmed
      - customer_postal_code: Service location postal code
      - customer_block_number: Service location block number
      - customer_unit_and_floor_number: Service location unit/floor
      - customer_street_name: Service location street name
      - attachment_ids: Array of attachment id numbers found in ALL recent messages. Process steps:
         - Look for "attachments" field in EACH message object within recent_messages
         - Collect ALL attachment id numbers found across ALL messages
         - Return as an array of id numbers in chronological order
         - If no attachments found, return empty array
         Note: Make sure to scan through ALL messages in recent_messages, not just the latest one
      - how_many_times_images_were_asked: Number of times the bot has asked for images, default is 0
      - intent: The intent of the customer. It is either "create_appointment", "reschedule_appointment","query_appointment" or "general_inquiry". Default is "create_appointment"

      Guidelines for tool execution:
      1. Check which tool was selected by tool_selection_task
      2. Extract only the arguments needed for that specific tool
      3. Only use arguments that are explicitly provided - do not invent or hallucinate values
      4. Ensure the conversation_id matches the one provided in context
      5. Pass the extracted arguments to the selected tool according to its requirements
      6. Return the raw tool output without modification

      Node information:
      {stage_node}

      Conversation ID:
      {conversation_id}

      Contextual Information:
      {context}
    expected_output: |
      Return the output of the executed tool. Do not hallucinate or input your own feedback or opinion. The tools will determine what needs to be included in the response to the customer.
    arguments:
      - stage_node
      - conversation_id
      - context
    context: [tool_selection_task]
