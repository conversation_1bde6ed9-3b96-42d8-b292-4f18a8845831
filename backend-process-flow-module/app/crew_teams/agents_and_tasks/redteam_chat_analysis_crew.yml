crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  bot_response_analyzer:
    role: "Bot Response Analyzer"
    goal: "Analyze the bot's response and determine its alignment with the customer persona, intent, and conversation context. Identify response quality and provide recommendations."
    backstory: "An expert in analyzing chatbot responses for accuracy, relevance, and next steps in customer service conversations."
    model: "gpt-4o-mini"
    verbose: False

  decision_reviewer:
    role: "Decision Reviewer"
    goal: "Analyze the output from the Bot Response Analyzer and decide whether to continue the conversation or conclude. Provide detailed reasoning and suggestions for improvement."
    backstory: "A skilled reviewer focused on ensuring logical decision-making and a seamless conversation flow."
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the analysis from the Decision Reviewer using a predefined rubric to ensure the quality threshold is met."
    backstory: "An evaluation expert applying scoring metrics to maintain high standards in decision-making and analysis."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  analyze_bot_response:
    agent: bot_response_analyzer
    description: |
      Analyze the bot's response based on the provided customer persona, conversation context, and response content. Determine if the objective of the conversation has been achieved. If not, provide guidance on how the customer should respond next.
      Also, do note that if the customer service agent is asking for more information, the conversation should continue with an appropriate customer response.

      Your context and background:
      {customer_persona}

      Conversation Context:
      {chat_history}

      Bot Response:
      {bot_response}

      Output should provide:
      - Whether the objective is achieved (true/false)
      - If not achieved, provide guidance on how the customer should respond next, considering:
        * What information the customer should provide
        * How to maintain the customer's persona and tone
        * How to address any questions or requests from the agent
        * Keep responses focused on a single point/question

      Return in a JSON format.

    expected_output: |
      {
          "objective_achieved": "True/False",
          "follow_up_response": "Instructions on how the customer should formulate their next response to the agent, maintaining their persona and addressing the current conversation state."
      }
    arguments:
      - customer_persona
      - chat_history
      - bot_response
    json_output: BotResponseAnalysisModel

  review_decision:
    agent: decision_reviewer
    description: |
      Take the output of the Bot Response Analyzer and decide whether the conversation should continue or conclude. Make a clear distinction between proposals and service recommendations.

      Your context and background:
      {customer_persona}

      Conversation Context:
      {chat_history}

      Bot Response:
      {bot_response}

      **Response Analysis Rules:**
      1. For Proposals:
         - MUST explicitly mention "Proposal A" and "Proposal B"
         - Only conclude if both proposals are clearly presented
         - Having just one proposal is not sufficient to conclude

      2. For Service Recommendations:
         - If bot suggests services without formal proposals
         - Select services based on your budget and preferences
         - Continue conversation to request formal proposals
         - Service recommendations alone are NOT sufficient to conclude

      Output should include:
      - A decision to continue or conclude the conversation
      - Reasons supporting the decision
      - Next steps (e.g., select services, request proposals, etc.) and clear identification if response contains proposals or just recommendations under improvement_suggestions

    expected_output: |
      {
          "decision": "Continue/Conclude",
          "reasoning": "Detailed explanation of the decision",
          "improvement_suggestions": ["List of suggestions, if any"]
      }
    arguments:
      - customer_persona
      - chat_history
      - bot_response
    context: [analyze_bot_response]

  score_evaluation_task:
    agent: rubric_scorer
    description: |
      Evaluate the analysis from the Decision Reviewer using the predefined rubric. Score accuracy, detail, relevance, and realism, ensuring the total score exceeds the quality threshold.

      # **Rubric for Scoring the Analysis of Response and Decision to Continue the Conversation**

      ## **1. Accuracy (0-10)**
      **Definition**: Measures how well the analysis aligns with the bot response, customer persona, and intent.

      | **Score Range**         | **Description**                                                                                                            | **Example**                                                                                   |
      |--------------------------|----------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Below Standard)** | The analysis contains major errors or misinterpretations of the bot response or customer persona.                           | Decision to conclude the conversation when the bot response explicitly requires further clarification. |
      | **4-6 (Basic)**          | The analysis partially aligns but misses critical aspects or shows some misinterpretation of the inputs.                    | Decision to follow up, but the provided message doesn't align well with the customer's query intent. |
      | **7-8 (Good)**           | The analysis is mostly accurate, with minor errors or omissions.                                                           | Analysis correctly identifies the need for clarification but misses nuances in the customer's tone. |
      | **9-10 (Excellent)**     | The analysis is fully accurate, correctly identifying whether to continue or conclude and aligns perfectly with inputs.      | Decision to continue with a follow-up message that aligns perfectly with the bot's response and customer intent. |

      ---

      ## **2. Detail (0-10)**
      **Definition**: Assesses whether the analysis provides a thorough and descriptive evaluation of the bot response and the decision.

      | **Score Range**         | **Description**                                                                                                            | **Example**                                                                                   |
      |--------------------------|----------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Insufficient)**   | The analysis lacks depth and fails to describe key aspects of the bot response or customer context.                         | "The response is fine. Continue." without explaining why it’s fine or what could improve it. |
      | **4-6 (Basic)**          | The analysis touches on key aspects but lacks depth or omits some important details.                                        | "The bot response aligns with the intent but could be improved," without describing how.     |
      | **7-8 (Good)**           | The analysis is detailed, covering most aspects, with only minor areas needing more elaboration.                           | "The bot response aligns with the intent and addresses the query, but the tone could match the customer’s frustration better." |
      | **9-10 (Exceptional)**   | The analysis is highly detailed, describing all relevant aspects and providing a comprehensive evaluation.                  | "The bot response aligns perfectly with the intent. The tone is empathetic, and it answers the query directly. Recommend following up with additional clarification on the timeline." |

      ---

      ## **3. Relevance (0-10)**
      **Definition**: Evaluates whether the analysis addresses the context of the conversation and focuses on critical aspects related to the bot response and customer intent.

      | **Score Range**         | **Description**                                                                                                            | **Example**                                                                                   |
      |--------------------------|----------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Poor)**           | The analysis is off-topic or does not address the key aspects of the bot response or customer intent.                       | Discusses technical issues with the bot response instead of focusing on whether to continue or conclude. |
      | **4-6 (Moderate)**       | The analysis addresses some relevant aspects but misses key elements of the conversation context.                           | Identifies the need for a follow-up but ignores the customer’s emotional tone in the bot response. |
      | **7-8 (Strong)**         | The analysis effectively integrates most aspects of the conversation, with minor gaps in addressing the full context.        | Addresses customer intent and tone but does not fully consider the relevance of the follow-up question. |
      | **9-10 (Full)**          | The analysis seamlessly integrates all relevant aspects of the bot response and customer intent into the evaluation.         | Perfectly identifies the need for clarification in the bot response, aligning with the customer’s query and tone. |

      ---

      ## **4. Realism and Plausibility (0-10)**
      **Definition**: Assesses whether the analysis and decision are realistic and plausible given the bot response, customer persona, and conversation flow.

      | **Score Range**         | **Description**                                                                                                            | **Example**                                                                                   |
      |--------------------------|----------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
      | **0-3 (Unrealistic)**    | The analysis or decision is implausible or unrealistic in the context of the conversation.                                  | Recommends concluding the conversation despite an unresolved customer query or vague bot response. |
      | **4-6 (Basic Realism)**  | The analysis or decision is somewhat plausible but includes unrealistic or unlikely elements.                               | Recommends a follow-up, but the suggested response is irrelevant to the customer’s query.    |
      | **7-8 (Strong Realism)** | The analysis and decision are realistic, with minor elements that could be improved.                                        | Recommends continuing with a follow-up message that aligns well but could better address customer concerns. |
      | **9-10 (Fully Realistic)** | The analysis and decision are entirely plausible and reflect an in-depth understanding of customer behavior and context.    | Recommends continuing with a detailed follow-up that addresses the bot’s gaps and aligns perfectly with the customer’s query and emotional tone. |

      ---

      ## **Scoring System**
      1. **Accuracy (0-10)**: Correctness of the analysis in identifying whether to continue or conclude.
      2. **Detail (0-10)**: Depth of the analysis in explaining why the decision was made.
      3. **Relevance (0-10)**: Contextual alignment of the analysis with the conversation and decision-making process.
      4. **Realism (0-10)**: Plausibility of the analysis and decision in the context of customer behavior and conversation flow.

      ---
    expected_output: |
      {
          "accuracy": {
              "score": X,
              "rating": "Below Standard/Basic/Good/Excellent",
              "reason": "Explanation of accuracy scoring"
          },
          "detail": {
              "score": X,
              "rating": "Insufficient/Basic/Good/Exceptional",
              "reason": "Explanation of detail scoring"
          },
          "relevance": {
              "score": X,
              "rating": "Poor/Moderate/Strong/Full",
              "reason": "Explanation of relevance scoring"
          },
          "realism": {
              "score": X,
              "rating": "Unrealistic/Basic/Strong/Fully Realistic",
              "reason": "Explanation of realism scoring"
          }
      }
    context: [review_decision]
    json_output: ConversationAnalysisModel
