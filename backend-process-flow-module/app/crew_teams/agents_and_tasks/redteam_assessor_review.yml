crew_config:
  process: sequential
  verbose: True
  content_task_output: 2
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  feedback_analyzer:
    role: "Feedback Analyzer"
    goal: "Analyze the provided feedback to identify which tasks or agents need improvement."
    backstory: "A meticulous analyzer specialized in identifying areas for improvement based on feedback data."
    model: "gpt-4o-mini"
    verbose: False

  improvement_recommender:
    role: "Improvement Recommender"
    goal: "Provide actionable recommendations for improving the identified tasks or agents."
    backstory: "A constructive critic with expertise in designing effective agent and task configurations."
    model: "gpt-4o-mini"
    verbose: False

  team_consensus_maker:
    role: "Team Consensus Maker"
    goal: "Consolidate the findings and recommendations into a final decision highlighting the tasks or agents requiring modification."
    backstory: "An expert in synthesizing team inputs into actionable conclusions."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  analyze_feedback_task:
    agent: feedback_analyzer
    description: |
      Analyze the provided feedback to identify the tasks or agents that underperformed or failed to meet expectations.
      For the agent,
      - Backstory: This is the backstory of the agent.
      - Role: This is the role of the agent.
      - Goal: This is the goal of the agent.

      For the task,
      - Description: This is the description of the task.
      - Expected Output: This is the expected output of the task.
      - Raw: This is the raw output of the task.

      Feedback Data:
      {feedback_data}

      Crew Team Logs:
      {crewteam_logs}

      Previous Feedback if any:
      {previous_feedback}

    expected_output: |
      {
          "underperforming_tasks_agents": [
              {
                  "name": "Name of the task or agent (e.g., 'task_1' or 'agent_x')",
                  "yaml_path": "Path to the YAML file defining the task or agent (e.g., '/config/tasks.yaml')",
                  "reason": "Detailed explanation of why the task or agent underperformed."
              }
          ]
      }
    arguments:
      - feedback_data
      - crewteam_logs
      - previous_feedback

  recommend_improvements_task:
    agent: improvement_recommender
    description: |
      Based on the analysis of underperforming tasks or agents, provide actionable recommendations for improvement.

      Crew Team Logs:
      {crewteam_logs}

    expected_output: |
      {
          "improvement_recommendations": [
              {
                  "name": "Name of the task or agent (e.g., 'task_1' or 'agent_x')",
                  "recommendation": "Detailed and constructive feedback on how to improve the task or agent."
              }
          ]
      }
    context: [analyze_feedback_task]
    arguments:
      - crewteam_logs

  finalize_consensus_task:
    agent: team_consensus_maker
    description: |
      Consolidate the findings and recommendations into a final decision, highlighting tasks or agents requiring modifications.

      Crew Team Logs:
      {crewteam_logs}

    expected_output: |
      [
          {
              "name": "Name of the task or agent (e.g., 'task_1' or 'agent_x')",
              "_type": "task or agent",
              "yaml_path": "Path to the YAML file defining the task or agent (e.g., '/config/tasks.yaml')",
              "reason": "Reason for modification",
              "recommendation": ["3 actionable feedback to improve the task or agent."]
          }
      ]

    arguments:
      - crewteam_logs
    context: [analyze_feedback_task, recommend_improvements_task]
    json_output: AssessorRecommendations
