crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: False
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  prompt_generator:
    role: "Prompt Generator"
    goal: "Generate an optimized prompt based on feedback and recommendations"
    backstory: "An expert in prompt engineering who excels at incorporating feedback to create effective and clear prompts"
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Evaluate the quality of the analysis and proposed changes using a detailed rubric-based scoring system."
    backstory: "An expert in rubric-based evaluation for quality assurance and consistency."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  generate_recommended_prompt:
    agent: prompt_generator
    description: |
      Generate an updated prompt based on the original draft and provided recommendations.

      Original Draft:
      {original_draft}

      Recommendations:
      {recommendation_reasoning}
      {recommendation_impact}
      {recommendation_risk}
      {recommendation_expected}

      Ensure the generated prompt maintains clarity while incorporating all necessary improvements.

      For all the variables marked with "{{" and "}}", they are placeholders for variables that are passed in as arguments. You should not replace them.

    expected_output: |
      Output the generated prompt without any additional instructions, feedback, opinion or comment. JUST THE RECOMMENDED CONTENT THAT WILL BE USED TO REPLACE THE ORIGINAL PROMPT
    arguments:
      - original_draft
      - recommendation_reasoning
      - recommendation_impact
      - recommendation_risk
      - recommendation_expected

  evaluate_analysis_evaluation_task:
    agent: rubric_scorer
    description: |
      Score the analysis of the recommendations using a rubric with detailed scoring categories. Each category has a range of scores with descriptions and examples.

      **Rubric Categories**:
      1. **Responsiveness (0-10):** How well the analysis addresses the recommendations.
      2. **Clarity (0-10):** How clear and understandable the analysis is.
      3. **Effectiveness (0-10):** How impactful the analysis is for improving the prompt.

      **Scoring Guide**:
      - **Responsiveness**:
        - 0-3: Analysis ignores key recommendations. Example: Recommendations to simplify are ignored.
        - 4-6: Analysis partially addresses recommendations. Example: Simplification is noted but not fully explored.
        - 7-8: Analysis is mostly responsive. Example: Most simplifications are addressed but with limited insight.
        - 9-10: Analysis fully addresses all recommendations comprehensively.

      - **Clarity**:
        - 0-3: Analysis is vague or uses overly technical language. Example: "Adjust wording for tone" with no specifics.
        - 4-6: Analysis is mostly clear but has some ambiguous points. Example: "Make tone professional" without examples.
        - 7-8: Analysis is clear but lacks simplicity in explanations. Example: "Simplify tone to accessible language."
        - 9-10: Analysis is exceptionally clear and actionable. Example: "Change tone to accessible: Replace technical terms with layman examples."

      - **Effectiveness**:
        - 0-3: Proposed changes are unlikely to improve the result. Example: Recommending irrelevant updates.
        - 4-6: Proposed changes may have some impact. Example: Changes address surface-level issues.
        - 7-8: Proposed changes are likely to improve outcomes significantly. Example: Targeted changes to address user needs.
        - 9-10: Proposed changes are transformative and result in an optimized prompt. Example: Comprehensive changes that improve readability and outcomes.

        Return in a JSON format.

    expected_output: |
      {
          "responsiveness": {
              "score": X,
              "reason": "Why this score was given."
          },
          "clarity": {
              "score": X,
              "reason": "Why this score was given."
          },
          "effectiveness": {
              "score": X,
              "reason": "Why this score was given."
          }
      }
    context: [generate_recommended_prompt]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: EvaluateAnalysisTaskOutput
