crew_config:
  process: sequential
  verbose: True
  content_task_output: 0
  content_task_output_json_flag: True
  output_quality_threshold: 35

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  consolidation_agent:
    role: "Feedback Consolidation Specialist"
    goal: "Analyze and consolidate chunk-based recommendations into a cohesive final recommendation set"
    backstory: "An expert in synthesizing multiple pieces of feedback and identifying key patterns and priorities"
    model: "gpt-4o-mini"
    verbose: False

  recommendation_scorer:
    role: "Recommendation Quality Scorer"
    goal: "Evaluate and score the quality of consolidated recommendations"
    backstory: "A specialist in assessing the potential impact and feasibility of agent improvements"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  consolidate_recommendations:
    agent: consolidation_agent
    description: |
      Analyze the recommendations from all chunks and synthesize them into a final consolidated list.
      Look for patterns, overlapping suggestions, and prioritize the most impactful changes.

      Input:
      {chunk_task_output_list}

      Guidelines:
      1. Identify common themes across chunks
      2. Merge similar recommendations
      3. Prioritize based on frequency and impact
      4. Ensure recommendations are specific and actionable
      5. Remove duplicates while preserving context
      6. MUST AVOID using " and : within sentences/phrases to maintain valid JSON

      Return a consolidated analysis in JSON format.
    expected_output: |
      {
        "final_recommendations": [
          {
            "agent_name": "<agent name>",
            "explanation": ["<detailed explanation of why changes are needed>"],
            "proposed_changes": ["<specific recommendations for improvements>"],
            "impact_assessment": ["<expected impact of changes>"]
          }
        ]
      }
    arguments:
      - chunk_task_output_list
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: FinalAgentSelectionModel

  recommendation_scoring:
    agent: recommendation_scorer
    description: |
      Score the consolidated recommendations based on clarity, impact, feasibility, evidence, and risk.

      Scoring Rubric:
      Each category is scored from 0 to 10:
      - Clarity: How clear and specific are the recommendations
      - Impact: Potential positive effect on agent performance
      - Feasibility: Ease of implementation
      - Evidence: Supporting data or reasoning
      - Risk: Potential negative side effects

      Return a detailed scoring analysis in JSON format.
    expected_output: |
      {
        "clarity": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score"
        },
        "impact": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score"
        },
        "feasibility": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score"
        },
        "evidence": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score"
        },
        "risk": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score"
        }
      }
    context: [consolidate_recommendations]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RecommendationScoringModel
