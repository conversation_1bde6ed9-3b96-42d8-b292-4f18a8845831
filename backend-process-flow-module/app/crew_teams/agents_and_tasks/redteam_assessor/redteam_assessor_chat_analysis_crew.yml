crew_config:
  process: sequential
  verbose: True
  content_task_output: 7
  content_task_output_json_flag: True
  output_quality_threshold: 20

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  conversation_summarizer:
    role: "Conversation Summarizer"
    goal: "Generate a concise summary of the conversation, highlighting key points and sentiment."
    backstory: "A specialist skilled in summarizing conversation details and extracting sentiment analysis."
    model: "gpt-4o-mini"
    verbose: False

  objective_evaluator:
    role: "Objective Evaluation Agent"
    goal: "Evaluate whether the conversation objective was met and provide reasoning if it failed"
    backstory: "An expert in evaluating conversation outcomes and providing actionable insights for improvement"
    model: "gpt-4o-mini"
    verbose: False

  answer_quality_evaluator:
    role: "Answer Quality Evaluation Agent"
    goal: "Evaluate the accuracy, clarity, empathy, and relevance of chatbot responses"
    backstory: "An expert in assessing the correctness and completeness of information provided in responses"
    model: "gpt-4o-mini"
    verbose: False

  tone_analyzer:
    role: "Tone Analysis Agent"
    goal: "Evaluate the tone of the conversation and provide insights on tone alignment"
    backstory: "An expert in analyzing the tone of conversations and providing actionable insights for improvement"
    model: "gpt-4o-mini"
    verbose: False

  customer_satisfaction_analyst:
    role: "Customer Satisfaction Analyst"
    goal: "Evaluate customer satisfaction levels and provide actionable insights for improvement"
    backstory: "An expert in customer experience analysis with deep understanding of satisfaction metrics and improvement strategies"
    model: "gpt-4o-mini"
    verbose: False

  flow_analyzer:
    role: "Message Flow Analyzer"
    goal: "Analyze conversation flow patterns and identify bottlenecks and their root causes"
    backstory: "An expert in conversation flow analysis, specializing in identifying patterns, bottlenecks, and optimization opportunities in chat interactions"
    model: "gpt-4o-mini"
    verbose: False

  improvement_recommender:
    role: "Improvement Recommendation Specialist"
    goal: "Extract and prioritize areas for improvement into actionable insights"
    backstory: "A strategic improvement specialist with expertise in analyzing conversation data and creating prioritized action plans"
    model: "gpt-4o-mini"
    verbose: False

  prompt_optimization_specialist:
    role: "Prompt Optimization Specialist"
    goal: "Analyze and optimize agent prompts for improved conversation quality and task effectiveness"
    backstory: "An expert in prompt engineering and conversational AI optimization, specializing in improving agent interactions and response quality"
    model: "gpt-4o-mini"
    verbose: False

  output_quality_analyzer:
    role: "Output Quality Evaluation Agent"
    goal: "Analyze the overall quality of outputs provided by the crew team and score them against a defined rubric metric."
    backstory: "An expert in quality evaluation, specializing in assessing task outputs and providing actionable feedback for improvement."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  conversation_summary_task:
    agent: "conversation_summarizer"
    description: |
      Given the conversation summary field and the raw conversation transcript, generate a cleaned and detailed summary.
      The task is to extract the most important information and sentiments from the conversation, providing a clear and concise summary that captures the essence of the conversation.

      Conversation transcript:
      {conversation_transcript}

      Previous Feedback if any:
      {previous_feedback}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
          "conversation_summary": "<Summary of the conversation>"
      }
    arguments:
      - conversation_transcript
      - previous_feedback
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: ConversationSummaryModel

  evaluate_objective:
    agent: objective_evaluator
    description: |
      Analyze whether the conversation objective was met based on the context and interactions. 
      If the objective was not met, provide clear reasoning about why it failed and recommendations for improvement.

      Conversation Summary:
      < Provided by conversation_summary_task task >

      Conversation transcript:
      {conversation_transcript}

      Customer objectives:
      {customer_objectives}

      Previous Feedback if any:
      {previous_feedback}

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "objective_met": bool,  # True if the conversation objective was met, False otherwise
          "objective_reasoning": str  # Detailed explanation as to why you have determined the objective was met/ not met
      }
    arguments:
      - conversation_transcript
      - customer_objectives
      - previous_feedback
    context: [conversation_summary_task]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: ObjectiveEvaluationModel

  evaluate_quality:
    agent: answer_quality_evaluator
    description: |
      Evaluate the accuracy, clarity, empathy, and relevance of the chatbot responses using the defined rubrics.

      Conversation Summary:
      < Provided by conversation_summary_task task >

      Conversation objective's evaluation:
      < Provided by evaluate_objective task >

      Conversation transcript:
      {conversation_transcript}

      Previous Feedback if any:
      {previous_feedback}

      ## 1. Accuracy of Answers
      - **0-2**: Completely inaccurate or misleading information.
        - **Example**: "Provided entirely incorrect billing information, leading to customer confusion."
      - **3-5**: Partially accurate but with significant errors or omissions.
        - **Example**: "Explained some aspects of the billing process correctly but missed crucial details about payment dates."
      - **6-8**: Mostly accurate with minor inaccuracies or omissions.
        - **Example**: "Correctly explained the billing cycle but slightly misstated the late payment fee amount."
      - **9-10**: Fully accurate and comprehensive information.
        - **Example**: "Provided precise and complete information about the billing process, including all relevant dates and fees."

      ## 2. Clarity of Answers
      - **0-2**: Completely unclear or confusing response.
        - **Example**: "Used technical jargon and complex sentences, making the explanation incomprehensible to the customer."
      - **3-5**: Partially clear but with significant ambiguities or complexities.
        - **Example**: "Explained the process in a roundabout way, requiring multiple clarifications from the customer."
      - **6-8**: Mostly clear with minor areas for improvement in expression or structure.
        - **Example**: "Provided a clear explanation but used one or two terms that might be unfamiliar to some customers."
      - **9-10**: Exceptionally clear, concise, and easy to understand.
        - **Example**: "Explained the process in simple, straightforward language that any customer could easily follow."

      ## 3. Empathy of Answers
      - **0-2**: Complete lack of empathy or understanding of customer's situation.
        - **Example**: "Dismissed the customer's concerns about a billing error without acknowledging their frustration."
      - **3-5**: Limited empathy with superficial acknowledgment of customer's feelings.
        - **Example**: "Briefly said 'I understand' without demonstrating true comprehension of the customer's situation."
      - **6-8**: Good empathy with genuine understanding, but room for deeper connection.
        - **Example**: "Acknowledged the customer's frustration and offered help, but didn't fully explore the emotional impact."
      - **9-10**: Exceptional empathy with full understanding and validation of customer's emotions.
        - **Example**: "Fully acknowledged the customer's frustration, validated their feelings, and showed genuine concern in resolving the issue."

      ## 4. Relevance of Answers
      - **0-2**: Completely irrelevant or off-topic response.
        - **Example**: "Responded about product features when the customer asked about billing issues."
      - **3-5**: Partially relevant but missing key aspects of the query.
        - **Example**: "Addressed the billing cycle when the customer specifically asked about payment methods."
      - **6-8**: Mostly relevant with minor tangential information.
        - **Example**: "Correctly answered the query about payment methods but included unnecessary details about the company's history."
      - **9-10**: Fully relevant and directly addresses the customer's query.
        - **Example**: "Provided a focused response that directly and completely answered the customer's specific question about payment methods."

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Generate a valid JSON object with all necessary closing braces.

    expected_output: |
      {
          "accuracy": {
              "score": X,
              "rating": "Below Standard/Basic/Good/Excellent",
              "reason": "Explanation of accuracy scoring"
          },
          "clarity": {
              "score": X,
              "rating": "Unclear/Moderate/Clear/Very Clear",
              "reason": "Explanation of clarity scoring"
          },
          "empathy": {
              "score": X,
              "rating": "Lacking/Moderate/Empathetic/Highly Empathetic",
              "reason": "Explanation of empathy scoring"
          },
          "relevance": {
              "score": X,
              "rating": "Poor/Moderate/Strong/Full",
              "reason": "Explanation of relevance scoring"
          }
      }

    arguments:
      - conversation_transcript
      - previous_feedback
    context: [conversation_summary_task, evaluate_objective]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: QualityEvaluationModel

  tone_analysis_task:
    agent: tone_analyzer
    description: |
      Evaluate whether the conversation tone was appropriate and consistent with the agent expected tone (if any),
      analyzing the tone_appropriateness and supporting reasoning to provide insights on tone alignment.

      Conversation Summary:
      < Provided by conversation_summary_task task >

      Conversation transcript:
      {conversation_transcript}

      Agent Expected Tone:
      {bot_setting}

      Previous Feedback if any:
      {previous_feedback}

      # Tone Appropriateness Rubric

      ## Metric: Appropriateness Score (1-10)

      ### **1-2: Highly Misaligned**
      - **Description**: The tone is completely inappropriate for the context. It disregards the audience, is unprofessional, or creates a negative impression.
      - **Example**: 
        - Context: Formal business discussion.
        - Tone: Overly casual or rude, e.g., "Whatever, it's not my problem."
        - Context: Customer support.
        - Tone: Dismissive, e.g., "Why don't you figure it out yourself?"

      ### **3-4: Partially Misaligned**
      - **Description**: The tone is somewhat inappropriate or inconsistent. It might fail to acknowledge the audience's needs or expectations fully.
      - **Example**:
        - Context: Apology letter.
        - Tone: Vague and lacking empathy, e.g., "Sorry if you feel that way."
        - Context: Motivational meeting.
        - Tone: Monotone and uninspiring, e.g., "I guess we should try harder."

      ### **5-6: Neutral**
      - **Description**: The tone is passable but not well-tailored to the context. It neither detracts significantly nor adds value to the communication.
      - **Example**:
        - Context: Performance review.
        - Tone: Generic and detached, e.g., "You did okay. Keep it up."
        - Context: Friendly email.
        - Tone: Minimal engagement, e.g., "Thanks for your email. Noted."

      ### **7-8: Aligned**
      - **Description**: The tone is appropriate and consistent with the context. It meets the audience's expectations and effectively conveys the intended message.
      - **Example**:
        - Context: Business pitch.
        - Tone: Professional and confident, e.g., "Our proposal offers significant benefits for your team."
        - Context: Support chat.
        - Tone: Polite and helpful, e.g., "I'd be happy to assist you with this issue."

      ### **9-10: Highly Aligned**
      - **Description**: The tone is exemplary, perfectly matched to the context and audience. It enhances the message, leaving a strong positive impression.
      - **Example**:
        - Context: Crisis communication.
        - Tone: Reassuring and empathetic, e.g., "We deeply regret the inconvenience caused and are taking immediate steps to resolve this."
        - Context: Inspirational speech.
        - Tone: Enthusiastic and motivational, e.g., "Together, we can achieve greatness. Let’s make it happen!"

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "tone_evaluation": {
          "score": "1-10",
          "rating": "Aligned/Partially Aligned/Misaligned",
          "reason": "Explanation of tone scoring"
        }
      }

    context: [evaluate_quality]
    arguments:
      - conversation_transcript
      - bot_setting
      - previous_feedback
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: ToneEvaluationModel

  customer_satisfaction_rating:
    agent: customer_satisfaction_analyst
    description: |
      Analyze the customer satisfaction levels based on provided ratings and reasoning,
      delivering comprehensive assessment and actionable recommendations for improvement.

      Conversation Summary:
      < Provided by conversation_summary_task task >

      Conversation objective's evaluation:
      < Provided by evaluate_objective task >

      Conversation transcript:
      {conversation_transcript}

      Previous Feedback if any:
      {previous_feedback}

      # Customer Satisfaction Rating Rubric

      ## Metric: Satisfaction Score (1-10)

      ### **1-2: Very Dissatisfied**
      - **Satisfaction Level**: Very Dissatisfied
      - **Description**: Customer experience is extremely negative. Expectations are unmet, with significant issues causing frustration or dissatisfaction.
      - **Example Factors**: 
        - Long response times.
        - Poor product quality or service delivery.
        - Unresolved complaints.
      - **Recommendations**:
        - Address specific complaints urgently.
        - Improve communication and response times.
        - Reevaluate product/service quality.

      ### **3-4: Dissatisfied**
      - **Satisfaction Level**: Dissatisfied
      - **Description**: Customer experience is below expectations, with noticeable gaps in service or product delivery.
      - **Example Factors**:
        - Delays in service delivery.
        - Unhelpful support interactions.
        - Minor but unresolved product defects.
      - **Recommendations**:
        - Conduct follow-ups to resolve issues.
        - Provide better training to support staff.
        - Refine processes to prevent recurring issues.

      ### **5-6: Neutral**
      - **Satisfaction Level**: Neutral
      - **Description**: Customer experience is adequate but lacks impact. The service meets minimum standards without exceeding expectations.
      - **Example Factors**:
        - Standard product or service with no unique value.
        - Limited engagement from the support team.
      - **Recommendations**:
        - Identify opportunities to enhance the customer experience.
        - Personalize interactions to increase engagement.
        - Introduce value-added services or benefits.

      ### **7-8: Satisfied**
      - **Satisfaction Level**: Satisfied
      - **Description**: Customer experience meets or slightly exceeds expectations. Most aspects are positive, with minor areas for improvement.
      - **Example Factors**:
        - Timely service with professional interactions.
        - Product/service fulfills its purpose effectively.
      - **Recommendations**:
        - Maintain service quality while addressing minor feedback.
        - Encourage feedback for continuous improvement.
        - Reward loyalty with customer-centric initiatives.

      ### **9-10: Very Satisfied**
      - **Satisfaction Level**: Very Satisfied
      - **Description**: Customer experience is outstanding, exceeding expectations in every area. Customers are likely to return and recommend.
      - **Example Factors**:
        - Exceptional product quality and service delivery.
        - Personalized and proactive support.
        - Memorable and positive interactions.
      - **Recommendations**:
        - Leverage satisfied customers for testimonials or referrals.
        - Continue delivering exceptional service.
        - Explore innovative ways to further enhance the experience.

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "satisfaction_metrics": {
          "satisfaction_score": "1-10",
          "satisfaction_level": "Very Satisfied/Satisfied/Neutral/Dissatisfied/Very Dissatisfied",
          "key_factors": ["List of main factors affecting satisfaction"],
          "improvement_recommendations": ["List of specific recommendations"]
        }
      }

    context: [tone_analysis_task, evaluate_objective]
    arguments:
      - conversation_transcript
      - previous_feedback
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: CustomerSatisfactionModel

  flow_analysis_task:
    agent: flow_analyzer
    description: |
      Analyze the conversation flow to identify bottlenecks, repeated patterns, and root causes of issues.
      Focus on nodes where conversation got stuck and determine if issues stem from unclear customer questions
      or misidentified bot flow intentions.

      Conversation Summary:
      < Provided by conversation_summary_task task >

      Conversation transcript with Node Selector:
      {conversation_for_flow_analysis}

      Previous Feedback if any:
      {previous_feedback}

      # Flow Analysis Rubric

      ## Bottleneck Severity Levels

      ### High Impact
      - Completely blocks conversation progress
      - Requires multiple attempts to proceed
      - Significantly affects user experience
      - Example: User repeats same question 3+ times due to misunderstanding

      ### Medium Impact
      - Temporarily impedes conversation flow
      - Requires clarification but eventually resolves
      - Moderately affects user experience
      - Example: Need for question reformulation once or twice

      ### Low Impact
      - Minor flow disruption
      - Quickly resolved with minimal intervention
      - Minimal impact on user experience
      - Example: Simple clarification request

      ## Analysis Focus Areas
      1. Node Repetition Patterns
      2. User Question Clarity
      3. Bot Intent Recognition Accuracy
      4. Conversation Progress Blocks

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "flow_metrics": {
          "flow_efficiency_score": "1-10",
          "primary_issues": ["List of main flow issues"],
          "improvement_suggestions": ["List of recommendations"]
        }
      }

    context: [conversation_summary_task]
    arguments:
      - conversation_for_flow_analysis
      - previous_feedback
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: FlowAnalysisModel

  improvement_plan_task:
    agent: improvement_recommender
    description: |
      Analyze areas for improvement from previous analyses and create a prioritized action plan
      with concrete, actionable recommendations.

      Previous Analysis Results:
      < Provided by evaluate_objective, evaluate_quality, tone_analysis_task, customer_satisfaction_rating, and flow_analysis_task >

      Previous Feedback if any:
      {previous_feedback}

      # Improvement Prioritization Rubric

      ## Priority Levels

      ### High Priority
      - **Criteria**:
        - Severely impacts user experience or business goals
        - Requires immediate attention
        - Significant ROI expected
      - **Timeline**: Implement within 1-2 weeks
      - **Example**:
        - Critical flow bottlenecks causing user abandonment
        - Serious tone misalignment in sensitive situations
        - Consistently low customer satisfaction scores

      ### Medium Priority
      - **Criteria**:
        - Moderately impacts user experience
        - Important but not urgent
        - Good ROI potential
      - **Timeline**: Implement within 1-2 months
      - **Example**:
        - Occasional flow inefficiencies
        - Minor tone inconsistencies
        - Specific feature improvements

      ### Low Priority
      - **Criteria**:
        - Minor impact on user experience
        - Nice-to-have improvements
        - Limited immediate ROI
      - **Timeline**: Implement within 3-6 months
      - **Example**:
        - Cosmetic enhancements
        - Optional feature additions
        - Minor efficiency improvements

      ## Implementation Difficulty Levels
      - **Easy**: Can be implemented with existing resources in < 1 week
      - **Medium**: Requires moderate resources/planning, 1-4 weeks
      - **Hard**: Significant resources/planning needed, > 1 month

      RETURN THE SUMMARY IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "improvement_metrics": {
          "prioritized_improvements": [
            {
              "priority_level": "High/Medium/Low",
              "improvements": [
                {
                  "action": "string",
                  "expected_impact": "string",
                  "implementation_difficulty": "Easy/Medium/Hard",
                  "timeline": "string"
                }
              ],
              "rationale": "string"
            }
          ],
          "critical_areas": ["List of most critical areas"],
          "quick_wins": ["List of easy, high-impact improvements"],
          "long_term_goals": ["List of strategic improvements"]
        }
      }

    context:
      [
        evaluate_objective,
        evaluate_quality,
        tone_analysis_task,
        customer_satisfaction_rating,
        flow_analysis_task,
      ]
    arguments:
      - previous_feedback
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: ImprovementPlanModel

  # bot_prompt_recommendation_task:
  #   agent: prompt_optimization_specialist
  #   description: |
  #     Analyze agent prompts and responses based on improvement plan insights.
  #     Recommend specific prompt optimizations for enhanced performance.
  #     Focus on response quality, prompt effectiveness, and system integration.

  #     Previous Analysis: < Provided by improvement_plan_task >

  #     Agent Prompts:
  #     {agent_prompts}

  #     Previous Feedback if any:
  #     {previous_feedback}

  #     Evaluate prompts against:
  #     1. Response Quality (accuracy, consistency, alignment)
  #     2. Prompt Effectiveness (clarity, specificity, edge case handling)
  #     3. System Integration (coordination, flow optimization, information sharing)

  #     Categorize recommendations as:
  #     - Critical Changes: High-impact fixes for recurring issues
  #     - Enhancement Opportunities: Quality and efficiency improvements
  #     - System-Level Improvements: Multi-agent optimizations

  #     RETURN ONLY A VALID JSON COMPATIBLE WITH PYTHON:
  #     - Use double quotes for all strings
  #     - Ensure proper syntax and formatting

  #   expected_output: |
  #     {
  #       "agent_recommendations": [
  #         {
  #           "agent_name": "string",
  #           "task_name": "string",
  #           "current_performance": "string",
  #           "prompt_changes": [
  #             {
  #               "current_prompt": "string",
  #               "suggested_prompt": "string",
  #               "rationale": "string",
  #               "expected_improvements": ["string"],
  #               "potential_risks": ["string"]
  #             }
  #           ]
  #         }
  #       ]
  #     }

  #   context: [improvement_plan_task]
  #   arguments:
  #     - agent_prompts
  #     - previous_feedback
  #   guardrails:
  #     - validate_pydantic_output
  #     - validate_json_output
  #   max_retries: 5
  #   output_pydantic: PromptRecommendationMetrics

  output_quality_analysis_evaluation_task:
    agent: output_quality_analyzer
    description: |
      Analyze the quality of outputs provided by the crew team across all tasks, evaluating them against a rubric metric for effectiveness, consistency, and impact. 

      The task will:
      - Assess the quality of recommendations and outputs from all tasks.
      - Score the crew team's performance based on defined criteria.
      - Provide actionable feedback for improving task outputs and team performance.

      Previous Feedback if any:
      {previous_feedback}

      ## Quality Rubric Metrics

      ### 1. Completeness
      - **Metric**: Evaluate whether the output fully addresses the task requirements and provides sufficient detail.
      - **Scoring**:
        | **Score** | **Description**                                     |
        |-----------|-----------------------------------------------------|
        | 1-3       | Incomplete: Significant gaps or missing elements.   |
        | 4-6       | Partially Complete: Addresses some requirements.    |
        | 7-9       | Mostly Complete: Minor gaps, mostly sufficient.     |
        | 10        | Fully Complete: Thorough, with no missing details.  |

      ### 2. Accuracy
      - **Metric**: Assess how correct and precise the output is in meeting the task's goals.
      - **Scoring**:
        | **Score** | **Description**                                     |
        |-----------|-----------------------------------------------------|
        | 1-3       | Inaccurate: Contains significant errors or flaws.   |
        | 4-6       | Moderately Accurate: Some errors, mostly correct.   |
        | 7-9       | Highly Accurate: Minimal errors, well-validated.    |
        | 10        | Perfect Accuracy: Fully correct and precise.        |

      ### 3. Clarity
      - **Metric**: Determine if the output is easy to understand, concise, and well-structured.
      - **Scoring**:
        | **Score** | **Description**                                     |
        |-----------|-----------------------------------------------------|
        | 1-3       | Unclear: Hard to understand, lacks structure.       |
        | 4-6       | Moderately Clear: Requires interpretation.          |
        | 7-9       | Highly Clear: Easy to follow, minor adjustments.    |
        | 10        | Exceptionally Clear: Fully structured and concise.  |

      ### 4. Relevance
      - **Metric**: Measure how well the output aligns with the task's objectives and goals.
      - **Scoring**:
        | **Score** | **Description**                                     |
        |-----------|-----------------------------------------------------|
        | 1-3       | Poorly Relevant: Tangential or off-topic.           |
        | 4-6       | Partially Relevant: Partially meets objectives.     |
        | 7-9       | Highly Relevant: Closely aligns with objectives.    |
        | 10        | Perfectly Relevant: Fully addresses objectives.     |

      ### 5. Impact
      - **Metric**: Evaluate the practical value and potential effectiveness of the output in improving system performance or user experience.
      - **Scoring**:
        | **Score** | **Description**                                     |
        |-----------|-----------------------------------------------------|
        | 1-3       | Low Impact: Minimal usefulness or effect.           |
        | 4-6       | Moderate Impact: Somewhat useful but limited.       |
        | 7-9       | High Impact: Valuable and actionable.               |
        | 10        | Exceptional Impact: Highly effective and valuable.  |

      RETURN THE ANALYSIS IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
        "completeness_score": {
          "score": X,
          "rating": "incomplete/partially_complete/mostly_complete/fully_complete",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in completeness."
        },
        "accuracy_score": {
          "score": X,
          "rating": "inaccurate/moderately_accurate/highly_accurate/perfect_accuracy",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in accuracy."
        },
        "clarity_score": {
          "score": X,
          "rating": "unclear/moderately_clear/highly_clear/exceptionally_clear",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in clarity."
        },
        "relevance_score": {
          "score": X,
          "rating": "poorly_relevant/partially_relevant/highly_relevant/perfectly_relevant",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in relevance."
        },
        "impact_score": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in impact."
        }
      }
    context:
      [
        evaluate_quality,
        tone_analysis_task,
        customer_satisfaction_rating,
        flow_analysis_task,
        improvement_plan_task,
      ]
    arguments:
      - previous_feedback
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: OutputQualityAnalysisModel
