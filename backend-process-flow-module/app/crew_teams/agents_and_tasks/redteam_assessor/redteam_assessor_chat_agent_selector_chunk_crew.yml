crew_config:
  process: sequential
  verbose: True
  content_task_output: 2
  content_task_output_json_flag: True
  output_quality_threshold: 35

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  answer_quality_analyzer:
    role: "Answer Quality Analysis Agent"
    goal: "Analyze the quality, tone alignment, consistency of agent responses and identify areas for improvement"
    backstory: "An expert in evaluating response quality, analyzing communication tone and providing detailed recommendations for enhancement"
    model: "gpt-4o-mini"
    verbose: False

  flow_analyzer:
    role: "Flow Analysis Agent"
    goal: "Analyze conversation flow patterns and identify optimization opportunities"
    backstory: "A specialist in conversation flow analysis and improvement strategies"
    model: "gpt-4o-mini"
    verbose: False

  agent_selector:
    role: "Agent Selection Specialist"
    goal: "Analyze and consolidate recommendations to determine final agent updates"
    backstory: "An expert in evaluating and prioritizing agent improvements"
    model: "gpt-4o-mini"
    verbose: False

  recommendation_scorer:
    role: "Recommendation Quality Scorer"
    goal: "Evaluate and score the quality of agent update recommendations"
    backstory: "A specialist in assessing the potential impact and feasibility of agent improvements"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  quality_tone_analysis:
    agent: answer_quality_analyzer
    description: |
      Analyze the agent prompt and responses based on answer quality, tone analysis, and prompt optimization criteria.
      Identify which agents need changes and provide detailed explanations.

      ## ANALYSIS INPUTS:
      Agent prompt:
      {agent_prompt}

      Answer quality analysis results:
      {answer_quality}

      Tone analysis results:
      {tone_analysis}

      Return a detailed analysis in JSON format.
    expected_output: |
      {
        "agent_recommendations": [
          {
            "agent_name": "<agent name>",
            "explanation": ["<detailed explanation of why changes are needed>"],
            "proposed_changes": ["<specific recommendations for improvements>"]
          }
        ]
      }
    arguments:
      - agent_prompt
      - answer_quality
      - tone_analysis

  flow_optimization_analysis:
    agent: flow_analyzer
    description: |
      Analyze the agent prompt based on flow analysis and prompt optimization criteria.
      Identify which agents need changes and provide detailed explanations.

      ## ANALYSIS INPUTS:
      Agent prompt:
      {agent_prompt}

      Flow analysis results:
      {flow_analysis}

      Return a detailed analysis in JSON format.
    expected_output: |
      {
        "agent_recommendations": [
          {
            "agent_name": "<agent name>",
            "explanation": ["<detailed explanation of why changes are needed>"],
            "proposed_changes": ["<specific recommendations for improvements>"]
          }
        ]
      }
    arguments:
      - agent_prompt
      - flow_analysis

  final_agent_selection:
    agent: agent_selector
    description: |
      Analyze the recommendations from both analyses and determine the final list of agents
      that need updates, including detailed explanations and proposed changes.

      Input:
      - Quality and tone analysis recommendations (Provided by quality_tone_analysis task)
      - Flow optimization recommendations (Provided by flow_optimization_analysis task)

      Return a consolidated analysis in JSON format.
      - The proposed changes should be specific and actionable. It should be in a bullet point format.
      - MUST AVOID using " and : at all costs within the sentences/phrases, as they break the JSON format.
    expected_output: |
      {
        "final_recommendations": [
          {
            "agent_name": "<agent name>",
            "explanation": ["<detailed explanation of why changes are needed>"],
            "proposed_changes": ["<specific recommendations for improvements>"],
            "impact_assessment": ["<expected impact of changes>"]
          }
        ]
      }
    context: [quality_tone_analysis, flow_optimization_analysis]
    output_pydantic: FinalAgentSelectionModel

  recommendation_scoring:
    agent: recommendation_scorer
    description: |
      Score the final recommendations (Provided by final_agent_selection task) based on a defined rubric to determine if they should proceed.

      Scoring Rubric:
      # Rubric Metric for Scoring Recommendation Quality

      Each category is scored from **0 to 10**, with detailed descriptions and examples for each range.

      ## 1. Clarity
      - **0-2**: Vague and ambiguous. The recommendation lacks specificity, and its intent is unclear.
        - **Example**: "The prompt should be more clear."
      - **3-5**: Somewhat clear, but key details are missing or the language is hard to interpret.
        - **Example**: "Refine the prompt to address common user issues."
      - **6-8**: Mostly clear and specific, but minor ambiguities remain.
        - **Example**: "Add examples to the prompt to clarify the expected user inputs."
      - **9-10**: Exceptionally clear, concise, and unambiguous. Provides actionable and detailed instructions.
        - **Example**: "Update the prompt to include: 'Provide a summary of input using bullet points and examples of actionable steps.'"

      ## 2. Impact
      - **0-2**: Minimal or no measurable positive impact. Changes appear trivial or irrelevant.
        - **Example**: "Change the font of the prompt text."
      - **3-5**: Some potential for improvement but lacks substantial benefit to performance.
        - **Example**: "Slightly reword the prompt for better readability."
      - **6-8**: Significant positive impact on agent performance or user experience.
        - **Example**: "Add validation steps in the prompt to ensure alignment with goals."
      - **9-10**: Transformational impact. Resolves critical issues and greatly enhances functionality.
        - **Example**: "Introduce conditional logic in the prompt to handle edge cases and improve accuracy."

      ## 3. Feasibility
      - **0-2**: Not feasible or highly impractical due to complexity or technical limitations.
        - **Example**: "Rewrite the entire backend to support this change."
      - **3-5**: Feasible but requires significant effort or resources to implement.
        - **Example**: "Redesign the prompt generation framework to allow for real-time updates."
      - **6-8**: Reasonably practical with moderate effort and resources.
        - **Example**: "Incorporate predefined templates into the prompts for consistency."
      - **9-10**: Highly practical and easy to implement with minimal effort or resources.
        - **Example**: "Add a single validation question to the prompt."

      ## 4. Evidence
      - **0-2**: No supporting evidence or rationale provided.
        - **Example**: "This change seems necessary."
      - **3-5**: Weak evidence or rationale. The justification is vague or partially relevant.
        - **Example**: "Users might benefit from clearer prompts."
      - **6-8**: Strong evidence, with relevant examples or data supporting the recommendation.
        - **Example**: "Testing showed that 60% of responses improved with clearer instructions."
      - **9-10**: Robust evidence, including detailed analysis or empirical data supporting the change.
        - **Example**: "A/B testing demonstrated a 90% improvement in user satisfaction with this adjustment."

      ## 5. Risk
      - **0-2**: High risk of significant negative side effects or unintended consequences.
        - **Example**: "Completely rewrite the prompt, potentially breaking other integrations."
      - **3-5**: Moderate risk with some potential for negative outcomes.
        - **Example**: "Refactor the prompt structure, which might confuse users initially."
      - **6-8**: Low risk with minimal potential for side effects.
        - **Example**: "Refine wording in the prompt without changing the logic."
      - **9-10**: Very low or no risk. The change is safe and straightforward.
        - **Example**: "Add a clarifying statement to the existing prompt."

      Return a detailed scoring analysis in JSON format.
    expected_output: |
      {
        "clarity": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in clarity."
        },
        "impact": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in impact."
        },
        "feasibility": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in feasibility."
        },
        "evidence": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in evidence."
        },
        "risk": {
          "score": X,
          "rating": "low_impact/moderate_impact/high_impact/exceptional_impact",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in risk."
        }
      }
    context: [final_agent_selection]
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RecommendationScoringModel
