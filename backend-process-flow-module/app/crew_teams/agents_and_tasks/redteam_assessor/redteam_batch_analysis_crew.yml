crew_config:
  process: sequential
  verbose: False
  content_task_output: 0
  content_task_output_json_flag: True
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  improvement_analyzer:
    role: "Improvement Analyzer"
    goal: "Analyze all areas of improvements and identify the top 3 most impactful improvements needed"
    backstory: "An expert in analyzing customer service interactions and prioritizing areas for improvement based on impact and feasibility"
    model: "gpt-4o-mini"
    verbose: False

  issue_pattern_analyzer:
    role: "Issue Pattern Analyzer"
    goal: "Analyze all areas of improvements to identify the top 3 most frequently occurring issues"
    backstory: "A data-driven analyst specializing in identifying patterns and recurring issues in customer service interactions"
    model: "gpt-4o-mini"
    verbose: False

  knowledge_gap_analyzer:
    role: "Knowledge Gap Analyzer"
    goal: "Analyze agent reasonings to identify potential knowledge gaps in understanding customer requests"
    backstory: "An expert in identifying communication gaps and areas where customer service agents may need additional training or support"
    model: "gpt-4o-mini"
    verbose: False

  satisfaction_scorer:
    role: "Satisfaction Scorer"
    goal: "Analyze all inputs and previous analyses to provide an overall satisfaction score and reasoning"
    backstory: "A holistic evaluator who synthesizes multiple data points to assess overall customer service quality"
    model: "gpt-4o-mini"
    verbose: False

  quality_assessor:
    role: "Quality Assessor"
    goal: "Evaluate the quality and reliability of all task outputs using predefined metrics"
    backstory: "A meticulous reviewer ensuring the accuracy and thoroughness of all analyses"
    model: "gpt-4o-mini"
    verbose: False

tasks:
  analyze_top_improvements:
    agent: improvement_analyzer
    description: |
      Analyze all areas of improvements and identify the top 3 most critical improvements needed.
      Consider:
      - Impact on customer satisfaction
      - Feasibility of implementation
      - Urgency of the improvement

      Input:
      {all_area_of_improvements}

      Output should provide:
      - Top 3 improvements in order of priority
      - Reasoning for each selection
      - Expected impact of implementing each improvement

      RETURN THE ANALYSIS IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "top_improvements": [
              {
                  "improvement": "string",
                  "reasoning": "string",
                  "expected_impact": "string"
              }
          ]
      }
    arguments:
      - all_area_of_improvements
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: TopImprovementsModel

  analyze_repeated_issues:
    agent: issue_pattern_analyzer
    description: |
      Analyze all areas of improvements to identify the top 3 most frequently occurring issues across conversations.
      Consider:
      - Frequency of occurrence
      - Pattern recognition
      - Common root causes

      Input:
      {all_area_of_improvements}

      Output should provide:
      - Top 3 recurring issues
      - Frequency of occurrence
      - Pattern analysis

      RETURN THE ANALYSIS IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "repeated_issues": [
              {
                  "issue": "string",
                  "frequency": "string",
                  "pattern_analysis": "string"
              }
          ]
      }
    arguments:
      - all_area_of_improvements
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: RepeatedIssuesModel

  analyze_knowledge_gaps:
    agent: knowledge_gap_analyzer
    description: |
      Analyze agent reasonings to identify any knowledge gaps in understanding customer requests.
      Consider:
      - Misunderstandings of customer requirements
      - Areas of confusion
      - Technical knowledge gaps

      Input:
      {all_reasonings}

      Output should provide:
      - Identified knowledge gaps (or indication that no gaps were observed)
      - Impact on service quality
      - Recommendations for improvement

      If no knowledge gaps are observed, clearly state this in the output.

      RETURN THE ANALYSIS IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "knowledge_gaps": {
              "identified_gaps": ["string"],
              "recommendations": ["string"]
          }
      }
    arguments:
      - all_reasonings
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: KnowledgeGapsModel

  calculate_satisfaction:
    agent: satisfaction_scorer
    description: |
      Analyze all inputs and previous analyses to provide an overall satisfaction score and detailed reasoning.
      Consider:
      - Quality of improvements needed
      - Frequency of issues
      - Knowledge gaps identified
      - Overall service effectiveness

      Inputs:
      {all_reasonings}

      Analysis for improvements:
      < Provided by analyze_top_improvements >

      Analysis for repeated issues:
      < Provided by analyze_repeated_issues >

      Analysis for knowledge gaps:
      < Provided by analyze_knowledge_gaps >

      # Satisfaction Scoring Rubric (0-10)
      ## 1. Quality of Improvements Needed (0-10)
      **Metric:** Evaluate the extent and complexity of the improvements required to meet customer expectations.

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Extensive improvements needed; significant gaps in quality. | Multiple unanswered queries and unclear recommendations for improvements. |
      | 3-4             | Moderate improvements needed; noticeable but manageable gaps. | Responses partially meet objectives, requiring additional clarifications. |
      | 5-6             | Few improvements needed; mostly aligns with expectations. | Minor rewording required to enhance clarity and relevance. |
      | 7-8             | Minimal improvements needed; minor adjustments required. | Most responses meet objectives with one or two minor tweaks. |
      | 9-10            | No improvements needed; exceptional quality. | Responses fully align with objectives and exceed expectations. |

      ## 2. Frequency of Issues (0-10)
      **Metric:** Measure how often issues occurred within the analyzed interactions.

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Issues are frequent and persistent, significantly impacting quality. | Users repeatedly ask for clarifications due to unclear or incorrect answers. |
      | 3-4             | Issues occur occasionally and moderately affect quality. | Occasional misalignment of bot intent with user queries. |
      | 5-6             | Few issues, with minor impact on overall quality. | Responses sometimes lack detail but still resolve the queries effectively. |
      | 7-8             | Rare issues, negligible impact on quality. | Rare edge cases cause slight confusion but are promptly resolved. |
      | 9-10            | No issues; interactions are smooth and problem-free. | All interactions proceed seamlessly without interruptions. |

      ## 3. Knowledge Gaps Identified (0-10)
      **Metric:** Assess how effectively knowledge gaps were addressed in the interaction.

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Major knowledge gaps identified; critical information missing. | Key details about product features or policies are omitted in responses. |
      | 3-4             | Some knowledge gaps identified; noticeable but not critical. | Responses provide partial details but miss specific use cases. |
      | 5-6             | Minor knowledge gaps; most queries adequately addressed. | Responses lack minor contextual details but remain largely accurate. |
      | 7-8             | Rare knowledge gaps; nearly all queries resolved effectively. | Responses include all key information with rare omissions. |
      | 9-10            | No knowledge gaps; comprehensive understanding displayed. | Responses are thorough and demonstrate complete expertise. |

      ## 4. Overall Service Effectiveness (0-10)
      **Metric:** Measure the overall effectiveness of the service in resolving customer queries and meeting objectives.

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Service is ineffective; objectives not met. | User queries remain unresolved, leading to dissatisfaction. |
      | 3-4             | Service is moderately effective; objectives partially met. | Responses resolve some aspects but leave key issues unaddressed. |
      | 5-6             | Service is mostly effective; minor improvements needed. | Responses fulfill most objectives but lack polish or depth in some areas. |
      | 7-8             | Service is highly effective; objectives nearly fully met. | Queries are answered comprehensively, with room for slight enhancements. |
      | 9-10            | Service is exceptional; exceeds objectives and expectations. | Responses provide additional insights and go beyond what was requested. |


      RETURN THE ANALYSIS IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.

    expected_output: |
      {
          "overall_satisfaction": {
              "score": X,
              "reason": "Comprehensive explanation for the overall satisfaction score, considering quality of improvements, frequency of issues, knowledge gaps, and service effectiveness. Highlight key strengths and areas for improvement."
          }
      }

    arguments:
      - all_reasonings
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: SatisfactionScoreModel

  assess_quality_evaluation_task:
    agent: quality_assessor
    description: |
      Evaluate the quality of all task outputs using predefined metrics.

      # Recommendation Quality Rubric (0-10)

      ## 1. Completeness (0-10)
      **Evaluates whether all required aspects of the task were addressed thoroughly.**

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Significant elements are missing; incomplete response. | Recommendations lack critical steps or omit entire sections, e.g., "Only partial analysis of the problem provided." |
      | 3-4             | Some key aspects are missing; partially complete. | Key elements are addressed, but minor details are omitted, e.g., "Discusses the issue but doesn't include proposed solutions." |
      | 5-6             | Mostly complete; a few minor details are missing. | Covers most aspects but lacks depth in some areas, e.g., "Provides solutions but misses timeline details." |
      | 7-8             | Comprehensive, with minor adjustments required. | Includes all necessary elements with slight refinements needed, e.g., "Well-structured plan but missing supporting data." |
      | 9-10            | Fully complete; thoroughly addresses all aspects. | Entire task is addressed with no omissions, e.g., "Detailed analysis with actionable recommendations and supporting data." |

      ## 2. Accuracy (0-10)
      **Assesses the correctness and validity of the analysis or recommendations provided.**

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Contains significant inaccuracies or flawed reasoning. | Key data points are incorrect, e.g., "Recommends a solution that contradicts available facts." |
      | 3-4             | Some inaccuracies or generalizations present. | Recommendations are partially correct but lack precision, e.g., "Uses outdated data for projections." |
      | 5-6             | Mostly accurate but includes minor errors. | Generally correct but misses subtle details, e.g., "Misinterprets a minor policy nuance." |
      | 7-8             | Highly accurate with negligible flaws. | Accurate with minor improvements possible, e.g., "All calculations correct, but references could be clearer." |
      | 9-10            | Fully accurate and supported by evidence. | Analysis is precise and well-reasoned, e.g., "Recommendations align perfectly with user requirements and are data-backed." |

      ## 3. Relevance (0-10)
      **Measures how well the recommendation aligns with the user's objectives or needs.**

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Largely irrelevant; does not address objectives. | Off-topic suggestions, e.g., "Focuses on general advice unrelated to the user's specific query." |
      | 3-4             | Partially relevant; includes unnecessary elements. | Some suggestions apply, but much is irrelevant, e.g., "Includes generic steps that don't align with the user's goals." |
      | 5-6             | Mostly relevant but includes minor misalignments. | Suggests applicable solutions but misses finer points, e.g., "Addresses part of the issue but overlooks secondary goals." |
      | 7-8             | Highly relevant with negligible extraneous details. | Nearly perfect alignment with objectives, e.g., "Focused suggestions with slight overgeneralization." |
      | 9-10            | Perfectly relevant and tailored to objectives. | Fully aligned recommendations, e.g., "Customized advice that directly addresses the user's problem." |

      ## 4. Actionability (0-10)
      **Determines the practical value and feasibility of implementing the recommendations.**

      | **Score Range** | **Description** | **Example** |
      |------------------|-----------------|-------------|
      | 0-2             | Vague and impractical suggestions. | Recommendations are theoretical with no clear path, e.g., "Suggests a broad strategy without specific steps." |
      | 3-4             | Somewhat actionable but lacks clarity or detail. | Includes general steps but lacks feasibility, e.g., "Mentions goals without timelines or responsibilities." |
      | 5-6             | Mostly actionable but needs refinement. | Practical suggestions but requires clarification, e.g., "Provides steps but doesn't prioritize them." |
      | 7-8             | Clear and actionable with minor improvements needed. | Feasible and structured recommendations, e.g., "A well-defined plan but some dependencies need elaboration." |
      | 9-10            | Fully actionable; clear and practical. | Steps are straightforward and easy to execute, e.g., "Provides a prioritized, step-by-step plan ready for implementation." |


      RETURN THE ANALYSIS IN JSON FORMAT COMPATIBLE WITH PYTHON SYNTAX.
      - Use double quotes (`"`) for all strings, including keys and values. Do not use single quotes (`'`).
      - Validate the JSON before returning to ensure it adheres to proper syntax.
      - Ensure that the JSON is strictly formatted to avoid parsing errors in Python.
    expected_output: |
      {
        "completeness": {
          "score": 0,
          "rating": "incomplete/partially_complete/mostly_complete/comprehensive/fully_complete",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in completeness."
        },
        "accuracy": {
          "score": 0,
          "rating": "inaccurate/partially_accurate/mostly_accurate/highly_accurate/fully_accurate",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in accuracy."
        },
        "relevance": {
          "score": 0,
          "rating": "irrelevant/partially_relevant/mostly_relevant/highly_relevant/perfectly_relevant",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in relevance."
        },
        "actionability": {
          "score": 0,
          "rating": "impractical/somewhat_actionable/mostly_actionable/clear_and_actionable/fully_actionable",
          "reason": "Explanation for the assigned score, describing strengths and areas for improvement in actionability."
        }
      }
    guardrails:
      - validate_pydantic_output
    max_retries: 5
    output_pydantic: QualityAssessmentModel
