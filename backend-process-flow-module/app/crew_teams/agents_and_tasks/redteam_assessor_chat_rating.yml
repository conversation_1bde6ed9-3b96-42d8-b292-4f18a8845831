crew_config:
  process: sequential
  verbose: True
  content_task_output: 2
  output_quality_threshold: 10

models:
  - name: "gpt-4o-mini"
    temperature: 0.7

default_model: "gpt-4o-mini"

agents:
  chat_context_analyzer:
    role: "Chat Context Analyzer"
    goal: "Analyze the context of the chat history, identifying key customer concerns, requests, or problems."
    backstory: "An expert in contextual analysis with a focus on understanding the customer's perspective."
    model: "gpt-4o-mini"
    verbose: False

  customer_response_evaluator:
    role: "Customer Service Response Evaluator"
    goal: "Evaluate the adequacy of the customer service response based on the context and rubric metrics."
    backstory: "An evaluator skilled in assessing response quality and adequacy in customer interactions."
    model: "gpt-4o-mini"
    verbose: False

  rubric_scorer:
    role: "Rubric Scorer"
    goal: "Apply rubric metrics to evaluate the adequacy, relevance, and tone of the customer service response."
    backstory: "A quality assurance agent focused on consistent rubric-based evaluation."
    model: "gpt-4o-mini"
    verbose: False

tasks:
  context_analysis_task:
    agent: chat_context_analyzer
    description: |
      Analyze the provided chat history to summarize the customer's concern or request.

      Chat History:
      {chat_history}

      Customer Profile:
      {customer_profile}

      Previous Feedback if any:
      {previous_feedback}
    expected_output: |
      {
          "customer_request": "A summary of the customer's concern or request based on chat history"
      }
    arguments:
      - chat_history
      - customer_profile
      - previous_feedback

  response_analysis_task:
    agent: customer_response_evaluator
    description: |
      Evaluate the adequacy of the customer service (bot_text) response based on the provided context and summarized customer request.

      Chat History:
      {chat_history}

      Customer Profile:
      {customer_profile}

      Use the following adequacy criteria:
      - Responsiveness: Does the response address the customer's concern?
      - Accuracy: Is the information provided correct and helpful?
      - Clarity: Is the response easy to understand and free of jargon?
      - Tone: Is the tone professional, empathetic, and appropriate?

    expected_output: |
      {
          "response_evaluation": {
              "responsiveness": "Score and explanation",
              "accuracy": "Score and explanation",
              "clarity": "Score and explanation",
              "tone": "Score and explanation"
          }
      }
    arguments:
      - chat_history
      - customer_profile
    context: [context_analysis_task]

  rubric_evaluation_task:
    agent: rubric_scorer
    description: |
      Apply rubric scoring to the response evaluation. Each metric is scored out of 10.

      ### **Enhanced Metrics**

      #### **1. Responsiveness (0-10)**
      Measures how well the response addresses the customer’s concern.

      | **Score Range** | **Description**                                                                                          | **Examples**                                                                                       |
      |------------------|----------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|
      | **0-3**          | **Unresponsive:** The response completely ignores or misunderstands the customer's concern.              | Customer: "I can't log in." Response: "Have a great day!"                                          |
      | **4-6**          | **Partially Responsive:** The response addresses part of the concern but lacks completeness or detail.    | Customer: "I can't log in." Response: "Try resetting your password."                              |
      | **7-8**          | **Mostly Responsive:** The response addresses the main concern but may lack additional useful suggestions.| Customer: "I can't log in." Response: "You can reset your password using this link: [link]."      |
      | **9-10**         | **Fully Responsive:** The response is thorough, addressing all aspects of the concern with actionable steps.| Customer: "I can't log in." Response: "You can reset your password using this link: [link]. If it doesn't work, please clear your cache or try a different browser. Let us know if you still face issues." |

      ---

      #### **2. Accuracy (0-10)**
      Evaluates the correctness and usefulness of the information provided.

      | **Score Range** | **Description**                                                                                          | **Examples**                                                                                       |
      |------------------|----------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|
      | **0-3**          | **Inaccurate:** The response contains incorrect or misleading information.                              | Customer: "How do I reset my password?" Response: "You cannot reset passwords on this platform."   |
      | **4-6**          | **Partially Accurate:** The response contains some correct information but also errors or omissions.     | Customer: "How do I reset my password?" Response: "Click the 'Forgot Password' link. It always works." (ignores cases when it might not). |
      | **7-8**          | **Mostly Accurate:** The response is correct but may omit minor details that could enhance clarity.      | Customer: "How do I reset my password?" Response: "Click the 'Forgot Password' link to receive a reset email." |
      | **9-10**         | **Fully Accurate:** The response is completely correct and provides all necessary details.               | Customer: "How do I reset my password?" Response: "Click the 'Forgot Password' link, then check your email for a reset link. Ensure to use a supported browser." |

      ---

      #### **3. Clarity (0-10)**
      Assesses how easy it is for the customer to understand the response.

      | **Score Range** | **Description**                                                                                          | **Examples**                                                                                       |
      |------------------|----------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|
      | **0-3**          | **Unclear:** The response is vague, confusing, or uses overly technical language.                       | Response: "You need to authenticate with the system API and regenerate a password token."          |
      | **4-6**          | **Partially Clear:** The response is mostly understandable but contains some ambiguity or jargon.        | Response: "Use the reset link and clear cache if it doesn’t work."                                 |
      | **7-8**          | **Mostly Clear:** The response is clear but may lack simplicity or structured guidance.                  | Response: "Click the 'Forgot Password' link. If you face issues, clear your cache or try another browser." |
      | **9-10**         | **Exceptionally Clear:** The response is simple, direct, and free of ambiguity.                         | Response: "To reset your password, click 'Forgot Password,' check your email for a link, and follow the instructions. If needed, clear your cache or use a different browser." |

      ---

      #### **4. Tone (0-10)**
      Measures the appropriateness and professionalism of the tone used.

      | **Score Range** | **Description**                                                                                          | **Examples**                                                                                       |
      |------------------|----------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|
      | **0-3**          | **Inappropriate:** The tone is rude, dismissive, or unprofessional.                                     | Customer: "I can’t reset my password." Response: "That's not my problem."                         |
      | **4-6**          | **Neutral:** The tone is professional but lacks empathy or warmth.                                      | Customer: "I can’t reset my password." Response: "Click the link to reset your password."          |
      | **7-8**          | **Professional:** The tone is polite, professional, and shows some empathy.                             | Customer: "I can’t reset my password." Response: "I’m here to help. Please click the link to reset your password." |
      | **9-10**         | **Warm and Professional:** The tone is empathetic, reassuring, and professional.                        | Customer: "I can’t reset my password." Response: "I’m sorry to hear that you’re having trouble. Let’s get this sorted! Please click the link to reset your password. If you need further assistance, I’m here to help." |

      Return in a JSON format.

    expected_output: |
      {
          "responsiveness": {
              "score": X,
              "rating": "poor/fair/good/excellent",
              "reason": "Explanation for the score."
          },
          "accuracy": {
              "score": X,
              "rating": "poor/fair/good/excellent",
              "reason": "Explanation for the score."
          },
          "clarity": {
              "score": X,
              "rating": "poor/fair/good/excellent",
              "reason": "Explanation for the score."
          },
          "tone": {
              "score": X,
              "rating": "poor/fair/good/excellent",
              "reason": "Explanation for the score."
          }
      }
    context: [response_analysis_task]
    guardrails:
      - validate_pydantic_output
      - validate_json_output
    max_retries: 5
    output_pydantic: AssessorRubicScore
