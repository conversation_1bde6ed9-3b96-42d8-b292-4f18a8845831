# Generated by Django 4.2.5 on 2024-12-31 06:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('redteam', '0017_alter_redteamattachment_message'),
        ('crew_teams', '0008_crewteamrecommendations_batch_run'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='CrewTeamRecommendations',
            new_name='RedTeamCrewTeamBatchRecommendations',
        ),
        migrations.CreateModel(
            name='RedTeamCrewTeamChatRecommendations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('original_prompt', models.J<PERSON><PERSON>ield(default=dict, help_text='Original prompt before any changes', null=True)),
                ('updated_prompt', models.J<PERSON>NField(default=dict, help_text='Updated prompt based on the recommendations', null=True)),
                ('yaml_path', models.CharField(help_text='Path to the YAML file containing the crew team configuration', max_length=255, null=True)),
                ('agent_name', models.CharField(help_text='Name of the agent that the recommendation is for', max_length=255, null=True)),
                ('task_name', models.CharField(help_text='Name of the task that the recommendation is for', max_length=255, null=True)),
                ('reasoning', models.TextField(help_text='Detailed reasoning for the recommendation', null=True)),
                ('impact_analysis', models.TextField(help_text='Impact of the recommendation', null=True)),
                ('risk_analysis', models.TextField(help_text='Risk analysis of the recommendation', null=True)),
                ('expected_outcome', models.TextField(help_text='Expected outcome of the recommendation', null=True)),
                ('is_completed', models.BooleanField(default=False, help_text='Whether the recommendation has been completed')),
                ('allowed_internal_to_approve', models.BooleanField(default=False, help_text='Whether the internal team is allowed to approve the recommendation')),
                ('allowed_external_to_approve', models.BooleanField(default=False, help_text='Whether the external team is allowed to approve the recommendation')),
                ('conversation', models.ForeignKey(blank=True, help_text='Associated red team conversation for this recommendation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crew_team_recommendations', to='redteam.redteamconversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
