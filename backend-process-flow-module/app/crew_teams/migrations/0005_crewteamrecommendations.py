# Generated by Django 4.2.5 on 2024-11-19 04:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crew_teams', '0004_crewteamlogs_yaml_path'),
    ]

    operations = [
        migrations.CreateModel(
            name='CrewTeamRecommendations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('original_prompt', models.JSONField(default=dict, help_text='Original prompt before any changes', null=True)),
                ('updated_prompt', models.JSONField(default=dict, help_text='Updated prompt based on the recommendations', null=True)),
                ('yaml_path', models.CharField(help_text='Path to the YAML file containing the crew team configuration', max_length=255, null=True)),
                ('agent_name', models.Char<PERSON>ield(help_text='Name of the agent that the recommendation is for', max_length=255, null=True)),
                ('task_name', models.CharField(help_text='Name of the task that the recommendation is for', max_length=255, null=True)),
                ('is_completed', models.BooleanField(default=False, help_text='Whether the recommendation has been completed')),
                ('allowed_internal_to_approve', models.BooleanField(default=False, help_text='Whether the internal team is allowed to approve the recommendation')),
                ('allowed_external_to_approve', models.BooleanField(default=False, help_text='Whether the external team is allowed to approve the recommendation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
