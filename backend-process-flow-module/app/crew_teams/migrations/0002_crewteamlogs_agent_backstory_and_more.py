# Generated by Django 4.2.5 on 2024-11-17 17:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0033_remove_statemachine_additional_context_and_more'),
        ('crew_teams', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='crewteamlogs',
            name='agent_backstory',
            field=models.TextField(help_text='Contextual background information about the agent', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='agent_config',
            field=models.JSONField(default=dict, help_text='JSON configuration settings for the agent', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='agent_goal',
            field=models.TextField(help_text='Primary goal or purpose of the agent for this task', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='agent_role',
            field=models.<PERSON><PERSON><PERSON><PERSON>(help_text='Specific role or function of the agent', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='task_config',
            field=models.JSONField(default=dict, help_text='JSON configuration settings specific to this task', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='task_description',
            field=models.TextField(help_text='Detailed description of the task to be performed', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='task_expected_output',
            field=models.TextField(help_text='Expected format and requirements for the task output', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='task_raw',
            field=models.TextField(help_text='Raw input or content provided for the task', null=True),
        ),
        migrations.AddField(
            model_name='crewteamlogs',
            name='task_summary',
            field=models.TextField(help_text='Brief summary describing the key points of the task', null=True),
        ),
        migrations.AlterField(
            model_name='crewteamlogs',
            name='agent_name',
            field=models.CharField(help_text='Name identifier for the agent', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='crewteamlogs',
            name='agent_response',
            field=models.TextField(help_text='Response or output generated by the agent', null=True),
        ),
        migrations.AlterField(
            model_name='crewteamlogs',
            name='conversation_queue',
            field=models.ForeignKey(blank=True, help_text='Associated conversation queue for this crew team interaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crewteam_logs', to='chat.conversationqueue'),
        ),
        migrations.AlterField(
            model_name='crewteamlogs',
            name='input_prompt',
            field=models.TextField(help_text='Input prompt or instructions given to the agent', null=True),
        ),
        migrations.AlterField(
            model_name='crewteamlogs',
            name='task_name',
            field=models.CharField(help_text='Short name/identifier for the task', max_length=255, null=True),
        ),
    ]
