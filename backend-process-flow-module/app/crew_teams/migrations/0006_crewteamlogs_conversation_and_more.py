# Generated by Django 4.2.5 on 2024-12-19 16:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('redteam', '0006_alter_redteamconversation_options'),
        ('chat', '0034_alter_attachment_message'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crew_teams', '0005_crewteamrecommendations'),
    ]

    operations = [
        migrations.AddField(
            model_name='crewteamlogs',
            name='conversation',
            field=models.ForeignKey(blank=True, help_text='Associated conversation for this crew team interaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crewteam_logs', to='chat.conversation'),
        ),
        migrations.AlterField(
            model_name='crewteamlogs',
            name='conversation_queue',
            field=models.ForeignKey(blank=True, help_text='Associated conversation queue for this crew team interaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crewteam_logs', to='chat.conversationqueue'),
        ),
        migrations.CreateModel(
            name='RedTeamCrewTeamLogs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('task_description', models.TextField(help_text='Detailed description of the task to be performed', null=True)),
                ('task_name', models.CharField(help_text='Short name/identifier for the task', max_length=255, null=True)),
                ('task_expected_output', models.TextField(help_text='Expected format and requirements for the task output', null=True)),
                ('task_summary', models.TextField(help_text='Brief summary describing the key points of the task', null=True)),
                ('task_raw', models.TextField(help_text='Raw input or content provided for the task', null=True)),
                ('task_config', models.JSONField(default=dict, help_text='JSON configuration settings specific to this task', null=True)),
                ('agent_name', models.CharField(help_text='Name identifier for the agent', max_length=255, null=True)),
                ('agent_role', models.CharField(help_text='Specific role or function of the agent', max_length=255, null=True)),
                ('agent_goal', models.TextField(help_text='Primary goal or purpose of the agent for this task', null=True)),
                ('agent_backstory', models.TextField(help_text='Contextual background information about the agent', null=True)),
                ('agent_config', models.JSONField(default=dict, help_text='JSON configuration settings for the agent', null=True)),
                ('input_prompt', models.TextField(help_text='Input prompt or instructions given to the agent', null=True)),
                ('agent_response', models.TextField(help_text='Response or output generated by the agent', null=True)),
                ('yaml_path', models.CharField(help_text='Path to the YAML file containing the task configuration', max_length=255, null=True)),
                ('conversation', models.ForeignKey(blank=True, help_text='Associated conversation for this crew team interaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='redteam_crewteam_logs', to='redteam.redteamconversation')),
                ('conversation_queue', models.ForeignKey(blank=True, help_text='Associated conversation queue for this crew team interaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='redteam_crewteam_logs', to='redteam.redteamconversationqueue')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
