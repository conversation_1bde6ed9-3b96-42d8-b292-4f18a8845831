import uuid

from django.contrib.auth import get_user_model
from django.db import models

from app.chats.chat.models import Conversation, ConversationQueue
from app.redteam.models import RedTeamConversation, RedTeamConversationQueue
from app.utility import models as utility_models

User = get_user_model()


class CrewTeamLogs(utility_models.BaseModel):
    # Task Details
    task_description = models.TextField(
        null=True,
        help_text="Detailed description of the task to be performed"
    )
    task_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Short name/identifier for the task"
    )
    task_expected_output = models.TextField(
        null=True,
        help_text="Expected format and requirements for the task output"
    )
    task_summary = models.TextField(
        null=True,
        help_text="Brief summary describing the key points of the task"
    )
    task_raw = models.TextField(
        null=True,
        help_text="Raw input or content provided for the task"
    )
    task_config = models.JSONField(
        default=dict,
        null=True,
        help_text="JSON configuration settings specific to this task"
    )

    # Agent Details
    agent_name = models.Char<PERSON>ield(
        max_length=255,
        null=True,
        help_text="Name identifier for the agent"
    )
    agent_role = models.CharField(
        max_length=255,
        null=True,
        help_text="Specific role or function of the agent"
    )
    agent_goal = models.TextField(
        null=True,
        help_text="Primary goal or purpose of the agent for this task"
    )
    agent_backstory = models.TextField(
        null=True,
        help_text="Contextual background information about the agent"
    )
    agent_config = models.JSONField(
        default=dict,
        null=True,
        help_text="JSON configuration settings for the agent"
    )

    # Input/Output
    input_prompt = models.TextField(
        null=True,
        help_text="Input prompt or instructions given to the agent"
    )
    agent_response = models.TextField(
        null=True,
        help_text="Response or output generated by the agent"
    )

    yaml_path = models.CharField(
        max_length=255,
        null=True,
        help_text="Path to the YAML file containing the task configuration"
    )

    is_final_output = models.BooleanField(
        default=False,
        help_text="Flag indicating whether this is the final output for the task"
    )
    
    # Mapping to the conversation queue
    conversation_queue = models.ForeignKey(
        ConversationQueue,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated conversation queue for this crew team interaction"
    )

    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated conversation for this crew team interaction"
    )
    

class RedTeamCrewTeamLogs(utility_models.BaseModel):
    """
    Model for logging crew team interactions and tasks.
    
    Stores details about tasks, agents, and their interactions including inputs/outputs.
    Used for tracking and auditing crew team activities.
    """

    # Task Details
    task_description = models.TextField(
        null=True,
        help_text="Detailed description of the task to be performed"
    )
    task_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Short name/identifier for the task"
    )
    task_expected_output = models.TextField(
        null=True,
        help_text="Expected format and requirements for the task output"
    )
    task_summary = models.TextField(
        null=True,
        help_text="Brief summary describing the key points of the task"
    )
    task_raw = models.TextField(
        null=True,
        help_text="Raw input or content provided for the task"
    )
    task_config = models.JSONField(
        default=dict,
        null=True,
        help_text="JSON configuration settings specific to this task"
    )

    # Agent Details
    agent_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Name identifier for the agent"
    )
    agent_role = models.CharField(
        max_length=255,
        null=True,
        help_text="Specific role or function of the agent"
    )
    agent_goal = models.TextField(
        null=True,
        help_text="Primary goal or purpose of the agent for this task"
    )
    agent_backstory = models.TextField(
        null=True,
        help_text="Contextual background information about the agent"
    )
    agent_config = models.JSONField(
        default=dict,
        null=True,
        help_text="JSON configuration settings for the agent"
    )

    # Input/Output
    input_prompt = models.TextField(
        null=True,
        help_text="Input prompt or instructions given to the agent"
    )
    agent_response = models.TextField(
        null=True,
        help_text="Response or output generated by the agent"
    )

    yaml_path = models.CharField(
        max_length=255,
        null=True,
        help_text="Path to the YAML file containing the task configuration"
    )

    is_final_output = models.BooleanField(
        default=False,
        help_text="Flag indicating whether this is the final output for the task"
    )

    # Mapping to the conversation queue
    conversation_queue = models.ForeignKey(
        RedTeamConversationQueue,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated conversation queue for this crew team interaction"
    )

    conversation = models.ForeignKey(
        RedTeamConversation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated conversation for this crew team interaction"
    )
    

class RedTeamCrewTeamBatchRecommendations(utility_models.BaseModel):
    original_prompt = models.JSONField(
        default=dict,
        null=True,
        help_text="Original prompt before any changes"
    )
    updated_prompt = models.JSONField(
        default=dict,
        null=True,
        help_text="Updated prompt based on the recommendations"
    )
    yaml_path = models.CharField(
        max_length=255,
        null=True,
        help_text="Path to the YAML file containing the crew team configuration"
    )
    agent_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Name of the agent that the recommendation is for"
    )
    task_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Name of the task that the recommendation is for"
    )  

    # Status tracker
    is_completed = models.BooleanField(
        default=False,
        help_text="Whether the recommendation has been completed"
    )
    allowed_internal_to_approve = models.BooleanField(
        default=False,
        help_text="Whether the internal team is allowed to approve the recommendation"
    )
    allowed_external_to_approve = models.BooleanField(
        default=False,
        help_text="Whether the external team is allowed to approve the recommendation"
    )

    batch_run = models.ForeignKey(
        "redteam.RedTeamBatchRun",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated red team batch run for this recommendation"
    )


class RedTeamCrewTeamChatRecommendations(utility_models.BaseModel):
    original_prompt = models.JSONField(
        default=dict,
        null=True,
        help_text="Original prompt before any changes"
    )
    updated_prompt = models.JSONField(
        default=dict,
        null=True,
        help_text="Updated prompt based on the recommendations"
    )
    yaml_path = models.CharField(
        max_length=255,
        null=True,
        help_text="Path to the YAML file containing the crew team configuration"
    )
    agent_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Name of the agent that the recommendation is for"
    )
    task_name = models.CharField(
        max_length=255,
        null=True,
        help_text="Name of the task that the recommendation is for"
    )
    reasoning = models.TextField(
        null=True,
        help_text="Detailed reasoning for the recommendation"
    )
    impact_analysis = models.TextField(
        null=True,
        help_text="Impact of the recommendation"
    )
    risk_analysis = models.TextField(
        null=True,
        help_text="Risk analysis of the recommendation"
    )
    expected_outcome = models.TextField(
        null=True,
        help_text="Expected outcome of the recommendation"
    )

    # Status tracker
    is_completed = models.BooleanField(
        default=False,
        help_text="Whether the recommendation has been completed"
    )
    allowed_internal_to_approve = models.BooleanField(
        default=False,
        help_text="Whether the internal team is allowed to approve the recommendation"
    )
    allowed_external_to_approve = models.BooleanField(
        default=False,
        help_text="Whether the external team is allowed to approve the recommendation"
    )

    conversation = models.ForeignKey(
        "redteam.RedTeamConversation",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated red team conversation for this recommendation",
        related_name="crew_team_recommendations"
    )
