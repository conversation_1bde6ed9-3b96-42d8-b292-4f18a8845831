import json
from typing import <PERSON><PERSON>, Union

from pydantic import BaseModel, ValidationError


def validate_json_output(result: str) -> <PERSON><PERSON>[bool, Union[dict, str]]:
    """Validate that the output is valid JSON."""
    try:
        json.loads(result.__dict__["raw"])
        return (True, result)

    except json.JSONDecodeError as e:
        return (False, "Output must be valid JSON format. Check all the ',' and '}'. Here's the error: " + str(e))

    except Exception as e:
        return (False, "Unexpected error during JSON validation: " + str(e))


def validate_pydantic_output(result: str) -> Tuple[bool, Union[dict, str]]:
    """Validate that the output follows the Pydantic model."""
    try:
        if not hasattr(result, "pydantic") or not result.pydantic:
            return (False, "Output must contain a Pydantic model")

        pydantic_output = result.pydantic
        if not isinstance(pydantic_output, BaseModel):
            return (False, "Output must be a Pydantic model")

        pydantic_output.model_dump()

        return (True, result)

    except ValidationError as ve:
        return (False, "Pydantic validation failed: " + str(ve))

    except Exception as e:
        return (False, "Unexpected error during Pydantic validation: " + str(e))
