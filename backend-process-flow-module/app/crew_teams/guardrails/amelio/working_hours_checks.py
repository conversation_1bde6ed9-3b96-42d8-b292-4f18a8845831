import pytz
from datetime import datetime
from typing import <PERSON>ple, Union
from tenant_schemas.utils import schema_context
from app.chats.chat_action_tracker.models import MessageToSend
from app.chats.chat.models import Conversation
from app.utility import models as utility_models
from django.utils import timezone

def working_hours_check(result: str) -> Tuple[bool, Union[dict, str]]:
    sgt = pytz.timezone('Asia/Singapore')
    current_time = datetime.now(sgt)
    schema_name = "amelio"
    conversation_id = result.pydantic.conversation_id
    bot_response = result.pydantic.bot_response

    # Check if the current time is between 12am (inclusive) and 5am (exclusive)
    if 0 <= current_time.hour < 5:
        with schema_context(schema_name):
            conversation = Conversation.objects.filter(id=conversation_id).first()
            third_party_id = conversation.third_party_id
            get_pending_message = MessageToSend.objects.filter(
                contact=third_party_id,
                status='Pending'
            ).first()

        if get_pending_message:
            # Delete existing pending messages for this contact
            MessageToSend.objects.filter(
                contact=third_party_id,
                status='Pending'
            ).delete()
            
            # Create new pending messages for each response
            for message in bot_response:
                MessageToSend.objects.create(
                    contact=third_party_id,
                    message=message,
                    status='Pending'
                )
        else:
            # Set target time to 5am Singapore time for the next day
            now_sg = timezone.now().astimezone(sgt)
            target_time = now_sg.replace(hour=5, minute=0, second=0, microsecond=0)
            if now_sg.hour >= 5:  # If current time is past 5am, schedule for next day
                target_time += timezone.timedelta(days=1)
                
            # Create new messages to send for each response
            for message in bot_response:
                MessageToSend.objects.create(
                    contact=third_party_id,
                    message=message,
                    status=utility_models.PENDING,
                    to_be_sent_at=target_time,
                    message_type=utility_models.MACHINE_TEXT,
                    target_source=utility_models.WHATSAPP_QR
                )
            
        result.pydantic.bot_response = ["Currently, we are closed. Will check back in with the team and get back to you once we are opened."]
        return (True, result)
    else:
        # reply the customer as usual
        return (True, result)
