from django.db import models

import app.documents.tag.models as tag_models
from app.utility import models as utility_models


# Create your models here.
class PolicyGeneratorInformation(utility_models.BaseModel):
    website_url = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    scrape_type = models.CharField(max_length=255, blank=True)
    customer_name = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    about_me = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    target_industries_audiences = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    contact = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    testimonials = models.Char<PERSON>ield(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    milestones = models.<PERSON><PERSON><PERSON><PERSON>(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )


class Document(utility_models.NamedBaseModel):
    file = models.FileField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, null=True, blank=True
    )
    status = models.CharField(
        choices=utility_models.STATUS_CHOICES,
        max_length=255,
        default=utility_models.SUCCEED,
    )
    status_description = models.CharField(max_length=255, blank=True)
    chroma_db_type = models.CharField(
        max_length=50,
        choices=utility_models.CHROMA_DB_TYPE,
        default=utility_models.EXTERNAL_TEXT,
    )
    is_internal = models.BooleanField(default=False)
    is_external = models.BooleanField(default=False)
    is_source_muted_external = models.BooleanField(default=True)
    is_source_muted_internal = models.BooleanField(default=True)
    is_marketing_material = models.BooleanField(default=False)
    is_auto_generated = models.BooleanField(default=False)
    checksum = models.CharField(max_length=255, null=True, blank=True)

    policy_generator_information = models.OneToOneField(
        PolicyGeneratorInformation,
        related_name="document",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )

    class Meta:
        ordering = ["name", "-created_at"]


class KnowledgeImage(utility_models.BaseModel):
    name = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    file = models.ImageField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, null=True, blank=True
    )
    chatbot_access = models.BooleanField(default=False)
    tags = models.ManyToManyField(
        tag_models.Tag,
        through=tag_models.KnowledgeImageToTagBridge,
        related_name="knowledge_images",
    )

    class Meta:
        ordering = ["name", "-created_at"]
