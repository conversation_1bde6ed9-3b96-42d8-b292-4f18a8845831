# Generated by Django 4.2.5 on 2023-10-16 06:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('document', '0004_alter_document_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='PolicyGeneratorInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('website_url', models.Char<PERSON>ield(blank=True, max_length=8912)),
                ('customer_name', models.CharField(blank=True, max_length=8912)),
                ('about_me', models.CharField(blank=True, max_length=8912)),
                ('target_industries_audiences', models.Char<PERSON>ield(blank=True, max_length=8912)),
                ('contact', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=8912)),
                ('testimonials', models.CharField(blank=True, max_length=8912)),
                ('milestones', models.CharField(blank=True, max_length=8912)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='document',
            name='policy_generator_information',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='document', to='document.policygeneratorinformation'),
        ),
    ]
