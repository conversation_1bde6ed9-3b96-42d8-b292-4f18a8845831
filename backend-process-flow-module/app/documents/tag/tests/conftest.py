import pytest
from rest_framework.test import APIClient
from authapi.models import User
from tenant_schemas.utils import schema_context, get_tenant_model, tenant_context
from user_tenant_bridge.models import UserTenantBridge
from authapi.models import ADMIN
from tag.models import Tag, KnowledgeImageToTagBridge
from document.models import KnowledgeImage


class TenantAPIClient(APIClient):
    def __init__(self, tenant, **defaults):
        super().__init__(**defaults)
        self.tenant = tenant

    def request(self, **kwargs):
        kwargs.setdefault(
            "HTTP_HOST", self.tenant.domain_url
        )  # Set the tenant-specific host
        with tenant_context(self.tenant):
            return super().request(**kwargs)


@pytest.fixture(scope="function")
def tenant():
    TenantModel = get_tenant_model()
    tenant = TenantModel.objects.create(
        name="Test Tenant",
        schema_name="test_tenant",  # Changed from "test_tenant" to "test-tenant"
        # Add other necessary fields here if required
        domain_url="test-tenant.localhost",  # Changed from "test_tenant.localhost" to "test-tenant.localhost"
    )
    tenant.save()
    yield tenant


@pytest.fixture(scope="function")
def db_tenant(tenant):
    with schema_context(tenant.schema_name):
        yield tenant


@pytest.fixture(scope="function")
def client(tenant):
    user = User.objects.create(email="<EMAIL>", password="test_password")
    UserTenantBridge.objects.create(user=user, tenant=tenant, role=ADMIN)
    client = TenantAPIClient(tenant)
    client.force_authenticate(user=user)
    yield client


@pytest.fixture(scope="function")
def knowledge_image(db_tenant):
    yield KnowledgeImage.objects.create(name="Test Knowledge Image")


@pytest.fixture(scope="function")
def tag(db_tenant):
    yield Tag.objects.create(name="Test Tag", color="#FFFFFF")


@pytest.fixture(scope="function")
def knowledge_image_to_tag_bridge(db_tenant, knowledge_image, tag):
    yield KnowledgeImageToTagBridge.objects.create(
        knowledge_image=knowledge_image, tag=tag
    )
