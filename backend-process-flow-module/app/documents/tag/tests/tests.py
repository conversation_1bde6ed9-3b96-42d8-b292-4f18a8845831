import pytest
from django.urls import reverse

import tag.models as tag_models

# Create your tests here.


# =====================TAG DB TESTS=====================
@pytest.mark.django_db
def test_tag_creation(tag):
    assert tag.name == "test_tag"  # Adjusted to match the model's transformation
    assert tag.color == "#FFFFFF"
    assert str(tag) == "test_tag"


# =====================TAG VIEW TESTS=====================
@pytest.mark.django_db
def test_tag_list_view(
    tag, client
):  # add tag in arg to create tag in db before running test
    url = reverse("tag-list")
    response = client.get(url)
    assert response.status_code == 200
    assert len(response.json()["results"]) == 1
    assert response.json()["results"][0]["name"] == "Test Tag"


@pytest.mark.django_db
def test_tag_create_view(client):
    url = reverse("tag-create")
    response = client.post(url, data={"name": "new_tag", "color": "#000000"})
    assert response.status_code == 201
    assert response.json()["name"] == "new_tag"
    assert response.json()["color"] == "#000000"


@pytest.mark.django_db
def test_tag_create_view_duplicate(tag, client):
    url = reverse("tag-create")
    response = client.post(url, data={"name": "test_tag", "color": "#000000"})
    assert response.status_code == 400
    assert response.json()["message"] == "Tag already exists!"


@pytest.mark.django_db
def test_tag_update_view(tag, client):
    url = reverse("tag-update", args=[tag.id])
    response = client.patch(url, data={"name": "updated_tag", "color": "#FFFFFF"})
    assert response.status_code == 200
    assert response.json()["name"] == "updated_tag"
    assert response.json()["color"] == "#FFFFFF"


@pytest.mark.django_db
def test_tag_delete_view(tag, client):
    url = reverse("tag-delete", args=[tag.id])
    response = client.delete(url)
    assert response.status_code == 204
    assert not tag_models.Tag.objects.filter(id=tag.id).exists()


# =====================KNOWLEDGE IMAGE TO TAG BRIDGE VIEW TESTS=====================
@pytest.mark.django_db
def test_knowledge_image_to_tag_bridge_creation(knowledge_image, tag, client):
    url = reverse("knowledge-image-to-tag-bridge-create")
    response = client.post(
        url,
        data={
            "imageId": knowledge_image.id,
            "imageTagId": tag.id,
        },
    )
    assert response.status_code == 200
    assert response.json()["detail"] == "Tag added successfully."


@pytest.mark.django_db
def test_knowledge_image_to_tag_bridge_creation_invalid_image(tag, client):
    url = reverse("knowledge-image-to-tag-bridge-create")
    response = client.post(
        url,
        data={"imageId": 999999, "imageTagId": tag.id},
    )
    assert response.status_code == 404


@pytest.mark.django_db
def test_knowledge_image_to_tag_bridge_delete_view(
    knowledge_image_to_tag_bridge, client
):
    url = reverse(
        "knowledge-image-to-tag-bridge-delete", args=[knowledge_image_to_tag_bridge.id]
    )
    response = client.delete(url)

    assert response.status_code == 204
    assert not tag_models.KnowledgeImageToTagBridge.objects.filter(
        id=knowledge_image_to_tag_bridge.id
    ).exists()
