import re

from django.db import models

from app.utility import models as utility_models


def to_snake_case(value):
    """Convert a string to snake_case."""
    value = re.sub(r"\s+", "_", value)  # Replace spaces with underscores
    return value.lower()


class Tag(utility_models.UniqueNamedBaseModel):
    color = models.CharField(max_length=15, blank=True)

    class Meta:
        ordering = ["name", "-created_at"]

    def save(self, *args, **kwargs):
        if self.name:
            self.name = to_snake_case(self.name)
        super().save(*args, **kwargs)


class KnowledgeImageToTagBridge(utility_models.BaseModel):
    knowledge_image = models.ForeignKey(
        "document.KnowledgeImage",
        on_delete=models.CASCADE,
        related_name="knowledge_image_to_tag_bridges",
    )
    tag = models.ForeignKey(
        Tag, on_delete=models.CASCADE, related_name="knowledge_image_to_tag_bridges"
    )

    class Meta:
        unique_together = ["knowledge_image", "tag"]

    def __str__(self):
        return f"{self.knowledge_image.name} - {self.tag.name}"
