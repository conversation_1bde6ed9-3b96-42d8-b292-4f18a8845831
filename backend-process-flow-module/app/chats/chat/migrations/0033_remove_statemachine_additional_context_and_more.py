# Generated by Django 4.2.5 on 2024-11-14 15:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0032_flowmapping_statemachine'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='statemachine',
            name='additional_context',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='completed_at',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='context_summary',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='conversation',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='current_state',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='decision_rationale',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='flow_mapping',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='is_completed',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='last_message',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='last_transitioned_at',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='started_at',
        ),
        migrations.RemoveField(
            model_name='statemachine',
            name='topic_name',
        ),
        migrations.AddField(
            model_name='statemachine',
            name='context',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='statemachine',
            name='message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='state_machines', to='chat.message'),
        ),
    ]
