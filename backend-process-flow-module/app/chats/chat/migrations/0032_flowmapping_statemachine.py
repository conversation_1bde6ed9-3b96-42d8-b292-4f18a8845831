# Generated by Django 4.2.5 on 2024-10-30 04:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0031_alter_conversation_session_uuid'),
    ]

    operations = [
        migrations.CreateModel(
            name='FlowMapping',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('version', models.IntegerField()),
                ('mapping_json', models.JSONField()),
                ('is_active', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-version'],
                'unique_together': {('version',)},
            },
        ),
        migrations.CreateModel(
            name='StateMachine',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('topic_name', models.CharField(max_length=255)),
                ('is_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('current_state', models.CharField(max_length=255)),
                ('last_transitioned_at', models.DateTimeField(auto_now=True)),
                ('context_summary', models.TextField(help_text='A brief summary of the context at this state for quick reference.')),
                ('decision_rationale', models.TextField(blank=True, help_text='Explanation or reasoning behind the last decision in this state.', null=True)),
                ('additional_context', models.JSONField(blank=True, help_text='Extended context data in JSON format for more complex workflows.', null=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='state_machines', to='chat.conversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('flow_mapping', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='chat.flowmapping')),
                ('last_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='last_state_machine', to='chat.message')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
