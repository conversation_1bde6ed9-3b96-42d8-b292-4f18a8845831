from django.contrib.auth import get_user_model
from rest_framework import serializers

from .models import Conversation, FlowMapping, Message, StateMachine, Attachment

User = get_user_model()


class UserEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["email"]


class StateMachineSerializer(serializers.ModelSerializer):
    """
    Main serializer for StateMachine model with nested related data
    """

    class Meta:
        model = StateMachine
        fields = ["context"]


class MessageMinimalSerializer(serializers.ModelSerializer):
    """Minimal serializer for Message model to be used in StateMachine serializer"""

    state_machines = StateMachineSerializer(many=True, read_only=True)
    sender = UserEmailSerializer(read_only=True)
    attachments = serializers.SerializerMethodField()

    def get_attachments(self, obj):
        return [attachment.id for attachment in obj.attachments.all()]

    class Meta:
        model = Message
        fields = [
            "id",
            "message",
            "state_machines",
            "timestamp",
            "sender",
            "message_type",
            "attachments",
        ]


class FlowMappingSerializer(serializers.ModelSerializer):
    """Serializer for FlowMapping model"""

    class Meta:
        model = FlowMapping
        fields = ["mapping_json"]
