import json
import logging
import traceback

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

import app.core.authapi.helper as authapi_helper
from app.chats.chat.chat_engine.chat_engine import chat_engine_main
from app.utility.custom_exception import CustomException
from app.utility.send_email import send_error_email_to_developers
from app.utility.websocket_client import send_message
from backend import settings
from backend.settings import (
    DEFAULT_FROM_EMAIL,
    ENVIRONMENT,
    ERROR_EMAIL_RECIPIENTS,
    WEBSOCKET_URL,
)

logger = logging.getLogger(__name__)


class ProcessMessages(APIView):
    def post(self, request):
        """
        Process incoming chat messages and send bot response via websocket.

        This endpoint accepts chat messages and related metadata, constructs a bot
        response, and sends it back through a websocket connection.

        Args:
            request: HTTP request object containing:
                message_ids (list): List of message IDs to process
                conversation_id (str): ID of the conversation
                queue_id (str): ID of the message queue

        Returns:
            Response: HTTP 200 response containing the original request data

        Flow:
            1. Extract message data from request
            2. Get current schema name for tenant context
            3. Construct websocket URL with schema
            4. Build bot response message
            5. Send message via websocket
            6. Return success response
        """
        try:
            schema_name = None
            logger.info(f"Processing messages: {request.data}")

            # Extract message data from request
            data = request.data

            conversation_id = data.get("conversation_id", None)
            message_ids = data.get("message_ids", [])  # An array of unread message ids
            queue_id = data.get(
                "queue_id", None
            )  # The id of the message queue. This is the queue for tracking the bot response.
            api_key = data.get(
                "api_key", None
            )  # Purely for API KEY authentication purpose.
            redteam_run = data.get(
                "redteam_run", False
            )  # Flag to indicate if the chat is a redteam run.
            seed_number = data.get(
                "seed_number", None
            )  # Seed number for random generation.
            if isinstance(redteam_run, str):
                redteam_run = bool(redteam_run)

            # Validate bot API key
            authapi_helper.validate_bot_api_key(api_key)

            # Get schema name for tenant context
            schema_name = authapi_helper.get_schema_name()
            logger.info(f"Schema name: {schema_name}")

            # Call the chat engine
            chat_response = chat_engine_main(
                schema_name, message_ids, conversation_id, queue_id, redteam_run, seed_number
            )

            # Construct websocket URL with schema name
            url = f"{WEBSOCKET_URL}/org/{schema_name.replace('_', '-')}/v1/chat/"
            print("url: ", url)

            # Construct the encrypted token
            data = {
                "uuid": "",
                "chat_id": conversation_id,
                "bot_api_key": settings.BOT_API_KEY,
            }
            # Convert dictionary to JSON string before encryption
            json_data = json.dumps(data)
            encrypted_token = authapi_helper.encrypt_data(json_data)

            # Build bot response message
            message = {
                "header": {"type": "bot_response"},
                "body": {
                    "encrypted_token": encrypted_token,
                    "source": "Web Application",
                    "message_type": "machine",
                    "bot_reply": chat_response,
                    "message_ids": message_ids,
                    "conversation_id": conversation_id,
                    "queue_id": queue_id,
                },
            }

            logger.info(f"Bot response message: {message}")

            # Send message via websocket asynchronously
            if not redteam_run:
                success =send_message(url, message)
                logger.info(f"Message sent to websocket successfully: {success}")

            return Response(data=message, status=status.HTTP_200_OK)

        except CustomException as e:
            logger.warning(f"CustomException: {e}")
            return Response(data={"message": e.args}, status=status.HTTP_403_FORBIDDEN)

        except Exception as e:
            
            traceback.print_exc()
            logger.error(f"Unexpected error: {e}")

            # Send error email to developer team
            from_ = f"UtterUnicorn V2 X {schema_name} <{DEFAULT_FROM_EMAIL}>"
            html_path = "email/error_notification.html"
            subject = f"Error in Running Process Flow for {schema_name}"
            to_ = ERROR_EMAIL_RECIPIENTS.split(',')
            custom_kwargs = {
                "schema_name": schema_name,
                "environment": ENVIRONMENT,
                "endpoint": request.path,
                "request_data": json.dumps(request.data),
                "message": "".join(traceback.format_exception(type(e), e, e.__traceback__))
            }

            send_error_email_to_developers(html_path, subject, from_, to_, **custom_kwargs)

            return Response(
                data={"message": e.args}, status=status.HTTP_400_BAD_REQUEST
            )
