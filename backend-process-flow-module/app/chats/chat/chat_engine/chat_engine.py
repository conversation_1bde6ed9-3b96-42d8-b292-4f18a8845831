import json
import logging
import os
import traceback
import uuid
from datetime import datetime
from typing import Dict, List, Optional

import pytz
from crewai import crew
from django.utils import timezone
from tenant_schemas.utils import schema_context

from app.chats.chat.models import Conversation, Message, StateMachine
from app.crew_teams.models import CrewTeamLogs, RedTeamCrewTeamLogs
from app.crew_teams.team import Agent<PERSON><PERSON>
from app.crew_teams.utils.html_output import *
from app.redteam.models import (RedTeamConversation, RedTeamFlowMapping,
                                RedTeamMessage, RedTeamStateMachine)
from app.utility.chat_engine_helper import (
    get_active_flow_mapping, get_bot_settings,
    get_conversation_name_and_number, get_latest_context, get_recent_messages,
    get_recent_messages_only, get_recent_messages_with_time_decay,
    get_unread_messages)
from backend.settings import LOCAL_OUTPUT

logger = logging.getLogger(__name__)


def get_conversation_context(
    schema_name: str,
    conversation_id: str,
    message_ids: List[str],
    variables: dict,
    redteam_run: bool,
) -> Dict:
    """
    Fetch conversation context
    """
    # Fetch conversation context
    # states = get_conversation_states(schema_name, conversation_id)
    recent_messages = get_recent_messages(schema_name, conversation_id, redteam_run)
    unread_messages = get_unread_messages(
        schema_name, conversation_id, message_ids, redteam_run
    )

    # Get active flow mapping using the helper function
    active_flow_mapping = get_active_flow_mapping(schema_name, redteam_run)

    # Get recent messages only
    recent_messages_only = get_recent_messages_only(recent_messages)

    # Get recent messages with time decay
    recent_messages_only_with_time_decay, recent_messages_with_time_decay = get_recent_messages_with_time_decay(recent_messages)

    # Get latest context
    latest_context = get_latest_context(recent_messages)

    # Add the above into variables
    variables.update(
        {
            "recent_messages": recent_messages,
            "unread_messages": unread_messages,
            "active_flow_mapping": active_flow_mapping,
            "recent_messages_only": recent_messages_only,
            "recent_messages_with_time_decay": recent_messages_with_time_decay,
            "recent_messages_only_with_time_decay": recent_messages_only_with_time_decay,
            "latest_context": latest_context
        }
    )

    # Get the conversation information (especially for conversation from Whatsapp)
    conversation_name, conversation_third_party_id = get_conversation_name_and_number(schema_name, conversation_id)

    variables['conversation_name'] = conversation_name
    variables['conversation_third_party_id'] = conversation_third_party_id

    return variables


def run_crew_team(variables: dict, config_path: str, seed_number: Optional[int] = None) -> Dict:
    crew_team = AgentCrew(config_path=config_path, seed_number=seed_number)
    output, task_output, task_full_dicts, final_task_output_index = crew_team.run(**variables)

    return output, task_output, task_full_dicts, final_task_output_index


def update_crew_team_table(task_full_dicts: List[dict], queue_id: int, yaml_path: str, redteam_run: bool = False, schema_name: str = None, final_task_output_index: int = None, conversation_id: str = None):
    """
    Update the crew team table with the task full dicts.

    Args:
        task_full_dicts (List[dict]): List of dictionaries containing task details including:
            - description: Task description
            - name: Task name
            - expected_output: Expected output format
            - summary: Task summary
            - raw: Raw task output
            - agent: Agent name
            - agent_details: Dict with agent configuration details
        queue_id (int): ID of the conversation queue
        yaml_path (str): Path to the YAML file containing the crew team configuration
        redteam_run (bool): Whether this is a redteam run
        schema_name (str): The tenant schema name
        final_task_output_index (int): Index of the final task output
        conversation_id (str): ID of the conversation
            
    Creates CrewTeamLogs entries to track crew team activities and task executions.
    """
    with schema_context(schema_name):
        counter = 0
        for task_dict in task_full_dicts:
            if redteam_run:
                RedTeamCrewTeamLogs.objects.create(
                    conversation_queue_id=queue_id,
                    task_description=task_dict.get('description'),
                    task_name=task_dict.get('name'),
                    task_expected_output=task_dict.get('expected_output'),
                    task_summary=task_dict.get('summary'), 
                    task_raw=task_dict.get('raw'),
                    agent_name=task_dict.get('agent'),
                    agent_role=task_dict.get('agent_details', {}).get('role'),
                    agent_goal=task_dict.get('agent_details', {}).get('goal'),
                    agent_backstory=task_dict.get('agent_details', {}).get('backstory'),
                    yaml_path=yaml_path,
                    is_final_output=True if counter == final_task_output_index else False,
                    conversation_id=conversation_id
                )
            else:
                CrewTeamLogs.objects.create(
                    conversation_queue_id=queue_id,
                    task_description=task_dict.get('description'),
                    task_name=task_dict.get('name'),
                    task_expected_output=task_dict.get('expected_output'),
                    task_summary=task_dict.get('summary'), 
                    task_raw=task_dict.get('raw'),
                    agent_name=task_dict.get('agent'),
                    agent_role=task_dict.get('agent_details', {}).get('role'),
                    agent_goal=task_dict.get('agent_details', {}).get('goal'),
                    agent_backstory=task_dict.get('agent_details', {}).get('backstory'),
                    yaml_path=yaml_path,
                    is_final_output=True if counter == final_task_output_index else False,
                    conversation_id=conversation_id
                )
            counter += 1


def get_stage_node(flow_mapping, intent_analysis_output):
    """
    Get the stage node from the flow mapping based on the intent analysis output.
    """
    # Get the stage node from the flow mapping
    for node in flow_mapping["nodes"]:
        if node["id"] == intent_analysis_output["node_id"]:
            return node
    return None


def get_following_nodes(intent_analysis_output, flow_mapping):
    """
    Get all following nodes for a given node ID.

    Parameters:
        intent_analysis_output (dict): The output of the intent analysis.
        flow_mapping (dict): The JSON structure containing nodes and connections.

    Returns:
        list: A list of node objects that are connected to the given node.
    """
    following_nodes = []
    node_id = intent_analysis_output["node_id"]

    # Extract connections from the data
    connections = flow_mapping.get("connections", [])

    # Find all "to" node IDs where "from" is the current node ID
    to_node_ids = [connection["to"] for connection in connections if connection["from"] == node_id]

    # Map "to" node IDs to their node objects
    nodes = {node["id"]: node for node in flow_mapping.get("nodes", [])}

    for to_node_id in to_node_ids:
        if to_node_id in nodes:
            following_nodes.append(nodes[to_node_id])

    return following_nodes


def chat_engine_main(
    schema_name: str,
    message_ids: List[str],
    conversation_id: Optional[str],
    queue_id: Optional[str],
    redteam_run: bool = False,
    seed_number: Optional[int] = None,
) -> Dict:
    """
    Process chat messages and generate bot response with state information.

    Args:
        schema_name (str): The tenant schema name
        message_ids (List[str]): List of message IDs to process
        conversation_id (Optional[str]): ID of the conversation
        queue_id (Optional[str]): ID of the message queue
        redteam_run (bool): Whether this is a redteam run
        seed_number (Optional[int]): Seed number for reproducibility

    Returns:
        dict containing:
            - bot_message (str): Generated bot response message
            - state_info (dict): State machine information including:
                - topic_name (str): Current topic being discussed
                - current_state (str): Current state in the workflow
                - context_summary (str): Brief summary of current context
                - decision_rationale (str): Reasoning for current state
                - additional_context (dict): Extra context data
                - is_completed (bool): Whether the state is completed
                - flow_mapping_id (str): ID of the active flow mapping

    Raises:
        ValueError: If conversation_id is None or if no active flow mapping exists
    """
    if not conversation_id:
        logger.error("conversation_id is required")
        raise ValueError("conversation_id is required")

    try:
        crew_variables = {
            "conversation_id": conversation_id,
            "message_ids": message_ids,
            "queue_id": queue_id,
            "schema_name": schema_name,
            "is_redteam_run": redteam_run,
            "seed_number": seed_number
        }

        # Get conversation context
        crew_variables = get_conversation_context(
            schema_name, conversation_id, message_ids, crew_variables, redteam_run
        )

        # Step 1: Get the chat metadata
        chat_metadata = {
            "Conversation ID": conversation_id,
            "Schema Name": schema_name,
        }

        # Step 2: Generate the HTML header
        filename = f"{schema_name}_{conversation_id}_conversation_log.html"
        filename_verbose = f"{schema_name}_{conversation_id}_conversation_log_verbose.html"
        filename_full_verbose = f"{schema_name}_{conversation_id}_conversation_log_full_verbose.html"

        html_output = ""
        html_output_verbose = ""
        html_output_full_verbose = ""
        if LOCAL_OUTPUT:
            if os.path.exists(filename):
                html_output = remove_html_footer(filename)  
                html_output_verbose = remove_html_footer(filename_verbose)
                html_output_full_verbose = remove_html_footer(filename_full_verbose)
            else:
                html_output = generate_html_header(chat_metadata)
                html_output_verbose = generate_html_header(chat_metadata)
                html_output_full_verbose = generate_html_header(chat_metadata)

            # Step 3: Inject the user unread messages into the HTML output
            unread_messages_html = []
            for message in crew_variables["unread_messages"]:
                unread_messages_html.append(generate_user_message(message['message'], message['timestamp']))

            html_output += "\n".join(unread_messages_html)
            html_output_verbose += "\n".join(unread_messages_html)
            html_output_full_verbose += "\n".join(unread_messages_html)

            # Step 3.1: Insert the variables used into the HTML output
            html_output_verbose += generate_input_message(
                "Ingredients", 
                {
                    "recent_messages": crew_variables["recent_messages"],
                    "unread_messages": crew_variables["unread_messages"],
                    "active_flow_mapping": crew_variables["active_flow_mapping"]
                }, 
                timezone.now()
            )

        # Step 4: Get the output from the intent analysis crew
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/intent_analysis_crew.yml"
        intent_analysis_output, intent_analysis_task_output, intent_analysis_task_full_dicts, final_task_output_index = run_crew_team(crew_variables, yaml_path, seed_number=seed_number)
        update_crew_team_table(intent_analysis_task_full_dicts, queue_id, yaml_path, redteam_run, schema_name, final_task_output_index, conversation_id)

        if LOCAL_OUTPUT:
            # Step 5: Append the intent analysis output to the HTML output
            html_output_verbose += generate_thought_process(intent_analysis_task_output, timezone.now())
            html_output_verbose += generate_crew_output(intent_analysis_output, timezone.now())
            html_output += generate_crew_output(intent_analysis_output, timezone.now())
            html_output_full_verbose += generate_crew_output(intent_analysis_task_full_dicts, timezone.now())
            html_output_full_verbose += generate_crew_output(intent_analysis_output, timezone.now())

        # Step 6: Convert the output into json
        if isinstance(intent_analysis_output, str):
            intent_analysis_output = json.loads(intent_analysis_output)

        # If intent_analysis_output is a list, get the first element
        if "intents" in intent_analysis_output.keys():
            intent_analysis_output = intent_analysis_output["intents"]
        else:
            intent_analysis_output = [intent_analysis_output]

        crew_variables["stage_execution_output"] = []
        for intent in intent_analysis_output:
            # Step 7: Get the exact stage node from the flow mapping
            stage_node = get_stage_node(
                crew_variables["active_flow_mapping"], intent
            )
            following_nodes = get_following_nodes(intent, crew_variables["active_flow_mapping"])
            crew_variables["stage_node"] = stage_node
            crew_variables["following_nodes"] = following_nodes

            # If the context exists in intent, add it to the crew variables
            if "context" in intent:
                crew_variables["context"] = intent["context"]

            if LOCAL_OUTPUT:
                # Step 7.1: Insert the inputs into the crew variables
                html_output_verbose += generate_input_message("Ingredients", crew_variables["stage_node"], timezone.now())
                html_output_full_verbose += generate_input_message("Ingredients", crew_variables["stage_node"], timezone.now())

            # Step 7.2: Get the bot setting for response
            bot_settings = get_bot_settings(schema_name, bot_type="external")
            crew_variables["bot_name"] = bot_settings["chatbot_name"]
            del bot_settings["chatbot_name"]
            crew_variables["bot_settings"] = bot_settings

            # remove all recent message from crew variable if should_reset_context is true (only for mj wines)
            should_reset_context = intent.get("context", {}).get("should_reset_context")
            if isinstance(should_reset_context, str):
                should_reset_context = should_reset_context.lower() == 'true'
            elif not isinstance(should_reset_context, bool):
                should_reset_context = False
            if should_reset_context and schema_name == 'mj_wines':
                crew_variables["recent_messages"] = []
                conversation_instance = Conversation.objects.get(id=conversation_id)
                message_instance = Message.objects.get(id=message_ids[0])
                metadata = conversation_instance.metadata
                metadata["reset_context_timestamp"] = timezone.localtime(message_instance.created_at, pytz.timezone("Asia/Singapore")).strftime("%Y-%m-%d %H:%M:%S")
                conversation_instance.metadata = metadata
                conversation_instance.save(update_fields=["metadata"])


            # Step 8: Get the output from the Stage Execution Crew
            yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/stage_execution_crew.yml"
            stage_execution_output, stage_execution_task_output, stage_execution_task_full_dicts, final_task_output_index = run_crew_team(crew_variables, yaml_path, seed_number=seed_number)
            update_crew_team_table(stage_execution_task_full_dicts, queue_id, yaml_path, redteam_run, schema_name, final_task_output_index, conversation_id)

            if LOCAL_OUTPUT:
                # Step 9: Append the intent analysis output to the HTML output
                html_output_verbose += generate_thought_process(stage_execution_task_output, timezone.now())
                html_output_verbose += generate_crew_output(stage_execution_output, timezone.now())
                html_output += generate_crew_output(stage_execution_output, timezone.now())
                html_output_full_verbose += generate_crew_output(stage_execution_task_full_dicts, timezone.now())
                html_output_full_verbose += generate_crew_output(stage_execution_output, timezone.now())

            # Step 10: Update the crew variables with the stage execution output
            crew_variables["stage_execution_output"].append(stage_execution_output)

            if LOCAL_OUTPUT:
                # Step 10.1: Insert the inputs into the crew variables
                html_output_verbose += generate_input_message("Ingredients", crew_variables["stage_execution_output"], timezone.now())
                html_output_full_verbose += generate_input_message("Ingredients", crew_variables["stage_execution_output"], timezone.now())

        # Step 11: Get the output from the response generation crew
        yaml_path = f"app/crew_teams/agents_and_tasks/{schema_name}/response_generation_crew.yml"
        response_output, response_task_output, response_task_full_dicts, final_task_output_index = run_crew_team(crew_variables, yaml_path, seed_number=seed_number)
        update_crew_team_table(response_task_full_dicts, queue_id, yaml_path, redteam_run, schema_name, final_task_output_index, conversation_id)

        if LOCAL_OUTPUT:
            # Step 12: Append the response generation output to the HTML output
            html_output_verbose += generate_thought_process(response_task_output, timezone.now())
            html_output_verbose += generate_final_bot_response(response_output, timezone.now())
            html_output_verbose += generate_final_bot_response_trimmed(response_output["bot_response"], timezone.now())
            html_output += generate_final_bot_response_trimmed(response_output["bot_response"], timezone.now())
            html_output_full_verbose += generate_crew_output(response_task_full_dicts, timezone.now())
            html_output_full_verbose += generate_final_bot_response(response_output, timezone.now())
            html_output_full_verbose += generate_final_bot_response_trimmed(response_output["bot_response"], timezone.now())
        
        # Step 13: Convert the response output into json
        if isinstance(response_output, str):
            response_output = json.loads(response_output)
        crew_variables["response_output"] = response_output
        crew_variables["bot_response"] = response_output["bot_response"]

        # Step 14: Decide if the conversation needs a reply.
        yaml_path = "app/crew_teams/agents_and_tasks/common/bot_response_checker.yml"
        bot_response_checker_output, bot_response_checker_task_output, bot_response_checker_task_full_dicts, final_task_output_index = run_crew_team(crew_variables, yaml_path, seed_number=seed_number)
        update_crew_team_table(bot_response_checker_task_full_dicts, queue_id, yaml_path, redteam_run, schema_name, final_task_output_index, conversation_id)

        if LOCAL_OUTPUT:
            # Step 15: Append the response generation output to the HTML output
            html_output_verbose += generate_thought_process(bot_response_checker_task_output, timezone.now())
            html_output_verbose += generate_final_bot_response(bot_response_checker_output, timezone.now())
            html_output_verbose += generate_final_bot_response_trimmed(bot_response_checker_output, timezone.now())
            html_output += generate_final_bot_response_trimmed(bot_response_checker_output, timezone.now())
            html_output_full_verbose += generate_crew_output(bot_response_checker_task_full_dicts, timezone.now())
            html_output_full_verbose += generate_final_bot_response(bot_response_checker_output, timezone.now())
            html_output_full_verbose += generate_final_bot_response_trimmed(bot_response_checker_output, timezone.now())

        # Step 16: Convert the response output into json
        if isinstance(bot_response_checker_output, str):
            response_output = json.loads(bot_response_checker_output)

        # If the bot_response_checker_output is True, then the conversation needs a reply. Else, no reply. Will overwrite bot response to None
        # if str(bot_response_checker_output.get("is_reply_required", True)).lower() == "false":
        #     response_output["bot_response"] = [""]

        # Step 17: Check the conversation metadata
        yaml_path = "app/crew_teams/agents_and_tasks/common/chat_metadata_retriever.yml"
        chat_metadata_output, chat_metadata_task_output, chat_metadata_task_full_dicts, final_task_output_index = run_crew_team(crew_variables, yaml_path, seed_number=seed_number)
        update_crew_team_table(chat_metadata_task_full_dicts, queue_id, yaml_path, redteam_run, schema_name, final_task_output_index, conversation_id)

        if LOCAL_OUTPUT:
            # Step 18: Append the response generation output to the HTML output
            html_output_verbose += generate_thought_process(chat_metadata_task_output, timezone.now())
            html_output_verbose += generate_final_bot_response(chat_metadata_output, timezone.now())
            html_output_verbose += generate_final_bot_response_trimmed(chat_metadata_output, timezone.now())
            html_output += generate_final_bot_response_trimmed(chat_metadata_output, timezone.now())
            html_output_full_verbose += generate_crew_output(chat_metadata_task_full_dicts, timezone.now())
            html_output_full_verbose += generate_final_bot_response(chat_metadata_output, timezone.now())
            html_output_full_verbose += generate_final_bot_response_trimmed(chat_metadata_output, timezone.now())

        # Step 19: Convert the response output into json
        if isinstance(chat_metadata_output, str):
            chat_metadata_output = json.loads(chat_metadata_output)

        # Prepare state information
        bot_message = response_output["bot_response"]
        state_info = { 
            "context_summary": response_output.get("context_summary", ""),
            "decision_rationale": response_output.get("decision_rationale", ""),
            "additional_context": response_output.get("additional_context", {}),
            "is_completed": response_output.get("is_completed", None),
        }        
        
        return {
            "bot_message": bot_message,
            "state_info": state_info,
            "chat_metadata": chat_metadata_output.get("metadata", {})
        }

    except Exception as e:
        if LOCAL_OUTPUT:
            # Output the error into the html
            html_output_verbose += generate_error_message(str(e), timezone.now())
            html_output += generate_error_message(str(e), timezone.now())
            html_output_full_verbose += generate_error_message(str(e), timezone.now())
            
        logger.error(f"Error in chat engine: {str(e)}")
        raise

    finally:
        if LOCAL_OUTPUT:
            # Step 20: Update the HTML footer
            html_output += generate_html_footer()
            html_output_verbose += generate_html_footer()
            # Step 21: Write the HTML output to a file
            write_to_html_file(html_output, filename=f"{schema_name}_{conversation_id}_conversation_log.html")
            write_to_html_file(html_output_verbose, filename=f"{schema_name}_{conversation_id}_conversation_log_verbose.html")
            write_to_html_file(html_output_full_verbose, filename=f"{schema_name}_{conversation_id}_conversation_log_full_verbose.html")
