# Generated by Django 4.2.5 on 2023-12-06 09:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0015_conversation_is_auto_reply'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat_action_tracker', '0007_alter_appointment_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='appointment',
            name='status',
            field=models.CharField(choices=[('Confirmed', 'Confirmed'), ('Canceled', 'Canceled')], default='Confirmed', max_length=31),
        ),
        migrations.CreateModel(
            name='MissingInfoAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
                ('question', models.CharField(blank=True, max_length=8912)),
                ('status', models.CharField(choices=[('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_list_actions', to='chat.conversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('message', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='missing_info_actions', to='chat.message')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CallListAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912)),
                ('contact', models.CharField(blank=True, max_length=8912)),
                ('email', models.CharField(blank=True, max_length=8912)),
                ('context', models.CharField(blank=True, max_length=8912)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_list_action_conversation', to='chat.conversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('message', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='call_list_action_message', to='chat.message')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
