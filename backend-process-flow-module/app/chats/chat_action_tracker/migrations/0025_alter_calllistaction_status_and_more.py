# Generated by Django 4.2.5 on 2024-06-11 06:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat_action_tracker', '0024_messagetosend_target_source'),
    ]

    operations = [
        migrations.AlterField(
            model_name='calllistaction',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Confirmed', 'Confirmed'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.AlterField(
            model_name='customchatactiontracker',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Confirmed', 'Confirmed'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.Alter<PERSON>ield(
            model_name='customersupport',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Confirmed', 'Confirmed'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.AlterField(
            model_name='missinginfoaction',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Confirmed', 'Confirmed'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
    ]
