# Generated by Django 4.2.5 on 2024-08-05 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat_action_tracker', '0025_alter_calllistaction_status_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageToSendAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(blank=True, max_length=8912, null=True, upload_to='message_to_send_attachments/')),
                ('attachment_type', models.CharField(choices=[('image', 'Image'), ('video', 'Video'), ('document', 'Document'), ('unknown', 'Unknown')], default='image', max_length=10)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('message_to_send', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='chat_action_tracker.messagetosend')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
