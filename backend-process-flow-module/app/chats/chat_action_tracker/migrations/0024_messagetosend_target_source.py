# Generated by Django 4.2.5 on 2024-06-05 07:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat_action_tracker', '0023_messagetosend_message_type_messagetosend_uu_message_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='messagetosend',
            name='target_source',
            field=models.CharField(choices=[('Web Application', 'Web Application'), ('Floating Chat', 'Floating Chat'), ('WhatsApp', 'WhatsApp'), ('WhatsApp QR', 'WhatsApp QR'), ('WeChat', 'WeChat'), ('Telegram', 'Telegram'), ('Instagram', 'Instagram'), ('Facebook', 'Facebook'), ('Shopify', 'Shopify'), ('Wix', 'Wix'), ('WooCommerce', 'WooCommerce')], default='WhatsApp QR', max_length=63),
        ),
    ]
