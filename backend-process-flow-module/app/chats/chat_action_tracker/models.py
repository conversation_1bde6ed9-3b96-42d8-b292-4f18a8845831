import json
import random
import string
from collections import OrderedDict

from django.db import models
from django.utils import timezone

import app.chats.chat.models as chat_models
import app.chats.chat_action.models as chat_action_models
import app.utility.general_helper as utility_general_helper
import app.utility.models as utility_models


# Create your models here.
class Appointment(utility_models.BaseModel):
    name = models.CharField(max_length=255, blank=True)
    email = models.CharField(max_length=255, blank=True)
    contact = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    location = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  ##legacy > no use
    datetime_legacy = models.Char<PERSON>ield(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # no use, previous version
    datetime = models.DateTimeField(blank=True, null=True)
    status = models.Char<PERSON><PERSON>(
        choices=utility_models.APPOINTMENT_STATUS_CHOICES,
        max_length=31,
        default=utility_models.PENDING,
    )
    remarks = models.CharField(  # Remarks for status
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    appointment_chat_feature_location = models.ForeignKey(
        chat_action_models.AppointmentChatFeatureLocation,
        related_name="appointments",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    conversation = models.ForeignKey(  # change to foreign key as it's possible to have multiple appointment in 1 conversation. Gap will depends on the time
        chat_models.Conversation,
        related_name="appointments",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    message = models.OneToOneField(  # 1 message is only associated to 1 appointment
        chat_models.Message,
        related_name="appointment",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    code = models.CharField(
        max_length=6, unique=True, blank=True, null=True
    )  # code to be used to redirect admin to say yes/no

    def save(self, *args, **kwargs):
        # Generate code only if this is a new object
        if not self.pk and not self.code:
            self.code = self.generate_unique_code()
        super(Appointment, self).save(*args, **kwargs)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.id}. {self.name} -> {self.email} -> {self.location} -> {self.datetime} -> {self.status}"

    @staticmethod
    def generate_unique_code():
        """Generate unique 6-digits alphanumeric code"""
        while True:
            code = "".join(random.choices(string.ascii_lowercase + string.digits, k=6))
            if not Appointment.objects.filter(code=code).exists():
                return code


class CustomerSupport(utility_models.BaseModel):
    name = models.CharField(max_length=255, blank=True)
    contact = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    complaint_description = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    email = models.CharField(max_length=255, blank=True)
    invoice_number = models.CharField(max_length=255, blank=True)
    conversation = models.ForeignKey(  # change to foreign key as it's possible to have multiple customer support in 1 conversation especially in fb. Gap will depends on the time
        chat_models.Conversation,
        related_name="customer_supports",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    message = models.OneToOneField(  # 1 message is only associated to 1 customer support instance
        chat_models.Message,
        related_name="customer_support",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.ACTION_STATUS_CHOICES,
        default=utility_models.PENDING,
    )
    remarks = models.CharField(  # Remarks for status
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return (
            f"{self.id}. {self.email} & {self.contact} -> {self.complaint_description}"
        )


class CallListAction(utility_models.BaseModel):
    """
    For admin to contact the customer for whatever reasons.
    """

    message = models.OneToOneField(
        chat_models.Message,
        on_delete=models.CASCADE,
        related_name="call_list_action",
    )
    conversation = models.ForeignKey(
        chat_models.Conversation,
        on_delete=models.CASCADE,
        related_name="call_list_actions",
    )
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.ACTION_STATUS_CHOICES,
        default=utility_models.PENDING,
    )
    remarks = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # Remarks for status
    name = models.CharField(max_length=255, blank=True)
    contact = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    email = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    context = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )


class MissingInfoAction(utility_models.BaseModel):
    message = models.OneToOneField(
        chat_models.Message,
        on_delete=models.CASCADE,
        related_name="missing_info_action",
    )
    conversation = models.ForeignKey(
        chat_models.Conversation,
        on_delete=models.CASCADE,
        related_name="missing_info_actions",
    )
    question = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.ACTION_STATUS_CHOICES,
        default=utility_models.PENDING,
    )
    remarks = models.CharField(  # Remarks for status
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    reason = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )


class MessageToSend(utility_models.BaseModel):
    appointment = models.ForeignKey(
        Appointment,
        related_name="messages_to_send",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    uu_message = models.OneToOneField(
        chat_models.Message,
        related_name="message_to_send",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )  # to track whether the msg sent or not on uu msg lvl
    target_source = models.CharField(
        max_length=63,
        choices=utility_models.CONVERSATION_SOURCE_CHOICES,
        default=utility_models.WHATSAPP_QR,
    )
    contact = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # whatsapp identifier, "|" separated for multiple
    message = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    message_type = models.CharField(
        max_length=10,
        choices=utility_models.MESSAGE_TO_SEND_TYPES,
        default=utility_models.MACHINE_TEXT,
    )
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.MESSAGE_TO_SEND_STATUS_CHOICES,
        default=utility_models.PENDING,
    )
    to_be_sent_at = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.to_be_sent_at}-{self.status}-{self.message} (created_at: {self.created_at})"

    class Meta:
        ordering = ["-to_be_sent_at"]  # latest


class MessageToSendAttachment(utility_models.BaseModel):
    message_to_send = models.ForeignKey(
        MessageToSend,
        related_name="attachments",
        on_delete=models.CASCADE,
    )

    file = models.FileField(
        upload_to="message_to_send_attachments/",
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        null=True,
        blank=True,
    )
    attachment_type = models.CharField(
        max_length=10,
        choices=utility_models.ATTACHMENT_TYPES,
        default=utility_models.IMAGE,
    )

    def __str__(self):
        return self.file.url


class CustomChatActionTracker(utility_models.BaseModel):
    tracker_identifier = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True)
    message = models.OneToOneField(
        chat_models.Message,
        related_name="custom_action_trackers",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    conversation = models.ForeignKey(
        chat_models.Conversation,
        related_name="custom_action_trackers",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.ACTION_STATUS_CHOICES,
        default=utility_models.PENDING,
    )
    remarks = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # Remarks for status
    additional_fields = models.TextField(blank=True)
    custom_chat_action_setting = models.ForeignKey(
        chat_action_models.CustomChatActionSetting,
        related_name="custom_action_trackers",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    def get_fixed_keys(self):
        fixed_keys = [
            field.name
            for field in self._meta.get_fields()
            if field.name
            not in [
                "additional_fields",
                "custom_chat_action_setting",
                "created_by",
                "updated_by",
                "created_at",
                "updated_at",
            ]
        ]

        # Add created_at and updated_at back to the end of the list
        if "created_at" in [field.name for field in self._meta.get_fields()]:
            fixed_keys.append("created_at")
        if "updated_at" in [field.name for field in self._meta.get_fields()]:
            fixed_keys.append("updated_at")

        return fixed_keys

    def get_extra_keys(self):
        fixed_keys = self.get_fixed_keys()
        extra_keys = []
        if self.additional_fields:
            extra_keys = list(
                json.loads(self.additional_fields, object_pairs_hook=OrderedDict).keys()
            )

        # remove item that existed in fixed_keys
        extra_keys = [key for key in extra_keys if key not in fixed_keys]
        return extra_keys

    def get_field_names(self):
        fixed_keys = self.get_fixed_keys()
        extra_keys = self.get_extra_keys()

        field_names = extra_keys + fixed_keys

        return field_names

    def get_field_labels(self):
        field_names = self.get_field_names()
        # convert them from snake case to Title cased
        field_labels = [
            utility_general_helper.convert_to_title_case(field_name)
            for field_name in field_names
        ]
        return field_labels

    def get_field_items(self):
        field_names = self.get_field_names()
        additional_field_json = {}
        if self.additional_fields:
            additional_field_json = json.loads(
                self.additional_fields, object_pairs_hook=OrderedDict
            )

        field_items = {
            key: (
                str(
                    getattr(
                        self,
                        key,
                        additional_field_json.get(key, ""),
                    )
                )
                if getattr(
                    self,
                    key,
                    additional_field_json.get(key, ""),
                )
                is not None
                else ""
            )
            for key in field_names
        }
        return field_items

    class Meta:
        ordering = ["-id"]  # latest
        
class Calendar(utility_models.BaseModel):
    doctor_id = models.CharField(max_length=255, blank=True)
    name = models.CharField(max_length=255, blank=True)
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    location = models.CharField(max_length=255, blank=True)
    
class Blacklist(utility_models.BaseModel):
    nric = models.CharField(max_length=255, blank=True)
    name = models.CharField(max_length=255, blank=True)
    count = models.IntegerField(default=0)
    reason = models.CharField(max_length=255, blank=True)
    status = models.CharField(max_length=255, blank=True)