from rest_framework import serializers

from app.chats.bot.models import BotSetting


class BotSettingSerializer(serializers.ModelSerializer):
    """Serializer for BotSetting model"""

    class Meta:
        model = BotSetting
        fields = [
            'tone',
            'word_per_chat_bubble', 
            'sentence_count',
            'general_inquiry_guide',
            'guideline_char_limit',
            'chatbot_name'
        ]
