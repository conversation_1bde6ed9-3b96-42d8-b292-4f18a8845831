# Generated by Django 4.2.5 on 2023-10-20 07:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bot', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='botsetting',
            name='chatbot_name',
            field=models.CharField(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='botsetting',
            name='color',
            field=models.CharField(blank=True, max_length=15),
        ),
        migrations.AddField(
            model_name='botsetting',
            name='english_type',
            field=models.CharField(choices=[('English', 'English'), ('Singlish', 'Singlish')], default='English', max_length=50),
        ),
        migrations.AddField(
            model_name='botsetting',
            name='image',
            field=models.FileField(blank=True, max_length=8912, null=True, upload_to=''),
        ),
        migrations.AddField(
            model_name='botsetting',
            name='is_show_branding',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='botsetting',
            name='questions',
            field=models.CharField(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='botsetting',
            name='title',
            field=models.CharField(blank=True, max_length=8912),
        ),
        migrations.AlterField(
            model_name='botsetting',
            name='bot_type',
            field=models.CharField(choices=[('internal', 'Internal'), ('external', 'External')], max_length=50),
        ),
        migrations.AlterField(
            model_name='botsetting',
            name='tone',
            field=models.CharField(default='clear, concise, neutral manner, maintaining consistency, politeness, friendly', max_length=8912),
        ),
    ]
