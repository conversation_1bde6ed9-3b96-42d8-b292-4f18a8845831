from django.db import models
from app.utility import models as utility_models


class Event(utility_models.NamedBaseModel):
    funnel_id = models.CharField(max_length=255, blank=True)
    form_id = models.CharField(max_length=255, blank=True)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return self.name
