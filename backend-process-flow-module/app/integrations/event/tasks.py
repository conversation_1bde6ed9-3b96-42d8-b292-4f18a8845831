import json
import logging
import re
import traceback
import pytz
import requests
import time
import jwt
import requests

from tenant_schemas.utils import schema_context
from bs4 import BeautifulSoup
from datetime import datetime
from celery import shared_task
from openai import OpenAI
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Max

from chat_action_tracker.nova_servicing_tasks import is_production
from event.models import Event
from integration.models import GoHighLevelConnection
from llm_action.models import CustomLLMAction
from utility.chroma_db_helper import OPENAI_API_KEY

from authapi.helper import send_user_email_with_html_template, encrypt_data, decrypt_data
from backend.settings import DEFAULT_FROM_EMAIL

from datetime import datetime, timedelta

import os

from chat_action_tracker.models import Blacklist, MessageToSend

import sys

from django.utils import timezone

logger = logging.getLogger(__name__)

def remove_special_characters(input_string):
    # Use regex to replace any character that is not a word character (a-z, A-Z, 0-9, _) or space
    cleaned_string = re.sub(r'[^\w\s]', '', input_string)
    return cleaned_string

def scrape_page(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}
    
    response = requests.get(url, headers=headers)
    response.raise_for_status()  # Ensure we notice bad responses

    soup = BeautifulSoup(response.content, 'html.parser')

    # Targeting the specific div with class 'preview-container'
    main_content = soup.find('div', {'id': 'preview-container'})
    
    information = ""

    if main_content:
        # Extracting all relevant text content
        text_content = main_content.get_text(separator="\n", strip=True)
        information = text_content
    else:
        logger.info("No content found.")
        
    return information

def clean_up_prompt(prompt):
    # left strip all spaces in front of each line
    return re.sub(r"^[^\S\r\n]+", "", prompt, flags=re.MULTILINE).strip()
    
def get_openai_completion(prompt, model="gpt-4o", format=None, max_retries:int = 5):
    
    client = OpenAI(
        api_key=OPENAI_API_KEY
    )
    
    retries = 0
    while retries < max_retries:
        try:

            if format:
                # Call the OpenAI API
                response = client.chat.completions.create(
                    model=model,  # Or any other engine you prefer
                    messages=prompt,
                    temperature=0,
                    response_format=format
                )
                completion_text = response.choices[0].message.content
            else:
                # Call the OpenAI API
                response = client.chat.completions.create(
                    model=model,  # Or any other engine you prefer
                    messages=prompt,
                    temperature=0
                )
                completion_text = str(response.choices[0].message.content).strip()

            return completion_text
        
        except Exception as e:
            logger.info(e)
            logger.info(f"Request timed out. Retrying... {retries + 1}/{max_retries}")
            retries += 1
            time.sleep(retries * 2)
    
    # Need to raise an exception here
    raise Exception("Request to Openai server timed out")

def extract_event_metadata(information, url, event_name):
    prompt = [
        {
            "role": "system",
            "content": """
            You are an information extraction model specialized in identifying key details from detailed texts about events.

            [Specific Information] 
            You will be provided with a text about an event. Extract the following key details:
            1. **Event Date**: The specific date when the event will take place.
            2. **Event Time**: The start and end time of the event.
            3. **Event Location**: The venue, city, or address where the event will occur. Specify if the event is online or in-person.
            4. **Event Duration**: The total length of time the event will last.
            5. **Event Description**: A comprehensive summary or overview of what the event is about.
            6. **Event Capacity**: The maximum number of participants or attendees allowed.
            7. **Speakers Information**: Details about the speakers, including their names, titles, and brief descriptions of their roles.
            8. **Contact Information**: How to reach the event organizers, including email addresses, phone numbers, or websites.
            9. **Event Schedule**: The specific sessions, their times, and descriptions of each session.
            10. **Additional Benefits**: Any extra perks or benefits provided by attending the event.

            [Intent] 
            Ensure that all extracted information is as detailed and complete as possible.

            [Response Format] 
            Provide the extracted information in a summarized and structured format.
            """
        },
        {
            "role": "user",
            "content": f"Please extract the required information from the following text: {information}"
        }
    ]

    
    response = get_openai_completion(prompt=prompt, model="gpt-4o-mini")

    # Print the response
    if response:
        url = f" **Event Url**: {url}"
        event_name = f" **Event Name**: {event_name}"
        result = response + url + event_name
        return result.replace("\n", "")
    else:
        logger.info("Failed to get a response from beefy model.")
    
    
def get_event_list(location_id, access_token):
    bearer_token = access_token
    
    url = "https://services.leadconnectorhq.com/funnels/funnel/list"

    querystring = {"locationId": location_id}

    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Accept": "application/json",
        "Version": "2021-07-28"
    }

    response = requests.get(url, headers=headers, params=querystring)

    return response.json()


def get_form_id(location_id, event_name, access_token):
    bearer_token = access_token

    url = "https://services.leadconnectorhq.com/forms/"
    
    querystring = {"locationId": location_id}

    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Accept": "application/json",
        "Version": "2021-07-28"
    }

    response = requests.get(url, headers=headers, params=querystring)
    
    form_array = response.json().get("forms")
    
    # Default FORM ID will be empty string
    form_id = ""
    
    # To match form to an event
    # event_name = 31 Jul 2024 - Cybersecurity Live Demo Event
    # form_name = 31 Jul 2024 - Cybersecurity Live Demo Event Form
    expected_form_name = event_name + " Form"
    for form in form_array:
        if form.get("name") == expected_form_name:
            form_id = form.get("id")
            break

    return form_id


def extract_event_name(funnel_name):
    parts = funnel_name.split("-")
    if len(parts) > 1:
        return parts[1].strip()
    return funnel_name

def extract_event_date(funnel_name):
    parts = funnel_name.split("-")
    if len(parts) > 1:
        return parts[0].strip()
    return funnel_name

# Function to convert the formatted date string back to a datetime object
def convert_to_datetime(formatted_date):
    if formatted_date:
        date_obj = datetime.strptime(formatted_date, '%d %B %Y')
        return date_obj
    return None

def get_event_active_status(event_date):
    today_datetime = datetime.now(pytz.UTC)
    gmt_plus_8 = pytz.timezone('Asia/Singapore')
    today_date = today_datetime.astimezone(gmt_plus_8).date()
    
    if today_date >= event_date.date():
        return False
    else:
        return True
    
def scrape_input_fields(url):
    # Send a GET request to the URL
    response = requests.get(url)
    
    # Check if the request was successful
    if response.status_code == 200:
        # Parse the content with BeautifulSoup
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Initialize a list to hold input field information
        input_fields = []
        
        # Extract all form-field-container elements
        field_containers = soup.find_all('div', class_='form-field-container')
        for container in field_containers:
            label = container.find('label')
            label_text = label.get_text(strip=True) if label else None
            
            # Extract all input fields
            inputs = container.find_all('input')
            for input_field in inputs:
                field_info = {
                    'type': input_field.get('type'),
                    'name': input_field.get('name'),
                    'required': input_field.get('data-required') == 'true',
                    'label': label_text
                }
                # Check if the input has the class 'multiselect__input'
                if 'multiselect__input' in input_field.get('class', []):
                    # Find all options within the same parent element
                    parent = input_field.find_parent('div', class_='multiselect')
                    options = [option.get_text() for option in parent.find_all('span', class_='multiselect__option') if option.find_parent('li').get('style') != 'display:none;']
                    field_info['options'] = options
                
                input_fields.append(field_info)
            
            # Extract all select fields
            selects = container.find_all('select')
            for select_field in selects:
                options = [option.get_text() for option in select_field.find_all('option')]
                field_info = {
                    'type': 'select',
                    'name': select_field.get('name'),
                    'options': options,
                    'required': select_field.get('data-required') == 'true',
                    'label': label_text
                }
                input_fields.append(field_info)
            
            # Extract all textarea fields
            textareas = container.find_all('textarea')
            for textarea_field in textareas:
                field_info = {
                    'type': 'textarea',
                    'name': textarea_field.get('name'),
                    'required': textarea_field.get('data-required') == 'true',
                    'label': label_text
                }
                input_fields.append(field_info)
        
        return input_fields
    else:
        return None

def generate_register_event_function(fields, func_name, form_id):
    # Start of the function definition
    func_code = f"def register_{func_name}("

    # Add parameters to the function signature
    params = ', '.join([f['label'] for f in fields]).lower()
    func_code += params + "):\n    "
    
    validate_event_name = f"validate_{func_name}"
    
    # Generating dynamic checks for the fields and preparing parameters for validation function
    labels = [f"{f['label']}" for f in fields]
    labels_condition = " or ".join([f"not {label}" for label in labels]).lower()
    labels_args = ", ".join([f"{label}" for label in labels]).lower()
    
    if 'phone' in labels:
        phone_str = f"""
            if phone:
                if phone.startswith(('6', '8', '9')) and re.match(r"^[689]\d{{7}}$", phone):
                    phone = '+65' + phone
                elif phone.startswith('0') and re.match(r"^0\d{{9,11}}$", phone):
                    phone = '+60' + phone[1:]
                else:
                    import phonenumbers
                    if not phone.startswith('+'):
                        phone = '+' + phone
                    
                    try:
                        from phonenumbers import NumberParseException
                        
                        # validate is it valid
                        parsed_number = phonenumbers.parse(phone, None)
                        
                        if not phonenumbers.is_valid_number(parsed_number):
                            output = utility_general_helper.clean_up_prompt(
                                "Tell the customer that the phone number seems to be invalid after verification, ask the customer to provide a valid phone number SO THAT YOU CAN REGISTER THEM TO THE EVENT."
                            )
                            
                            return {{
                                "status": llm_action_constant.ACTION_FAILED,
                                "output": output
                            }}
                    except NumberParseException as e:
                        output = utility_general_helper.clean_up_prompt(
                            "Tell the customer that the phone number seems to be invalid after verification, ask the customer to provide a valid phone number SO THAT YOU CAN REGISTER THEM TO THE EVENT."
                        )
                        
                        return {{
                            "status": llm_action_constant.ACTION_FAILED,
                            "output": output
                        }}
        """
    else:
        phone_str = ""

    if 'email' in labels:
        email_str = f"""
            if email:
                response = verify_email_address(email)
                response_data = response.json()
                if response_data.get('statusCode', 400) == 200:
                    if response_data.get('body',{{}}).get('is_valid', False) == False:
                        output = utility_general_helper.clean_up_prompt(
                            f"This is the EMAIL ADDRESS that your customer provided: {{email}}. You MUST tell the customer that the EMAIL ADDRESS that they provided is unable to use to register for the event, ask the customer to provide another EMAIL ADDRESS."
                        )
                        return {{
                            "status": llm_action_constant.ACTION_FAILED,
                            "output": output
                        }}
                else:
                    output = utility_general_helper.clean_up_prompt(
                        "There was an error verifying the email address. Please try again later."
                    )
                    return {{
                        "status": llm_action_constant.ACTION_FAILED,
                        "output": output
                    }}
        """
    else:
        email_str = ""
        
    # Add function body
    func_body = f"""
        import time
        import os
        import pytz
        import re
        
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        from django.utils import timezone
        from event.models import Event
        
        from webdriver_manager.chrome import ChromeDriverManager
        from webdriver_manager.core.utils import ChromeType
        
        import utility.general_helper as utility_general_helper
        import llm_action.constant as llm_action_constant
        
        current_date = timezone.now().astimezone(pytz.timezone('Asia/Singapore')).date()
        event = Event.objects.get(form_id='{form_id}')
        
        try:
            if current_date >= event.start_date.date():
                output = utility_general_helper.clean_up_prompt(
                    "Tell the customer that the registration for the event has closed. Thanks for their interest."
                )
            
                return {{
                    "status": llm_action_constant.ACTION_COMPLETED,
                    "output": output
                }}
        
            {phone_str}
            
            {email_str}
            
            if {labels_condition}:
                return {validate_event_name}({labels_args})
                
            url = 'https://api.leadconnectorhq.com/widget/form/{form_id}'
            
            # Set up Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--use-fake-ui-for-media-stream')
            chrome_options.add_argument('--disable-infobars')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--incognito')
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--window-size=1440,934')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-webrtc')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36')

            # Add experimental options
            prefs = {{
                "profile.default_content_setting_values.media_stream_mic": 1,
                "profile.default_content_setting_values.media_stream_camera": 1,
                "profile.default_content_setting_values.geolocation": 0,
                "profile.default_content_setting_values.notifications": 2
            }}
            chrome_options.add_experimental_option("prefs", prefs)

            # Specify the user data directory
            user_data_dir = os.path.join(os.getcwd(), 'chrome_user_data')
            chrome_options.add_argument(f'--user-data-dir={{user_data_dir}}')

            # Set up the WebDriver with Chrome options and specified version
            driver = webdriver.Chrome(
                service=Service(
                    ChromeDriverManager(chrome_type=ChromeType.CHROMIUM).install()
                ),
                options=chrome_options,
            )
            
            # Navigate to the webpage
            driver.get(url)
            
            max_retries = 3  # Maximum number of retries
            attempt = 1

            while attempt <= max_retries:
                try:
                    # Wait for the document to be fully loaded
                    WebDriverWait(driver, 10).until(
                        lambda d: d.execute_script('return document.readyState') == 'complete'
                    )
    """
    
    # Generate dynamic code for each field
    for field in fields:
        func_body += f"""
                    {field['label']}_field = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.NAME, '{field['name']}'))
                    )
                    {field['label']}_field.send_keys({field['label'].lower()})\n
                """
        
        
    func_body += f"""
                    # Wait for the submit button to be clickable and then click it
                    submit_button = WebDriverWait(driver, 20).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']"))
                    )
                    driver.execute_script("arguments[0].click();", submit_button)
                    
                    # Wait for the "Thank You!" message to be present
                    thank_you_message = WebDriverWait(driver, 40).until(
                        EC.presence_of_element_located((By.XPATH, "//div[@id='heading-95mSi9GHXZ-']//h1[strong='Thank You!']"))
                    )

                    if thank_you_message:
                        print("Form submitted successfully and 'Thank You!' message is present.")
                        break  # Exit the loop if successful
                    else:
                        raise Exception("Thank You message not found")
            
                except Exception as e:
                    attempt += 1
                    if attempt > max_retries:
                        print("Max retries reached. Form submission failed.")
                finally:
                    if attempt > max_retries:
                        # Add a delay to observe results before closing
                        time.sleep(10)
                        # Ensure the driver quits after execution
                        driver.quit()
                
            output = utility_general_helper.clean_up_prompt(
                "Tell the customer that we have completed register a seat for them. The event name is {func_name}."
            )
            
            return {{
                "status": llm_action_constant.ACTION_COMPLETED,
                "output": output
            }}
        except Exception as e:
            import traceback
            print(traceback.print_exc())
            return {{
                "status": llm_action_constant.ACTION_FAILED,
                "output": e.args[0]
            }}
    """

    # Complete the function code with a closing line
    func_code += func_body

    # Return the complete function code as a string
    return func_code

def generate_validate_register_event_function(fields, func_name):
    # Start of the function definition
    func_code = f"def validate_{func_name}("

    # Adding parameters based on field labels
    params = ', '.join([f"{f['label']}" for f in fields]).lower()
    func_code += params + "):\n    "
    func_code += '"""\n    To validate if necessary event details are provided.\n    """\n    '

    # Initialize a list to collect missing information
    func_code += "missing_info = []\n    "
    
    # Generate checks for each parameter
    for field in fields:
        current_field = field['label'].lower()
        func_code += f"if not {current_field}:\n        "
        if current_field == 'phone':
            func_code += f"missing_info.append('You do not have the {current_field} (including country code) that the customer is asking for, ask the customer to provide.')\n    "
        else:
            func_code += f"missing_info.append('You do not have the {current_field} that the customer is asking for, ask the customer to provide.')\n    "

    # Generate the error raising and return statement
    func_code += "if missing_info:\n        "
    func_code += "missing_info_str = '\\n'.join(missing_info)\n        "
    func_code += "raise ValueError(f'You need the customer to provide more information in order to help them to register for a seat. Now: \\n{missing_info_str}\\n')\n    "
    func_code += "return {'is_rerun': True}\n"

    return func_code

def populate_event(event_name, form_id):
    event_name = event_name.lower().replace(' ', '_')
    
    # Remove special characters from the event name
    event_name = remove_special_characters(event_name)
    
    # URL of the form page
    url = f'https://api.leadconnectorhq.com/widget/form/{form_id}'
    
    # Scrape the input fields from the page
    input_fields = scrape_input_fields(url)
    
    parameter_list = []
    # Print the extracted input fields
    if input_fields:
        for field in input_fields:
            # print(field)
            label = field['label']
            name = field['name']
            
            if '*' in label:
                label = name
            
            parameter = {
                "label": label,
                "name": name
            }
            
            parameter_list.append(parameter)
    
        # Register Event
        register_event_function = generate_register_event_function(parameter_list, event_name, form_id)
        function_name = f"register_{event_name}"
        description = f"Trigger this whenever the customer want to register for the event {event_name}."
        
        with schema_context("nucleo"):
            max_id = CustomLLMAction.objects.aggregate(Max('id'))['id__max']
            new_id = (max_id or 0) + 1
            llm_action = CustomLLMAction(
                id=new_id,
                name=function_name,
                description=description,
                function_str=register_event_function
            )   
            llm_action.save()
        
        # Validate Register Event
        validate_register_event_function = generate_validate_register_event_function(parameter_list, event_name)
        function_name = f"validate_{event_name}"
        description = f"Trigger this function to obtain customer details whenever the customer want to register for the event {event_name}."
        
        with schema_context("nucleo"):
            max_id = CustomLLMAction.objects.aggregate(Max('id'))['id__max']
            new_id = (max_id or 0) + 1
            llm_action = CustomLLMAction(
                id=new_id,
                name=function_name,
                description=description,
                function_str=validate_register_event_function
            )   
            llm_action.save()

def is_valid_date(date_string):
    try:
        # Define the format that the date_string should match
        datetime.strptime(date_string, "%d %B %Y")
        return True
    except ValueError:
        return False

def check_keywords_in_page(url):
    try:
        
        response = requests.get(url)
        final_url = response.url
    
        if final_url != url:
            return False
        
        return True
        
    except requests.RequestException as e:
        logger.error(f"Error fetching the page: {e}")
        return False
   
def insert_event_data(event_list, access_token, location_id):
    
    for funnel in event_list["funnels"]:
        event_name = extract_event_name(funnel["name"])
        event_date = extract_event_date(funnel["name"])
        
        # Only populate event with correct event date
        if is_valid_date(event_date):
        
            event_date = convert_to_datetime(event_date)
            event_funnel_id = funnel["_id"]
            
            if event_date is not None:
                # Check if funnelID exists in the database
                # with connect_postgres_db() as conn:
                    # event_details = get_event_details(conn, funnel_id=event_funnel_id)
                
                # Event exists in the database
                # if event_details is not None:
                    
                    # db_updated_at = event_details.get("updated_at")
                    # event_updated_at_str = funnel["updatedAt"]
                    # event_updated_at = datetime.strptime(event_updated_at_str, "%Y-%m-%dT%H:%M:%S.%fZ").replace(tzinfo=pytz.utc)

                    # Check if the event has been updated
                #     if event_updated_at > db_updated_at:
                #         # Get form id
                #         form_id = get_form_id(location_id=location_id, event_name=funnel["name"], access_token=access_token)
                        
                #         # Get event active status
                #         is_active = get_event_active_status(event_date=event_date)
                        
                #         # Get event metadata
                #         steps = funnel["steps"][0]
                #         event_url = steps.get("url")
                #         domain = "https://inucleo.com"
                        
                #         url = domain + event_url
                        
                #         information = scrape_page(url)
                #         information = " ".join(information.split())
                #         information = re.sub(r'[^a-zA-Z0-9\s]', '', information)
                #         summarize_event_details = extract_event_metadata(information=information, url=url)
                #         metadata = {
                #             "summary" : summarize_event_details
                #         }
                        
                #         # Update table column
                #         with connect_postgres_db() as conn:
                #             update_event_details(conn, name=event_name, start_date=event_date, metadata=json.dumps(metadata), is_active=is_active, form_id=form_id, funnel_id=event_funnel_id)
                            
                #         # Insert custom llm action
                # else:
                    # Event not exists in the database
                    # Get form id
                    
                # Check is funnel_id exists in database
                
                try:
                    # Try to get the Event object with the specified funnel_id
                    with schema_context("nucleo"):
                        event = Event.objects.get(funnel_id=event_funnel_id)
                except ObjectDoesNotExist:
                    try:
                        # If the event does not exist, proceed to create a new one
                        form_id = get_form_id(location_id=location_id, event_name=funnel["name"], access_token=access_token)
                        if form_id == "":
                            raise ValueError(f"Unable to find form for funnel in GHL: {event_name}")
                        
                        # Get event active status
                        is_active = get_event_active_status(event_date=event_date)
                        
                        # Get event metadata
                        steps = funnel["steps"][0]
                        event_url = steps.get("url")
                        domain = "https://inucleo.com"
                        
                        url = domain + event_url
                        
                        # Make request to ensure it shows event name, if not put www.
                        if not check_keywords_in_page(url):
                            domain = "https://www.inucleo.com"
                            url = domain + event_url
                        
                        information = scrape_page(url)
                        information = " ".join(information.split())
                        information = re.sub(r'[^a-zA-Z0-9\s]', '', information)
                        summarize_event_details = extract_event_metadata(information=information, url=url, event_name=event_name)
                        metadata = {
                            "summary": summarize_event_details
                        }
                        
                        # Insert event into the database
                        with schema_context("nucleo"):
                            event = Event(
                                name=event_name,
                                start_date=event_date,
                                metadata=json.dumps(metadata),
                                is_active=is_active,
                                form_id=form_id,
                                funnel_id=event_funnel_id
                            )
                            event.save()
                        
                        # Insert custom llm action
                        populate_event(event_name, form_id=form_id)
                    except (ValueError, Exception) as e:
                        logger.error(f"Error inserting event data: {traceback.format_exc()}")
            
def update_access_token(connection):
    try:
        # get refresh token
        refresh_token = connection.refresh_token
        url = connection.redirect_uri
        client_id = connection.client_id
        client_secret = connection.client_secret

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }

        body = {
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "user_type": "Location"
        }
        
        response = requests.post(url, headers=headers, data=body)
        response_data = response.json()
        access_token = response_data['access_token']
        refresh_token = response_data['refresh_token']
        location_id = response_data['locationId']
        
       # Update the instance with new values
        with schema_context("nucleo"):
            connection.access_token = access_token
            connection.refresh_token = refresh_token
            connection.location_id = location_id
            connection.save()
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error in update_access_token: {e}")

def decode_token(access_token):
    try:
        decoded_token = jwt.decode(access_token, options={"verify_signature": False})
        return decoded_token
    except jwt.InvalidTokenError:
        return None
    
def is_token_expired(decoded_token):
    if decoded_token is None:
        return True
    exp_timestamp = decoded_token.get("exp")
    if exp_timestamp is None:
        return True
    expiry_date = datetime.fromtimestamp(exp_timestamp)
    return expiry_date < datetime.now()
        
def get_credential():
    try:
        with schema_context("nucleo"):
            connection = GoHighLevelConnection.objects.first()
            
            access_token = connection.access_token
            location_id = connection.location_id
            
            decoded_token = decode_token(access_token) 
            if is_token_expired(decoded_token):
                update_access_token(connection)
                connection.refresh_from_db()
            
                # Get updated Access Token and Location Id
                access_token = connection.access_token
                location_id = connection.location_id

            return access_token, location_id
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error in get_credential: {e}")

@shared_task()
def populate_events_task():
    try:
        logger.info("Starting populate_events_task")
        access_token, location_id = get_credential()
        event_list = get_event_list(location_id=location_id, access_token=access_token)
        insert_event_data(event_list=event_list, access_token=access_token, location_id=location_id)
        logger.info("Finished populate_events_task")
    except Exception as e:
        logger.error(f"Error in populate_events_task: {traceback.format_exc()}")
        
@shared_task()
def notify_upcoming_appointments():
    def fetch_data_from_api(endpoint, max_retries=3):
        bearer_token = os.getenv("BEARER_TOKEN")
        if not bearer_token:
            raise ValueError("Bearer token not found in environment variables.")
        headers = {"Authorization": f"Bearer {bearer_token}"}
        
        for attempt in range(max_retries):
            try:
                response = requests.get(endpoint, headers=headers)
                if response.status_code == 200:
                    json_response = response.json()
                    return {
                        "data": json_response.get("data", []),
                        "pager": json_response.get("pager", {})
                    }
                elif response.status_code == 401:
                    raise ValueError("Access denied. Check if the bearer token is correct.")
                else:
                    raise ValueError(f"Failed to access the endpoint with status code: {response.status_code}.")
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Attempt {attempt + 1} failed. Retrying in 30 seconds...")
                    time.sleep(30)
                else:
                    raise ValueError(f"Failed to access the endpoint after {max_retries} attempts: {str(e)}")
    
    logger.info("Starting getting the upcoming appointments")
    
    one_day_later = datetime.today() + timedelta(days=1)
    one_day_later = one_day_later.strftime("%Y-%m-%d")
    
    one_week_later = datetime.today() + timedelta(days=7)
    one_week_later = one_week_later.strftime("%Y-%m-%d")
    
    logger.info("Starting getting the appointments before one day")
    url_one_day = f"https://api.hb.sgimed.com/openapi/appointment?start_date={one_day_later}&end_date={one_day_later}"
    url_one_week = f"https://api.hb.sgimed.com/openapi/appointment?start_date={one_week_later}&end_date={one_week_later}"
    
    appointments = []
    
    for url in [url_one_day, url_one_week]:
        page = 1
        while True:
            url_with_page = f"{url}&page={page}"
            response = fetch_data_from_api(url_with_page)
            appointments.extend(response['data'])
            
            pager = response['pager']
            if page >= pager.get('pages', 1):
                break
            
            page += 1
    
    logger.info(f"Total number of appointments fetched: {len(appointments)}")
    
    for appointment in appointments:
        patient_details = appointment["patient"]
        if patient_details and "email" in patient_details:
            email = patient_details["email"]
            
            to_emails = ["<EMAIL>"]
            
            for to_email in to_emails:
                # Prepare email details
                from_ = f"UtterUnicorn X Amelio <{DEFAULT_FROM_EMAIL}>"
                html_path = "amelio/notification_template.html"
                subject = "Upcoming Appointment Reminder"
                to_ = [to_email]  # Send to individual email
                
                appointment_date = datetime.strptime(appointment["start_date"][:-6], "%Y-%m-%dT%H:%M:%S")
                appointment_time = datetime.strptime(appointment["start_time"], "%H:%M:%S").time()
                formatted_date_time = f"{appointment_date.strftime('%A, %d %B %Y')} at {appointment_time.strftime('%I:%M %p')}"
                
                custom_kwargs = {
                    "name": patient_details["name"],
                    "appointment_date_time": formatted_date_time
                }
                
                # Send email
                send_user_email_with_html_template(html_path, subject, from_, to_, **custom_kwargs)
            
    logger.info("Finished sending the upcoming appointment reminder")
    
@shared_task()
def get_blacklisted_patient():
    def fetch_data_from_api(endpoint, max_retries=3):
        bearer_token = os.getenv("BEARER_TOKEN")
        if not bearer_token:
            raise ValueError("Bearer token not found in environment variables.")
        headers = {"Authorization": f"Bearer {bearer_token}"}
        
        for attempt in range(max_retries):
            try:
                response = requests.get(endpoint, headers=headers)
                if response.status_code == 200:
                    json_response = response.json()
                    return {
                        "data": json_response.get("data", []),
                        "pager": json_response.get("pager", {})
                    }
                elif response.status_code == 401:
                    raise ValueError("Access denied. Check if the bearer token is correct.")
                else:
                    raise ValueError(f"Failed to access the endpoint with status code: {response.status_code}.")
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Attempt {attempt + 1} failed. Retrying in 30 seconds...")
                    time.sleep(30)
                else:
                    raise ValueError(f"Failed to access the endpoint after {max_retries} attempts: {str(e)}")

    try:
        logger.info("Starting getting yesterday's patient note")
        yesterday = (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        today = datetime.today().strftime("%Y-%m-%d")
        
        url_yesterday = f"https://api.hb.sgimed.com/openapi/appointment?start_date={yesterday}&end_date={yesterday}"
        
        appointments = []
        page = 1
        while True:
            url_with_page = f"{url_yesterday}&page={page}"
            response = fetch_data_from_api(url_with_page)
            appointments.extend(response['data'])
            
            pager = response['pager']
            if page >= pager.get('pages', 1):
                break
            
            page += 1
        
        logger.info("Finished getting yesterday's appointments")
        
        blacklisted_patients = []
        for appointment in appointments:
            # Check if patient exists in the appointment
            if not appointment.get("patient"):
                logger.warning(f"Appointment {appointment.get('id', 'unknown')} has no patient information. Skipping.")
                continue

            patient_id = appointment["patient"]["id"]
            
            invoice_url = f"https://api.hb.sgimed.com/openapi/invoice?patient_id={patient_id}&start_date={yesterday}&end_date={today}&include_drafts=true&include_void=true"
            
            invoices = []
            page = 1
            while True:
                url_with_page = f"{invoice_url}&page={page}"
                response = fetch_data_from_api(url_with_page)
                invoices.extend(response['data'])
                
                pager = response['pager']
                if page >= pager.get('pages', 1):
                    break
                
                page += 1
            
            if invoices:
                # If no invoice found, the patient missed the appointment
                logger.info(f"Patient {patient_id} has missed the appointment.")
                name = appointment["patient"].get("name", "")
                nric = appointment["patient"].get("nric", "")
                reason = "Appointment missed"
                
                # Check patient's spending in the last 180 days
                six_months_ago = (datetime.today() - timedelta(days=180)).strftime("%Y-%m-%d")
                spending_url = f"https://api.hb.sgimed.com/openapi/invoice?patient_id={patient_id}&start_date={six_months_ago}&end_date={today}&include_drafts=true&include_void=true"
                
                spending_invoices = []
                page = 1
                while True:
                    url_with_page = f"{spending_url}&page={page}"
                    response = fetch_data_from_api(url_with_page)
                    spending_invoices.extend(response['data'])
                    
                    pager = response['pager']
                    if page >= pager.get('pages', 1):
                        break
                    
                    page += 1
                
                total_spending = sum(float(invoice.get('total', 0)) for invoice in spending_invoices)
                
                if total_spending <= 5000:
                    try:
                        with schema_context("amelio"):
                            # Try to get the blacklist entry
                            # Decrypt all NRICs in the database for comparison
                            all_blacklists = Blacklist.objects.all()
                            decrypted_nrics = {decrypt_data(entry.nric): entry for entry in all_blacklists}
                            
                            if nric in decrypted_nrics:
                                blacklist_entry = decrypted_nrics[nric]
                                # If found, increment count
                                blacklist_entry.count += 1
                                # If count is three, change status to 1
                                if blacklist_entry.count == 3:
                                    blacklist_entry.status = "1"
                                blacklist_entry.save()
                                logger.info(f"Updated blacklist for patient {decrypt_data(blacklist_entry.name)} (NRIC: {nric}). Count: {blacklist_entry.count}, Status: {blacklist_entry.status}")
                            else:
                                # If not found, create a new blacklist entry
                                blacklist_entry = Blacklist.objects.create(
                                    nric=encrypt_data(nric),
                                    name=encrypt_data(name),
                                    reason=reason,
                                    count=1,
                                    status="0"
                                )
                                logger.info(f"Created new blacklist entry for patient {name} (NRIC: {nric})")
                            decrypted_name = decrypt_data(blacklist_entry.name)
                            masked_name = ' '.join(word[0] + '*' * (len(word) - 1) for word in decrypted_name.split())
                            decrypted_nric = decrypt_data(blacklist_entry.nric)
                            masked_nric = '*' * (len(decrypted_nric) - 3) + decrypted_nric[-3:]
                            blacklisted_patients.append(f"{masked_name} (NRIC: {masked_nric})")
                    except Exception as e:
                        logger.error(f"Error updating/creating blacklist for patient {name} (NRIC: {nric}): {str(e)}")
                else:
                    logger.info(f"Patient {name} (NRIC: {nric}) not blacklisted due to high spending (${total_spending:.2f}) in the last 90 days")
            else:
                # If invoice found, the patient attended the appointment
                logger.info(f"Patient {patient_id} has attended the appointment.")
                name = appointment["patient"].get("name", "")
                nric = appointment["patient"].get("nric", "")
                try:
                    with schema_context("amelio"):
                        blacklist_entry = Blacklist.objects.filter(nric=encrypt_data(nric)).first()
                        if blacklist_entry:
                            blacklist_entry.status = "0"  # Set status to 0 (not blacklisted)
                            blacklist_entry.count = 0  # Reset count
                            blacklist_entry.save()
                            logger.info(f"Updated blacklist for patient {decrypt_data(blacklist_entry.name)} (NRIC: {decrypt_data(blacklist_entry.nric)}). Status set to 0 and count reset.")
                except Exception as e:
                    logger.error(f"Error updating blacklist for patient {name} (NRIC: {nric}): {str(e)}")
        
        if blacklisted_patients:
            # Prepare email details
            from_ = f"UtterUnicorn X Amelio <{DEFAULT_FROM_EMAIL}>"
            html_path = "amelio/success_notification_template.html"
            subject = "Daily Blacklisted Patients Report"
            to_ = ["<EMAIL>"]
            
            custom_kwargs = {
                "function_name": "get_blacklisted_patient",
                "blacklisted_patients": blacklisted_patients,
                "date": yesterday,
                "processed_count": len(blacklisted_patients),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Send email
            send_user_email_with_html_template(html_path, subject, from_, to_, **custom_kwargs)
            
            logger.info("Sent email with blacklisted patients report")
        else:
            logger.info("No blacklisted patients to report")

    except Exception as e:
        error_traceback = traceback.format_exc()
        error_message = f"An error occurred in get_blacklisted_patient:\n{error_traceback}"
        logger.error(error_message)
        
        # Prepare error email details
        from_ = f"UtterUnicorn X Amelio <{DEFAULT_FROM_EMAIL}>"
        html_path = "amelio/error_notification_template.html"
        subject = "Error in Daily Blacklisted Patients Report"
        to_ = ["<EMAIL>"]
        
        custom_kwargs = {
            "function_name": "get_blacklisted_patient",
            "error_message": error_message,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Send error email
        send_user_email_with_html_template(html_path, subject, from_, to_, **custom_kwargs)
        
        logger.info("Sent email with error report including traceback")
        
@shared_task()
def resend_appointment_reminder():
    def fetch_data_from_api(endpoint, max_retries=3):
        bearer_token = os.getenv("BEARER_TOKEN")
        if not bearer_token:
            raise ValueError("Bearer token not found in environment variables.")
        headers = {"Authorization": f"Bearer {bearer_token}"}
        
        for attempt in range(max_retries):
            try:
                response = requests.get(endpoint, headers=headers)
                if response.status_code == 200:
                    json_response = response.json()
                    return json_response
                elif response.status_code == 401:
                    raise ValueError("Access denied. Check if the bearer token is correct.")
                else:
                    raise ValueError(f"Failed to access the endpoint with status code: {response.status_code}.")
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Attempt {attempt + 1} failed. Retrying in 30 seconds...")
                    time.sleep(30)
                else:
                    raise ValueError(f"Failed to access the endpoint after {max_retries} attempts: {str(e)}")
    
    with schema_context("amelio"):  
        messages_to_resend = MessageToSend.objects.filter(
            metadata__is_send_reminder=True,
            metadata__is_resend_reminder=False,
            metadata__is_send_to_staff=False
        ).values('metadata__appointment_id', 'metadata__appointment_date', 'metadata__appointment_time')

        messages_send_to_staff = MessageToSend.objects.filter(
            metadata__is_send_reminder=True,
            metadata__is_resend_reminder=True,
            metadata__is_send_to_staff=False
        ).values('metadata__appointment_id', 'metadata__appointment_date', 'metadata__appointment_time')
        
        print("Messages to resend:", messages_to_resend)
        print("Messages to send to staff:", messages_send_to_staff)

        # Create a set of appointment IDs from messages_send_to_staff
        staff_appointment_ids = set(msg['metadata__appointment_id'] for msg in messages_send_to_staff)

        # Filter out messages_to_resend that are also in messages_send_to_staff
        messages_to_resend = [msg for msg in messages_to_resend if msg['metadata__appointment_id'] not in staff_appointment_ids]

        def process_messages(messages, is_staff=False):
            for message in messages:
                appointment_id = message.get('metadata__appointment_id')
                appointment_url = f"https://api.hb.sgimed.com/openapi/appointment/{appointment_id}"
                response = fetch_data_from_api(appointment_url)
                
                appointment_date_to_check = message['metadata__appointment_date']
                appointment_time_to_check = message['metadata__appointment_time']
                
                if response:
                    appointment = response
                    start_date = appointment["start_date"]
                    start_time = appointment["start_time"]
                    
                    if start_date == appointment_date_to_check and start_time == appointment_time_to_check:
                        patient_details = appointment.get("patient", {})
                        if patient_details and "email" in patient_details:
                            email = "<EMAIL>"
                            from_ = f"UtterUnicorn X Amelio <{DEFAULT_FROM_EMAIL}>"
                            subject = "Appointment Rescheduling Notice"
                            to_ = [email]
                                
                            appointment_date = datetime.strptime(appointment["start_date"][:-6], "%Y-%m-%dT%H:%M:%S")
                            formatted_date = appointment_date.strftime("%A, %d %B %Y")
                            formatted_time = datetime.strptime(appointment["start_time"], "%H:%M:%S").strftime("%I:%M %p")
                            
                            phone_number = {
                                "CCK": "+**********",
                                "KCS": "+**********",
                                "OR": "+**********",
                                "SBW": "+**********"
                            }.get(appointment['branch_id'], "Not available")
                                
                            custom_kwargs = {
                                "name": patient_details.get("name", "Patient"),
                                "date": formatted_date,
                                "time": formatted_time,
                                "phone_number": phone_number
                            }
                            
                            if is_staff:
                                html_path = "amelio/important_notification_to_staff.html"
                                message_text = f"Attention staff,\n\nAn appointment has been rescheduled due to doctor unavailability. Please take necessary actions.\n\nPatient: {patient_details.get('name', 'Patient')}\Current Appointment:\nDate: {formatted_date}\nTime: {formatted_time}\n\nPlease contact the patient at their registered number to reschedule the appointment. Use {phone_number} as the clinic contact number when communicating with the patient.\n\nEnsure to update the system once the appointment has been rescheduled. Thank you for your prompt attention to this matter."
                                is_send_to_staff = True
                            else:
                                html_path = "amelio/important_appointment_template.html"
                                message_text = f"Dear {patient_details.get('name', 'Patient')},\n\nWe regret to inform you that your appointment needs to be rescheduled due to the doctor unavailability. We apologize for any inconvenience.\n\Current Appointment:\nDate: {formatted_date}\nTime: {formatted_time}\n\nPlease contact us at {phone_number} to reschedule your appointment at your earliest convenience. Our team will assist in finding a new time that works for you.\n\nThank you for your understanding. We look forward to seeing you soon!"
                                is_send_to_staff = False
                                
                            send_user_email_with_html_template(html_path, subject, from_, to_, **custom_kwargs)
                            
                            MessageToSend.objects.create(
                                contact=**********,
                                message=message_text,
                                to_be_sent_at=timezone.now(),
                                metadata={
                                    "is_send_reminder": True,
                                    "is_resend_reminder": True,
                                    "is_send_to_staff": is_send_to_staff,
                                    "appointment_id": appointment_id,
                                    "appointment_date": appointment_date_to_check,
                                    "appointment_time": appointment_time_to_check
                                }
                            )
                        else:
                            logger.info(f"Skipped sending reminder for appointment {appointment_id} as it hasn't been edited")
                    else:
                        logger.info(f"Appointment {appointment_id} has been rescheduled. Skipping.")
                else:
                    logger.warning(f"Failed to fetch data for appointment {appointment_id}")

        process_messages(messages_send_to_staff, is_staff=True)
        process_messages(messages_to_resend)
        