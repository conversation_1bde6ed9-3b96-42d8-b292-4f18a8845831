# Generated by Django 4.2.5 on 2023-10-09 23:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('integration', '0002_rename_label_shopifyconnection_shop_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='DatabaseConnection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('label', models.Char<PERSON>ield(max_length=8912)),
                ('host', models.Char<PERSON>ield(max_length=8912)),
                ('port', models.IntegerField()),
                ('username', models.<PERSON>r<PERSON><PERSON>(max_length=8912)),
                ('password', models.<PERSON>r<PERSON><PERSON>(max_length=8912)),
                ('db_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=8912)),
                ('db_type', models.<PERSON><PERSON><PERSON><PERSON>(max_length=8912)),
                ('multi_select', models.CharField(max_length=8912)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive')], default='Active', max_length=8912)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
