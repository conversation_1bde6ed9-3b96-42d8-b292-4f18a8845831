# Generated by Django 4.2.5 on 2024-07-19 05:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import encrypted_model_fields.fields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('integration', '0022_alter_whatsappqrconnection_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='GoHighLevelConnection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('redirect_uri', models.CharField(blank=True, max_length=8912)),
                ('client_id', models.CharField(blank=True, max_length=8912)),
                ('client_secret', encrypted_model_fields.fields.Encrypted<PERSON>harField(blank=True)),
                ('scope', models.Char<PERSON>ield(blank=True, max_length=8912)),
                ('access_token', encrypted_model_fields.fields.EncryptedCharField(blank=True)),
                ('refresh_token', encrypted_model_fields.fields.EncryptedCharField(blank=True)),
                ('expired_datetime', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
