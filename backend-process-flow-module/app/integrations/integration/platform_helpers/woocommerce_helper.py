from woocommerce import API


def get_wc_api(url: str, consumer_key: str, consumer_secret: str) -> API:
    wc_api = API(
        url,
        consumer_key,
        consumer_secret,
        wp_api=True,
        version="wc/v3",
    )

    return wc_api


def verify_api_keys(url: str, consumer_key: str, consumer_secret: str) -> bool:
    wc_api = get_wc_api(url, consumer_key, consumer_secret)

    try:
        response = wc_api.get("products")
        return response.status_code == 200
    except Exception as e:
        print("Error when verify API keys: ", e)
        return False
