from datetime import datetime
from enum import unique

from django.db import models
from encrypted_model_fields.fields import EncryptedChar<PERSON>ield

import app.chats.chat_action.models as chat_action_models
import app.utility.models as utility_models


# Create your models here.
class DatabaseConnection(utility_models.BaseModel):
    label = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    host = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    port = models.IntegerField()
    username = models.Char<PERSON>ield(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    password = EncryptedCharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    db_name = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    db_type = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    multi_select = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )


class ShopifyConnection(utility_models.BaseModel):
    shop_name = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    site_url = models.TextField()
    api_key = models.TextField()
    api_secret = models.TextField()
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )


class WhatsAppAPIConnection(utility_models.BaseModel):
    number = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    # phone_number_id = models.CharField(
    #     max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    # ) #enable this
    access_token = models.TextField()
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )
    verification_code = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX
    )


class WhatsAppQRConnection(utility_models.BaseModel):
    profile = models.FileField(unique=True)
    ec2_id = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, null=True
    )
    auto_reply = models.BooleanField(default=False)
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.QR_CONNECTION_STATUS_CHOICES,
        default=utility_models.INACTIVE,
    )
    last_heart_beat = models.DateTimeField(default=datetime.now)
    session_uuid = models.UUIDField(null=True, blank=True)


class WhatsAppQRWhiteList(utility_models.BaseModel):
    appointment_chat_feature_location = models.ForeignKey(
        chat_action_models.AppointmentChatFeatureLocation,
        related_name="whatsapp_qr_whitelists",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)


class WeChatConnection(utility_models.BaseModel):
    account_name = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    token = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    app_id = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    app_secret = EncryptedCharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )


class TelegramConnection(utility_models.BaseModel):
    bot_name = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    bot_username = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    token = EncryptedCharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )


class WooCommerceConnection(utility_models.BaseModel):
    shop_name = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    url = models.TextField()
    consumer_key = EncryptedCharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    consumer_secret = EncryptedCharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX
    )
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )


class GoHighLevelConnection(utility_models.BaseModel):
    redirect_uri = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    client_id = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    location_id = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    client_secret = EncryptedCharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    scope = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    access_token = EncryptedCharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    refresh_token = EncryptedCharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    expired_datetime = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)


class TenantCredentials(utility_models.BaseModel):
    key = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, null=True, unique=True)
    value = EncryptedCharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
