import os

from django.core.management.base import BaseCommand
from django.db import transaction
from tenant_schemas.utils import schema_context
from app.core.tenants.models import Tenant
from app.integrations.integration.models import TenantCredentials


class Command(BaseCommand):
    help = "Runs the process to populate tenant credentials from .txt file"

    def add_arguments(self, parser):
        parser.add_argument("schema_name", type=str, help="Schema name")
        parser.add_argument(
            "--file_name",
            type=str,
            help="File name to populate (default: schema_name.txt)",
        )

    def handle(self, *args, **options):
        try:
            # Get the arguments
            schema_name = options["schema_name"]
            file_name = options["file_name"] or f"{schema_name}.txt"

            self.stdout.write(f"Schema name: {schema_name}")
            self.stdout.write(f"File name: {file_name}")

            # Check if the schema exists
            if not Tenant.objects.filter(schema_name=schema_name).exists():
                raise Exception(f"Schema {schema_name} not found")

            # Check if the file exists
            if not os.path.exists(file_name):
                raise FileNotFoundError(f"File {file_name} not found")

            # Read the file and populate credentials using the correct schema
            with schema_context(schema_name):
                with transaction.atomic():
                    with open(file_name, "r") as file:
                        for line in file:
                            # Skip empty lines and comments
                            line = line.strip()
                            if not line or line.startswith("#"):
                                continue

                            # Split line into key and value
                            try:
                                key, value = line.split("=", 1)
                                key = key.strip()
                                value = value.strip()

                                # Create or update credential
                                TenantCredentials.objects.update_or_create(
                                    key=key, defaults={"value": value}
                                )
                                self.stdout.write(
                                    f"Successfully added/updated credential for key: {key}"
                                )
                            except ValueError:
                                self.stdout.write(
                                    self.style.WARNING(f"Invalid line format: {line}")
                                )

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully populated credentials for {schema_name}"
                )
            )

        except Exception as e:
            self.stdout.write(self.style.ERROR(str(e)))
            raise e
