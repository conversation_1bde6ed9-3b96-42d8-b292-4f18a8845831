# Generated by Django 4.2.5 on 2023-10-24 05:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tenants', '0003_tenant_card_tenant_stripe_customer_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Credential',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('WEBHOOK_URL', models.CharField(max_length=8912)),
                ('PAGE_ID', models.CharField(max_length=8912)),
                ('PAGE_NAME', models.CharField(blank=True, max_length=8912)),
                ('PAGE_ACCESS_TOKEN', models.Char<PERSON>ield(max_length=8912)),
                ('VERIFY_TOKEN', models.CharField(max_length=8912)),
                ('SHOP_URL', models.CharField(blank=True, max_length=8912)),
                ('PORT', models.CharField(default='4000', max_length=7)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive')], default='Active', max_length=8912)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credentials', to='tenants.tenant')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
