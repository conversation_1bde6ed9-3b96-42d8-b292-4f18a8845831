from django.db import models
from app.utility import models as utility_models
import app.core.tenants.models as tenants_models


# Create your models here.
class Credential(utility_models.BaseModel):  # 1 credential = 1 page
    WEBHOOK_URL = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX
    )  # webhook will be the same across credentials
    PAGE_ID = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    PAGE_NAME = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    PAGE_ACCESS_TOKEN = models.Char<PERSON>ield(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX
    )
    VERIFY_TOKEN = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX
    )  # verify token will be the same across credentials
    SHOP_URL = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    PORT = models.Char<PERSON>ield(max_length=7, default="4000")
    is_live = models.<PERSON><PERSON>an<PERSON>ield(default=False)
    is_pulling_messages = models.<PERSON><PERSON>anField(default=False)
    is_messages_pulled = models.BooleanField(default=False)
    status = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        choices=utility_models.CONNECTION_STATUS_CHOICES,
        default=utility_models.ACTIVE,
    )
    tenant = models.ForeignKey(
        tenants_models.Tenant, related_name="credentials", on_delete=models.CASCADE
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.id}. {self.PAGE_ID} & {self.PAGE_NAME} -> Live: {self.is_live} & Status: {self.status}"
