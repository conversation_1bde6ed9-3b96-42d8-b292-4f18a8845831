import re
import os
import uuid
import time

import app.utility.models as utility_models


def convert_to_snake_case(text):
    # Remove special characters and replace spaces with underscores
    text = re.sub(r"[^a-zA-Z0-9\s]", "", text)
    text = re.sub(r"\s+", "_", text)
    # Convert to lowercase
    return text.lower()


def convert_to_title_case(text):
    return text.title().replace("_", " ")


def clean_up_prompt(prompt):
    # left strip all spaces in front of each line
    return re.sub(r"^[^\S\r\n]+", "", prompt, flags=re.MULTILINE).strip()


def extract_json_from_str(response_str):
    response_json = re.search(r"\{.*\}", response_str, re.DOTALL)
    return response_json.group(0) if response_json else response_str


def extract_list_from_str(response_str):
    response_list = re.search(r"\[.*\]", response_str, re.DOTALL)
    return response_list.group(0) if response_list else response_str


def get_printable_oneliner(anything):
    return str(anything).replace("\n", ";")


def categorize_file(file_name):
    # Define sets of file extensions for each category
    image_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg"}
    video_extensions = {".mp4", ".avi", ".mov", ".mkv", ".flv", ".wmv", ".webm"}
    document_extensions = {
        ".pdf",
        ".doc",
        ".docx",
        ".txt",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx",
    }

    # Get the file extension
    _, extension = os.path.splitext(file_name)
    extension = extension.lower()

    # Categorize based on file extension
    if extension in image_extensions:
        return utility_models.IMAGE
    elif extension in video_extensions:
        return utility_models.VIDEO
    elif extension in document_extensions:
        return utility_models.DOCUMENT
    else:
        return utility_models.UNKNOWN


def normalize_contact(contact):
    """Normalize contact numbers to a consistent format."""
    contact = str(contact).strip()
    # Remove any country code (assuming +65 for Singapore)
    if contact.startswith("+65"):
        contact = contact[3:]
    elif contact.startswith("65") and len(contact) > 8:
        contact = contact[2:]
    # Remove any non-numeric characters
    contact = "".join(filter(str.isdigit, contact))
    return contact

def generate_unique_id():
    """
    Generate a unique ID based on the current timestamp and a random UUID.
    """
    current_timestamp = int(time.time())
    unique_id = f"{current_timestamp}{uuid.uuid4().hex[:8]}"
    return unique_id