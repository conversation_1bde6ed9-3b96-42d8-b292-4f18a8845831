import logging

import boto3

from backend.settings import ENVIRONMENT

logger = logging.getLogger(__name__)

def get_ecs_client():
    """Initialize and return boto3 ECS client."""
    return boto3.client('ecs')

def trigger_redteam_chat_conversations(ecs_client, schema_name, customer_profile_id, ip_address, batch_run_id, redteam_run, seed_number=None):
    """
    Trigger ECS task for running red team conversations.
    
    Args:
        ecs_client: Boto3 ECS client
        schema_name (str): Schema name for the red team conversations
        customer_profile_id (int): Customer profile ID
        ip_address (str): IP address of the user
        batch_run_id (int): Batch run ID
        redteam_run (bool): Flag indicating if it's a red team run
        seed_number (int, optional): Seed number for reproducibility
    """
    
    logger.info("Triggering red team conversations in ECS", extra={
        "customer_profile_id": customer_profile_id,
        "ip_address": ip_address,
        "batch_run_id": batch_run_id,
        "redteam_run": redteam_run,
        "seed_number": seed_number,
        "schema_name": schema_name,
        "environment": ENVIRONMENT
    })

    environment_configs = {
        "Production": {
            "cluster": "arn:aws:ecs:ap-southeast-1:403220057834:cluster/utterunicorn-cluster-v2",
            "container_name": "utterunicorn-crew-redteam-chat-v2-production-container",
            "task_definition": "utterunicorn-crew-redteam-chat-v2-production-task",
            "security_groups": [
                "sg-026185d1ed7c8d594",
            ]
        },
        "Staging": {
            "cluster": "arn:aws:ecs:ap-southeast-1:403220057834:cluster/utterunicorn-cluster-v2",
            "container_name": "utterunicorn-crew-redteam-chat-v2-staging-container",
            "task_definition": "utterunicorn-crew-redteam-chat-v2-staging-task",
            "security_groups": [
                "sg-085ade4afa17fb4a6",
            ]
        }
    }

    config = environment_configs[ENVIRONMENT]
    
    # Prepare environment variables
    commands = [
        schema_name,
        str(customer_profile_id),
        str(batch_run_id),
        "--ip_address",
        ip_address
    ]

    if redteam_run:
        commands.append("--redteam_run")

    if seed_number is not None:
        commands.append("--seed_number")
        commands.append(str(seed_number))
    else:
        commands.append("--seed_number")
        commands.append(str(0))

    task_params = {
        'cluster': config['cluster'],
        'launchType': 'FARGATE',
        'count': 1,
        'networkConfiguration': {
            'awsvpcConfiguration': {
                'subnets': [
                    'subnet-0c11424e3bfec4515',
                    'subnet-053f0ba3e3bc6c559'
                ],
                'securityGroups': config['security_groups'],
                'assignPublicIp': 'ENABLED'
            }
        },
        'overrides': {
            'containerOverrides': [
                {
                    'name': config['container_name'],
                    'command': commands
                },
            ],
        },
        'propagateTags': 'TASK_DEFINITION',
        'taskDefinition': config['task_definition'],
        'platformVersion': 'LATEST'
    }

    response = ecs_client.run_task(**task_params)
    logger.info("Successfully triggered ECS task", extra={"task_response": response})
    
    return response


def trigger_redteam_chat_assessment(ecs_client, schema_name, redteam_conversation_id):
    """
    Trigger ECS task for running red team conversations.
    
    Args:
        ecs_client: Boto3 ECS client
        schema_name (str): Schema name for the red team conversations
        redteam_conversation_id (int): ID of the red team conversation
    """
    
    logger.info("Triggering red team conversations in ECS", extra={
        "redteam_conversation_id": redteam_conversation_id,
        "schema_name": schema_name,
        "environment": ENVIRONMENT
    })

    environment_configs = {
        "Production": {
            "cluster": "arn:aws:ecs:ap-southeast-1:403220057834:cluster/utterunicorn-cluster-v2",
            "container_name": "utterunicorn-crew-redteam-chat-assessor-v2-production-container",
            "task_definition": "utterunicorn-crew-redteam-chat-assessor-v2-production-task",
            "security_groups": [
                "sg-026185d1ed7c8d594",
            ]
        },
        "Staging": {
            "cluster": "arn:aws:ecs:ap-southeast-1:403220057834:cluster/utterunicorn-cluster-v2",
            "container_name": "utterunicorn-crew-redteam-chat-assessor-v2-staging-container",
            "task_definition": "utterunicorn-crew-redteam-chat-assessor-v2-staging-task",
            "security_groups": [
                "sg-085ade4afa17fb4a6",
            ]
        }
    }

    config = environment_configs[ENVIRONMENT]
    
    # Prepare environment variables
    commands = [
        schema_name,
        str(redteam_conversation_id)
    ]

    task_params = {
        'cluster': config['cluster'],
        'launchType': 'FARGATE',
        'count': 1,
        'networkConfiguration': {
            'awsvpcConfiguration': {
                'subnets': [
                    'subnet-0c11424e3bfec4515',
                    'subnet-053f0ba3e3bc6c559'
                ],
                'securityGroups': config['security_groups'],
                'assignPublicIp': 'ENABLED'
            }
        },
        'overrides': {
            'containerOverrides': [
                {
                    'name': config['container_name'],
                    'command': commands
                },
            ],
        },
        'propagateTags': 'TASK_DEFINITION',
        'taskDefinition': config['task_definition'],
        'platformVersion': 'LATEST'
    }

    response = ecs_client.run_task(**task_params)
    logger.info("Successfully triggered ECS task", extra={"task_response": response})
    
    return response

