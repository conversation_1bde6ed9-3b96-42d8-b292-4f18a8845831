import json
import unittest
from unittest.mock import patch

from app.utility.websocket_authentication import validate_ws_token


class TestValidateWsToken(unittest.TestCase):
    def setUp(self):
        # Sample valid token data
        self.valid_data = {
            'uuid': 'test-uuid-123',
            'chat_id': 456,
            'bot_api_key': 'test-api-key'
        }
        self.valid_json = json.dumps(self.valid_data)

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_valid_token(self, mock_decrypt):
        """Test with a valid token containing all fields"""
        # Mock the decrypt_data to return our valid JSON string
        mock_decrypt.return_value = self.valid_json
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result['uuid'], self.valid_data['uuid'])
        self.assertEqual(result['chat_id'], self.valid_data['chat_id'])
        self.assertEqual(result['bot_api_key'], self.valid_data['bot_api_key'])

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_valid_token_minimal(self, mock_decrypt):
        """Test with a valid token containing only required fields"""
        minimal_data = {
            'uuid': 'test-uuid-123'
        }
        mock_decrypt.return_value = json.dumps(minimal_data)
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertTrue(isinstance(result, dict))
        self.assertEqual(result['uuid'], minimal_data['uuid'])
        self.assertIsNone(result['chat_id'])
        self.assertIsNone(result['bot_api_key'])

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_decryption_failure(self, mock_decrypt):
        """Test behavior when decryption fails"""
        mock_decrypt.return_value = None
        
        result = validate_ws_token('invalid-encrypted-token')
        
        self.assertEqual(result, {})

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_invalid_json(self, mock_decrypt):
        """Test with invalid JSON data"""
        mock_decrypt.return_value = "invalid json string"
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertEqual(result, {})

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_missing_required_fields(self, mock_decrypt):
        """Test with missing required fields in token data"""
        # Test with missing uuid
        incomplete_data = {'chat_id': 456, 'bot_api_key': 'test-key'}
        mock_decrypt.return_value = json.dumps(incomplete_data)
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertEqual(result, {})

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_invalid_data_types(self, mock_decrypt):
        """Test with invalid data types for required fields"""
        # Test with invalid uuid type
        invalid_data = {
            'uuid': 123,  # Should be string
            'chat_id': 456,
            'bot_api_key': 'test-key'
        }
        mock_decrypt.return_value = json.dumps(invalid_data)
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertEqual(result, {})

        # Test with invalid chat_id type
        invalid_data = {
            'uuid': 'test-uuid-123',
            'chat_id': {'id': 456},  # Should be int or str
            'bot_api_key': 'test-key'
        }
        mock_decrypt.return_value = json.dumps(invalid_data)
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertEqual(result, {})

        # Test with invalid bot_api_key type
        invalid_data = {
            'uuid': 'test-uuid-123',
            'chat_id': 456,
            'bot_api_key': 123  # Should be string
        }
        mock_decrypt.return_value = json.dumps(invalid_data)
        
        result = validate_ws_token('dummy-encrypted-token')
        
        self.assertEqual(result, {})

    @patch('app.utility.websocket_authentication.decrypt_data')
    def test_valid_optional_fields(self, mock_decrypt):
        """Test with different valid combinations of optional fields"""
        # Test with string chat_id
        data_with_str_fields = {
            'uuid': 'test-uuid-123',
            'chat_id': '456',
            'bot_api_key': 'test-key'
        }
        mock_decrypt.return_value = json.dumps(data_with_str_fields)
        result = validate_ws_token('dummy-encrypted-token')
        self.assertEqual(result['chat_id'], '456')
        self.assertEqual(result['bot_api_key'], 'test-key')

        # Test with None values
        data_with_none_values = {
            'uuid': 'test-uuid-123',
            'chat_id': None,
            'bot_api_key': None
        }
        mock_decrypt.return_value = json.dumps(data_with_none_values)
        result = validate_ws_token('dummy-encrypted-token')
        self.assertIsNone(result['chat_id'])
        self.assertIsNone(result['bot_api_key'])


if __name__ == '__main__':
    unittest.main()
