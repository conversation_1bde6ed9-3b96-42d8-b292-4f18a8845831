import os
from django.test import Simple<PERSON>estCase
from unittest.mock import patch, MagicMock
from datetime import datetime
from botocore.exceptions import Client<PERSON>rror

from app.utility.aws_s3 import AWSS3Helper
from app.utility.custom_exception import CustomException


class TestAWSS3Helper(SimpleTestCase):
    """
    Test suite for AWSS3Helper class.
    Tests initialization scenarios and file upload functionality.
    """
    
    def setUp(self):
        """Set up test environment before each test"""
        # Set environment variables for testing
        os.environ['AWS_REGION'] = 'us-east-1'
        os.environ['AWS_BUCKET_NAME'] = 'test-bucket'
    
    def tearDown(self):
        """Clean up test environment after each test"""
        # Remove environment variables
        for key in ['AWS_REGION', 'AWS_BUCKET_NAME', 'AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY']:
            os.environ.pop(key, None)

    def test_init_with_credentials(self):
        """Test initialization with AWS credentials"""
        os.environ['AWS_ACCESS_KEY_ID'] = 'test_key'
        os.environ['AWS_SECRET_ACCESS_KEY'] = 'test_secret'
        
        with patch('boto3.client') as mock_boto3:
            s3_helper = AWSS3Helper()
            mock_boto3.assert_called_once_with(
                's3',
                aws_access_key_id='test_key',
                aws_secret_access_key='test_secret',
                region_name='us-east-1'
            )

    def test_init_without_credentials(self):
        """Test initialization without AWS credentials (IAM role scenario)"""
        with patch('boto3.client') as mock_boto3:
            s3_helper = AWSS3Helper()
            mock_boto3.assert_called_once_with(
                's3',
                region_name='us-east-1'
            )

    def test_generate_file_path(self):
        """Test file path generation with various inputs"""
        s3_helper = AWSS3Helper()
        
        test_cases = [
            {
                'file_name': 'test.pdf',
                'folder_path': 'documents/',
                'expected_pattern': r'documents/\d{8}_\d{6}_test\.pdf'
            },
            {
                'file_name': 'test@file#.jpg',
                'folder_path': '/images/',
                'expected_pattern': r'images/\d{8}_\d{6}_testfile\.jpg'
            },
            {
                'file_name': ' spaces.doc ',
                'folder_path': 'files/docs/',
                'expected_pattern': r'files/docs/\d{8}_\d{6}_spaces\.doc'
            }
        ]
        
        for case in test_cases:
            path = s3_helper._generate_file_path(case['file_name'], case['folder_path'])
            self.assertRegex(path, case['expected_pattern'])

    @patch('boto3.client')
    def test_upload_file_success(self, mock_boto3):
        """Test successful file upload with various content types"""
        mock_s3_client = MagicMock()
        mock_boto3.return_value = mock_s3_client
        
        s3_helper = AWSS3Helper()
        test_file_data = b'test file content'
        
        # Test cases with different file types
        test_cases = [
            ('test.pdf', 'documents', 'application/pdf'),
            ('image.jpg', 'images', 'image/jpeg'),
            ('doc.txt', 'texts', 'text/plain'),
            ('unknown.xyz', 'misc', 'application/octet-stream')
        ]
        
        for file_name, folder_path, expected_content_type in test_cases:
            s3_path, download_url = s3_helper.upload_file(
                file_data=test_file_data,
                file_name=file_name,
                folder_path=folder_path,
                content_type=expected_content_type
            )
            
            # Verify s3_path format
            self.assertRegex(s3_path, rf'{folder_path}/\d{{8}}_\d{{6}}_{file_name}')
            
            # Verify download_url format
            expected_url_pattern = f'https://test-bucket.s3.us-east-1.amazonaws.com/{folder_path}/'
            self.assertTrue(download_url.startswith(expected_url_pattern))
            
            # Verify S3 client called correctly
            mock_s3_client.put_object.assert_called_with(
                Bucket='test-bucket',
                Key=s3_path,
                Body=test_file_data,
                ContentType=expected_content_type
            )

    @patch('boto3.client')
    def test_upload_file_client_error(self, mock_boto3):
        """Test handling of ClientError during file upload"""
        mock_s3_client = MagicMock()
        mock_s3_client.put_object.side_effect = ClientError(
            error_response={'Error': {'Message': 'Test error'}},
            operation_name='PutObject'
        )
        mock_boto3.return_value = mock_s3_client
        
        s3_helper = AWSS3Helper()
        
        with self.assertRaises(CustomException) as context:
            s3_helper.upload_file(
                file_data=b'test content',
                file_name='test.txt',
                folder_path='documents'
            )
        
        self.assertIn('Failed to upload file to S3', str(context.exception))

    @patch('boto3.client')
    def test_upload_file_general_error(self, mock_boto3):
        """Test handling of general exceptions during file upload"""
        mock_s3_client = MagicMock()
        mock_s3_client.put_object.side_effect = Exception('Unexpected error')
        mock_boto3.return_value = mock_s3_client
        
        s3_helper = AWSS3Helper()
        
        with self.assertRaises(CustomException) as context:
            s3_helper.upload_file(
                file_data=b'test content',
                file_name='test.txt',
                folder_path='documents'
            )
        
        self.assertIn('Unexpected error during S3 upload', str(context.exception))

    @patch('boto3.client')
    def test_upload_file_empty_data(self, mock_boto3):
        """Test upload with empty file data"""
        mock_s3_client = MagicMock()
        mock_boto3.return_value = mock_s3_client
        
        s3_helper = AWSS3Helper()
        
        # Test with empty file data
        s3_path, download_url = s3_helper.upload_file(
            file_data=b'',
            file_name='empty.txt',
            folder_path='documents'
        )
        
        self.assertIsInstance(s3_path, str)
        self.assertIsInstance(download_url, str)

    @patch('boto3.client')
    @patch('mimetypes.guess_type')
    def test_upload_file_content_type_detection(self, mock_guess_type, mock_boto3):
        """Test automatic content type detection for file uploads"""
        mock_s3_client = MagicMock()
        mock_boto3.return_value = mock_s3_client
        
        # Mock the guess_type function
        mock_guess_type.return_value = ('test/content-type', None)
        
        s3_helper = AWSS3Helper()
        
        # Test automatic content type detection
        s3_path, download_url = s3_helper.upload_file(
            file_data=b'test content',
            file_name='test.xyz',
            folder_path='documents'
        )
        
        # Verify S3 client called with guessed content type
        mock_s3_client.put_object.assert_called_with(
            Bucket='test-bucket',
            Key=s3_path,
            Body=b'test content',
            ContentType='test/content-type'
        )
        
        # Test fallback to application/octet-stream
        mock_guess_type.return_value = (None, None)
        
        s3_path, download_url = s3_helper.upload_file(
            file_data=b'test content',
            file_name='unknown.file',
            folder_path='documents'
        )
        
        # Verify fallback content type
        mock_s3_client.put_object.assert_called_with(
            Bucket='test-bucket',
            Key=s3_path,
            Body=b'test content',
            ContentType='application/octet-stream'
        )