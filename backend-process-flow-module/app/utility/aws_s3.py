import boto3
from botocore.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Di<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
import os
from datetime import datetime
import mimetypes
from app.utility.custom_exception import CustomException

class AWSS3Helper:
    def __init__(self):
        """
        Initialize AWS S3 client using environment variables or IAM role.
        When running on AWS infrastructure with proper IAM roles, explicit credentials are not needed.
        """
        self.region = os.environ.get('AWS_REGION', 'us-east-1')
        self.bucket_name = os.environ.get('AWS_BUCKET_NAME')
        
        # Check if running locally (with access keys) or on AWS (with IAM role)
        self.aws_access_key = os.environ.get('AWS_ACCESS_KEY_ID')
        self.aws_secret_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
        
        if self.aws_access_key and self.aws_secret_key:
            # Initialize with explicit credentials (local development)
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.region
            )
        else:
            # Initialize without explicit credentials (AWS environment)
            self.s3_client = boto3.client(
                's3',
                region_name=self.region
            )

    def _generate_file_path(self, file_name: str, folder_path: str) -> str:
        """
        Generate a unique file path for S3 upload.
        
        Args:
            file_name (str): Original file name
            folder_path (str): Desired folder path in S3
            
        Returns:
            str: Generated S3 file path
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        clean_filename = ''.join(e for e in file_name if e.isalnum() or e in ('-', '_', '.'))
        return f"{folder_path.strip('/')}/{timestamp}_{clean_filename}"

    def upload_file(
        self, 
        file_data: bytes,
        file_name: str,
        folder_path: str,
        content_type: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        Upload a file to AWS S3.
        
        Args:
            file_data (bytes): File content in bytes
            file_name (str): Original file name
            folder_path (str): Desired folder path in S3
            content_type (str, optional): File content type. If not provided, will be guessed from file extension
            
        Returns:
            Tuple[str, str]: Tuple containing (s3_path, download_url)
            
        Raises:
            CustomException: If upload fails
        """
        try:
            # Generate unique file path
            s3_path = self._generate_file_path(file_name, folder_path)
            
            # Determine content type if not provided
            if not content_type:
                content_type, _ = mimetypes.guess_type(file_name)
                content_type = content_type or 'application/octet-stream'
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_path,
                Body=file_data,
                ContentType=content_type
            )
            
            # Generate download URL
            download_url = f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{s3_path}"
            
            return s3_path, download_url
            
        except ClientError as e:
            error_message = f"Failed to upload file to S3: {str(e)}"
            raise CustomException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during S3 upload: {str(e)}"
            raise CustomException(error_message)
