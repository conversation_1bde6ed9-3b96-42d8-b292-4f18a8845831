from datetime import datetime, timed<PERSON><PERSON>

import pytz
from django.conf import settings
from django.utils import timezone

DATE_FORMAT = "%Y-%m-%d"
TIME_FORMAT = "%H:%M:%S%z"
DATE_TIME_FORMAT = f"{DATE_FORMAT}T{TIME_FORMAT}"

FE_DATE_TIME_FORMAT = f"{DATE_FORMAT}T%H:%M:%SZ"


# def convert_string_to_datetime_object(date_string, desired_timezone=settings.TIME_ZONE):
#     # Convert the string to a naive datetime object
#     print("date_string: ", date_string)
#     if date_string.endswith("Z"):
#         naive_datetime = datetime.strptime(date_string, FE_DATE_TIME_FORMAT)
#     else:
#         naive_datetime = datetime.strptime(date_string, DATE_TIME_FORMAT)

#     # Convert the naive datetime to a UTC-aware datetime object
#     utc_aware_datetime = timezone.make_aware(naive_datetime, timezone=timezone.utc)

#     # Adjust to the desired timezone
#     adjusted_datetime = utc_aware_datetime.astimezone(pytz.timezone(desired_timezone))

#     return adjusted_datetime


def get_current_year_str():
    return datetime.today().strftime("%Y")


def convert_created_at_str_to_datetime_object(date_string):
    # Convert the string to a naive datetime object
    return datetime.strptime(date_string, f"{DATE_FORMAT}T%H:%M:%S.%f%z")


def convert_to_yyyymmdd(dt):
    """
    Converts a timezone-aware datetime object to the format "YYYY-MM-DD".

    :param dt: datetime object
    :return: A string in the format "YYYY-MM-DD"
    """
    # Make sure the datetime is in the current timezone
    dt = timezone.localtime(dt)
    return dt.strftime(DATE_FORMAT)


def convert_to_yyyymmddThhmmZ(dt):
    """
    Converts a timezone-aware datetime object to the format "YYYY-MM-DD hh:mm AM/PM".

    :param dt: datetime object
    :return: A string in the format "YYYY-MM-DD hh:mm AM/PM"
    """
    # Make sure the datetime is in the current timezone
    if not dt:
        return "-"
    dt = timezone.localtime(dt)
    return dt.strftime(DATE_TIME_FORMAT)


def convert_to_human_friendly_format(dt):
    """
    Converts a timezone-aware datetime object to a human-friendly format.

    :param dt: datetime object
    :return: A string in a friendly format like "Monday, January 1, 2023 at 10:30 PM"
    """
    # Ensure the datetime object is not None
    if not dt:
        return "-"

    # Convert the datetime to the current timezone
    dt = timezone.localtime(dt)

    # Define the friendly format
    # friendly_format = "%A, %B %d, %Y at %I:%M %p"
    friendly_format = "Date: %d-%b-%Y | Time: %H:%M"

    # Return the formatted datetime
    return dt.strftime(friendly_format)


# By Leon to tackle payment thingy
def convert_epoch_to_sgt_datetime_str(epoch):
    try:
        datetime_obj = datetime.fromtimestamp(epoch, tz=pytz.timezone("Asia/Singapore"))
        # datetime_str = datetime_obj.strftime("%Y-%m-%d %H:%M:%S GMT+8")
        datetime_str = datetime_obj.strftime("%Y-%m-%d GMT+8")
        return datetime_str
    except:
        return ""


def convert_epoch_to_sgt_datetime(epoch):
    datetime_obj = datetime.fromtimestamp(epoch, tz=pytz.timezone("Asia/Singapore"))
    return datetime_obj


def have_same_date(datetime1, datetime2):
    return (
        (datetime1.year == datetime2.year)
        and (datetime1.month == datetime2.month)
        and (datetime1.day == datetime2.day)
    )


def convert_datetime_to_desired_timezone_str(
    datetime: datetime, desired_timezone=settings.TIME_ZONE
):
    desired_tz = pytz.timezone(desired_timezone)

    # Convert the UTC timestamp to the desired timezone
    localized_datetime = datetime.astimezone(desired_tz)
    formatted_datetime = localized_datetime.strftime(DATE_TIME_FORMAT)
    return formatted_datetime


def convert_datetime_to_desired_timezone_datetime(
    datetime: datetime, desired_timezone=settings.TIME_ZONE
):
    """
    if datetime don't have timzone, it will convert it to +8
    if got, then it will add/reduce the time accordingly
    """
    desired_tz = pytz.timezone(desired_timezone)
    datetime = datetime.astimezone(desired_tz)
    return datetime


def get_utc_offset(timezone_str):
    # Get the timezone object
    tz = pytz.timezone(timezone_str)

    # Get the current time in that timezone
    now = datetime.now(tz)

    # Calculate the offset in minutes
    offset_minutes = int(now.utcoffset().total_seconds() / 60)

    # Convert minutes to hours and minutes
    hours, minutes = divmod(offset_minutes, 60)
    sign = "+" if hours >= 0 else "-"

    # Return formatted string
    if minutes:
        return f"UTC{sign}{hours:02d}:{minutes:02d}"
    else:
        return f"UTC{sign}{hours:02d}"


def get_day_of_week(dt):
    """
    Convert a datetime object to its corresponding day of the week.

    Parameters:
    - dt (datetime.datetime): A datetime object.

    Returns:
    - str: The day of the week corresponding to the given datetime.
    """
    return dt.strftime("%A")


def get_today_str():
    now = timezone.now()
    today = (
        convert_to_yyyymmddThhmmZ(now)
        + f" ({get_day_of_week(now)})"
        + f" (We are in Timezone: {settings.TIME_ZONE})"
    )
    return today


def ensure_date_in_future(date_str):
    """
    Ensure the given date is in the future. If not, increment the year until it is.

    Parameters:
        date_str: A date string in various formats (e.g., "YYYY-MM-DD", "DD-MMM-YYYY", "DD Month YYYY").

    Returns:
        A date string in the same format as the input, adjusted to be in the future.
    """
    date_formats = [
        "%Y-%m-%d",         # 2024-12-31
        "%d-%m-%Y",         # 31-12-2024
        "%m-%d-%Y",         # 12-31-2024
        "%d/%m/%Y",         # 31/12/2024
        "%m/%d/%Y",         # 12/31/2024
        "%Y/%m/%d",         # 2024/12/31
        "%d.%m.%Y",         # 31.12.2024
        "%d-%b-%Y",         # 31-Dec-2024
        "%d-%B-%Y",         # 31-December-2024
        "%b %d, %Y",        # Dec 31, 2024
        "%B %d, %Y",        # December 31, 2024
        "%d %b %Y",         # 31 Dec 2024
        "%d %B %Y",         # 31 December 2024
        "%b %d %Y",         # Dec 31 2024
        "%B %d %Y",         # December 31 2024
    ]

    for date_format in date_formats:
        try:
            date_obj = datetime.strptime(date_str, date_format).date()
            today = datetime.now().date()

            while date_obj < today:
                date_obj = date_obj.replace(year=date_obj.year + 1)

            return date_obj.strftime(date_format)
        except ValueError:
            continue  # Try the next format

    # Return the original string if no format matched
    return date_str
