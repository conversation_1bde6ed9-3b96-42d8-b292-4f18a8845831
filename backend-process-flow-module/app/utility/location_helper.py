import traceback

import requests
from geopy.distance import geodesic
from geopy.geocoders import Nominatim


def get_coordinates(location_or_postal_code):
    url = "https://www.onemap.gov.sg/api/common/elastic/search"
    params = {
        "searchVal": location_or_postal_code,
        "returnGeom": "Y",
        "getAddrDetails": "N",
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        results = response.json().get("results", [])
        if results:
            return float(results[0]["LATITUDE"]), float(results[0]["LONGITUDE"])
    except Exception as e:
        traceback.print_exc()
    return None, None


def calculate_distance(coord1, coord2):
    # Using Haversine formula to calculate the distance
    from math import atan2, cos, radians, sin, sqrt

    lat1, lon1 = coord1
    lat2, lon2 = coord2

    R = 6371.0  # Earth radius in kilometers

    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = (
        sin(dlat / 2) ** 2
        + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
    )
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    return R * c


def get_bus_stop_nearby(coordinates, radius=500):
    overpass_url = "http://overpass-api.de/api/interpreter"
    overpass_query = f"""
    [out:json];
    node
      ["highway"="bus_stop"]
      (around:{radius},{coordinates[0]},{coordinates[1]});
    out body;
    """
    try:
        response = requests.get(
            overpass_url, params={"data": overpass_query}, timeout=10
        )
        response.raise_for_status()
        results = response.json().get("elements", [])
        nearby_stops = []
        for result in results:
            stop_coordinates = (result["lat"], result["lon"])
            distance = geodesic(coordinates, stop_coordinates).meters
            if distance <= radius:
                nearby_stops.append(
                    {
                        "stop_name": result.get("tags", {}).get("name", "Unknown"),
                        "latitude": stop_coordinates[0],
                        "longitude": stop_coordinates[1],
                        "distance": distance,
                    }
                )
        return nearby_stops
    except requests.exceptions.RequestException as e:
        print(f"Error making request to Overpass API: {e}")
        traceback.print_exc()
        return []


def get_station_nearby(coordinates, radius=500):
    overpass_url = "http://overpass-api.de/api/interpreter"
    overpass_query = f"""
    [out:json];
    node
      ["railway"="station"]
      (around:{radius},{coordinates[0]},{coordinates[1]});
    out body;
    """
    try:
        response = requests.get(
            overpass_url, params={"data": overpass_query}, timeout=10
        )
        response.raise_for_status()
        results = response.json().get("elements", [])
        nearby_stations = []
        for result in results:
            station_coordinates = (result["lat"], result["lon"])
            distance = geodesic(coordinates, station_coordinates).meters
            if distance <= radius:
                nearby_stations.append(
                    {
                        "station_name": result.get("tags", {}).get("name", "Unknown"),
                        "latitude": station_coordinates[0],
                        "longitude": station_coordinates[1],
                        "distance": distance,
                    }
                )
        return nearby_stations
    except requests.exceptions.RequestException as e:
        print(f"Error making request to Overpass API: {e}")
        traceback.print_exc()
        return []


def check_is_transport_nearby(stations):
    if len(stations) == 0:
        return False
    try:
        closest_station = min(stations, key=lambda x: x["distance"])
        # print(f"{closest_bus_stop=}")
        distance = closest_station["distance"]
        # print(f"{distance=}")

        if distance <= 500:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False


def check_is_any_transport_nearby(location_name_or_postal_code) -> bool:
    try:
        coordinates = get_coordinates(location_name_or_postal_code)
        # print(f"{coordinates=}")
        if not coordinates:
            print("Location not found")
            return False

        bus_stop_nearby = get_bus_stop_nearby(coordinates)
        station_nearby = get_station_nearby(coordinates)

        if check_is_transport_nearby(bus_stop_nearby) or check_is_transport_nearby(
            station_nearby
        ):
            return True

        return False
    except Exception as e:
        traceback.print_exc()
        return False


# Example usage
# location_name1 = (
#     "Singapore Zoo"  # Change to any location name like "Sentosa", "Swiss Club", etc.
# )
# location_name2 = "Sentosa"
# postal_code1 = "018956"  # Change to any postal code
# postal_code2 = "238858"

# charge_location = check_is_transpot_nearby(location_name1)
# print(f"Charge by location ({location_name1}): ${charge_location}")

# charge_location = check_is_transpot_nearby(location_name2)
# print(f"Charge by location ({location_name2}): ${charge_location}")

# charge_postal = check_is_transpot_nearby(postal_code1)
# print(f"Charge by postal code ({postal_code1}): ${charge_postal}")

# charge_postal = check_is_transpot_nearby(postal_code2)
# print(f"Charge by postal code ({postal_code2}): ${charge_postal}")
