import os

from django.db import models
from rest_framework.pagination import PageNumberPagination

import app.utility.repo_variables as utility_repo_variables
from app.core.authapi.models import User

DEFAULT_TOPIC_LIST = utility_repo_variables.DEFAULT_TOPIC_LIST
DEFAULT_CHAR_FIELD_MAX = 8912

USER_TEXT = "user"
ANONYMOUS_TEXT = "anonymous"
MACHINE_TEXT = "machine"
PAGE_ADMIN_TEXT = "page-admin"  # for third party integration self reply
SCHEDULED_TEXT = "Scheduled"  # text scheduled by page admin

MESSAGE_TYPES = (
    (USER_TEXT, "User"),
    (ANONYMOUS_TEXT, "Anonymous"),
    (MACHINE_TEXT, "Machine"),
    (PAGE_ADMIN_TEXT, "Page Admin"),
    (SCHEDULED_TEXT, SCHEDULED_TEXT),
)
MESSAGE_TO_SEND_TYPES = (
    (MACHINE_TEXT, "Machine"),
    (PAGE_ADMIN_TEXT, "Page Admin"),
    (SCHEDULED_TEXT, SCHEDULED_TEXT),
)

IMAGE = "image"
VIDEO = "video"
DOCUMENT = "document"
UNKNOWN = "unknown"

ATTACHMENT_TYPES = [
    (IMAGE, "Image"),
    (VIDEO, "Video"),
    (DOCUMENT, "Document"),
    (UNKNOWN, "Unknown"),
]


MANDARIN = "中文"
ENGLISH = "English"
ENGLISH_US = "English (US)"
ENGLISH_UK = "English (UK)"
SINGLISH = "Singlish"
BAHASA = "Bahasa"

# LANGUAGE_CHOICE = (
#     (MANDARIN, MANDARIN),
#     (ENGLISH_US, ENGLISH_US),
#     (BAHASA, BAHASA),
# )
ENGLISH_LANGUAGE_CHOICE = (
    (ENGLISH_US, ENGLISH_US),
    (ENGLISH_UK, ENGLISH_UK),
    (SINGLISH, SINGLISH),
)

APPROVED = "Approved"
CONFIRMED = "Confirmed"
RESOLVED = "Resolved"
PENDING = "Pending"
REJECTED = "Rejected"

ACTION_STATUS_CHOICES = (
    (APPROVED, APPROVED),
    (CONFIRMED, CONFIRMED),
    (RESOLVED, RESOLVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED),
)

INTERNAL_TEXT = "internal"
EXTERNAL_TEXT = "external"

CHROMA_DB_TYPE = (
    (INTERNAL_TEXT, INTERNAL_TEXT),
    (EXTERNAL_TEXT, EXTERNAL_TEXT),
)

BOT_SETTING_TYPE = (
    (INTERNAL_TEXT, "Internal"),
    (EXTERNAL_TEXT, "External"),
)

GOOD = "Good"
BAD = "Bad"
NEUTRAL = "Neutral"

MESSAGE_FEEDBACK_CHOICES = (
    (GOOD, GOOD),
    (BAD, BAD),
    (NEUTRAL, NEUTRAL),
)

PENDING = "Pending"
SUCCEED = "Succeed"
FAILED = "Failed"

# message to send
COMPLETED = "Completed"
APPT_UPDATED = "Appointment Updated"

STATUS_CHOICES = (
    (PENDING, PENDING),
    (SUCCEED, SUCCEED),
    (FAILED, FAILED),
)

MESSAGE_TO_SEND_STATUS_CHOICES = (
    (PENDING, PENDING),  # initialize and not send yet
    (FAILED, FAILED),  # failed to send out
    (COMPLETED, COMPLETED),  # reach the time and sent out
    (
        APPT_UPDATED,
        APPT_UPDATED,
    ),  # haven't send out and appointment status updated, so no need to send again.
)

# QR status, grouping them depending on who can update them
# backend > the initialize_whatsapp_ec2_instance task (inside this repo)
# task > the update_status task (inside this repo)
# api > utter_unicorn_bot.py (whatsapp script)

ACTIVE = "Active"  # all
INACTIVE = "Inactive"  # all
STARTING = "Starting"  # backend
STARTED = "Started, waiting to be active"  # task, api
PULLING = "Pulling"  # backend
GENERATING_QR = "Generating QR"  # backend
LOGIN_FAILED = "Login Failed"  # all
LOGGED_OUT = "Logged Out"  # all

CONNECTION_STATUS_CHOICES = (
    (ACTIVE, ACTIVE),
    (INACTIVE, INACTIVE),
)

QR_CONNECTION_STATUS_CHOICES = (
    (INACTIVE, INACTIVE),
    (GENERATING_QR, GENERATING_QR),
    (PULLING, PULLING),
    (STARTING, STARTING),
    (STARTED, STARTED),
    (LOGIN_FAILED, LOGIN_FAILED),
    (LOGGED_OUT, LOGGED_OUT),
    (ACTIVE, ACTIVE),
)

DEFAULT_PASSWORD = "AtAVJx7UgPWUWIa"

SUBSCRIPTION_PLANS = [
    # {
    #     "name": "Essentials",
    #     "currency": "sgd",
    #     "unit_amount": 0,
    #     "recurring": {
    #         "interval": "month",
    #     },
    # },
    {
        "name": "Business Class",
        "currency": "usd",
        "unit_amount": 19900,
        "recurring": {
            "interval": "month",
        },
    },
    {
        "name": "Enterprise Elite",
        "currency": "usd",
        "unit_amount": 39900,
        "recurring": {
            "interval": "month",
        },
    },
    {
        "name": "Global Dominator",
        "currency": "usd",
        "unit_amount": 99900,
        "recurring": {
            "interval": "month",
        },
    },
]

WEB_APPLICATION = "Web Application"
FLOATING_CHAT = "Floating Chat"
WHATSAPP = "WhatsApp"
WHATSAPP_QR = "WhatsApp QR"
WECHAT = "WeChat"
INSTAGRAM = "Instagram"
TELEGRAM = "Telegram"
FACEBOOK = "Facebook"
SHOPIFY = "Shopify"
WIX = "Wix"
WOOCOMMERCE = "WooCommerce"

CONVERSATION_SOURCE_CHOICES = (
    (WEB_APPLICATION, WEB_APPLICATION),
    (FLOATING_CHAT, FLOATING_CHAT),
    (WHATSAPP, WHATSAPP),
    (WHATSAPP_QR, WHATSAPP_QR),
    (WECHAT, WECHAT),
    (TELEGRAM, TELEGRAM),
    (INSTAGRAM, INSTAGRAM),
    (FACEBOOK, FACEBOOK),
    (SHOPIFY, SHOPIFY),
    (WIX, WIX),
    (WOOCOMMERCE, WOOCOMMERCE),
)

SALES_MADE = "Sales Made"
SALES_NOT_MADE = "Sales Not Made"
ABSENT = "Absent"


APPOINTMENT_STATUS_CHOICES = (
    (PENDING, PENDING),
    (SALES_MADE, SALES_MADE),
    (SALES_NOT_MADE, SALES_NOT_MADE),
    (ABSENT, ABSENT),
)

# CHAT
CHAT_MINUTE_SPLIT_VALUE = 1

# URL
SCRAPE_LIGHT = "light"
SCRAPE_STANDARD = "standard"
SCRAPE_COMPREHENSIVE = "comprehensive"

WEB_SCRAPE_URL_COUNT_COMPREHENSIVE = int(
    os.environ.get("WEB_SCRAPE_URL_COUNT_COMPREHENSIVE", 100)
)
WEB_SCRAPE_ITERATION_COUNT_COMPREHENSIVE = int(
    os.environ.get("WEB_SCRAPE_ITERATION_COUNT_COMPREHENSIVE", 5)
)

WEB_SCRAPE_URL_COUNT_STANDARD = int(os.environ.get("WEB_SCRAPE_URL_COUNT_STANDARD", 60))
WEB_SCRAPE_ITERATION_COUNT_STANDARD = int(
    os.environ.get("WEB_SCRAPE_ITERATION_COUNT_STANDARD", 4)
)

WEB_SCRAPE_URL_COUNT_LIGHT = int(os.environ.get("WEB_SCRAPE_URL_COUNT_LIGHT", 20))
WEB_SCRAPE_ITERATION_COUNT_LIGHT = int(
    os.environ.get("WEB_SCRAPE_ITERATION_COUNT_LIGHT", 3)
)


class LargeResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 10000


class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        default=None,
        on_delete=models.SET_NULL,
        null=True,
        related_name="%(class)s_created_by",
    )
    updated_by = models.ForeignKey(
        User,
        default=None,
        on_delete=models.SET_NULL,
        null=True,
        related_name="%(class)s_updated_by",
    )

    class Meta:
        abstract = True


class NamedBaseModel(BaseModel):
    name = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name


class UniqueNamedBaseModel(NamedBaseModel):
    name = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX, unique=True)

    class Meta:
        abstract = True

STOP_WORDS = {
    'u', 'ur', 'pls', 'thx', 'btw', 'omg', 'lol', 'tbh', 'idk', 'wtf', 
    'y', 'cuz', 'dunno', 'gonna', 'wanna', 'gotta', 'kinda', 'sorta', 'lemme',
    'lah', 'lor', 'leh', 'meh', 'sia', 'ah', 'yah', 'ok', 'alamak', 'liao', 
    'walao', 'siao', 'shiok', 'chope', 'know', 'thanks', 'yes', 'yep', 'team', 
    'sure', 'okay', 'yep', 'yeah', 'thanks', 'thank', 'please', 'hi', 'hello',
    'actually', 'really', 'basically', 'literally', 'seriously', 'totally', 
    'definitely', 'probably', 'obviously', 'maybe', 'perhaps', 'certainly', 
    'absolutely', 'apparently', 'seem', 'seems', 'look', 'looks', 'just', 'like', 
    'thing', 'things', 'way', 'ways', 'now', 'then', 'again', 'also', 'often', 
    'always', 'never', 'sometimes', 'everyone', 'everybody', 'someone', 'somebody', 
    'anyone', 'anybody', 'nobody', 'people', 'person', 'man', 'woman', 'men', 'women', 
    'boy', 'girl', 'boys', 'girls', 'friend', 'friends', 'buddy', 'buddies', 'partner', 
    'partners', 'sir', 'maam', 'madam', 'mr', 'mrs', 'ms', 'dr', 'prof', 'teacher', 
    'student', 'students', 'class', 'classes', 'course', 'courses', 'job', 'jobs', 
    'work', 'works', 'thing', 'things', 'fact', 'facts', 'idea', 'ideas', 'point', 
    'points', 'side', 'sides', 'bit', 'bits', 'lot', 'lots', 'part', 'parts', 'case', 
    'cases', 'form', 'forms', 'area', 'areas', 'group', 'groups', 'number', 'numbers', 
    'kind', 'kinds', 'sort', 'sorts', 'type', 'types', 'matter', 'matters', 'problem', 
    'problems', 'question', 'questions', 'issue', 'issues', 'story', 'stories', 
    'thing', 'things', 'think', 'thought', 'thinking', 'thoughts', 'feel', 'feels', 
    'feeling', 'feelings', 'say', 'says', 'said', 'tell', 'tells', 'told', 'talk', 
    'talks', 'talking', 'hear', 'hears', 'heard', 'listen', 'listens', 'listening', 
    'see', 'sees', 'seeing', 'seen', 'want', 'wants', 'wanted', 'need', 'needs', 
    'needing', 'make', 'makes', 'made', 'going', 'goes', 'gone', 'came', 'coming', 
    'get', 'gets', 'got', 'getting', 'put', 'puts', 'putting', 'use', 'uses', 
    'using', 'used', 'good', 'better', 'best', 'bad', 'worse', 'worst', 'big', 
    'bigger', 'biggest', 'small', 'smaller', 'smallest', 'long', 'longer', 'longest', 
    'short', 'shorter', 'shortest', 'high', 'higher', 'highest', 'low', 'lower', 
    'lowest', 'new', 'newer', 'newest', 'old', 'older', 'oldest', 'young', 
    'younger', 'youngest', 'right', 'left', 'sure', 'yes', 'no', 'maybe', 'perhaps', 
    'okay', 'alright', 'cool', 'awesome', 'great', 'fine', 'well', 'okay', 'like', 
    'likely', 'unlikely', 'seem', 'seems', 'seemed', 'seeming', 'appear', 'appears', 
    'appeared', 'appearing', 'become', 'becomes', 'became', 'becoming', 'take', 
    'takes', 'taken', 'taking', 'give', 'gives', 'gave', 'given', 'find', 'finds', 
    'found', 'finding', 'look', 'looks', 'looked', 'looking', 'show', 'shows', 
    'showed', 'showing', 'begin', 'begins', 'began', 'beginning', 'end', 'ends', 
    'ended', 'ending', 'start', 'starts', 'started', 'starting', 'stop', 'stops', 
    'stopped', 'stopping', 'continue', 'continues', 'continued', 'continuing', 
    'keep', 'keeps', 'kept', 'keeping', 'remain', 'remains', 'remained', 
    'remaining', 'stay', 'stays', 'stayed', 'staying', 'run', 'runs', 'ran', 
    'running', 'walk', 'walks', 'walked', 'walking', 'move', 'moves', 'moved', 
    'moving', 'leave', 'leaves', 'left', 'leaving', 'live', 'lives', 'lived', 
    'living', 'change', 'changes', 'changed', 'changing', 'need', 'needs', 
    'needed', 'needing', 'want', 'wants', 'wanted', 'wanting', 'like', 'likes', 
    'liked', 'liking', 'love', 'loves', 'loved', 'loving', 'hate', 'hates', 
    'hated', 'hating', 'work', 'works', 'worked', 'working', 'play', 'plays', 
    'played', 'playing', 'learn', 'learns', 'learned', 'learning', 'know', 
    'knows', 'knew', 'knowing', 'believe', 'believes', 'believed', 'believing', 
    'understand', 'understands', 'understood', 'understanding', 'remember', 
    'remembers', 'remembered', 'remembering', 'forget', 'forgets', 'forgot', 
    'forgetting', 'try', 'tries', 'tried', 'trying', 'help', 'helps', 'helped', 
    'helping', 'support', 'supports', 'supported', 'supporting', 'suggest', 
    'suggests', 'suggested', 'suggesting', 'ask', 'asks', 'asked', 'asking', 
    'answer', 'answers', 'answered', 'answering', 'agree', 'agrees', 'agreed', 
    'agreeing', 'disagree', 'disagrees', 'disagreed', 'disagreeing', 'thank', 
    'thanks', 'thanked', 'thanking', 'welcome', 'welcomes', 'welcomed', 'welcoming', 
    'bye', 'goodbye', 'morning', 'afternoon', 'evening', 'night', 'day', 'week', 
    'month', 'year', 'minute', 'hour', 'time', 'moment', 'second', 'third', 'fourth', 
    'first', 'last', 'next', 'previous', 'today', 'tomorrow', 'yesterday', 
    'now', 'then', 'later', 'soon', 'before', 'after', 'early', 'late', 'fast', 
    'slow', 'quick', 'quickly', 'slowly', 'early', 'earlier', 'latest', 'current', 
    'recent', 'recently', 'soon', 'already', 'yet', 'still', 'once', 'twice', 
    'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'hundred'
}
