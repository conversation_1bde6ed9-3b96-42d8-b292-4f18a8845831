import asyncio
import json
import logging
import threading
from typing import Any, Dict, Optional

import websockets

from backend import settings

# Configure logging
logger = logging.getLogger(__name__)

class WebSocketClient:
    def __init__(self, url: str):
        """
        Initialize WebSocket client with connection URL
        
        Args:
            url (str): WebSocket server URL (e.g., 'ws://localhost:8000/ws/')
        """
        self.url = url
        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self._loop = None
        self._keep_alive = True
        self._keep_alive_task = None

    def connect(self) -> bool:
        """
        Synchronous wrapper for async connect
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Create a new event loop in a separate thread
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            
            # Run the connection in the event loop
            success = self._loop.run_until_complete(self._async_connect())
            
            if success:
                # Start the keep-alive task in a separate thread
                def run_keep_alive():
                    asyncio.set_event_loop(self._loop)
                    self._keep_alive_task = self._loop.create_task(self._keep_connection_alive())
                    try:
                        self._loop.run_forever()
                    except Exception as e:
                        logger.error(f"Keep-alive loop error: {str(e)}")
                
                self._keep_alive_thread = threading.Thread(target=run_keep_alive, daemon=True)
                self._keep_alive_thread.start()
            
            return success
            
        except Exception as e:
            logger.error(f"Sync connect failed: {str(e)}")
            return False

    async def _async_connect(self) -> bool:
        """
        Establish WebSocket connection asynchronously
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Ensure URL starts with ws:// or wss://
            if not self.url.startswith(('ws://', 'wss://')):
                self.url = f"ws://{self.url.replace('http://', '').replace('https://', '')}"
            
            logger.info(f"Attempting to connect to WebSocket at: {self.url}")
            self.websocket = await websockets.connect(
                self.url,
                # ping_interval=20,  # Keep connection alive with ping every 20 seconds
                # ping_timeout=60,   # Wait up to 60 seconds for pong response
                close_timeout=30,  # Disable auto-close
                max_size=None,
                origin=settings.WEBSOCKET_URL
            )
            logger.info(f"Successfully connected to {self.url}")
            return True
            
        except websockets.exceptions.InvalidURI as e:
            logger.error(f"Invalid WebSocket URI '{self.url}': {str(e)}")
            return False
        except websockets.exceptions.InvalidHandshake as e:
            logger.error(f"WebSocket handshake failed: {str(e)}")
            return False
        except ConnectionRefusedError as e:
            logger.error(f"Connection refused to {self.url}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Failed to connect to {self.url}: {str(e)}")
            logger.exception("Full connection error traceback:")
            return False

    async def _keep_connection_alive(self):
        """Background task to keep the WebSocket connection alive"""
        logger.info("Starting keep-alive task")
        while self._keep_alive and self.websocket:
            try:
                # Send a ping and wait for pong
                pong_waiter = await self.websocket.ping()
                await pong_waiter
                logger.debug("Ping-pong successful")
                await asyncio.sleep(20)  # Wait 20 seconds before next ping
            except Exception as e:
                logger.error(f"Error in keep-alive: {str(e)}")
                if not self._keep_alive:
                    break
                await asyncio.sleep(5)  # Wait before retry if error occurs

    def send_message(self, message: Dict[str, Any]) -> bool:
        """
        Synchronous wrapper for async send_message
        
        Args:
            message (Dict[str, Any]): Message to send (will be converted to JSON)
            
        Returns:
            bool: True if message sent successfully, False otherwise
        """
        if not self.websocket:
            logger.error("No active connection")
            return False
            
        try:
            future = asyncio.run_coroutine_threadsafe(
                self._async_send_message(message), 
                self._loop
            )
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Sync send_message failed: {str(e)}")
            return False

    async def _async_send_message(self, message: Dict[str, Any]) -> bool:
        """
        Send message through WebSocket connection asynchronously
        
        Args:
            message (Dict[str, Any]): Message to send (will be converted to JSON)
            
        Returns:
            bool: True if message sent successfully, False otherwise
        """
        if not self.websocket:
            logger.error("No active connection")
            return False
        
        try:
            # Convert dictionary to JSON string
            json_message = json.dumps(message)
            await self.websocket.send(json_message)
            logger.info(f"Message sent: {message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send message: {str(e)}")
            return False

    def close(self) -> None:
        """Manually close the WebSocket connection"""
        try:
            self._keep_alive = False
            if self._keep_alive_task:
                self._keep_alive_task.cancel()
            
            if self._loop:
                # Schedule the close in the event loop
                future = asyncio.run_coroutine_threadsafe(
                    self._async_close(), 
                    self._loop
                )
                future.result(timeout=10)
                
                # Stop the event loop
                self._loop.call_soon_threadsafe(self._loop.stop)
                
                if hasattr(self, '_keep_alive_thread'):
                    self._keep_alive_thread.join(timeout=5)
                
                self._loop.close()
                self._loop = None
                
        except Exception as e:
            logger.error(f"Error closing connection: {str(e)}")

    async def _async_close(self) -> None:
        """Close WebSocket connection asynchronously"""
        if self.websocket:
            await self.websocket.close()
            logger.info("Connection manually closed by client")
            self.websocket = None

def send_message(url: str, message: Dict[str, Any]) -> bool:
    """
    Utility function to send a single message and close connection
    
    Args:
        url (str): WebSocket server URL
        message (Dict[str, Any]): Message to send
        
    Returns:
        bool: True if operation successful, False otherwise
    """
    client = WebSocketClient(url)
    
    try:
        if not client.connect():
            return False
            
        success = client.send_message(message)
        # client.close()
        return success

    except Exception as e:
        logger.error(f"Error in send_one_message: {str(e)}")
        # client.close()
        return False

    # finally:
    #     if client.connect():
    #         client.close()
