from datetime import datetime
from typing import List

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags


def send_error_email_to_developers(
    html_path: str, subject: str, from_: str, to_: List[str], **kwargs
):
    """
    Send an email with HTML content.

    Arguments:
        html_path (str): Path to the HTML template.
        subject (str): Subject of the email.
        from_ (str): Sender's email address.
        to_ (List[str]): List of recipient email addresses.
        **kwargs: Additional context variables for the HTML template.
    """

    current_year_str = datetime.today().strftime("%Y")
    html_message = render_to_string(
        html_path,
        {**kwargs, "current_year_str": current_year_str},
    )
    plain_message = strip_tags(html_message)
    res = send_mail(
        subject,
        plain_message,
        from_,
        to_,
        html_message=html_message,
    )
    print(f"sent email titled '{subject}' to {to_},  with res: ", res)