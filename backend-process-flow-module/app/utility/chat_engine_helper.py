import datetime
import logging
from typing import Dict, List

from tenant_schemas.utils import schema_context

from app.chats.bot.models import BotSetting
from app.chats.bot.serializers import BotSettingSerializer
from app.chats.chat.models import Conversation, FlowMapping, Message, StateMachine
from app.chats.chat.serializers import FlowMappingSerializer, MessageMinimalSerializer
from app.redteam.models import RedTeamFlowMapping, RedTeamMessage
from app.redteam.serializers import (
    RedTeamFlowMappingSerializer,
    RedTeamMessageMinimalSerializer,
)

logger = logging.getLogger(__name__)


def get_recent_messages(schema_name, conversation_id, redteam_run, limit=30):
    """
    Fetch the last 30 messages from a conversation.

    Args:
        schema_name (str): Tenant schema name
        conversation_id (str): Conversation ID
        limit (int): Maximum number of messages to retrieve (default: 30)
        redteam_run (bool): Flag to indicate if the chat is a redteam run.

    Returns:
        list: List of Message objects
    """
    with schema_context(schema_name):
        if redteam_run:
            messages = (
                RedTeamMessage.objects.filter(conversation_id=conversation_id)
                .select_related("sender")
                .prefetch_related("state_machines")
                .order_by("-created_at")[:limit]
            )
            messages = sorted(
                messages, key=lambda x: x.created_at
            )  # sort by created_at because the crew ai updates the messages in the list order
            data = RedTeamMessageMinimalSerializer(messages, many=True).data
        else:
            conversation_instance = Conversation.objects.get(id=conversation_id)
            reset_context_timestamp = conversation_instance.metadata.get(
                "reset_context_timestamp"
            )
            if reset_context_timestamp and schema_name == "mj_wines":
                messages = (
                    Message.objects.filter(
                        conversation_id=conversation_id,
                        created_at__gt=reset_context_timestamp,
                    )
                    .select_related("sender")
                    .prefetch_related("state_machines")
                    .order_by("-created_at")[:limit]
                )
                messages = sorted(
                    messages, key=lambda x: x.created_at
                )  # sort by created_at because the crew ai updates the messages in the list order
                data = MessageMinimalSerializer(messages, many=True).data
            else:
                messages = (
                    Message.objects.filter(
                        conversation_id=conversation_id, bot_reply_status="completed"
                    )
                    .select_related("sender")
                    .prefetch_related("state_machines")
                    .order_by("-created_at")[:limit]
                )
            messages = sorted(
                messages, key=lambda x: x.created_at
            )  # sort by created_at because the crew ai updates the messages in the list order
            data = MessageMinimalSerializer(messages, many=True).data

        # Convert the messages into a list of messages
        messages = []
        for message in data:
            if message.get("sender"):
                sender = message.get("sender", {}).get("email") or message.get(
                    "message_type"
                )
            else:
                sender = message.get("message_type")
            messages.append(
                {
                    "sender": sender,
                    "message": message["message"],
                    "context": message["state_machines"],
                    "timestamp": message["timestamp"],
                    "attachments": message["attachments"],
                }
            )
        return messages


def get_unread_messages(schema_name, conversation_id, message_ids, redteam_run):
    """
    Fetch specific unread messages by their IDs.

    Args:
        schema_name (str): Tenant schema name
        conversation_id (str): Conversation ID
        message_ids (list): List of message IDs to retrieve
        redteam_run (bool): Flag to indicate if the chat is a redteam run.
    Returns:
        list: List of Message objects
    """
    with schema_context(schema_name):
        if redteam_run:
            messages = RedTeamMessage.objects.filter(
                conversation_id=conversation_id, id__in=message_ids
            ).order_by("-created_at")
            data = RedTeamMessageMinimalSerializer(messages, many=True).data
        else:
            messages = Message.objects.filter(
                conversation_id=conversation_id, id__in=message_ids
            ).order_by("-created_at")

            data = MessageMinimalSerializer(messages, many=True).data

        messages = []
        for message in data:
            if message.get("sender"):
                sender = message.get("sender", {}).get("email") or message.get(
                    "message_type"
                )
            else:
                sender = message.get("message_type")
            messages.append(
                {
                    "sender": sender,
                    "message": message["message"],
                    "context": message["state_machines"],
                    "timestamp": message["timestamp"],
                    "attachments": message["attachments"],
                }
            )
        return messages


def get_active_flow_mapping(schema_name: str, redteam_run: bool):
    """
    Retrieve the active flow mapping configuration.

    Args:
        schema_name (str): Tenant schema name

    Returns:
        FlowMapping: The active flow mapping object

    Raises:
        ValueError: If no active flow mapping is found
    """
    with schema_context(schema_name):
        if redteam_run:
            flow_mapping = (
                RedTeamFlowMapping.objects.filter(is_active=True)
                .order_by("-version")
                .first()
            )
        else:
            flow_mapping = (
                FlowMapping.objects.filter(is_active=True).order_by("-version").first()
            )
        if not flow_mapping:
            logger.error("No active flow mapping found")
            raise ValueError("No active flow mapping found")

        if redteam_run:
            data = RedTeamFlowMappingSerializer(flow_mapping).data
        else:
            data = FlowMappingSerializer(flow_mapping).data
        return data.get("mapping_json", {})


def get_bot_settings(schema_name: str, bot_type: str = "external"):
    """
    Retrieve the bot settings configuration.
    """
    with schema_context(schema_name):
        bot_settings = BotSetting.objects.filter(bot_type=bot_type).first()
        if not bot_settings:
            logger.error("No active bot settings found")
            raise ValueError("No active bot settings found")

        data = BotSettingSerializer(bot_settings).data
        return data


def get_conversation_name_and_number(schema_name: str, conversation_id: int):
    """
    Retrieve conversation name and number for whatsapp
    """
    try:
        conversation_name = ""
        conversation_third_party_id = ""
        with schema_context(schema_name):
            conversation = Conversation.objects.filter(id=conversation_id).first()

            if conversation:
                conversation_name = conversation.name
                conversation_third_party_id = conversation.third_party_id

        return conversation_name, conversation_third_party_id

    except Conversation.DoesNotExist:
        return "", ""


def get_recent_messages_only(recent_messages: List[dict]) -> List[dict]:
    recent_messages_only = []

    for message in recent_messages:
        recent_messages_only.append(
            {
                "sender": message["sender"],
                "message": message["message"],
                "timestamp": message["timestamp"],
            }
        )
    return recent_messages_only


def get_recent_messages_with_time_decay(recent_messages: List[dict]) -> List[dict]:
    recent_messages_only_with_time_decay = []
    recent_messages_with_time_decay = []

    now = datetime.datetime.now()

    for message in recent_messages:
        timestamp = message.get("timestamp")
        if timestamp:
            try:
                # Convert timestamp to datetime if it's not already
                if isinstance(timestamp, str):
                    # Handle different timestamp formats
                    try:
                        timestamp = datetime.datetime.fromisoformat(
                            timestamp.replace("Z", "+00:00")
                        )
                    except ValueError:
                        # Try parsing with different formats
                        try:
                            timestamp = datetime.datetime.strptime(
                                timestamp, "%Y-%m-%dT%H:%M:%S.%f%z"
                            )
                        except ValueError:
                            try:
                                timestamp = datetime.datetime.strptime(
                                    timestamp, "%Y-%m-%dT%H:%M:%S%z"
                                )
                            except ValueError:
                                # If all parsing attempts fail, skip this message
                                continue

                # Ensure timestamp is a valid datetime object
                if not isinstance(timestamp, datetime.datetime):
                    continue

                # Calculate time difference in days
                time_diff = (now - timestamp).days
            except Exception as e:
                # Skip this message if there's any error processing the timestamp
                continue

            # Skip messages older than 2 months (approx 60 days)
            if time_diff > 60:
                continue

            # Calculate priority based on time decay
            # 1.0 for messages within 7 days, decreasing by 0.125 every week up to 2 months
            if time_diff <= 7:
                priority = 1.0
            else:
                # Decay by 0.125 for each week after the first week
                weeks_after_first = (time_diff - 7) // 7
                priority = max(0.0, 1.0 - (weeks_after_first * 0.125))

            recent_messages_only_with_time_decay.append(
                {
                    "sender": message["sender"],
                    "message": message["message"],
                    "timestamp": message["timestamp"],
                    "priority": priority,
                }
            )

            recent_messages_with_time_decay.append(
                {
                    "sender": message["sender"],
                    "message": message["message"],
                    "timestamp": message["timestamp"],
                    "context": message["context"],
                    "attachments": message["attachments"],
                    "priority": priority,
                }
            )

    # Sort by priority (higher priority first)
    recent_messages_only_with_time_decay.sort(key=lambda x: x["priority"], reverse=True)

    return recent_messages_only_with_time_decay, recent_messages_with_time_decay


def get_latest_context(recent_messages: List[dict]) -> dict:
    for message in recent_messages:
        context = message.get("context")
        if context is not None and context:
            return context
    return {}
