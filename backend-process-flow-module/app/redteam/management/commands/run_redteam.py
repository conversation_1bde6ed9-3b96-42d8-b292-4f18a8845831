import json
import logging
import os
import traceback
from datetime import datetime

from django.core.management.base import BaseCommand
from tenant_schemas.utils import schema_context

from app.chats.chat.chat_engine.chat_engine import run_crew_team
from app.redteam.initialize_chat import start_conversation
from app.redteam.models import (
    RedTeamBatchRun,
    RedTeamConversation,
    RedTeamCustomerProfile,
)
from app.utility.aws_ecs import get_ecs_client, trigger_redteam_chat_conversations
from app.utility.send_email import send_error_email_to_developers
from backend.settings import (
    DEFAULT_FROM_EMAIL,
    ENVIRONMENT,
    ERROR_EMAIL_RECIPIENTS,
    LOCAL_OUTPUT,
)

logger = logging.getLogger(__name__)


def create_batch_run(seed_number=None, num_profiles=None, status="Pending"):
    try:
        seed_number = int(seed_number)
    except (ValueError, TypeError):
        seed_number = None

    batch_run = RedTeamBatchRun.objects.create(status=status,
                                               num_profiles=num_profiles)

    if seed_number:
        batch_run.seed_id = seed_number
    else:
        batch_run.seed_id = batch_run.id
        seed_number = batch_run.id

    batch_run.save()
    return batch_run, seed_number


def update_batch_run(batch_run_id, status="Succeed"):
    batch_run = RedTeamBatchRun.objects.filter(id=batch_run_id).first()
    batch_run.status = status
    batch_run.end_time = datetime.now()
    batch_run.save()
    return batch_run


def update_customer_profile(tenant_name: str, customer_profile: dict, run_with_redteam: bool = True) -> RedTeamCustomerProfile:
    """
    Creates or updates a RedTeamCustomerProfile for a conversation.

    Args:
        tenant_name (str): The schema/tenant name
        customer_profile (dict): Dictionary containing customer profile information
        run_with_redteam (bool): Whether to run the red team evaluation

    Returns:
        RedTeamCustomerProfile: The created/updated customer profile object
    """
    customer_profile_obj = None
    if run_with_redteam:
        try:
            customer_profile_dict = json.loads(str(customer_profile.get('customer_profile')))
        except json.JSONDecodeError:
            customer_profile_dict = str(customer_profile.get('customer_profile')).replace("'", '"')

        try:
            objectives_dict = json.loads(str(customer_profile.get('objectives')))
        except json.JSONDecodeError:
            objectives_dict = str(customer_profile.get('objectives')).replace("'", '"')

        customer_profile_obj = RedTeamCustomerProfile.objects.create(
            description=customer_profile_dict,
            customer_background=customer_profile.get('journey_stage', ""),
            context=customer_profile.get('scenario_context', {}),
            personality=customer_profile.get('personality', {}),
            objectives=objectives_dict,
            additional_details=customer_profile.get('additional_details', {})
        )

    if LOCAL_OUTPUT:
        # Save the json into a file where the file name is the conversation id
        customer_profile_obj_id = str(customer_profile_obj.id)
        file_path = f"app/redteam/agents_and_tasks/{tenant_name}/customer_profiles/{customer_profile_obj_id}.json"
        # Create the directory if it doesn't exist
        if not os.path.exists(os.path.dirname(file_path)):
            os.makedirs(os.path.dirname(file_path))
        with open(file_path, "w") as f:
            json.dump(customer_profile, f)

    return customer_profile_obj


class Command(BaseCommand):
    help = 'Runs the red team evaluation process'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str)
        parser.add_argument(
            '--num_profiles',
            type=int,
            default=10,
            help='Number of customer profiles to generate (default: 10)'
        )
        parser.add_argument(
            '--ip_address',
            type=str,
            default='**********:8001',
            help='IP address for the backend (default: **********:8001)'
        )
        parser.add_argument(
            '--seed_number',
            type=int,
            default=None,
            help='Seed number for random generation (default: None)'
        )

    def handle(self, *args, **options):
        try:
            # Get the arguments
            schema_name = options['schema_name']
            num_profiles = options['num_profiles']
            ip_address = options['ip_address']
            seed_number = options['seed_number']
            redteam_run = str(os.environ.get("REDTEAM_RUN", False)).lower() == "true"

            print(f"Schema name: {schema_name}")
            print(f"Number of profiles: {num_profiles}")
            print(f"Seed Number: {seed_number}")
            print(f"Redteam Run: {redteam_run}")

            with schema_context(schema_name):
                # Step 1: Create a new batch run
                batch_run = None
                if redteam_run:
                    batch_run, seed_number = create_batch_run(seed_number, num_profiles)
                batch_run_id = batch_run.id if batch_run else None

                # Step 2: Declare the variables
                crew_variables = {
                    "previous_feedback": "",
                    "company_metadata": "",
                    "company_knowledge": "",
                    "existing_customer_profiles": "",
                    "num_of_customer_profiles": num_profiles,
                    "date_today": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }

                # TODO: Step 3: Get the conversation history and formulate the nature of the customer

                # Step 4: Retrieve context of the company knowledge to formulate the nature of the customer
                module_name = f"app.crew_teams.tools.{schema_name}.get_client_background_context_redteam"
                get_client_context = __import__(module_name, fromlist=['get_client_context']).get_client_context
                documents = get_client_context()

                crew_variables["company_knowledge"] = documents

                # Step 5: Create the customer profiles
                customer_profiles, _, _, _ = run_crew_team(crew_variables, f"app/crew_teams/agents_and_tasks/{schema_name}/redteam_customer_creation_team.yml", seed_number=seed_number)

                # Step 6: Convert customer_profiles into a list of dictionaries
                if isinstance(customer_profiles, str):
                    customer_profiles = json.loads(customer_profiles)

                # Step 7: Save the customer profiles to the database
                customer_profile_obj_array = []
                for customer_profile in customer_profiles.get("scenarios", []):
                    customer_profile_obj = update_customer_profile(schema_name, customer_profile, redteam_run)
                    customer_profile_obj_array.append(customer_profile_obj)

                # Step 6: Loop through each of the customer profiles and starts the chat
                print(f"Number of customer profiles: {len(customer_profiles)}")
                for customer_profile in customer_profile_obj_array:
                    print(customer_profile)

                    if ENVIRONMENT in ["Production", "Staging", "Test"]:
                        ecs_client = get_ecs_client()
                        trigger_redteam_chat_conversations(ecs_client, schema_name, customer_profile.id, ip_address, batch_run_id, redteam_run, seed_number)
                    else:
                        start_conversation(schema_name, customer_profile.id, ip_address, batch_run_id, redteam_run, seed_number)

                if not redteam_run and ENVIRONMENT not in ["Production", "Staging", "Test"]:
                    update_batch_run(schema_name, batch_run_id)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Unexpected error: {e}")

            # Send error email to developer team
            from_ = f"UtterUnicorn V2 X {schema_name} <{DEFAULT_FROM_EMAIL}>"
            html_path = "email/error_notification.html"
            subject = f"Error in Running Run RedTeam Command for {schema_name}"
            to_ = ERROR_EMAIL_RECIPIENTS.split(',')
            custom_kwargs = {
                "schema_name": schema_name,
                "environment": ENVIRONMENT,
                "num_profiles": num_profiles,
                "seed_number": seed_number,
                "redteam_run": redteam_run,
                "ip_address": ip_address,
                "message": "".join(traceback.format_exception(type(e), e, e.__traceback__))
            }

            send_error_email_to_developers(html_path, subject, from_, to_, **custom_kwargs)

            raise e