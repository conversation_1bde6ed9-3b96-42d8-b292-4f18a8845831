import json
import logging
import os
import traceback
from datetime import datetime

import pandas as pd
import yaml
from django.core.management.base import BaseCommand
from tenant_schemas.utils import schema_context

from app.chats.bot.models import BotSetting
from app.chats.chat.chat_engine.chat_engine import run_crew_team
from app.crew_teams.models import (
    RedTeamCrewTeamChatRecommendations,
    RedTeamCrewTeamLogs,
)
from app.redteam.models import (
    RedTeamConversation,
    RedTeamConversationEvaluation,
    RedTeamCustomerProfile,
    RedTeamMessage,
)
from app.utility.send_email import send_error_email_to_developers
from backend.settings import DEFAULT_FROM_EMAIL, ENVIRONMENT, ERROR_EMAIL_RECIPIENTS

logger = logging.getLogger(__name__)


def get_task_output_into_dict(task_output):
    if isinstance(task_output, str):
        task_output = task_output.replace("```json", "")
        task_output = task_output.replace("```", "")
    if isinstance(task_output, str):
        task_output = json.loads(task_output)

    return task_output


def get_seed_number(redteam_conversation_id):
    conversation = RedTeamConversation.objects.filter(id=redteam_conversation_id).select_related('batch_run').first()
    batch_run = conversation.batch_run
    return batch_run.seed_id


def save_recommendations_to_db(recommendation: dict, original_draft: str, updated_content: str, yaml_path: str, redteam_conversation_id: int):
    """Save crew team recommendations to database.
    
    Args:
        prompt_recommendations: An array of dictionary containing original and updated prompts
        yaml_path: Path to YAML config file
        redteam_conversation_id: ID of the conversation
        
    """
    recommendation_type = recommendation.get("type_")
    recommendation_name = recommendation.get("name")
    recommendation_component = recommendation.get("component")
    recommendation_reasoning = recommendation.get("reasoning")
    recommendation_impact = recommendation.get("impact_analysis")
    recommendation_risk = recommendation.get("risk_analysis")
    recommendation_expected = recommendation.get("expected_outcome")

    crew_recommendation = RedTeamCrewTeamChatRecommendations.objects.create(
        conversation_id=redteam_conversation_id,
    )

    crew_recommendation.original_prompt = original_draft
    crew_recommendation.updated_prompt = updated_content
    crew_recommendation.reasoning = recommendation_reasoning
    crew_recommendation.impact_analysis = recommendation_impact
    crew_recommendation.risk_analysis = recommendation_risk
    crew_recommendation.expected_outcome = recommendation_expected

    crew_recommendation.agent_name = recommendation_name if recommendation_type.lower() == 'agent' else None
    crew_recommendation.task_name = recommendation_name if recommendation_type.lower() == 'task' else None

    crew_recommendation.yaml_path = yaml_path

    crew_recommendation.is_completed = False
    crew_recommendation.allowed_internal_to_approve = True
    crew_recommendation.allowed_external_to_approve = False
    crew_recommendation.save()


class Command(BaseCommand):
    help = 'Runs the red team evaluation process'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str)
        parser.add_argument("redteam_conversation_id", type=int)
        parser.add_argument(
            '--bot_setting',
            type=str,
            choices=['external', 'internal'],
            default='external',
            help='Bot setting to use (default: external)'
        )

    def handle(self, *args, **options):
        try:
            schema_name = options['schema_name']
            redteam_conversation_id = options['redteam_conversation_id']
            bot_setting = options['bot_setting']
            bot_setting = "internal" if str(bot_setting).lower() == "internal" else "external"

            print(f"Schema name: {schema_name}")
            print(f"Redteam conversation id: {redteam_conversation_id}")
            print(f"Bot setting: {bot_setting}")

            with schema_context(schema_name):
                # Extract all the information from the redteam conversation
                redteam_message_qs = RedTeamMessage.objects.filter(conversation_id=redteam_conversation_id).order_by('created_at')

                # Get all the redteam crew messages
                redteam_crew_message_qs = RedTeamCrewTeamLogs.objects.filter(conversation_id=redteam_conversation_id).order_by('created_at')

                # Get the customer profile
                customer_profile = RedTeamCustomerProfile.objects.filter(conversation_id=redteam_conversation_id).first()

                # Retrieve the bot setting
                bot_setting = BotSetting.objects.filter(bot_type=bot_setting).first()

                # Get the seed number
                seed_number = get_seed_number(redteam_conversation_id)

                # Prepare for crew_variables
                # Get the conversation only
                conversation_only = []
                for message in redteam_message_qs:
                    created_at = message.created_at.strftime('%d-%m-%Y %H:%M:%S') if isinstance(message.created_at, datetime) else message.created_at
                    conversation_only.append({"sender": message.message_type, "message": message.message, "created_at": created_at})

                # Construct the conversation for flow analysis
                conversation_for_flow_analysis = conversation_only.copy()
                agent_node_analyzer_qs = redteam_crew_message_qs.filter(agent_name="Node Purpose Analyzer")

                for agent_node in agent_node_analyzer_qs:
                    created_at = agent_node.created_at.strftime('%d-%m-%Y %H:%M:%S') if isinstance(agent_node.created_at, datetime) else agent_node.created_at
                    conversation_for_flow_analysis.append({"agent name": "Node Selector Agent", "node purpose": agent_node.task_raw, "created_at": created_at})
                # Sort the conversation_for_flow_analysis by created_at
                conversation_for_flow_analysis = sorted(conversation_for_flow_analysis, key=lambda x: x['created_at'])

                # Construct for the agent prompt recommendation
                agent_prompts = []
                for agent_task in redteam_crew_message_qs.filter(is_final_output=True):
                    agent_prompts.append({
                        "agent name": agent_task.agent_name,
                        # "agent role": agent_task.agent_role,
                        "agent goal": agent_task.agent_goal,
                        "agent backstory": agent_task.agent_backstory,
                        "task description": agent_task.task_description,
                        "task expected output": agent_task.task_expected_output,
                        # "task actual output": agent_task.task_raw
                    })

                crew_variables = {
                    "conversation_transcript": conversation_only,
                    "customer_objectives": customer_profile.objectives,
                    "bot_setting": bot_setting.tone if bot_setting else None,
                    "conversation_for_flow_analysis": conversation_for_flow_analysis,
                    # "agent_prompts": agent_prompts
                }

                # Save the assessment to the db
                conversation_assessment, _ = RedTeamConversationEvaluation.objects.get_or_create(conversation_id=redteam_conversation_id)

                max_attempt = 5
                attempt = 0

                while attempt < max_attempt:
                    try:
                        # Run the redteam assessor
                        _, _, task_full_dicts, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_chat_analysis_crew.yml", seed_number=seed_number)

                        summary_done = False
                        objective_done = False
                        quality_done = False
                        tone_done = False
                        satisfaction_done = False
                        flow_done = False

                        for task in task_full_dicts:
                            agent_name = task.get("agent")

                            # Update the conversation summary
                            if agent_name == "Conversation Summarizer":
                                print("Conversation Summarizer")
                                pydantic_model = task.get("pydantic")
                                print(pydantic_model)
                                task_output = get_task_output_into_dict(pydantic_model.model_dump())
                                conversation_assessment.conversation_summary = task_output.get("conversation_summary")
                                summary_done = True

                            # Update the objective evaluation
                            elif agent_name == "Objective Evaluation Agent":
                                print("Objective Evaluation Agent")
                                pydantic_model = task.get("pydantic")
                                print(pydantic_model)
                                task_output = get_task_output_into_dict(pydantic_model.model_dump())
                                conversation_assessment.objective_met = task_output.get("objective_met")
                                conversation_assessment.objective_failed_reasoning = task_output.get("objective_reasoning")
                                objective_done = True

                            # Update evaluate quality
                            elif agent_name == "Answer Quality Evaluation Agent":
                                print("Answer Quality Evaluation Agent")
                                pydantic_model = task.get("pydantic")
                                print(pydantic_model)
                                task_output = get_task_output_into_dict(pydantic_model.model_dump())
                                conversation_assessment.accuracy_of_answers = task_output.get("accuracy", {}).get("score")
                                conversation_assessment.accuracy_of_answers_reasoning = task_output.get("accuracy", {}).get("reason")
                                conversation_assessment.clarity_of_answers = task_output.get("clarity", {}).get("score")
                                conversation_assessment.clarity_of_answers_reasoning = task_output.get("clarity", {}).get("reason")
                                conversation_assessment.empathy_of_answers = task_output.get("empathy", {}).get("score")
                                conversation_assessment.empathy_of_answers_reasoning = task_output.get("empathy", {}).get("reason")
                                conversation_assessment.relevance_of_answers = task_output.get("relevance", {}).get("score")
                                conversation_assessment.relevance_of_answers_reasoning = task_output.get("relevance", {}).get("reason")
                                quality_done = True

                            # Update tone analysis
                            elif agent_name == "Tone Analysis Agent":
                                print("Tone Analysis Agent")
                                pydantic_model = task.get("pydantic")
                                print(pydantic_model)
                                task_output = get_task_output_into_dict(pydantic_model.model_dump())
                                conversation_assessment.tone_appropriateness = task_output.get("tone_evaluation", {}).get("score")
                                conversation_assessment.tone_appropriateness_reasoning = task_output.get("tone_evaluation", {}).get("reason")
                                tone_done = True

                            elif agent_name == "Customer Satisfaction Analyst":
                                print("Customer Satisfaction Analyst")
                                pydantic_model = task.get("pydantic")
                                print(pydantic_model)
                                task_output = get_task_output_into_dict(pydantic_model.model_dump())
                                conversation_assessment.customer_satisfaction_rating = task_output.get("satisfaction_metrics", {}).get("satisfaction_score")
                                customer_satisfaction_reasoning = {
                                    "key_factors": task_output.get("satisfaction_metrics", {}).get("key_factors"),
                                    "improvement_recommendations": task_output.get("satisfaction_metrics", {}).get("improvement_recommendations")
                                }
                                conversation_assessment.customer_satisfaction_reasoning = customer_satisfaction_reasoning
                                satisfaction_done = True

                            elif agent_name == "Message Flow Analyzer":
                                print("Message Flow Analyzer")
                                pydantic_model = task.get("pydantic")
                                print(pydantic_model)
                                task_output = get_task_output_into_dict(pydantic_model.model_dump())
                                conversation_assessment.flow_analysis = task_output.get("flow_metrics", {}).get("flow_efficiency_score")
                                flow_analysis_reasoning = {
                                    "primary_issues": task_output.get("flow_metrics", {}).get("primary_issues"),
                                    "improvement_suggestions": task_output.get("flow_metrics", {}).get("improvement_suggestions")
                                }
                                conversation_assessment.flow_analysis_reasoning = flow_analysis_reasoning
                                flow_done = True

                        logger.info([
                            summary_done,
                            objective_done,
                            quality_done,
                            tone_done,
                            satisfaction_done,
                            flow_done,
                        ])
                        if all([
                            summary_done,
                            objective_done,
                            quality_done,
                            tone_done,
                            satisfaction_done,
                            flow_done,
                        ]):
                            logger.info("All assessments are completed.")
                            break

                    except json.JSONDecodeError as e:
                        traceback.print_exc()

                        if attempt == max_attempt - 1:
                            raise e
                        
                        print("="*100)
                        print(task.get("raw"))
                        print("="*100) 

                        logger.warning(f"JSONDecodeError: Attempt {attempt + 1} failed. Retrying...")
                        attempt += 1

                # Create the crew team to provide prompt recommendations
                # Get the agent and task set that needs to be changed.
                # To process agent_prompts as chunks 
                chunk_size = 20
                agent_prompt_chunks = [agent_prompts[i:i + chunk_size] for i in range(0, len(agent_prompts), chunk_size)]
                
                chunk_task_output_list = []
                for chunk in agent_prompt_chunks:
                    crew_variables_list= {
                        "agent_prompt": chunk,
                        "answer_quality": {
                            "accuracy": conversation_assessment.accuracy_of_answers_reasoning,
                            "clarity": conversation_assessment.clarity_of_answers_reasoning,
                            "empathy": conversation_assessment.empathy_of_answers_reasoning,
                            "relevance": conversation_assessment.relevance_of_answers_reasoning,
                        },
                        "tone_analysis": conversation_assessment.tone_appropriateness_reasoning,
                        "flow_analysis": conversation_assessment.flow_analysis_reasoning
                    }

                    chunk_task_output, _, _, _ = run_crew_team(crew_variables_list, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_chat_agent_selector_chunk_crew.yml", seed_number=seed_number)
                    chunk_task_output_list.append(chunk_task_output)

                # Summarize the chunk_task_output_list
                crew_variables = {
                    "chunk_task_output_list": chunk_task_output_list
                }

                task_output, _, _, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_chat_agent_selector_crew.yml", seed_number=seed_number)

                if isinstance(task_output, str):
                    task_output = json.loads(task_output)

                agent_to_update = task_output.get("final_recommendations", [])

                for agent_task in agent_to_update:
                    agent_name = agent_task.get("agent_name")

                    # Get the full agent information
                    agent_qs = redteam_crew_message_qs.filter(agent_name=agent_name).first()

                    if not agent_qs:
                        logger.error(f"Agent {agent_name} not found in the database.")
                        continue

                    # Get the yaml file
                    yaml_path = agent_qs.yaml_path

                    actual_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

                    try:
                        # Read the existing YAML file
                        with open(os.path.join(actual_path, yaml_path), 'r') as f:
                            yaml_content = yaml.safe_load(f)
                    
                    except FileNotFoundError:
                        logger.error(f"YAML file not found: {yaml_path}")
                        continue

                    # loop through the yaml content agents to get the role, goal and backstory
                    agents = []
                    for agent_name, value in yaml_content["agents"].items():
                        agents.append({
                            "name": agent_name,
                            "goal": value.get("goal"),
                            "backstory": value.get("backstory")
                        })

                    # loop through the yaml content tasks to get the description and backstory
                    tasks = []
                    for task_name, value in yaml_content["tasks"].items():
                        tasks.append({
                            "name": task_name,
                            "agent": value.get("agent"),
                            "description": value.get("description")
                        })
                    
                    agent_crew_variables = {
                        "recommendations": agent_task,
                        "agent_prompt": {
                            "agents": agents,
                            "tasks": tasks,
                        }
                    }

                    task_output, _, _, _ = run_crew_team(agent_crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_chat_agent_recommendation_crew.yml", seed_number=seed_number)
                    print(f"{task_output=}")
                    for recommendation in task_output.get("tasks", []):
                        try:
                            recommendation_type = recommendation.get("type_")
                            recommendation_name = recommendation.get("name")
                            recommendation_component = recommendation.get("component")
                            recommendation_reasoning = recommendation.get("reasoning")
                            recommendation_impact = recommendation.get("impact_analysis")
                            recommendation_risk = recommendation.get("risk_analysis")
                            recommendation_expected = recommendation.get("expected_outcome")

                            # Update the YAML file with the new values
                            if str(recommendation_type).lower() == "agent":
                                original_agent = yaml_content["agents"][recommendation_name]
                                original_draft = original_agent[recommendation_component]

                            else:
                                original_task = yaml_content["tasks"][recommendation_name]
                                original_draft = original_task[recommendation_component]

                            recommendation_crew_variables = {
                                "original_draft": original_draft,
                                "recommendation_reasoning": recommendation_reasoning,
                                "recommendation_impact": recommendation_impact,
                                "recommendation_risk": recommendation_risk,
                                "recommendation_expected": recommendation_expected,
                            }

                            recommendation_task_output, _, _, _ = run_crew_team(recommendation_crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_chat_agent_recommendation_prompt_crew.yml", seed_number=seed_number)

                            # Update the YAML file with the new values
                            if str(recommendation_type).lower() == "agent":
                                yaml_content["agents"][recommendation_name][recommendation_component] = recommendation_task_output

                            # Save to db
                            save_recommendations_to_db(recommendation, original_draft, recommendation_task_output, yaml_path, redteam_conversation_id)
                        except KeyError:
                            logger.error(f"Agent {recommendation_name} not found in the YAML file.")
                            continue 

                # Calculate the number of messages exchange
                conversation_only = []
                for message in redteam_message_qs:
                    conversation_only.append({"sender": message.message_type, "message": message.message, "created_at": message.created_at})

                # Convert conversation_only into df
                conversation_only_df = pd.DataFrame(conversation_only)
                conversation_only_df["group"] = (conversation_only_df["sender"] != conversation_only_df["sender"].shift()).cumsum()

                grouped = conversation_only_df.groupby(["group", "sender"]).agg(
                    created_at=("created_at", lambda x: x.iloc[-1] if x.name[1] == "user" else x.iloc[0])
                ).reset_index()

                # Re-filter user and bot_text rows with the corrected datetime
                user_rows = grouped[grouped["sender"] == "user"].reset_index(drop=True)
                bot_text_rows = grouped[grouped["sender"] == "bot_text"].reset_index(drop=True)

                # Recalculate response times between user and bot_text rows
                if len(user_rows) > len(bot_text_rows):
                    user_rows = user_rows.iloc[:len(bot_text_rows)]

                response_times = (bot_text_rows["created_at"] - user_rows["created_at"]).dt.total_seconds()
                first_quartile = response_times.quantile(0.25)
                third_quartile = response_times.quantile(0.75)

                # Calculate statistics
                response_stats = {
                    "min_response_time": int(response_times.min()),
                    "first_quartile_response_time": int(first_quartile),
                    "third_quartile_response_time": int(third_quartile),
                    "max_response_time": int(response_times.max()),
                    "mean_response_time": int(response_times.mean()),
                    "median_response_time": int(response_times.median()),
                    "raw_time_taken": [int(time) for time in response_times]
                }
                conversation_assessment.message_response_time = response_stats

                # Calculate the number of messages exchange
                conversation_assessment.number_of_messages_exchanged = len(user_rows)
                
                conversation_assessment.save()

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Unexpected error: {e}")

            # Send error email to developer team
            from_ = f"UtterUnicorn V2 X {schema_name} <{DEFAULT_FROM_EMAIL}>"
            html_path = "email/error_notification.html"
            subject = f"Error in Running RedTeam Assessor Deployment for {schema_name}"
            to_ = ERROR_EMAIL_RECIPIENTS.split(',')
            custom_kwargs = {
                "schema_name": schema_name,
                "environment": ENVIRONMENT,
                "redteam_conversation_id": redteam_conversation_id,
                "message": "".join(traceback.format_exception(type(e), e, e.__traceback__))
            }

            send_error_email_to_developers(html_path, subject, from_, to_, **custom_kwargs)
            raise e
