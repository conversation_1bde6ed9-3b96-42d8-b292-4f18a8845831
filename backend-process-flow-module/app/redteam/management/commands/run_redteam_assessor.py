import json
from datetime import datetime, timedelta
from typing import List

import pandas as pd
import yaml
from django.core.management.base import BaseCommand
from django.forms.models import model_to_dict
from django.utils import timezone
from tenant_schemas.utils import schema_context

from app.chats.chat.chat_engine.chat_engine import run_crew_team
from app.crew_teams.models import RedTeamCrewTeamChatRecommendations
from app.crew_teams.tools.chromadb_tools import get_all_documents
from app.redteam.initialize_chat import start_conversation
from app.redteam.models import (
    RedTeamBatchRun,
    RedTeamConversation,
    RedTeamConversationQueue,
)


def get_redteam_conversations(schema_name: str, batch_run_id: int):
    print(f"Getting redteam conversations for {schema_name}")
    with schema_context(schema_name):
        from django.db import connection
        with connection.cursor() as cursor:
            # Build query with schema name and timestamp filter
            query = f"""
                SELECT DISTINCT c.* 
                FROM {schema_name}.redteam_redteamconversation c
                WHERE c.batch_run_id = {batch_run_id}
                ORDER BY c.created_at DESC
                LIMIT 15
            """
            cursor.execute(query)
            
            # Convert results to RedTeamConversation objects
            columns = [col[0] for col in cursor.description]
            conversations = [
                RedTeamConversation(**dict(zip(columns, row)))
                for row in cursor.fetchall()
            ]
            
            return conversations


def get_seed_number(schema_name: str, batch_run_id: int):
    with schema_context(schema_name):
        batch_run = RedTeamBatchRun.objects.filter(id=batch_run_id).first()
        return batch_run.seed_id


def extract_messages_from_conversation(schema_name: str, conversation: RedTeamConversation):
    print(f"Extracting messages from conversation {conversation.id} for {schema_name}")
    messages = []
    with schema_context(schema_name):
        for message in conversation.messages.all().order_by('created_at'):
            messages.append({
                "sender": message.message_type,
                "message": message.message,
                "created_at": message.created_at
            })
    return messages


def extract_customer_profile_from_conversation(schema_name: str, conversation: RedTeamConversation):
    print(f"Extracting customer profile from conversation {conversation.id} for {schema_name}")
    with schema_context(schema_name):
        customer_profile = conversation.customer_profiles.first()
        return model_to_dict(customer_profile)


def extract_crewteam_logs_from_conversation(schema_name: str, conversation: RedTeamConversation):
    print(f"Extracting crewteam logs from conversation {conversation.id} for {schema_name}")
    with schema_context(schema_name):
        # Get the conversation queue
        redteam_conversation_queue = RedTeamConversationQueue.objects.filter(conversation=conversation)
        
        queue_list = []
        for queue in redteam_conversation_queue:
            crewteam_logs = queue.crewteam_logs.all()
            queue_list.extend([{
                "task_description": log.task_description,
                "task_name": log.task_name,
                "task_expected_output": log.task_expected_output,
                "task_summary": log.task_summary,
                "task_raw": log.task_raw,
                "agent_name": log.agent_name,
                "agent_role": log.agent_role,
                "agent_goal": log.agent_goal,
                "agent_backstory": log.agent_backstory,
                "yaml_path": log.yaml_path,
                "created_at": log.created_at
            } for log in crewteam_logs])

        # Convert it into a list of dictionaries
        return queue_list


def convert_dict_to_df(dict_data: List[dict]):
    """Convert list of recommendation dictionaries to DataFrame.
    
    Args:
        dict_data: List of dictionaries containing recommendation data with fields:
            - name: Name of task/agent
            - _type: "task" or "agent" 
            - yaml_path: Path to YAML config
            - reason: Reason for modification
            - recommendation: List of improvement suggestions
            
    Returns:
        pd.DataFrame with columns matching the recommendation fields
    """
    dict_data = dict_data.get('underperforming_tasks_agents', [])
    df = pd.DataFrame(dict_data)
    # Ensure expected columns are present
    expected_cols = ['name', '_type', 'yaml_path', 'reason', 'recommendation']
    for col in expected_cols:
        if col not in df.columns:
            df[col] = None
    return df


def save_recommendations_to_db(schema_name: str, prompt_recommendations: dict, yaml_path: str, type_: str, name: str):
    """Save crew team recommendations to database.
    
    Args:
        schema_name: Schema name for tenant
        prompt_recommendations: Dictionary containing original and updated prompts
        yaml_path: Path to YAML config file
        type_: Type of recommendation ('task' or 'agent') 
        name: Name of task or agent
    """
    with schema_context(schema_name):
        RedTeamCrewTeamChatRecommendations.objects.create(
            original_prompt=prompt_recommendations.get('original_prompt', {}),
            updated_prompt=prompt_recommendations.get('updated_prompt', {}),
            yaml_path=yaml_path,
            agent_name=name if type_.lower() == 'agent' else None,
            task_name=name if type_.lower() == 'task' else None,
            is_completed=False,
            allowed_internal_to_approve=True,
            allowed_external_to_approve=False
        )


class Command(BaseCommand):
    help = 'Runs the red team evaluation process'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str)
        parser.add_argument("batch_run_id", type=int)

    def handle(self, *args, **options):
        try:
            schema_name = options['schema_name']
            batch_run_id = options['batch_run_id']
            df = None

            # Step 1: Get the redteam conversations that are within 1 day
            conversations = get_redteam_conversations(schema_name, batch_run_id)

            seed_number = get_seed_number(schema_name, batch_run_id)

            # Step 2: For each conversation, get the ratings and feedback
            for conversation in conversations:
                messages = extract_messages_from_conversation(schema_name, conversation)
                customer_profile = extract_customer_profile_from_conversation(schema_name, conversation)

                # Get the chat rating
                crew_variables = {
                    "chat_history": messages,
                    "customer_profile": customer_profile,
                    "previous_feedback": None
                }
                chat_rating, _, _, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor_chat_rating.yml", seed_number=seed_number)

                # Convert the output into json
                if isinstance(chat_rating, str):
                    chat_rating = json.loads(chat_rating)

                # Calculate the overall score. Max is 40
                overall_score = chat_rating['responsiveness']['score'] + chat_rating['accuracy']['score'] + chat_rating['clarity']['score'] + chat_rating['tone']['score']

                if overall_score >= 30:
                    print(f"Conversation {conversation.id} has an overall score of {overall_score}")

                    continue
                
                # Step 3: For each of the conversation, we will need to pass in all the crewteam logs and get it to recommend which task/agent to update and the suggestions
                crewteam_logs = extract_crewteam_logs_from_conversation(schema_name, conversation)

                # Combine the crewteam_logs with messages and sort by created_at
                combined_data = crewteam_logs + messages
                combined_data = sorted(combined_data, key=lambda x: x['created_at'])

                # Run the crew team to get the recommendations
                crew_variables = {
                    "crewteam_logs": combined_data,
                    "feedback_data": chat_rating
                }
                crew_recommendations, _, _, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor_review.yml", seed_number=seed_number)

                # Convert the output into json
                if isinstance(crew_recommendations, str):
                    crew_recommendations = json.loads(crew_recommendations)

                # Step 5: Compile all the suggestions and sort it by the crew team and by task and agent
                df_tmp = convert_dict_to_df(crew_recommendations)

                if df is None:
                    df = df_tmp
                else:
                    df = pd.concat([df, df_tmp])

            # Group by the yaml path and then by the _type and then by the name
            grouped = df.groupby(['yaml_path', '_type', 'name']).agg({'reason': 'first', 'recommendation': 'sum'})

            for index, row in grouped.iterrows():
                # Step 6: For each of the task/agent, will need to read the yaml and propose a change to it.
                yaml_path, type_, name = index
                
                # Read the existing YAML file
                with open(yaml_path, 'r') as f:
                    yaml_content = yaml.safe_load(f)
                
                # Find the specific task/agent in the YAML
                target_item = None
                if str(type_).lower() == 'task':
                    target_item = next((yaml_content['tasks'][task] for task in yaml_content['tasks'] if task == name), None)
                elif str(type_).lower() == 'agent':
                    target_item = next((yaml_content['agents'][agent] for agent in yaml_content['agents'] if yaml_content['agents'][agent]['role'] == name), None)
                
                if target_item:
                    # Create proposed changes based on recommendations
                    proposed_changes = {
                        'yaml_path': yaml_path,
                        'type': str(type_).lower(),
                        'name': name,
                        'current_content': target_item,
                        'reason': row['reason'],
                        'recommendations': row['recommendation'],
                        "previous_feedback": None
                    }

                    # Step 7: Set up a crew team to review the proposed changes and summarize it
                    prompt_recommendations, _, _, _ = run_crew_team(proposed_changes, "app/crew_teams/agents_and_tasks/redteam_assessor_recommendations.yml", seed_number=seed_number)

                    if isinstance(prompt_recommendations, str):
                        prompt_recommendations = json.loads(prompt_recommendations)
                    
                    # Step 8: Save the suggestion to the database
                    save_recommendations_to_db(schema_name, prompt_recommendations, yaml_path, type_, name)
            
        except Exception as e:
            print(e)
            raise e
