import logging
import os
import traceback

from django.core.management.base import BaseCommand
from tenant_schemas.utils import schema_context

from app.redteam.initialize_chat import start_conversation
from app.utility.aws_ecs import get_ecs_client, trigger_redteam_chat_assessment
from app.utility.send_email import send_error_email_to_developers
from backend.settings import (
    DEFAULT_FROM_EMAIL,
    ENVIRONMENT,
    ERROR_EMAIL_RECIPIENTS,
    LOCAL_OUTPUT,
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Runs the red team chat'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='Schema name for the red team conversations')
        parser.add_argument('customer_profile_id', type=int, help='Customer profile ID')
        parser.add_argument('batch_run_id', type=int, help='Batch run ID')
        parser.add_argument(
            '--ip_address',
            type=str,
            default='**********:8001',
            help='IP address for the backend (default: **********:8001)'
        )
        parser.add_argument(
            '--redteam_run',
            action='store_true',
            help='Flag indicating if it\'s a red team run'
        )
        parser.add_argument(
            '--seed_number',
            type=int,
            default=None,
            help='Seed number for random generation (default: None)'
        )

    def handle(self, *args, **options):
        try:
            # Get the arguments
            schema_name = options['schema_name']
            customer_profile_id = options['customer_profile_id']
            batch_run_id = options['batch_run_id']
            ip_address = options['ip_address']
            seed_number = options['seed_number']
            redteam_run = options['redteam_run']

            # Set the environment variable
            os.environ['REDTEAM_RUN'] = str(str(redteam_run).lower() == "true")

            print(f"Schema name: {schema_name}")
            print(f"Customer Profile ID: {customer_profile_id}")
            print(f"Batch Run ID: {batch_run_id}")
            print(f"Seed Number: {seed_number}")
            print(f"Redteam Run: {redteam_run}")

            conversation_id = start_conversation(schema_name, customer_profile_id, ip_address, batch_run_id, redteam_run, seed_number)

            # Call the ECS task for Assessor
            ecs_client = get_ecs_client()

            # Trigger ECS task for running red team conversations
            trigger_redteam_chat_assessment(ecs_client, schema_name, conversation_id)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Unexpected error: {e}")

            # Send error email to developer team
            from_ = f"UtterUnicorn V2 X {schema_name} <{DEFAULT_FROM_EMAIL}>"
            html_path = "email/error_notification.html"
            subject = f"Error in Running Run RedTeam Chat Command for {schema_name}"
            to_ = ERROR_EMAIL_RECIPIENTS.split(',')
            custom_kwargs = {
                "schema_name": schema_name,
                "environment": ENVIRONMENT,
                "customer_profile_id": customer_profile_id,
                "batch_run_id": batch_run_id,
                "seed_number": seed_number,
                "redteam_run": redteam_run,
                "ip_address": ip_address,
                "message": "".join(traceback.format_exception(type(e), e, e.__traceback__))
            }

            send_error_email_to_developers(html_path, subject, from_, to_, **custom_kwargs)

            raise e
