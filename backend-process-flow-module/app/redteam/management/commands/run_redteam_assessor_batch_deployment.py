import json
import logging
import traceback
from typing import List

import pandas as pd
import yaml
from django.core.management.base import BaseCommand
from tenant_schemas.utils import schema_context

from app.chats.chat.chat_engine.chat_engine import run_crew_team
from app.crew_teams.models import CrewTeamRecommendations, RedTeamCrewTeamLogs
from app.redteam.models import (
    RedTeamConversationBatchEvaluation,
    RedTeamConversationEvaluation,
)
from app.utility.send_email import send_error_email_to_developers
from backend.settings import DEFAULT_FROM_EMAIL, ENVIRONMENT, ERROR_EMAIL_RECIPIENTS

logger = logging.getLogger(__name__)


def get_task_output_into_dict(task_output):
    if isinstance(task_output, str):
        task_output = task_output.replace("```json", "")
        task_output = task_output.replace("```", "")
    if isinstance(task_output, str):
        task_output = json.loads(task_output)

    return task_output


def convert_dict_to_df(dict_data: List[dict]):
    """Convert list of recommendation dictionaries to DataFrame.
    
    Args:
        dict_data: List of dictionaries containing recommendation data with fields:
            - name: Name of task/agent
            - _type: "task" or "agent" 
            - yaml_path: Path to YAML config
            - reason: Reason for modification
            - recommendation: List of improvement suggestions
            
    Returns:
        pd.DataFrame with columns matching the recommendation fields
    """
    df = pd.DataFrame(dict_data)
    # Ensure expected columns are present
    expected_cols = ['name', '_type', 'yaml_path', 'reason', 'recommendations']
    for col in expected_cols:
        if col not in df.columns:
            df[col] = None
    return df


def save_recommendations_to_db(schema_name: str, prompt_recommendations: dict, yaml_path: str, type_: str, name: str, redteam_batch_conversation_id: int):
    """Save crew team recommendations to database.
    
    Args:
        schema_name: Schema name for tenant
        prompt_recommendations: Dictionary containing original and updated prompts
        yaml_path: Path to YAML config file
        type_: Type of recommendation ('task' or 'agent') 
        name: Name of task or agent
        redteam_batch_conversation_id: ID of the batch run
        
    """
    with schema_context(schema_name):
        crew_recommendation, _ = CrewTeamRecommendations.objects.get_or_create(
            batch_run_id=redteam_batch_conversation_id,
        )

        crew_recommendation.original_prompt = prompt_recommendations.get('original_prompt', {})
        crew_recommendation.updated_prompt = prompt_recommendations.get('updated_prompt', {})
        crew_recommendation.yaml_path = yaml_path
        crew_recommendation.agent_name = name if type_.lower() == 'agent' else None
        crew_recommendation.task_name = name if type_.lower() == 'task' else None
        crew_recommendation.is_completed = False
        crew_recommendation.allowed_internal_to_approve = True
        crew_recommendation.allowed_external_to_approve = False
        crew_recommendation.save()


class Command(BaseCommand):
    help = 'Runs the red team evaluation process'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str)
        parser.add_argument("redteam_batch_conversation_id", type=int)
        parser.add_argument(
            '--bot_setting',
            type=str,
            choices=['external', 'internal'],
            default='external',
            help='Bot setting to use (default: external)'
        )

    def handle(self, *args, **options):
        try:
            schema_name = options['schema_name']
            redteam_batch_conversation_id = options['redteam_batch_conversation_id']
            bot_setting = options['bot_setting']
            bot_setting = "internal" if str(bot_setting).lower() == "internal" else "external"

            print(f"Schema name: {schema_name}")
            print(f"Redteam batch conversation id: {redteam_batch_conversation_id}")
            print(f"Bot setting: {bot_setting}")

            with schema_context(schema_name):
                redteam_conversation_evaluation_qs = RedTeamConversationEvaluation.objects.filter(conversation__batch_run_id=redteam_batch_conversation_id).select_related('conversation')

                # Get all the redteam crew messages
                redteam_crew_message_qs = RedTeamCrewTeamLogs.objects.filter(conversation__batch_run_id=redteam_batch_conversation_id).select_related('conversation').order_by('created_at')

                # Construct for the agent prompt recommendation
                agent_prompts = []
                for agent_task in redteam_crew_message_qs:
                    agent_prompts.append({
                        "agent name": agent_task.agent_name,
                        "agent role": agent_task.agent_role,
                        "agent goal": agent_task.agent_goal,
                        "agent backstory": agent_task.agent_backstory,
                        "task description": agent_task.task_description,
                        "task expected output": agent_task.task_expected_output,
                        "task actual output": agent_task.task_raw,
                        "yaml_path": agent_task.yaml_path
                    })

                # Construct all the information for the crew
                all_area_of_improvements = []
                all_reasonings = []
                number_of_messages_exchanged = []
                response_stats = []
                all_scorings = []
                for redteam_conversation_evaluation in redteam_conversation_evaluation_qs:
                    all_area_of_improvements.append(redteam_conversation_evaluation.areas_for_improvement)

                    all_reasonings.append({
                        "objective_failed_reasoning": redteam_conversation_evaluation.objective_failed_reasoning,
                        "accuracy_of_answers_reasoning": redteam_conversation_evaluation.accuracy_of_answers_reasoning,
                        "clarity_of_answers_reasoning": redteam_conversation_evaluation.clarity_of_answers_reasoning,
                        "empathy_of_answers_reasoning": redteam_conversation_evaluation.empathy_of_answers_reasoning,
                        "relevance_of_answers_reasoning": redteam_conversation_evaluation.relevance_of_answers_reasoning,
                        "tone_appropriateness_reasoning": redteam_conversation_evaluation.tone_appropriateness_reasoning,
                        "flow_analysis_reasoning": redteam_conversation_evaluation.flow_analysis_reasoning,
                        "customer_satisfaction_reasoning": redteam_conversation_evaluation.customer_satisfaction_reasoning,
                        "areas_for_improvement": redteam_conversation_evaluation.areas_for_improvement
                    })

                    try:
                        number_of_messages_exchanged.append(int(redteam_conversation_evaluation.number_of_messages_exchanged))

                    except ValueError:
                        pass

                    response_stats.append(redteam_conversation_evaluation.message_response_time)

                    all_scorings.append({
                        "accuracy_of_answers": int(redteam_conversation_evaluation.accuracy_of_answers or 0),
                        "clarity_of_answers": int(redteam_conversation_evaluation.clarity_of_answers or 0),
                        "empathy_of_answers": int(redteam_conversation_evaluation.empathy_of_answers or 0),
                        "relevance_of_answers": int(redteam_conversation_evaluation.relevance_of_answers or 0),
                        "tone_appropriateness": int(redteam_conversation_evaluation.tone_appropriateness or 0),
                        "flow_analysis": int(redteam_conversation_evaluation.flow_analysis or 0),
                        "customer_satisfaction": int(redteam_conversation_evaluation.customer_satisfaction_rating or 0)
                    })

                crew_variables = {
                    "agent_prompts": agent_prompts,
                    "all_area_of_improvements": all_area_of_improvements,
                    "all_reasonings": all_reasonings
                }

                # Save the assessment to the db
                batch_assessment, _ = RedTeamConversationBatchEvaluation.objects.get_or_create(batch_evaluation_id=redteam_batch_conversation_id)
                print(batch_assessment)

                max_attempt = 5
                attempt = 0

                while attempt < max_attempt:
                    try:
                        # Run the redteam assessor
                        _, _, task_full_dicts, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_batch_analysis_crew.yml")

                        improvement_done = False
                        repeated_issues_done = False
                        knowledge_gap_done = False
                        satisfaction_done = False

                        feedback_data_for_recommendation = {}

                        for task in task_full_dicts:
                            agent_name = task.get("agent")

                            if agent_name == "Improvement Analyzer":
                                task_output = get_task_output_into_dict(task.get("raw"))
                                batch_assessment.top_areas_for_improvement = task_output.get("top_improvements")
                                feedback_data_for_recommendation["top_areas_for_improvement"] = task_output.get("top_improvements")
                                improvement_done = True

                            elif agent_name == "Issue Pattern Analyzer":
                                task_output = get_task_output_into_dict(task.get("raw"))
                                batch_assessment.top_repeated_issues = task_output.get("repeated_issues")
                                feedback_data_for_recommendation["top_repeated_issues"] = task_output.get("repeated_issues")
                                repeated_issues_done = True

                            elif agent_name == "Knowledge Gap Analyzer":
                                task_output = get_task_output_into_dict(task.get("raw"))
                                batch_assessment.knowledge_gaps = task_output.get("knowledge_gaps")
                                feedback_data_for_recommendation["knowledge_gaps"] = task_output.get("knowledge_gaps")
                                knowledge_gap_done = True

                            elif agent_name == "Satisfaction Scorer":
                                task_output = get_task_output_into_dict(task.get("raw"))
                                batch_assessment.overall_satisfaction_reasoning = task_output.get("overall_satisfaction", {}).get("reason")
                                batch_assessment.overall_satisfaction_score = task_output.get("overall_satisfaction", {}).get("score")
                                feedback_data_for_recommendation["overall_satisfaction_reasoning"] = task_output.get("overall_satisfaction", {}).get("reason")
                                satisfaction_done = True
                        print("+"*100)
                        print([
                            improvement_done,
                            repeated_issues_done,
                            knowledge_gap_done,
                            satisfaction_done
                        ])
                        print("+"*100)
                        logger.info([
                            improvement_done,
                            repeated_issues_done,
                            knowledge_gap_done,
                            satisfaction_done
                        ])
                        if all([
                            improvement_done,
                            repeated_issues_done,
                            knowledge_gap_done,
                            satisfaction_done
                        ]):
                            logger.info("All assessments are completed.")
                            break

                    except json.JSONDecodeError as e:
                        traceback.print_exc()

                        if attempt == max_attempt - 1:
                            raise e
                        
                        print("="*100)
                        print(task_output)
                        print("="*100)

                        logger.warning(f"JSONDecodeError: Attempt {attempt + 1} failed. Retrying...")
                        attempt += 1

                # Calculate the batch message exchange stats
                batch_assessment.number_of_messages_exchanged = sum(number_of_messages_exchanged)

                # Calculate the response stats using pandas
                # Convert the array of dictionaries to a DataFrame
                df = pd.DataFrame(response_stats)

                # Calculate statistics for each column
                response_stats = {
                    "min_response_time": int(df["min_response_time"].min()),
                    "first_quartile_response_time": int(df["first_quartile_response_time"].mean()),
                    "third_quartile_response_time": int(df["third_quartile_response_time"].mean()),
                    "max_response_time": int(df["max_response_time"].max()),
                    "mean_response_time": int(df["mean_response_time"].mean()),
                    "median_response_time": int(df["median_response_time"].median())
                }

                # Convert all values in response_stats to integers
                batch_assessment.batch_response_time_stats = response_stats

                # Calculate the batch satisfaction stats
                satisfaction_df = pd.DataFrame(all_scorings)
                
                # Get the mean for all columns
                batch_assessment.batch_satisfaction_stats = {
                    "accuracy_of_answers": int(satisfaction_df["accuracy_of_answers"].mean()),
                    "clarity_of_answers": int(satisfaction_df["clarity_of_answers"].mean()),
                    "mempathy_of_answers": int(satisfaction_df["empathy_of_answers"].mean()),
                    "relevance_of_answers": int(satisfaction_df["relevance_of_answers"].mean()),
                    "tone_appropriateness": int(satisfaction_df["tone_appropriateness"].mean()),
                    "flow_analysis": int(satisfaction_df["flow_analysis"].mean()),
                    "customer_satisfaction": int(satisfaction_df["customer_satisfaction"].mean()),
                }

                # Save the batch assessment
                batch_assessment.save()

                # Generate the recommendation to change
                df = None
                crew_variables = {
                    "crewteam_logs": agent_prompts,
                    "feedback_data": all_reasonings
                }

                crew_recommendations, _, _, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_review.yml")

                print("-"*100)
                print(crew_recommendations)
                print("-"*100)

                # Convert the output into json
                if isinstance(crew_recommendations, str):
                    crew_recommendations = json.loads(crew_recommendations)

                # Step 5: Compile all the suggestions and sort it by the crew team and by task and agent
                df = convert_dict_to_df(crew_recommendations)

                print("-"*100)
                print(df)

                # Group by the yaml path and then by the _type and then by the name
                grouped = df.groupby(['yaml_path', '_type', 'name']).agg({'reason': 'first', 'recommendations': 'sum'})

                for index, row in grouped.iterrows():
                    # Step 6: For each of the task/agent, will need to read the yaml and propose a change to it.
                    yaml_path, type_, name = index

                    # Read the existing YAML file
                    with open(yaml_path, 'r') as f:
                        yaml_content = yaml.safe_load(f)
                    
                    # Find the specific task/agent in the YAML
                    target_item = None
                    if str(type_).lower() == 'task':
                        target_item = next((yaml_content['tasks'][task] for task in yaml_content['tasks'] if task == name), None)
                    elif str(type_).lower() == 'agent':
                        target_item = next((yaml_content['agents'][agent] for agent in yaml_content['agents'] if yaml_content['agents'][agent]['role'] == name), None)
                    
                    if target_item:
                        # Create proposed changes based on recommendations
                        proposed_changes = {
                            'yaml_path': yaml_path,
                            'type': str(type_).lower(),
                            'name': name,
                            'current_content': target_item,
                            'reason': row['reason'],
                            'recommendations': row['recommendations'],
                            "previous_feedback": None
                        }

                        print(f"{proposed_changes=}")

                        # Step 7: Set up a crew team to review the proposed changes and summarize it
                        prompt_recommendations, _, _, _ = run_crew_team(proposed_changes, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_assessor_recommendations.yml")

                        if isinstance(prompt_recommendations, str):
                            prompt_recommendations = json.loads(prompt_recommendations)

                        print(f"{prompt_recommendations=}")

                        # Step 8: Save the suggestion to the database
                        save_recommendations_to_db(schema_name, prompt_recommendations, yaml_path, type_, name, redteam_batch_conversation_id)


        except Exception as e:
            traceback.print_exc()
            logger.error(f"Unexpected error: {e}")

            # Send error email to developer team
            from_ = f"UtterUnicorn V2 X {schema_name} <{DEFAULT_FROM_EMAIL}>"
            html_path = "email/error_notification.html"
            subject = f"Error in Running RedTeam Assessor Batch Deployment for {schema_name}"
            to_ = ERROR_EMAIL_RECIPIENTS.split(',')
            custom_kwargs = {
                "schema_name": schema_name,
                "environment": ENVIRONMENT,
                "redteam_batch_conversation_id": redteam_batch_conversation_id,
                "message": "".join(traceback.format_exception(type(e), e, e.__traceback__))
            }

            send_error_email_to_developers(html_path, subject, from_, to_, **custom_kwargs)
            raise e
