from django.contrib.auth import get_user_model
from rest_framework import serializers

from app.chats.chat.serializers import UserEmailSerializer
from app.redteam.models import (
    RedTeamConversation,
    RedTeamFlowMapping,
    RedTeamMessage,
    RedTeamStateMachine,
)

User = get_user_model()


class RedTeamUserEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['email']

class RedTeamStateMachineSerializer(serializers.ModelSerializer):
    """
    Main serializer for StateMachine model with nested related data
    """
    
    class Meta:
        model = RedTeamStateMachine
        fields = [
            'context'
        ]

class RedTeamMessageMinimalSerializer(serializers.ModelSerializer):
    """Minimal serializer for Message model to be used in StateMachine serializer"""
    
    state_machines = RedTeamStateMachineSerializer(many=True, read_only=True)
    sender = UserEmailSerializer(read_only=True)
    attachments = serializers.SerializerMethodField()

    def get_attachments(self, obj):
        return [attachment.id for attachment in obj.attachments.all()]

    class Meta:
        model = RedTeamMessage
        fields = [
            "id",
            "message",
            "state_machines",
            "timestamp",
            "sender",
            "message_type",
            "attachments",
        ]

class RedTeamFlowMappingSerializer(serializers.ModelSerializer):
    """Serializer for FlowMapping model"""
    class Meta:
        model = RedTeamFlowMapping
        fields = ['mapping_json']
