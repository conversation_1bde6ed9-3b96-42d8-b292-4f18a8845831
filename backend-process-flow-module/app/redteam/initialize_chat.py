import json
import re
import uuid
from typing import List

import requests
from django.utils import timezone
from tenant_schemas.utils import schema_context

from app.chats.chat.chat_engine.chat_engine import run_crew_team
from app.chats.chat.models import Conversation, ConversationQueue, Message, StateMachine
from app.documents.document.models import KnowledgeImage
from app.redteam.models import (
    RedTeamConversation,
    RedTeamConversationQueue,
    RedTeamCustomerProfile,
    RedTeamMessage,
    RedTeamStateMachine,
)
from app.utility import models as utility_models
from app.utility.chat_engine_helper import get_active_flow_mapping
from backend import settings
from backend.settings import AWS_STORAGE_BUCKET_NAME


def create_new_chat(tenant_name: str, batch_run_id, run_with_redteam: bool = True):
    with schema_context(tenant_name):
        if run_with_redteam:
            conversation = RedTeamConversation.objects.create(
                sub_source="",
                third_party_id="",
                session_uuid=uuid.uuid4(),
                batch_run_id=batch_run_id
            )
            return conversation, conversation.id
        
        else:
            conversation = Conversation.objects.create(
                sub_source="",
                third_party_id="",
                session_uuid=uuid.uuid4(),
            )
            return conversation, conversation.id


def create_new_message(tenant_name: str, message_list: list, conversation: RedTeamConversation | Conversation, message_type: str, run_with_redteam: bool = True):
    created_messages = []
    with schema_context(tenant_name):
        for message_text in message_list:
            # message_text must be in string
            message_text = str(message_text)

            # Find all image markdown patterns with optional leading slash
            image_pattern = fr'!\[Image\]\(?/?{tenant_name}/[^)]+\)'
            matches = re.finditer(image_pattern, message_text)
            
            # Process each match
            for match in matches:
                full_match = match.group(0)
                # Extract the path between parentheses, handling optional leading slash
                path_match = re.search(fr'\(?/?{tenant_name}/([^)]+)\)', full_match)

                print(f"full_match: {full_match}")
                print(f"path_match: {path_match}")
                if path_match:
                    image_path = path_match.group(1)
                    try:
                        # Get the KnowledgeImage object based on the file path
                        knowledge_image = KnowledgeImage.objects.get(file=f"{tenant_name}/{image_path}")
                        # Get the S3 URL from the file field
                        s3_url = knowledge_image.file.url

                        if str(s3_url).startswith("/"):
                            # If the S3 URL starts with a slash, remove it
                            s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/{str(s3_url)[1:]}"
                        elif str(s3_url).startswith("https://"):
                            # If the S3 URL starts with "https://", keep it as is
                            pass
                        else:
                            # If the S3 URL does not start with "https://", prepend it
                            s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/{s3_url}"

                        # Replace the original path with S3 URL
                        message_text = message_text.replace(
                            path_match.group(0),
                            f"({s3_url})"
                        )

                        print(f"image_path: {image_path}")
                        print(f"s3_url: {s3_url}")

                    except KnowledgeImage.DoesNotExist: 
                        # If image not found, leave the path as is
                        pass

            if run_with_redteam:
                message = RedTeamMessage.objects.create(
                conversation=conversation,
                created_at=timezone.now(),
                message_type=message_type,
                feedback=utility_models.NEUTRAL,
                is_audio=False,
                chat_language=utility_models.ENGLISH_US,
                bot_reply_status="in_process" if message_type == "user" else "completed",
                message=message_text,
                sender=None,
                third_party_id="",
                third_party_sender="",
                timestamp=None,
                feedback_reason="",
                message_audio=None,
                sentiment=None,
                topics=None,
                metadata={}
            )
            else:
                message = Message.objects.create(
                    conversation=conversation,
                    created_at=timezone.now(),
                    message_type=message_type,
                    feedback=utility_models.NEUTRAL,
                    is_audio=False,
                    chat_language=utility_models.ENGLISH_US,
                    bot_reply_status="in_process" if message_type == "user" else "completed",
                    message=message_text,
                    sender=None,
                    third_party_id="",
                    third_party_sender="",
                    timestamp=None,
                    feedback_reason="",
                    message_audio=None,
                    sentiment=None,
                    topics=None,
                    metadata={}
                )
            created_messages.append(message)
    return created_messages


def create_new_bot_message(tenant_name: str, message_list: list, conversation: RedTeamConversation | Conversation, message_type: str, run_with_redteam: bool = True):
    created_messages = []
    with schema_context(tenant_name):
        for message_text in message_list:
            # message_text must be in string
            message_text = str(message_text)
            
            # Find all image markdown patterns with optional leading slash
            image_pattern = fr'!\[Image\]\(?/?{tenant_name}/[^)]+\)'
            matches = re.finditer(image_pattern, message_text)
            
            # Process each match
            for match in matches:
                full_match = match.group(0)
                # Extract the path between parentheses, handling optional leading slash
                path_match = re.search(fr'\(?/?{tenant_name}/([^)]+)\)', full_match)

                print(f"full_match: {full_match}")
                print(f"path_match: {path_match}")
                if path_match:
                    image_path = path_match.group(1)
                    try:
                        # Get the KnowledgeImage object based on the file path
                        knowledge_image = KnowledgeImage.objects.get(file=f"{tenant_name}/{image_path}")
                        # Get the S3 URL from the file field
                        s3_url = knowledge_image.file.url

                        if str(s3_url).startswith("/"):
                            # If the S3 URL starts with a slash, remove it
                            s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}/{str(s3_url)[1:]}"
                        elif str(s3_url).startswith("https://"):
                            # If the S3 URL starts with "https://", keep it as is
                            pass
                        else:
                            # If the S3 URL does not start with "https://", prepend it
                            s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}/{s3_url}"

                        # Replace the original path with S3 URL
                        message_text = message_text.replace(
                            path_match.group(0),
                            f"({s3_url})"
                        )

                        print(f"image_path: {image_path}")
                        print(f"s3_url: {s3_url}")

                    except KnowledgeImage.DoesNotExist: 
                        # If image not found, leave the path as is
                        pass

            if run_with_redteam:
                message = RedTeamMessage.objects.create(
                    conversation=conversation,
                    created_at=timezone.now(),
                    message_type=message_type,
                    feedback=utility_models.NEUTRAL,
                    is_audio=False,
                    chat_language=utility_models.ENGLISH_US,
                    bot_reply_status="in_process" if message_type == "user" else "completed",
                    message=message_text,
                    sender=None,
                    third_party_id="",
                    third_party_sender="",
                    timestamp=None,
                    feedback_reason="",
                    message_audio=None,
                    sentiment=None,
                    topics=None,
                    metadata={}
                )
                created_messages.append(message)
    return created_messages


def create_message_queue(tenant_name: str, message_ids: list, conversation: RedTeamConversation, run_with_redteam: bool = True):
    with schema_context(tenant_name):
        if run_with_redteam:
            queue_entry = RedTeamConversationQueue.objects.create(
                conversation_id=conversation.id,
                messages_in_processing=message_ids,
                is_completed=False
            )
        else:
            queue_entry = ConversationQueue.objects.create(
                conversation_id=conversation.id,
                messages_in_processing=message_ids,
                is_completed=False
            )
        return queue_entry


def call_chatbot_endpoint(schema_name: str, payload: dict, ip_address) -> dict:
    """
    Makes a POST request to the chatbot endpoint to process messages.

    Args:
        schema_name (str): The schema/organization name
        payload (dict): The request payload containing message data

    Returns:
        dict: The response from the chatbot endpoint

    Raises:
        requests.exceptions.RequestException: If the request fails
    """
    url = f"http://{ip_address}/org/{schema_name}/chat/v1/process_messages/"
    response = requests.post(url, json=payload, timeout=1800)
    response.raise_for_status()
    return response.json()


def update_state_machine(state_info: dict, tenant_name: str, message: RedTeamMessage, run_with_redteam: bool = True) -> RedTeamStateMachine:
    """Update or create a state machine entry for the conversation"""
    with schema_context(tenant_name):
        if run_with_redteam:
            state_machine = RedTeamStateMachine.objects.create(
                message=message,
                context=state_info
            )
        else:
            state_machine = StateMachine.objects.create(
                message=message,
                context=state_info
            )

        return state_machine


def update_in_process_messages(
    conversation: RedTeamConversation, message_ids: List[str], tenant_name: str, run_with_redteam: bool = True) -> None:
    """Update all in-process messages to completed status"""
    with schema_context(tenant_name):
        if run_with_redteam:
            RedTeamMessage.objects.filter(
                conversation=conversation,
                id__in=message_ids,
                bot_reply_status="in_process",
            ).update(bot_reply_status="completed")

        else:
            Message.objects.filter(
                conversation=conversation,
                id__in=message_ids,
                bot_reply_status="in_process",
            ).update(bot_reply_status="completed")


def update_conversation_queue(queue_id: str, tenant_name: str, run_with_redteam: bool = True) -> None:
    """Update conversation queue is_completed status"""
    with schema_context(tenant_name):
        if run_with_redteam:
            RedTeamConversationQueue.objects.filter(id=queue_id).update(is_completed=True)

        else:
            ConversationQueue.objects.filter(id=queue_id).update(is_completed=True)
        


def update_customer_profile(tenant_name: str, conversation: RedTeamConversation, customer_profile_id: int) -> RedTeamCustomerProfile:
    """
    Creates or updates a RedTeamCustomerProfile for a conversation.

    Args:
        tenant_name (str): The schema/tenant name
        conversation (RedTeamConversation): The conversation to associate the profile with
        customer_profile_id (int): The ID of the customer profile

    Returns:
        RedTeamCustomerProfile: The created/updated customer profile object
    """
    with schema_context(tenant_name):

        customer_profile_obj = RedTeamCustomerProfile.objects.filter(id=customer_profile_id).first()

        # Update the object with the new conversation
        customer_profile_obj.conversation = conversation
        customer_profile_obj.save()

        # Construct the customer profile dict
        customer_profile = {
            "customer_profile": customer_profile_obj.description,
            "journey_stage": customer_profile_obj.customer_background,
            "scenario_context": customer_profile_obj.context,
            "personality": customer_profile_obj.personality,
            "objectives": customer_profile_obj.objectives,
            "additional_details": customer_profile_obj.additional_details
        }

        return customer_profile


def update_conversation_metadata(conversation_id, metadata, tenant_name):
    """Update the conversation metadata"""
    with schema_context(tenant_name):
        conversation_obj = Conversation.objects.filter(id=conversation_id).first()

        if conversation_obj:
            current_metadata = conversation_obj.metadata

            # Loop through the new metadata and update the existing metadata
            for key, value in metadata.items():
                current_metadata[key] = value

            conversation_obj.metadata = current_metadata
            conversation_obj.save(update_fields=["metadata"])


def start_conversation(tenant_name: str, customer_profile_id: int, ip_address, batch_run_id, run_with_redteam: bool = True, seed_number=None):
    try:
        is_conversation_active = True
        conversation_history = []
        count = 0
        crew_variables = {
            "schema_name": tenant_name,
            "customer_persona": {},
            "chat_history": conversation_history,
            "flow_mapping": {},
            "conversation_analysis": ""
        }
        print("+"*50)
        print("run_with_redteam", run_with_redteam)
        print("+"*50)

        conversation, conversation_id = create_new_chat(tenant_name, batch_run_id, run_with_redteam)

        # Update the redteam customer profile
        customer_profile = update_customer_profile(tenant_name, conversation, customer_profile_id)
        crew_variables.update({
            "customer_persona": customer_profile
        })
        
        while is_conversation_active and count < 30:
            
            # Step 1: Extract all the information from the db
            flow_mapping = get_active_flow_mapping(tenant_name, redteam_run=run_with_redteam)
            crew_variables.update({
                "flow_mapping": flow_mapping
            })

            # Step 2: Get the messages for the conversation
            customer_messages, _, _, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_conversation_crew.yml", seed_number=seed_number)
            if isinstance(customer_messages, str):
                customer_messages = json.loads(customer_messages)

            for message in customer_messages.get("formulated_messages", []):
                crew_variables.get("chat_history", []).append({
                    "role": "user",
                    "content": message
                })

            # Step 3: Create the messages in the db with the queue.
            message_list = create_new_message(tenant_name, customer_messages.get("formulated_messages", []), conversation, message_type="user", run_with_redteam=run_with_redteam)
            message_ids = [str(message.id) for message in message_list]
            queue = create_message_queue(tenant_name, message_ids, conversation, run_with_redteam=run_with_redteam)
            # Step 4: Update the crew variables
            crew_variables.update({
                "conversation_id": conversation.id,
                "message_ids": message_ids,
                "queue_id": queue.id
            })
            
            # Step 5: Call the endpoint
            payload = {
                "conversation_id": crew_variables.get("conversation_id"),
                "message_ids": crew_variables.get("message_ids"),
                "queue_id": crew_variables.get("queue_id"),
                "api_key": settings.BOT_API_KEY,
                "tenant": tenant_name,
                "redteam_run": run_with_redteam,
                "seed_number": seed_number
            }
            print(payload)
            response = call_chatbot_endpoint(tenant_name, payload,ip_address)
            bot_response = response.get("body", {}).get("bot_reply", {})

            crew_variables.update({
                "bot_response": bot_response
            })

            # Step 6: Update the conversation history
            for message in bot_response.get("bot_message", []):
                crew_variables.get("chat_history", []).append({
                    "role": "bot_text",
                    "content": message
                })

            # Step 7: Update the database
            bot_message_list = create_new_bot_message(tenant_name, bot_response.get("bot_message", []), conversation, message_type="bot_text", run_with_redteam=run_with_redteam)

            state_info = bot_response.get("state_info", {})
            for message in bot_message_list:
                _ = update_state_machine(state_info, tenant_name, message, run_with_redteam=run_with_redteam)

            update_in_process_messages(conversation, message_ids, tenant_name, run_with_redteam=run_with_redteam)

            update_conversation_queue(queue.id, tenant_name, run_with_redteam=run_with_redteam)

            update_conversation_metadata(conversation.id, bot_response.get("metadata", {}), tenant_name)

            # Step 8: Determine if the we should continue the conversation
            conversation_analysis, _, _, _ = run_crew_team(crew_variables, "app/crew_teams/agents_and_tasks/redteam_assessor/redteam_chat_analysis_crew.yml", seed_number=seed_number)
            if isinstance(conversation_analysis, str):
                conversation_analysis = json.loads(conversation_analysis)
            
            if str(conversation_analysis.get("objective_achieved", "false")).lower() == "true":
                is_conversation_active = False

                print("Objective achieved!")
                break

            else:
                # Add the conversation analysis to the crew variables
                crew_variables.update({
                    "conversation_analysis": conversation_analysis.get("follow_up_response", "")
                })

                print("Conversation History: ", crew_variables.get("chat_history", []))

                count += 1

        return conversation_id

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise Exception(f"Error starting conversation: {e}")