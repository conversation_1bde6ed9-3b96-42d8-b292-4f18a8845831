# Generated by Django 4.2.5 on 2024-11-18 14:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('redteam', '0003_redteamconversation_redteammessage_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='redteamconversationqueue',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_queue', to='redteam.redteamconversation'),
        ),
        migrations.AlterField(
            model_name='redteamcustomerprofile',
            name='conversation',
            field=models.ForeignKey(help_text='Associated red team conversation', on_delete=django.db.models.deletion.CASCADE, related_name='customer_profiles', to='redteam.redteamconversation'),
        ),
        migrations.AlterField(
            model_name='redteammessage',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='redteam.redteamconversation'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='redteammessage',
            name='message_audio',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='message', to='redteam.redteammessageaudio'),
        ),
        migrations.AlterField(
            model_name='redteamstatemachine',
            name='message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='state_machines', to='redteam.redteammessage'),
        ),
    ]
