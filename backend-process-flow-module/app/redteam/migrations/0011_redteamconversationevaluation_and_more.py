# Generated by Django 4.2.5 on 2024-12-27 16:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('redteam', '0010_redteambatchrun_seed_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='RedTeamConversationEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('conversation_summary', models.TextField(blank=True, help_text='A summary of the conversation, including the key points and the overall sentiment of the conversation.', null=True)),
                ('objective_met', models.Bo<PERSON>anField(blank=True, help_text='Whether the objective of the conversation was met', null=True)),
                ('objective_failed_reasoning', models.TextField(blank=True, help_text='Detailed reasoning for why the objective was not met', null=True)),
                ('number_of_messages_exchanged', models.IntegerField(blank=True, help_text='The number of messages exchanged in the conversation', null=True)),
                ('average_response_time', models.FloatField(blank=True, help_text='The average time it took to respond to each message in the conversation', null=True)),
                ('accuracy_of_answers', models.IntegerField(blank=True, help_text='The accuracy of the answers provided by the chatbot', null=True)),
                ('accuracy_of_answers_reasoning', models.TextField(blank=True, help_text='Detailed reasoning for the accuracy of the answers provided by the chatbot', null=True)),
                ('clarity_of_answers', models.IntegerField(blank=True, help_text='The quality of the answers provided by the chatbot', null=True)),
                ('clarity_of_answers_reasoning', models.TextField(blank=True, help_text='Detailed reasoning for the quality of the answers provided by the chatbot', null=True)),
                ('empathy_of_answers', models.IntegerField(blank=True, help_text="The empathy and understanding of the chatbot's responses", null=True)),
                ('empathy_of_answers_reasoning', models.TextField(blank=True, help_text="Detailed reasoning for the empathy and understanding of the chatbot's responses", null=True)),
                ('relevance_of_answers', models.IntegerField(blank=True, help_text="The relevance and accuracy of the chatbot's responses", null=True)),
                ('relevance_of_answers_reasoning', models.TextField(blank=True, help_text="Detailed reasoning for the relevance and accuracy of the chatbot's responses", null=True)),
                ('tone_appropriateness', models.IntegerField(blank=True, help_text="The tone and style of the chatbot's responses", null=True)),
                ('tone_appropriateness_reasoning', models.TextField(blank=True, help_text="Detailed reasoning for the tone and style of the chatbot's responses", null=True)),
                ('customer_satisfaction_rating', models.IntegerField(blank=True, help_text="The level of satisfaction of the customer with the chatbot's responses", null=True)),
                ('customer_satisfaction_reasoning', models.TextField(blank=True, help_text="Detailed reasoning for the level of satisfaction of the customer with the chatbot's responses", null=True)),
                ('areas_for_improvement', models.JSONField(blank=True, help_text='Areas where the chatbot could improve its responses', null=True)),
                ('batch_evaluation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='redteam_conversation_evaluations', to='redteam.redteambatchrun')),
                ('conversation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='evaluations', to='redteam.redteamconversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RedTeamConversationBatchEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('batch_satisfaction_stats', models.JSONField(blank=True, help_text='Statistics for overall satisfaction: mean, median, max, min', null=True)),
                ('batch_message_exchange_stats', models.JSONField(blank=True, help_text='Statistics for number of messages exchanged: mean, median, max, min', null=True)),
                ('batch_response_time_stats', models.JSONField(blank=True, help_text='Statistics for response time: mean, median, max, min', null=True)),
                ('overall_satisfaction_score', models.IntegerField(blank=True, help_text="The overall level of satisfaction of the customer with the chatbot's responses", null=True)),
                ('overall_satisfaction_reasoning', models.TextField(blank=True, help_text="Detailed reasoning for the overall level of satisfaction of the customer with the chatbot's responses", null=True)),
                ('top_repeated_issues', models.JSONField(blank=True, help_text='Top repeated issues in the conversations', null=True)),
                ('top_areas_for_improvement', models.JSONField(blank=True, help_text='Areas where the chatbot could improve its responses', null=True)),
                ('knowledge_gaps', models.JSONField(blank=True, help_text='Knowledge gaps identified in the conversations', null=True)),
                ('batch_evaluation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='redteam_conversation_batch_evaluations', to='redteam.redteambatchrun')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
