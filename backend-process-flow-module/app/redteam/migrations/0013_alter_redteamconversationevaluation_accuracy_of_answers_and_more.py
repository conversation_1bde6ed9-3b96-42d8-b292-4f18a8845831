# Generated by Django 4.2.5 on 2024-12-28 16:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('redteam', '0012_redteamconversationevaluation_flow_analysis_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='redteamconversationevaluation',
            name='accuracy_of_answers',
            field=models.CharField(blank=True, help_text='The accuracy of the answers provided by the chatbot', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='redteamconversationevaluation',
            name='clarity_of_answers',
            field=models.CharField(blank=True, help_text='The quality of the answers provided by the chatbot', max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='redteamconversationevaluation',
            name='customer_satisfaction_rating',
            field=models.Char<PERSON>ield(blank=True, help_text="The level of satisfaction of the customer with the chatbot's responses", max_length=255, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='redteamconversationevaluation',
            name='empathy_of_answers',
            field=models.Char<PERSON><PERSON>(blank=True, help_text="The empathy and understanding of the chatbot's responses", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='redteamconversationevaluation',
            name='flow_analysis',
            field=models.CharField(blank=True, help_text="The level of flow of the chatbot's responses", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='redteamconversationevaluation',
            name='number_of_messages_exchanged',
            field=models.CharField(blank=True, help_text='The number of messages exchanged in the conversation', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='redteamconversationevaluation',
            name='relevance_of_answers',
            field=models.CharField(blank=True, help_text="The relevance and accuracy of the chatbot's responses", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='redteamconversationevaluation',
            name='tone_appropriateness',
            field=models.CharField(blank=True, help_text="The tone and style of the chatbot's responses", max_length=255, null=True),
        ),
    ]
