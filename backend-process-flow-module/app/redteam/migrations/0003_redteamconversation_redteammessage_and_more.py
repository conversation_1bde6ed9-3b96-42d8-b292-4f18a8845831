# Generated by Django 4.2.5 on 2024-11-17 17:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('redteam', '0002_alter_questions_star'),
    ]

    operations = [
        migrations.CreateModel(
            name='RedTeamConversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=8912)),
                ('third_party_id', models.CharField(blank=True, max_length=8912)),
                ('sender_psid', models.Char<PERSON>ield(blank=True, max_length=127)),
                ('source', models.CharField(choices=[('Web Application', 'Web Application'), ('Floating Chat', 'Floating Chat'), ('WhatsApp', 'WhatsApp'), ('WhatsApp QR', 'WhatsApp QR'), ('WeChat', 'WeChat'), ('Telegram', 'Telegram'), ('Instagram', 'Instagram'), ('Facebook', 'Facebook'), ('Shopify', 'Shopify'), ('Wix', 'Wix'), ('WooCommerce', 'WooCommerce')], default='Web Application', max_length=63)),
                ('sub_source', models.CharField(blank=True, max_length=8912)),
                ('is_auto_reply', models.BooleanField(default=True)),
                ('is_group_chat', models.BooleanField(default=False)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('session_uuid', models.UUIDField(blank=True, null=True)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RedTeamMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('third_party_id', models.CharField(blank=True, max_length=8912)),
                ('third_party_sender', models.CharField(blank=True, max_length=8912)),
                ('timestamp', models.DateTimeField(null=True)),
                ('message_type', models.CharField(choices=[('user', 'User'), ('anonymous', 'Anonymous'), ('machine', 'Machine'), ('page-admin', 'Page Admin'), ('Scheduled', 'Scheduled')], default='user', max_length=10)),
                ('message', models.TextField(blank=True, max_length=8912)),
                ('feedback', models.CharField(choices=[('Good', 'Good'), ('Bad', 'Bad'), ('Neutral', 'Neutral')], default='Neutral', max_length=15)),
                ('feedback_reason', models.CharField(blank=True, max_length=8912)),
                ('is_audio', models.BooleanField(default=False)),
                ('chat_language', models.CharField(default='English (US)', max_length=127)),
                ('sentiment', models.CharField(blank=True, max_length=15, null=True)),
                ('topics', models.CharField(blank=True, max_length=8912, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('bot_reply_status', models.CharField(choices=[('pending', 'Pending'), ('in_process', 'In Process'), ('completed', 'Completed')], default='pending', max_length=15)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='redteam_messages', to='redteam.redteamconversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RedTeamStateMachine',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('context', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='redteam_state_machines', to='redteam.redteammessage')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RedTeamMessageAudio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(blank=True, max_length=8912, null=True, upload_to='')),
                ('duration', models.DurationField(default=0)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='redteammessage',
            name='message_audio',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='redteam_message', to='redteam.redteammessageaudio'),
        ),
        migrations.AddField(
            model_name='redteammessage',
            name='sender',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='redteam_messages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='redteammessage',
            name='updated_by',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='RedTeamCustomerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(help_text='Detailed description of the target customer, including demographics, interests, and potential needs', null=True)),
                ('customer_background', models.TextField(help_text='Comprehensive background information about the customer, including their profession, lifestyle, and interests', null=True)),
                ('context', models.JSONField(default=dict, help_text='JSON object containing intent, query type and scenario details', null=True)),
                ('personality', models.JSONField(default=dict, help_text='JSON object containing temperament, communication style and emotional state', null=True)),
                ('objectives', models.TextField(help_text='Specific goals or resolutions the customer seeks from this interaction', null=True)),
                ('conversation', models.ForeignKey(help_text='Associated red team conversation', on_delete=django.db.models.deletion.CASCADE, related_name='redteam_customer_profiles', to='redteam.redteamconversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Red Team Customer Profile',
                'verbose_name_plural': 'Red Team Customer Profiles',
            },
        ),
        migrations.CreateModel(
            name='RedTeamConversationQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('messages_in_processing', models.JSONField(blank=True, default=list, help_text='List of messages currently being processed')),
                ('is_completed', models.BooleanField(default=False)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='redteam_conversation_queue', to='redteam.redteamconversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RedTeamAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(blank=True, max_length=8912, null=True, upload_to='messages_attachments/')),
                ('google_drive_url', models.URLField(blank=True, default=None, max_length=255, null=True)),
                ('attachment_type', models.CharField(choices=[('image', 'Image'), ('video', 'Video'), ('document', 'Document'), ('unknown', 'Unknown')], default='image', max_length=10)),
                ('tags', models.CharField(blank=True, max_length=8912)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='redteam_attachments', to='redteam.redteammessage')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RedTeamFlowMapping',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('version', models.IntegerField()),
                ('mapping_json', models.JSONField()),
                ('is_active', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-version'],
                'abstract': False,
                'unique_together': {('version',)},
            },
        ),
    ]
