from django.db import models

import app.utility.models as utility_models
from app.chats.chat.abstract_models import *
from app.chats.chat.models import *
from app.utility.models import BaseModel


class RedTeamBatchRun(BaseModel):
    status = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        default="pending",
    )

    start_time = models.DateTimeField(
        null=True,
        blank=True,
        auto_now_add=True
    )

    end_time = models.DateTimeField(
        null=True,
        blank=True,
    )

    seed_id = models.IntegerField(
        null=True,
        blank=True,
    )

    num_profiles = models.IntegerField(
        null=True,
        blank=True,
        default=1
    )

class RedTeamMessageAudio(AbstractMessageAudio):
    pass


class RedTeamConversation(AbstractConversation):
    batch_run = models.ForeignKey(
        RedTeamBatchRun,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="redteam_conversations",
    )

class RedTeamMessage(AbstractMessage):
    conversation = models.ForeignKey(
        RedTeamConversation, related_name="messages", on_delete=models.CASCADE
    )
    sender = models.ForeignKey(
        User, related_name="redteam_messages", on_delete=models.CASCADE, null=True, blank=True
    )
    message_audio = models.OneToOneField(
        RedTeamMessageAudio,
        related_name="message",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )  # from user, changed from foreign to one to one

    def get_previous_message(self):
        return (
            RedTeamMessage.objects.filter(
                conversation=self.conversation, created_at__lt=self.created_at
            )
            .order_by("-created_at")
            .only("id")
            .first()
        )

    def get_next_message(self):
        return (
            RedTeamMessage.objects.filter(
                conversation=self.conversation, created_at__gt=self.created_at
            )
            .order_by("created_at")
            .only("id")
            .first()
        )


class RedTeamConversationQueue(AbstractConversationQueue):
    conversation = models.ForeignKey(RedTeamConversation, null=False, blank=False, related_name="conversation_queue", on_delete=models.CASCADE)


class RedTeamAttachment(AbstractAttachment):
    message = models.ForeignKey(
        RedTeamMessage, related_name="attachments", on_delete=models.CASCADE
    )


class RedTeamStateMachine(AbstractStateMachine):
    message = models.ForeignKey(RedTeamMessage, on_delete=models.CASCADE, related_name='state_machines', null=True, blank=True)


class RedTeamFlowMapping(AbstractFlowMapping):
    pass


class RedTeamCustomerProfile(BaseModel):
    """
    Model for storing detailed customer profiles for red team testing scenarios.
    Contains comprehensive information about simulated customer personas.
    """
    conversation = models.ForeignKey(
        RedTeamConversation,
        on_delete=models.SET_NULL,
        null=True,
        related_name='customer_profiles',
        help_text="Associated red team conversation"
    )

    # Basic profile description
    description = models.TextField(
        null=True,
        help_text="Detailed description of the target customer, including demographics, interests, and potential needs"
    )
    customer_background = models.TextField(
        null=True,
        help_text="Comprehensive background information about the customer, including their profession, lifestyle, and interests"
    )

    # Customer context as JSON
    context = models.JSONField(
        default=dict,
        null=True,
        help_text="JSON object containing intent, query type and scenario details"
    )

    # Personality traits as JSON
    personality = models.JSONField(
        default=dict,
        null=True,
        help_text="JSON object containing temperament, communication style and emotional state"
    )

    objectives = models.TextField(
        null=True,
        help_text="Specific goals or resolutions the customer seeks from this interaction"
    )

    additional_details = models.JSONField(
        null=True,
        help_text="Additional information not included in the basic profile"
    )

    class Meta:
        verbose_name = "Red Team Customer Profile"
        verbose_name_plural = "Red Team Customer Profiles"


class Evaluation(BaseModel):  # Each individual Report
    updated_by_user = models.BooleanField(default=False)
    status = models.CharField(
        choices=utility_models.STATUS_CHOICES,
        max_length=255,
        default=utility_models.SUCCEED,
    )
    status_description = models.CharField(max_length=255, blank=True)


# Create your models here.
class Questions(BaseModel):
    evaluation = models.ForeignKey(
        Evaluation,
        on_delete=models.CASCADE,
        related_name="questions",
    )
    question = models.TextField()
    reference_answer = models.TextField()
    star = models.IntegerField(
        default=1
    )  # what's the difficulty level, 1 - very easy, 5 - very hard
    chatbot_answer = models.TextField(null=True, blank=True)
    justification = models.TextField()
    score = models.FloatField(
        default=0
    )  # how much it score compared to the legit answer
    passing = models.BooleanField(
        default=False
    )  # is the answer relevant to the question or not
    feedback = models.TextField(null=True, blank=True)


class RedTeamConversationEvaluation(utility_models.BaseModel):
    conversation_summary = models.TextField(
        null=True,
        blank=True,
        help_text="A summary of the conversation, including the key points and the overall sentiment of the conversation.",
    )

    objective_met = models.BooleanField(
        null=True,
        blank=True,
        help_text="Whether the objective of the conversation was met",
    )

    objective_failed_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for why the objective was not met",
    )

    number_of_messages_exchanged = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The number of messages exchanged in the conversation",
    )

    message_response_time = models.JSONField(
        default=dict,
        null=True,
        blank=True,
        help_text="The time taken to respond to each message in the conversation",
    )

    # Question Answering Quality
    accuracy_of_answers = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The accuracy of the answers provided by the chatbot",
    )
    accuracy_of_answers_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the accuracy of the answers provided by the chatbot",
    )

    clarity_of_answers = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The quality of the answers provided by the chatbot",
    )
    clarity_of_answers_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the quality of the answers provided by the chatbot",
    )

    empathy_of_answers = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The empathy and understanding of the chatbot's responses",
    )

    empathy_of_answers_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the empathy and understanding of the chatbot's responses",
    )

    relevance_of_answers = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The relevance and accuracy of the chatbot's responses",
    )

    relevance_of_answers_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the relevance and accuracy of the chatbot's responses",
    )

    tone_appropriateness = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The tone and style of the chatbot's responses",
    )

    tone_appropriateness_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the tone and style of the chatbot's responses",
    )

    # Customer Satisfaction
    customer_satisfaction_rating = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The level of satisfaction of the customer with the chatbot's responses",
    )

    customer_satisfaction_reasoning = models.JSONField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the level of satisfaction of the customer with the chatbot's responses",
    )

    # Flow Analysis
    flow_analysis = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="The level of flow of the chatbot's responses",
    )

    flow_analysis_reasoning = models.JSONField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the flow of the chatbot's responses",
    )

    areas_for_improvement = models.TextField(
        null=True,
        blank=True,
        help_text="Areas where the chatbot could improve its responses",
    )

    conversation = models.ForeignKey(
        RedTeamConversation,
        null=True,
        on_delete=models.SET_NULL,
        related_name="evaluations",
    )

    batch_evaluation = models.ForeignKey(
        RedTeamBatchRun,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="redteam_conversation_evaluations",
    )


class RedTeamConversationBatchEvaluation(utility_models.BaseModel):
    batch_satisfaction_stats = models.JSONField(
        null=True,
        blank=True,
        help_text="Statistics for overall satisfaction: mean, median, max, min",
    )

    batch_message_exchange_stats = models.JSONField(
        null=True,
        blank=True,
        help_text="Statistics for number of messages exchanged: mean, median, max, min",
    )

    batch_response_time_stats = models.JSONField(
        null=True,
        blank=True,
        help_text="Statistics for response time: mean, median, max, min",
    )

    overall_satisfaction_score = models.IntegerField(
        null=True,
        blank=True,
        help_text="The overall level of satisfaction of the customer with the chatbot's responses",
    )

    overall_satisfaction_reasoning = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed reasoning for the overall level of satisfaction of the customer with the chatbot's responses",
    )

    top_repeated_issues = models.JSONField(
        null=True,
        blank=True,
        help_text="Top repeated issues in the conversations",
    )

    top_areas_for_improvement = models.JSONField(
        null=True,
        blank=True,
        help_text="Areas where the chatbot could improve its responses",
    )

    knowledge_gaps = models.JSONField(
        null=True,
        blank=True,
        help_text="Knowledge gaps identified in the conversations",
    )

    batch_evaluation = models.ForeignKey(
        RedTeamBatchRun,
        null=True,
        on_delete=models.SET_NULL,
        related_name="redteam_conversation_batch_evaluations",
    )
