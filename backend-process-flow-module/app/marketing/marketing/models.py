from django.db import models
from app.utility import models as utility_models
import app.chats.chat.models as chat_models
from django.db.models import Q


class ClientCustomer(utility_models.BaseModel):
    """
    email: string
    """

    name = models.CharField(max_length=255, blank=True)
    email = models.EmailField(max_length=255, blank=True)
    contact = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    is_signup_newsletter = models.BooleanField(default=False)
    conversation = models.OneToOneField(
        chat_models.Conversation,
        related_name="client_customer",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    def get_all_related_client_customers(self):
        all_related_client_customer_instances = ClientCustomer.objects.filter(
            (Q(email=self.email) & ~Q(email=""))
            | (Q(contact=self.contact) & ~Q(contact=""))
        )
        return all_related_client_customer_instances

    class Meta:
        ordering = ["-created_at"]
