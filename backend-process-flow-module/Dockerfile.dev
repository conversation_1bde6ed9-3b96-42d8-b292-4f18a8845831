# Use the desired Python image as a base
FROM python:3.11

# Set the working directory
WORKDIR /usr/src/backend

# Copy only requirements.txt to leverage Docker caching
COPY requirements.txt requirements.txt

# Install dependencies
RUN python -m pip install --no-cache-dir -r requirements.txt 


# Environment variable for unbuffered Python output
ENV PYTHONUNBUFFERED=1

# Command to run your services (if needed)
# CMD ["your_startup_command"]
