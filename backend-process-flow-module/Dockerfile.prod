FROM python:3.11

# Creating the work directory
WORKDIR /home/<USER>

# Update and upgrade the system packages
RUN apt-get update && \
    apt-get upgrade -y

# create the app user
RUN useradd --create-home --shell /bin/bash app

# Installing dependencies for Gunicorn
# RUN apt-get update
RUN apt-get install -y libpq-dev zip
RUN apt-get install -y gcc
RUN apt-get install -y dos2unix
RUN apt-get install -y wkhtmltopdf

#Install ffmpeg for whisper
RUN apt-get install -y ffmpeg

#Install chrome in server
# RUN apt-get install -y chromium=114.0.5735.198-1~deb12u1 chromium-common=114.0.5735.198-1~deb12u1 
# RUN apt-get update && \
#     apt-get install -y --no-install-recommends gnupg wget curl unzip && \
#     wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
#     echo "deb http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google.list && \
#     apt-get update -y && \
#     apt-get install -y --no-install-recommends google-chrome-stable && \
#     apt-get clean && \
#     rm -rf /var/lib/apt/lists/* /var/cache/apt/* && \
#     CHROME_VERSION=$(google-chrome --product-version) && \
#     wget -q --continue -P /chromedriver "https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/$CHROME_VERSION/linux64/chromedriver-linux64.zip" && \
#     unzip /chromedriver/chromedriver* -d /home/<USER>/ && \
#     rm -rf /chromedriver

RUN apt-get -y update && wget -O ./google-chrome-stable_current_amd64.deb https://mirror.cs.uchicago.edu/google-chrome/pool/main/g/google-chrome-stable/google-chrome-stable_114.0.5735.198-1_amd64.deb && apt-get install -y ./google-chrome-stable_current_amd64.deb && rm -rf /var/lib/apt/lists/* && rm ./google-chrome-stable_current_amd64.deb

# Copying over all the files to work directory
COPY analytic analytic
COPY authapi authapi
COPY backend backend
COPY bot bot
COPY chat chat
COPY chat_action chat_action
COPY chat_action_tracker chat_action_tracker
COPY demo_related demo_related
COPY document document
COPY event event
COPY facebook_integration facebook_integration
COPY integration integration
COPY llm_action llm_action
COPY logs logs
COPY marketing marketing
COPY nova_related nova_related
COPY onboarding onboarding
COPY payment payment
COPY redteam redteam
COPY tenants tenants
COPY user_tenant_bridge user_tenant_bridge
COPY utility utility
COPY tag tag
COPY aws_start.sh aws_start.sh
COPY gunicorn_config.py gunicorn_config.py
COPY manage.py manage.py

# Installing dependencies
COPY requirements.txt requirements.txt
RUN pip3 install -r requirements.txt --no-cache-dir
RUN pip install gunicorn

# Convert ,sh from windows to unix format
RUN dos2unix aws_start.sh

# Expose the port 4000 for access from ELB
EXPOSE 4000
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# chown all the files to the app user
RUN chown -R app:app /home/<USER>

# create nonexistant to tackle nltk download directory - TODO: Change in future!
RUN mkdir /nonexistent
RUN chown -R app:app /nonexistent

# Cleanup section (Worker Template)
RUN apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# change to the app user
USER app

# Entry command for each dockers
ENTRYPOINT ["bash", "./aws_start.sh"]
