FROM python:3.11

# Creating the work directory
WORKDIR /home/<USER>

# Update and upgrade the system packages
RUN apt-get update && \
    apt-get upgrade -y

# create the app user
RUN useradd --create-home --shell /bin/bash app

# Installing dependencies for Gunicorn
# RUN apt-get update
RUN apt-get install -y libpq-dev zip
RUN apt-get install -y gcc
RUN apt-get install -y dos2unix

COPY app app
COPY backend backend
CO<PERSON><PERSON> logs logs
COPY gunicorn_config.py gunicorn_config.py
COPY aws_start.sh aws_start.sh
COPY aws_start_run_redteam.sh aws_start_run_redteam.sh
COPY aws_start_run_redteam_chat.sh aws_start_run_redteam_chat.sh
COPY aws_start_run_redteam_assessor_deployment.sh aws_start_run_redteam_assessor_deployment.sh
COPY manage.py manage.py

COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt 
RUN pip install gunicorn

RUN dos2unix aws_start.sh

EXPOSE 4000
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# chown all the files to the app user
RUN chown -R app:app /home/<USER>

# create nonexistant to tackle nltk download directory - TODO: Change in future!
RUN mkdir /nonexistent
RUN chown -R app:app /nonexistent

# Cleanup section (Worker Template)
RUN apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# change to the app user
USER app

# Entry command for each dockers
ENTRYPOINT ["bash", "./aws_start.sh"]