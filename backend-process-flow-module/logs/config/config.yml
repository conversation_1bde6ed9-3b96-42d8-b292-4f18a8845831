version: 1

formatters:
    simple:
        format: "%(asctime)s - %(levelname)s - %(filename)s-%(module)s-%(funcName)s-%(lineno)d: %(message)s"
        class: pythonjsonlogger.jsonlogger.JsonFormatter
    error:
        format: "%(asctime)s - %(levelname)s - <PID %(process)d:%(processName)s> - %(filename)s-%(module)s-%(funcName)s-%(lineno)d: %(message)s"
        class: pythonjsonlogger.jsonlogger.JsonFormatter

handlers:
    console:
        class: logging.StreamHandler
        level: INFO
        formatter: simple
        stream: ext://sys.stdout

root:
    level: DEBUG
    handlers: [console]
    propogate: no
