include:
  - project: "reluvate/pipeline"
    ref: main
    file:
      - Docker/2-envs/Docker-Django-Service-3.11.gitlab-ci.yml

variables:
  POSTGRES_HOST: $POSTGRES_HOST
  POSTGRES_PASSWORD: $POSTGRES_PASSWORD
  POSTGRES_USER: $POSTGRES_USER
  SECRET_KEY: $SECRET_KEY
  DEBUG: $DEBUG
  ENVIRONMENT: $ENVIRONMENT
  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
  AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
  EBS_ENVIRONMENT: $EBS_ENVIRONMENT
  DOCKER_REGISTRY: $DOCKER_REGISTRY
  ECR_REPOSITORY_NAME: $ECR_REPOSITORY_NAME
  REPOSITORY_URL: $DOCKER_REGISTRY/$ECR_REPOSITORY_NAME
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  SALT_KEY: "$SALT_KEY"
  ECS_SERVICE: $ECS_SERVICE
  CLUSTER_NAME: $CLUSTER_NAME
  FIELD_ENCRYPTION_KEY: $FIELD_ENCRYPTION_KEY

check-settings-job:
  image: python:3.11-slim
  stage: unit-test
  before_script:
    - apt update
    - apt install python3-dev -y
    - apt-get install build-essential -y
    - python -V
    - pip install virtualenv
    - virtualenv venv
    - source venv/bin/activate
    - pip install --upgrade pip
    - pip install -r requirements.txt
  script:
    # - python manage.py migrate_schemas
    - python manage.py check
    # - python manage.py test */tests
    - python manage.py test 

.build:
  tags:
    - saas-linux-medium-amd64
