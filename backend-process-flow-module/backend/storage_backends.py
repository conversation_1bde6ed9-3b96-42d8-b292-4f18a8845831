# your_app/storage_backends.py

from storages.backends.s3boto3 import S3Boto3Storage


class TenantAwareS3Boto3Storage(S3Boto3Storage):
    def get_available_name(self, name, max_length=None):
        from django.db import connection

        tenant_schema = connection.schema_name
        if tenant_schema and tenant_schema != "public":
            name = f"{tenant_schema}/{name}"
        return super().get_available_name(name, max_length=max_length)
