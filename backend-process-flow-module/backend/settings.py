"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 4.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import logging.config
import os
from datetime import timed<PERSON><PERSON>
from logging import ERROR
from pathlib import Path
from socket import gethostbyname, gethostname

import yaml

from backend import load_env

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# fetch the project_root
PROJECT_ROOT = os.path.dirname(BASE_DIR)

# the name of the whole site
SITE_NAME = os.path.basename(BASE_DIR)

# collect static files here
STATIC_ROOT = os.path.join(BASE_DIR, "run", "static")

# collect media files here
MEDIA_ROOT = os.path.join(BASE_DIR, "run", "media")


# look for static assets here
STATICFILES_DIRS = []

# Template Directory
PROJECT_TEMPLATES = []

# Load Secrets
load_env.load_env()

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# Tiktoken for langchain
TIKTOKEN_CACHE_DIR = os.environ.get("TIKTOKEN_CACHE_DIR", "")

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get("DEBUG", "True").lower() == "true"

ENVIRONMENT = os.environ.get("ENVIRONMENT")
# HTTPS_URL_1 = backend, HTTPS_APP_URL_1 = frontend
if ENVIRONMENT in ["Production", "Staging", "Test"]:
    ALLOWED_HOSTS = [
        gethostname(),
        gethostbyname(gethostname()),
        os.environ.get("HTTPS_URL_1"),
        os.environ.get("HTTPS_URL_2"),
    ]

    CORS_ALLOWED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        os.environ.get("HTTPS_APP_URL_3"),
        os.environ.get("HTTPS_APP_URL_4"),
    ]

    CSRF_TRUSTED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        os.environ.get("HTTPS_APP_URL_3"),
        os.environ.get("HTTPS_APP_URL_4"),
    ]

elif ENVIRONMENT == "Development":
    ALLOWED_HOSTS = [
        gethostname(),
        gethostbyname(gethostname()),
        os.environ.get("HTTPS_URL_1"),
        os.environ.get("HTTPS_URL_2"),
    ]

    CORS_ALLOWED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        os.environ.get("HTTPS_APP_URL_3"),
        os.environ.get("HTTPS_APP_URL_4"),
        "http://localhost:3000",
        "http://localhost:8000",
    ]

    CSRF_TRUSTED_ORIGINS = [
        os.environ.get("HTTPS_APP_URL_1"),
        os.environ.get("HTTPS_APP_URL_2"),
        os.environ.get("HTTPS_APP_URL_3"),
        os.environ.get("HTTPS_APP_URL_4"),
        "http://localhost:3000",
        "http://localhost:8000",
    ]
else:
    ALLOWED_HOSTS = ["*"]
    CORS_ALLOWED_ORIGINS = [
        # "*"
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000",
    ]

    CSRF_TRUSTED_ORIGINS = [
        # "*"
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000",
    ]


if os.environ.get("SECURE", "False").lower() == "true":
    SECURE_SSL_REDIRECT = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
    APPEND_SLASH = True


# Application definition
SILENCED_SYSTEM_CHECKS = ["tenant_schemas.W003"]

SHARED_APPS = [
    "tenant_schemas",  # mandatory, should always be before any django app
    "app.core.tenants",  # Use the app name, not the config class
    "corsheaders",
    "django.contrib.contenttypes",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "djoser",
    "app.core.authapi",  # Use the app name, not the config class
    "app.integrations.facebook_integration",
    "app.core.payment",
    "app.core.onboarding",  # onboarding is public
    "app.core.user_tenant_bridge",  # bridge
]


# demo related in tenant cause we need to check individually is the tenant in demo mode (probably in future when dealing with sandbox)
TENANT_APPS = [
    "encrypted_model_fields",
    "app.analytic",
    "app.chats.bot",
    "app.chats.chat",
    "app.chats.chat_action",
    "app.chats.chat_action_tracker",
    "app.chats.customer",
    "app.documents.document",
    "app.integrations.integration",  # shopify
    "app.integrations.event",
    "app.marketing.marketing",
    "app.chats.llm_action",
    "app.redteam",
    "app.documents.tag",
    "app.utility",
    "app.crew_teams",
]


INSTALLED_APPS = SHARED_APPS + TENANT_APPS

TENANT_MODEL = "tenants.Tenant"  # Instead of "app.core.tenants.Tenant"
PUBLIC_SCHEMA_NAME = "public"

MIDDLEWARE = [
    "backend.middleware.health",
    "app.core.tenants.custom_middleware.HostnameMiddleware",
    "tenant_schemas.middleware.TenantMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    # "corsheaders.middleware.CorsPostCsrfMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

AUTHENTICATION_BACKENDS = [
    "app.core.authapi.custom_auth.CustomAuthBackend",
]


ROOT_URLCONF = "backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

TEMPLATE_CONTEXT_PROCESSORS = ("django.core.context_processors.request",)

# WSGI_APPLICATION = "backend.wsgi.application"
ASGI_APPLICATION = "backend.asgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

if os.environ.get("LOCAL", "True").lower() == "true":
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": os.path.join(BASE_DIR, "db.sqlite3"),
        }
    }

else:
    DATABASES = {
        "default": {
            "ENGINE": "tenant_schemas.postgresql_backend",
            "NAME": os.environ.get("POSTGRES_NAME", "postgres"),
            "USER": os.environ.get("POSTGRES_USER"),
            "PASSWORD": os.environ.get("POSTGRES_PASSWORD"),
            "HOST": os.environ.get("POSTGRES_HOST"),
            "PORT": "5432",
        }
    }

DATABASE_ROUTERS = ("tenant_schemas.routers.TenantSyncRouter",)

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTH_USER_MODEL = "authapi.User"  # Instead of "app.core.authapi.User"

APPEND_SLASH = True

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

# TIME_ZONE = "UTC"
TIME_ZONE = "Asia/Singapore"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "/static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# Logging
with open(os.path.join(BASE_DIR, "logs", "config", "config.yml"), "r") as stream:
    config = yaml.load(stream, Loader=yaml.FullLoader)
logging.config.dictConfig(config)


# REST
REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 100,
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.AllowAny",),
}

# AWS

AWS_ACCESS_KEY_ID = os.environ.get("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.environ.get("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = os.environ.get("AWS_STORAGE_BUCKET_NAME")
AWS_S3_SIGNATURE_VERSION = os.environ.get("AWS_S3_SIGNATURE_VERSION")
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": "max-age=86400",
}
AWS_S3_REGION_NAME = os.environ.get("AWS_S3_REGION_NAME")
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = None
AWS_S3_VERIFY = True
AWS_QUERYSTRING_AUTH = False

# DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"
DEFAULT_FILE_STORAGE = "backend.storage_backends.TenantAwareS3Boto3Storage"

# DJOSER
DJOSER = {
    "SERIALIZERS": {
        "user": "app.core.authapi.serializers.CustomUserSerializer",
        "current_user": "app.core.authapi.serializers.CustomUserSerializer",
        "user_create": "app.core.authapi.serializers.CustomUserCreateSerializer",
        "user_create_password_retype": "app.core.authapi.serializers.CustomUserCreateSerializer",
    },
    "LOGIN_FIELD": "email",
    "SEND_ACTIVATION_EMAIL": False,
    "SEND_CONFIRMATION_EMAIL": False,
    "USER_CREATE_PASSWORD_RETYPE": True,
    "PASSWORD_RESET_CONFIRM_URL": "/reset-password/{uid}/{token}",  # the reset link
    "PASSWORD_CHANGED_EMAIL_CONFIRMATION": True,
    "EMAIL": {
        "password_reset": "app.core.authapi.email.PasswordResetEmail",
        "password_changed_confirmation": "app.core.authapi.email.PasswordChangedConfirmationEmail",
    },
    "ACTIVATION_URL": "activate/{uid}/{token}",
    "HTTPS_APP_URL_1": os.environ.get("HTTPS_APP_URL_1"),
    "HTTPS_APP_URL_3": os.environ.get("HTTPS_APP_URL_3"),
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=1),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,
}


# EMAIL
EMAIL_BACKEND = "django_ses.SESBackend"
AWS_SES_REGION_NAME = os.environ.get("AWS_S3_REGION_NAME")

CHROMA_SERVER_IP = os.environ.get("CHROMA_SERVER_IP")
CHROMA_SERVER_PORT = os.environ.get("CHROMA_SERVER_PORT")

# Celery settings
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD")
REDIS_HOST = os.environ.get("REDIS_HOST")

BOT_API_KEY = os.environ.get("BOT_API_KEY", default="your-default-key-here")

TENANT_BACKEND_URL = os.environ.get("TENANT_BACKEND_URL")
WEBSOCKET_URL = os.environ.get("WEBSOCKET_URL")

# Cryptography
FIELD_ENCRYPTION_KEY = os.environ.get("FIELD_ENCRYPTION_KEY")

# Check for LOCAL RUN
LOCAL_OUTPUT = str(os.environ.get("LOCAL_OUTPUT", False)).lower() == "true"

# Email Notification
ERROR_EMAIL_RECIPIENTS = os.environ.get("ERROR_EMAIL_RECIPIENTS")
DEFAULT_FROM_EMAIL = os.environ.get("DEFAULT_FROM_EMAIL")

BEEFY_API_KEY = os.environ.get("BEEFY_API_KEY")
BEEFY_BASE_URL = os.environ.get("BEEFY_BASE_URL")
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
