aiohappyeyeballs==2.4.3
aiohttp==3.11.11
aiosignal==1.3.1
alembic==1.14.0
annotated-types==0.7.0
anyio==4.6.2.post1
appdirs==1.4.4
asgiref==3.8.1
asttokens==2.4.1
attrs==24.2.0
auth0-python==4.7.2
Authlib==1.3.1
backoff==2.2.1
bcrypt==4.2.0
beautifulsoup4==4.12.3
blinker==1.9.0
boto3==1.35.59
botocore==1.35.59
build==1.2.2.post1
cachetools==5.5.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.7
cohere==5.11.3
coloredlogs==15.0.1
comm==0.2.2
crewai==0.95.0
crewai-tools==0.25.8
cryptography==43.0.3
dataclasses-json==0.6.7
debugpy==1.8.8
decorator==5.1.1
defusedxml==0.8.0rc2
Deprecated==1.2.14
deprecation==2.1.0
distro==1.9.0
Django==5.1.3
django-cors-headers==4.6.0
django-encrypted-model-fields==0.6.5
django-ses==4.3.1
django-storages==1.14.4
django-templated-mail==1.1.1
django-tenant-schemas==1.12.0
django-tenants==3.7.0
django-timezone-field==7.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
djoser==2.3.1
docker==7.1.0
docstring_parser==0.16
docx2txt==0.8
durationpy==0.9
embedchain==0.1.126
et_xmlfile==2.0.0
executing==2.1.0
fastapi==0.115.5
fastavro==1.9.7
filelock==3.16.1
flatbuffers==24.3.25
frozenlist==1.5.0
fsspec==2024.10.0
fuzzywuzzy==0.18.0
geographiclib==2.0
geopy==2.4.1
google-api-core==2.23.0
google-api-python-client==2.154.0
google-auth==2.36.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-cloud-aiplatform==1.72.0
google-cloud-bigquery==3.27.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.13.0
google-cloud-storage==2.18.2
google-crc32c==1.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
gptcache==0.1.44
greenlet==3.1.1
grpc-google-iam-v1==0.13.1
grpcio==1.69.0
grpcio-health-checking==1.69.0
grpcio-status==1.67.1
grpcio-tools==1.67.1
gspread==6.1.4
h11==0.14.0
h2==4.1.0
holidays==0.63
hpack==4.0.0
httpcore==1.0.6
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.26.2
humanfriendly==10.0
hyperframe==6.0.1
idna==3.10
ijson==3.3.0
importlib_metadata==8.5.0
importlib_resources==6.4.5
iniconfig==2.0.0
instructor==1.6.3
ipython==8.29.0
jedi==0.19.2
Jinja2==3.1.4
jiter==0.5.0
jmespath==1.0.1
json_repair==0.30.1
jsonpatch==1.33
jsonpickle==4.0.0
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kubernetes==31.0.0
lancedb==0.15.0
langchain==0.3.7
langchain-cohere==0.3.1
langchain-community==0.3.7
langchain-core==0.3.17
langchain-experimental==0.3.3
langchain-openai==0.2.8
langchain-text-splitters==0.3.2
langsmith==0.1.142
linkup-sdk==0.2.1
litellm==1.52.5
Mako==1.3.6
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.40
mmh3==5.0.1
monotonic==1.6
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nodeenv==1.9.1
numpy==1.26.4
oauthlib==3.2.2
onnxruntime==1.20.0
openai==1.54.4
openpyxl==3.1.5
opentelemetry-api==1.28.1
opentelemetry-exporter-otlp-proto-common==1.28.1
opentelemetry-exporter-otlp-proto-grpc==1.28.1
opentelemetry-exporter-otlp-proto-http==1.28.1
opentelemetry-instrumentation==0.49b1
opentelemetry-instrumentation-asgi==0.49b1
opentelemetry-instrumentation-fastapi==0.49b1
opentelemetry-proto==1.28.1
opentelemetry-sdk==1.28.1
opentelemetry-semantic-conventions==0.49b1
opentelemetry-util-http==0.49b1
ordered-set==4.1.0
orjson==3.10.11
outcome==1.3.0.post0
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parameterized==0.9.0
parso==0.8.4
pdfminer.six==20231228
pdfplumber==0.11.5
pexpect==4.9.0
pillow==11.0.0
platformdirs==4.3.6
pluggy==1.5.0
portalocker==2.10.1
posthog==3.7.0
prompt_toolkit==3.0.48
propcache==0.2.0
proto-plus==1.25.0
protobuf==5.28.3
psutil==6.1.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==18.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.4
pydantic-settings==2.6.1
pydantic_core==2.27.2
Pygments==2.18.0
PyJWT==2.9.0
pylance==0.19.1
pyparsing==3.2.0
pypdf==5.1.0
PyPDF2==3.0.1
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.388
pysbd==0.3.4
PySocks==1.7.1
pytest==8.3.3
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==2.0.7
python3-openid==3.2.0
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
pyzmq==26.2.0
qdrant-client==1.12.1
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.21.0
rsa==4.9
s3transfer==0.10.3
schema==0.7.7
scrapegraph_py==1.8.0
selenium==4.26.1
serpapi==0.1.5
shapely==2.0.6
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
social-auth-app-django==5.4.2
social-auth-core==4.5.4
sortedcontainers==2.4.0
soupsieve==2.6
spider-client==0.1.25
SQLAlchemy==2.0.35
sqlparse==0.5.1
stack-data==0.6.3
starlette==0.41.2
sympy==1.13.3
tabulate==0.9.0
tenacity==9.0.0
tiktoken==0.7.0
tokenizers==0.20.3
tomli==2.1.0
tomli_w==1.1.0
tornado==6.4.1
tqdm==4.67.0
traitlets==5.14.3
trio==0.27.0
trio-websocket==0.11.1
typer==0.13.0
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
uritemplate==4.1.1
urllib3==2.2.3
uv==0.5.1
uvicorn==0.32.0
uvloop==0.21.0
validators==0.34.0
watchfiles==0.24.0
wcwidth==0.2.13
weaviate-client==4.10.2
websocket-client==1.8.0
websockets==14.0
WooCommerce==3.0.0
wrapt==1.16.0
wsproto==1.2.0
yarl==1.17.1
zipp==3.21.0
groq