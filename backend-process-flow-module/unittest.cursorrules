You are an expert in Python, Django, and scalable web application development.

Key Principles
- Write clear, technical unit tests that verify the logic and behavior of individual functions and methods.
- Use assert statements judiciously to check:
    - Response type (e.g., dict, list, int).
    - Required keys or attributes in complex responses (e.g., dictionary or JSON objects).
    - Value types within response objects to ensure consistency.
- Structure unit tests in a modular way, organizing them by app and functionality to promote readability and reusability.
- Focus tests on edge cases and stress testing, covering common, boundary, and failure scenarios to ensure robust coverage.
- Include clear documentation and comments for each test case to explain:
    - Purpose and scope of the test
    - Input data and expected outcomes
    - Any special setup or conditions required
    - Steps for modification if changes are needed

Django Unit Testing Framework
- Use Django's TestCase class for database-dependent tests; use SimpleTestCase for tests that do not require database access.
- Prefer Django's built-in assert methods (e.g., assertEqual, assertTrue, assertContains, assertRaises) for clear and descriptive assertions.
- Adhere to Django's PEP 8-compliant coding style; use descriptive and specific test case names and method names.
- Place test files under the app's tests/ directory with naming convention test_<filename>_<function name>.py (e.g., test_helper_create_user.py for testing create_user function in helper.py)
- Document test classes with docstrings explaining the overall test suite purpose and any setup requirements

Testing Focus and Structure
- Function Scope: Test functions independently to isolate business logic and ensure that each function performs as expected.
- Unit Test Files: Maintain separate test files within each app's tests/ directory, with logical groupings of tests for specific views, models, forms, or utilities.
- Edge Cases and Stress Testing:
    - Include tests for minimum and maximum input values, empty data, null values, and data types outside expected ranges.
    - Simulate edge cases (e.g., invalid IDs, missing required fields) to validate error handling and resilience.
- Documentation Requirements:
    - Each test method must have a clear docstring explaining what is being tested
    - Include comments for complex test logic or setup
    - Document any assumptions or dependencies

Response Assertions
- Response Type: Use assertIsInstance to verify that the response matches the expected type (e.g., dict, HttpResponse, JsonResponse).
- Content Structure:
    - For dictionaries, check the existence of compulsory keys with assertIn or assertTrue(key in response).
    - Use assertEqual and assertIsInstance to validate the types of individual values within a response (e.g., int, list, str).
- Complex Data Types:
    - When expecting lists, use assertIsInstance on each item if items are complex objects.
    - For nested dictionaries, verify nested keys and value types where critical to the function's purpose.

Django-Specific Guidelines
- Models: Use Django ORM queries in tests to ensure data integrity and behavior consistency (e.g., check model constraints, signals).
- Forms: Test form validation thoroughly, focusing on valid, invalid, and boundary input data to assert correct behavior and error messages.
- Views: For CBVs, test both GET and POST methods (where applicable), using Django's Client to simulate HTTP requests and validate responses.
- Serializers: Use Django REST Framework's Serializer tests to check validation, data conversion, and error handling for APIs.
- Signals: For functions that rely on signals, create tests that trigger the signal event, and use assert to verify expected outcomes.

Testing Edge Cases
- Include tests for:
    - Empty data (e.g., empty strings, empty lists).
    - Maximum and minimum allowable values.
    - Invalid data types (e.g., string instead of integer).
    - Invalid data that should trigger validation errors (e.g., incorrect email format).
    - Non-existent foreign keys or relationships.

Error Handling and Exception Testing
- Use assertRaises to verify that expected exceptions are raised when passing invalid data or encountering error conditions.
- For custom error messages, use assertEqual to verify that exceptions include the correct message.
- Mock external dependencies where possible to simulate failures and ensure resilience.

Stress Testing and Performance
- Implement stress tests for critical functions, simulating high-volume data or concurrency to assess response under load.
- Measure the performance of functions using Django's test tools, aiming to identify potential bottlenecks or optimizations.

Dependencies
- unittest.mock: Use patch to mock dependencies like external API calls, file I/O, and network connections to control test conditions.
- pytest: Use pytest-django for enhanced test readability and advanced features like fixtures and parameterized tests.