# Server Setup and Testing Instructions

This guide will walk you through the process of running the server and testing it.

## Running the Server

1. Ensure you have all the necessary dependencies installed. You can install them using:

   ```
   pip install -r requirements.txt
   ```

2. Start the backend server using Gun<PERSON>, which is a Python WSGI HTTP Server for UNIX:

   ```
   gunicorn --config ./gunicorn_config.py backend.wsgi:application
   ```

   This command will start the server, typically on `127.0.0.1:8000`.

## Testing the Server

1. Use a tool like <PERSON><PERSON> or <PERSON> to send HTTP requests to the server.

2. Set the URL to:

   ```
   http://127.0.0.1:8000/
   ```

3. You can now send requests to the server and receive responses.

## Important Notes

- Ensure that your application is configured correctly in `gunicorn_config.py`.

- If you're testing locally and encounter any issues with the hostname, you may need to adjust your hosts file or use `localhost` instead of `127.0.0.1`.

## Troubleshooting

- If you encounter connection issues, ensure that:
  1. The Gunicorn server is running correctly.
  2. You're using the correct URL.
  3. There are no firewall or network issues blocking the connection.

- Check the server logs for any error messages or warnings that might indicate configuration issues.

- If you're developing locally, make sure you're not running into any CORS (Cross-Origin Resource Sharing) issues.

Remember to adapt these instructions if you make any changes to the server configuration or deployment setup.
