import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "./ui/dialog";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";

interface ConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  config: string;
  onUpdateConfig: (newConfig: string) => void;
}

const ConfigModal: React.FC<ConfigModalProps> = ({
  isOpen,
  onClose,
  config,
  onUpdateConfig,
}) => {
  const [localConfig, setLocalConfig] = useState(config);

  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  const handleUpdateConfig = () => {
    try {
      const parsedConfig = JSON.parse(localConfig);
      // Update the lastUpdated field
      parsedConfig.meta.lastUpdated = new Date().toISOString();
      // Ensure connection labels are preserved
      parsedConfig.connections = parsedConfig.connections.map(
        (conn: { label?: string; type: string }) => ({
          ...conn,
          label: conn.label || conn.type,
        })
      );
      const updatedConfig = JSON.stringify(parsedConfig, null, 2);
      onUpdateConfig(updatedConfig);
      onClose();
    } catch (error) {
      console.error("Config parsing error:", error);
      alert("Invalid JSON. Please check your input.");
    }
  };

  const generateSampleConfig = () => {
    const sampleConfig = {
      meta: {
        title: "Customer Order Processing",
        description:
          "A workflow for processing customer orders and handling account status",
        lastUpdated: new Date().toISOString(),
      },
      processFlow: [
        {
          id: "node-1",
          type: "triggerNode",
          position: { x: 100, y: 200 },
          label: "Start",
          description: "Process initiation",
          owner: "System",
          action: "Trigger process",
          conditions: [],
          altFlow: [],
          validation: { requiredFields: [], fieldTypes: {} },
        },
        {
          id: "node-2",
          type: "actionNode",
          position: { x: 300, y: 100 },
          label: "Fetch Data",
          description: "Retrieve customer information",
          owner: "Database",
          action: "Query customer data",
          conditions: ["Valid customer ID"],
          altFlow: [],
          validation: {
            requiredFields: ["customerID"],
            fieldTypes: { customerID: "string" },
          },
        },
        {
          id: "node-3",
          type: "decisionNode",
          position: { x: 500, y: 200 },
          label: "Check Status",
          description: "Verify customer account status",
          owner: "System",
          action: "Evaluate account status",
          conditions: ["Account is active"],
          altFlow: [
            {
              condition: "Account is inactive",
              action: "Notify support",
              nextNode: "node-6",
            },
          ],
          validation: {
            requiredFields: ["accountStatus"],
            fieldTypes: { accountStatus: "string" },
          },
        },
        {
          id: "node-4",
          type: "actionNode",
          position: { x: 700, y: 100 },
          label: "Process Order",
          description: "Create new order in the system",
          owner: "Order System",
          action: "Generate order",
          conditions: ["Inventory available"],
          altFlow: [],
          validation: {
            requiredFields: ["orderDetails"],
            fieldTypes: { orderDetails: "object" },
          },
        },
        {
          id: "node-5",
          type: "actionNode",
          position: { x: 900, y: 200 },
          label: "Send Confirmation",
          description: "Email order confirmation to customer",
          owner: "Notification System",
          action: "Send email",
          conditions: [],
          altFlow: [],
          validation: {
            requiredFields: ["customerEmail"],
            fieldTypes: { customerEmail: "string" },
          },
        },
        {
          id: "node-6",
          type: "actionNode",
          position: { x: 700, y: 300 },
          label: "Notify Support",
          description: "Alert support team about inactive account",
          owner: "Notification System",
          action: "Create support ticket",
          conditions: [],
          altFlow: [],
          validation: {
            requiredFields: ["accountID"],
            fieldTypes: { accountID: "string" },
          },
        },
        {
          id: "node-7",
          type: "endNode",
          position: { x: 1100, y: 200 },
          label: "End",
          description: "Process completion",
          owner: "System",
          action: "Conclude process",
          conditions: [],
          altFlow: [],
          validation: { requiredFields: [], fieldTypes: {} },
        },
      ],
      connections: [
        {
          from: "node-1",
          to: "node-2",
          type: "standard",
          label: "Start order process",
        },
        {
          from: "node-2",
          to: "node-3",
          type: "standard",
          label: "Verify account status",
        },
        { from: "node-3", to: "node-4", type: "yes", label: "Account active" },
        { from: "node-3", to: "node-6", type: "no", label: "Account inactive" },
        {
          from: "node-4",
          to: "node-5",
          type: "standard",
          label: "Order processed successfully",
        },
        {
          from: "node-5",
          to: "node-7",
          type: "standard",
          label: "Send confirmation",
        },
        {
          from: "node-6",
          to: "node-7",
          type: "standard",
          label: "Support notified",
        },
      ],
    };

    setLocalConfig(JSON.stringify(sampleConfig, null, 2));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[80vw] sm:max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Update Configuration</DialogTitle>
        </DialogHeader>
        <div className="flex-grow py-4 overflow-hidden">
          <Textarea
            value={localConfig}
            onChange={(e) => setLocalConfig(e.target.value)}
            placeholder="Current flow configuration will appear here..."
            className="h-full min-h-[60vh] resize-none"
          />
        </div>
        <DialogFooter>
          <Button onClick={generateSampleConfig}>Generate Sample Config</Button>
          <Button type="submit" onClick={handleUpdateConfig}>
            Update Config
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfigModal;
