'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface FlowValidityModalProps {
  isOpen: boolean;
  onClose: () => void;
  validityReport: string;
  isValidating?: boolean;
}

export function FlowValidityModal({ isOpen, onClose, validityReport, isValidating = false }: FlowValidityModalProps) {
  // Determine status icon and color based on report content
  const getStatusInfo = () => {
    if (isValidating) {
      return {
        icon: '⏳',
        title: 'Validating Workflow',
        className: 'text-amber-500'
      };
    } else if (validityReport.includes('❌')) {
      return {
        icon: '❌',
        title: 'Validation Failed',
        className: 'text-red-500'
      };
    } else if (validityReport.includes('⚠️')) {
      return {
        icon: '⚠️',
        title: 'Validation Warnings',
        className: 'text-amber-500'
      };
    } else {
      return {
        icon: '✅',
        title: 'Validation Passed',
        className: 'text-green-500'
      };
    }
  };

  const { icon, title, className } = getStatusInfo();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className={`flex items-center gap-2 text-xl ${className}`}>
            <span>{icon}</span>
            <span>{title}</span>
            {isValidating && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
          </DialogTitle>
          <DialogDescription>
            Workflow validation results using Groq LLM analysis
          </DialogDescription>
        </DialogHeader>
        
        {isValidating ? (
          <div className="p-6 flex flex-col items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <p className="text-center text-muted-foreground">Analyzing workflow against validation rules...</p>
          </div>
        ) : (
          <div className="p-4 my-2 bg-gray-100 dark:bg-gray-800 rounded-md overflow-y-auto max-h-[50vh]">
            <div className="prose prose-sm dark:prose-invert" dangerouslySetInnerHTML={{ 
              __html: validityReport
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\n/g, '<br />')
            }} />
          </div>
        )}
        
        <DialogFooter>
          <Button onClick={onClose} variant="outline" disabled={isValidating}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
