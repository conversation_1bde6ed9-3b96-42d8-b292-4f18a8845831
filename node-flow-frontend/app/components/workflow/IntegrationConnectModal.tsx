'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { InfoIcon, KeyIcon, LockIcon, CheckCircle2, ExternalLink, Copy } from 'lucide-react';
import { Integration } from '@/types/integration';

// Using the same types as in IntegrationsDrawer
type IntegrationStatus = 'connected' | 'setup_required' | 'not_connected';

interface IntegrationWithStatus {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  authType: string;
  status: IntegrationStatus;
}
import { getIntegrationIcon, categoryColors } from '@/lib/integrationIcons';

interface IntegrationConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
  integration: IntegrationWithStatus | null;
  onConnect: (integrationId: string, credentials: Record<string, string>) => void;
}

export function IntegrationConnectModal({ 
  isOpen, 
  onClose, 
  integration, 
  onConnect 
}: IntegrationConnectModalProps) {
  const [activeTab, setActiveTab] = useState<string>('credentials');
  const [credentials, setCredentials] = useState<Record<string, string>>({});
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  if (!integration) return null;

  const handleInputChange = (field: string, value: string) => {
    setCredentials((prev: Record<string, string>) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    setConnectionStatus('testing');
    
    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, we'll simulate a successful connection
      setConnectionStatus('success');
      onConnect(integration.id, credentials);
      
      // Close modal after successful connection with a slight delay
      setTimeout(() => {
        onClose();
      }, 1000);
    } catch (error) {
      setConnectionStatus('error');
      setErrorMessage('Failed to connect. Please check your credentials and try again.');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderCredentialsForm = () => {
    // Common API key form for most API-based integrations
    const renderApiKeyForm = () => (
      <div className="flex flex-col gap-2">
        <Label htmlFor="apiKey">API Key</Label>
        <div className="relative">
          <Input
            id="apiKey"
            type="password"
            placeholder="Enter your API key"
            value={credentials.apiKey || ''}
            onChange={(e) => handleInputChange('apiKey', e.target.value)}
            className="pr-10"
          />
          <KeyIcon className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
        </div>
      </div>
    );

    // Specific forms based on integration ID
    switch (integration.id) {
      case 'openai':
        return (
          <div className="space-y-4">
            {renderApiKeyForm()}
            <div className="flex flex-col gap-2">
              <Label htmlFor="model">Model</Label>
              <Select
                value={credentials.model || 'gpt-4'}
                onValueChange={(value: string) => handleInputChange('model', value)}
              >
                <SelectTrigger id="model">
                  <SelectValue placeholder="Select a model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="organization">Organization ID (optional)</Label>
              <Input
                id="organization"
                type="text"
                placeholder="org-..."
                value={credentials.organization || ''}
                onChange={(e) => handleInputChange('organization', e.target.value)}
              />
            </div>
          </div>
        );

      case 'anthropic':
        return (
          <div className="space-y-4">
            {renderApiKeyForm()}
            <div className="flex flex-col gap-2">
              <Label htmlFor="model">Model</Label>
              <Select
                value={credentials.model || 'claude-3-opus'}
                onValueChange={(value: string) => handleInputChange('model', value)}
              >
                <SelectTrigger id="model">
                  <SelectValue placeholder="Select a model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                  <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                  <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 'stripe':
        return (
          <div className="space-y-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="publishableKey">Publishable Key</Label>
              <Input
                id="publishableKey"
                type="text"
                placeholder="pk_..."
                value={credentials.publishableKey || ''}
                onChange={(e) => handleInputChange('publishableKey', e.target.value)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="secretKey">Secret Key</Label>
              <div className="relative">
                <Input
                  id="secretKey"
                  type="password"
                  placeholder="sk_..."
                  value={credentials.secretKey || ''}
                  onChange={(e) => handleInputChange('secretKey', e.target.value)}
                  className="pr-10"
                />
                <KeyIcon className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="webhookSecret">Webhook Secret (optional)</Label>
              <Input
                id="webhookSecret"
                type="password"
                placeholder="whsec_..."
                value={credentials.webhookSecret || ''}
                onChange={(e) => handleInputChange('webhookSecret', e.target.value)}
              />
            </div>
          </div>
        );

      case 'notion':
        return (
          <div className="space-y-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="apiKey">Integration Token</Label>
              <div className="relative">
                <Input
                  id="apiKey"
                  type="password"
                  placeholder="secret_..."
                  value={credentials.apiKey || ''}
                  onChange={(e) => handleInputChange('apiKey', e.target.value)}
                  className="pr-10"
                />
                <KeyIcon className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            <Alert className="bg-blue-50 border-blue-100">
              <InfoIcon className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                Create an integration in your Notion workspace to get an integration token.
              </AlertDescription>
            </Alert>
          </div>
        );

      case 'aws-s3':
        return (
          <div className="space-y-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="accessKeyId">Access Key ID</Label>
              <Input
                id="accessKeyId"
                type="text"
                placeholder="AKIA..."
                value={credentials.accessKeyId || ''}
                onChange={(e) => handleInputChange('accessKeyId', e.target.value)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="secretAccessKey">Secret Access Key</Label>
              <div className="relative">
                <Input
                  id="secretAccessKey"
                  type="password"
                  placeholder="Enter your secret access key"
                  value={credentials.secretAccessKey || ''}
                  onChange={(e) => handleInputChange('secretAccessKey', e.target.value)}
                  className="pr-10"
                />
                <KeyIcon className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="region">Region</Label>
              <Select
                value={credentials.region || 'us-east-1'}
                onValueChange={(value: string) => handleInputChange('region', value)}
              >
                <SelectTrigger id="region">
                  <SelectValue placeholder="Select a region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="us-east-1">US East (N. Virginia)</SelectItem>
                  <SelectItem value="us-east-2">US East (Ohio)</SelectItem>
                  <SelectItem value="us-west-1">US West (N. California)</SelectItem>
                  <SelectItem value="us-west-2">US West (Oregon)</SelectItem>
                  <SelectItem value="eu-west-1">EU (Ireland)</SelectItem>
                  <SelectItem value="eu-central-1">EU (Frankfurt)</SelectItem>
                  <SelectItem value="ap-northeast-1">Asia Pacific (Tokyo)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="bucket">Bucket Name</Label>
              <Input
                id="bucket"
                type="text"
                placeholder="my-bucket"
                value={credentials.bucket || ''}
                onChange={(e) => handleInputChange('bucket', e.target.value)}
              />
            </div>
          </div>
        );

      // Default handlers based on auth type
      default:
        switch (integration.authType) {
          case 'api_key':
            return (
              <div className="space-y-4">
                {renderApiKeyForm()}
              </div>
            );

          case 'oauth2':
            return (
              <div className="space-y-4">
                <Alert className="bg-blue-50 border-blue-100">
                  <InfoIcon className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    You'll be redirected to {integration.name} to authorize access to your account.
                  </AlertDescription>
                </Alert>
                <Button 
                  className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
                  variant="outline"
                  onClick={() => handleInputChange('oauth', 'initiated')}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Connect with {integration.name}
                </Button>
              </div>
            );

          case 'basic_auth':
            return (
              <div className="space-y-4">
                <div className="flex flex-col gap-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter your username"
                    value={credentials.username || ''}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      value={credentials.password || ''}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="pr-10"
                    />
                    <LockIcon className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
                  </div>
                </div>
                {integration.category === 'database' && (
                  <>
                    <div className="flex flex-col gap-2">
                      <Label htmlFor="host">Host</Label>
                      <Input
                        id="host"
                        type="text"
                        placeholder="localhost"
                        value={credentials.host || ''}
                        onChange={(e) => handleInputChange('host', e.target.value)}
                      />
                    </div>
                    <div className="flex flex-col gap-2">
                      <Label htmlFor="port">Port</Label>
                      <Input
                        id="port"
                        type="text"
                        placeholder={integration.id === 'postgres' ? '5432' : integration.id === 'mongodb' ? '27017' : ''}
                        value={credentials.port || ''}
                        onChange={(e) => handleInputChange('port', e.target.value)}
                      />
                    </div>
                    <div className="flex flex-col gap-2">
                      <Label htmlFor="database">Database Name</Label>
                      <Input
                        id="database"
                        type="text"
                        placeholder="my_database"
                        value={credentials.database || ''}
                        onChange={(e) => handleInputChange('database', e.target.value)}
                      />
                    </div>
                  </>
                )}
              </div>
            );

          default:
            return (
              <div className="py-4 text-center text-gray-500">
                Custom authentication method not supported in this interface.
              </div>
            );
        }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] p-0 overflow-hidden">
        
        <div className="flex h-[600px]">
          {/* Left side - Information */}
          <div className="w-1/3 border-r border-gray-200 p-6 bg-gray-50 overflow-y-auto">
            <div className="flex items-center gap-2 mb-6">
              {getIntegrationIcon(integration.id, 24, categoryColors[integration.category])}
              <h3 className="text-lg font-semibold">{integration.name}</h3>
            </div>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-2">Description</h3>
                <p className="text-sm text-gray-600">{integration.description}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">Authentication Type</h3>
                <p className="text-sm text-gray-600">
                  {integration.authType === 'oauth2' ? 'OAuth 2.0' : 
                   integration.authType === 'api_key' ? 'API Key' : 
                   integration.authType === 'basic_auth' ? 'Username & Password' : 'Custom Authentication'}
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">Documentation</h3>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="text-blue-600 border-blue-200 hover:bg-blue-50 text-xs"
                  onClick={() => window.open(`https://docs.example.com/integrations/${integration.id}`, '_blank')}
                >
                  <ExternalLink className="mr-1.5 h-3 w-3" />
                  View Documentation
                </Button>
              </div>
              
              {integration.authType === 'api_key' && (
                <div>
                  <h3 className="text-sm font-medium mb-2">How to get your API Key</h3>
                  <ol className="text-sm text-gray-600 list-decimal pl-5 space-y-1">
                    <li>Log in to your {integration.name} account</li>
                    <li>Go to Account Settings or Developer Settings</li>
                    <li>Look for API Keys or Developer Keys section</li>
                    <li>Create a new API key and copy it</li>
                    <li>Paste it in the credentials form</li>
                  </ol>
                </div>
              )}
            </div>
          </div>
          
          {/* Right side - Configuration */}
          <div className="w-2/3 p-6 overflow-y-auto">
            <DialogHeader className="pb-4 mb-4 border-b">
              <DialogTitle>Connect to {integration.name}</DialogTitle>
            </DialogHeader>
            
            {connectionStatus === 'error' && (
              <Alert className="bg-red-50 border-red-100 mb-6">
                <AlertDescription className="text-red-800">
                  {errorMessage}
                </AlertDescription>
              </Alert>
            )}
            
            {connectionStatus === 'success' && (
              <Alert className="bg-green-50 border-green-100 mb-6">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Successfully connected to {integration.name}!
                </AlertDescription>
              </Alert>
            )}
            
            {renderCredentialsForm()}
          </div>
        </div>
        
        <div className="p-4 border-t border-gray-200 flex justify-end gap-2 bg-gray-50">
          <Button 
            variant="outline" 
            onClick={onClose}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConnect}
            disabled={isConnecting || connectionStatus === 'success'}
            className={connectionStatus === 'success' ? 'bg-green-600 hover:bg-green-700' : ''}          
          >
            {connectionStatus === 'testing' ? 'Connecting...' : 
             connectionStatus === 'success' ? 'Connected' : 'Connect'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
