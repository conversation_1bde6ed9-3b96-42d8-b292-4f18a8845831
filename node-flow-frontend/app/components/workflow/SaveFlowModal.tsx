'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FlowConfig } from '@/lib/flows';

interface SaveFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string, flowId?: string) => void;
  existingFlows: FlowConfig[];
}

export function SaveFlowModal({ isOpen, onClose, onSave, existingFlows }: SaveFlowModalProps) {
  const [flowName, setFlowName] = useState('');
  const [selectedFlowId, setSelectedFlowId] = useState<string | null>(null);

  const handleSave = () => {
    if (selectedFlowId) {
      // If a flow is selected, override it
      onSave(flowName || existingFlows.find(f => f.id === selectedFlowId)?.name || '', selectedFlowId);
    } else if (flowName.trim()) {
      // If no flow is selected but we have a name, create new
      onSave(flowName.trim());
    }
    setFlowName('');
    setSelectedFlowId(null);
    onClose();
  };

  // Filter out the example flow from the list of overridable flows
  const overridableFlows = existingFlows.filter(flow => flow.id !== 'example-flow');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle>Save Flow Configuration</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* New Flow Section */}
          <div className="space-y-2">
            <Label>Create New Flow</Label>
            <Input
              value={flowName}
              onChange={(e) => {
                setFlowName(e.target.value);
                setSelectedFlowId(null); // Deselect any selected flow when typing
              }}
              placeholder="Enter flow name..."
              disabled={!!selectedFlowId}
            />
          </div>

          {/* Existing Flows Section */}
          {overridableFlows.length > 0 && (
            <div className="space-y-2">
              <Label>Or Override Existing Flow</Label>
              <ScrollArea className="h-[150px] w-full rounded-md border">
                <div className="p-2 space-y-2">
                  {overridableFlows.map((flow) => (
                    <Button
                      key={flow.id}
                      variant={selectedFlowId === flow.id ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setSelectedFlowId(flow.id);
                        setFlowName(''); // Clear the name input when selecting a flow
                      }}
                    >
                      <div className="flex flex-col items-start">
                        <span className="font-medium">{flow.name}</span>
                        <span className="text-xs text-gray-500">
                          Last modified: {new Date(flow.updatedAt).toLocaleString()}
                        </span>
                      </div>
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button 
            onClick={handleSave} 
            disabled={!flowName.trim() && !selectedFlowId}
          >
            {selectedFlowId ? 'Override' : 'Save New'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 