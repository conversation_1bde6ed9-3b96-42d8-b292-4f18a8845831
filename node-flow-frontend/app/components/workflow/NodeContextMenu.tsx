"use client";

import React, { useCallback, useState } from "react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";

interface NodeContextMenuProps {
  children: React.ReactNode;
  onAddNode: (type: string, event?: React.MouseEvent) => void;
  onDeleteNode: (nodeId: string) => void;
}

export function NodeContextMenu({
  children,
  onAddNode,
  onDeleteNode,
}: NodeContextMenuProps) {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    // Get the closest node element
    const nodeElement = (event.target as HTMLElement).closest(
      ".react-flow__node"
    );
    if (nodeElement) {
      setSelectedNodeId(nodeElement.getAttribute("data-id"));
    } else {
      setSelectedNodeId(null);
    }
  }, []);

  return (
    <ContextMenu>
      <ContextMenuTrigger onContextMenu={handleContextMenu}>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        {selectedNodeId ? (
          // Show delete option if a node is selected
          <ContextMenuItem
            className="flex items-center cursor-pointer hover:bg-red-50"
            onClick={() => onDeleteNode(selectedNodeId)}
          >
            <span className="text-red-600">Delete Node</span>
          </ContextMenuItem>
        ) : (
          // Show add node options if no node is selected
          <>
            <ContextMenuItem
              onClick={(e) => onAddNode("start", e as React.MouseEvent)}
            >
              Add Start Node
            </ContextMenuItem>
            <ContextMenuItem
              onClick={(e) => onAddNode("action", e as React.MouseEvent)}
            >
              Add Action Node
            </ContextMenuItem>
            {/* Add other node types as needed */}
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
}
