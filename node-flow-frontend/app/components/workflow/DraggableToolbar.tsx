"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { NodeToolbar } from "./NodeToolbar";
import { NodeType } from "@/types/nodes";

interface DraggableToolbarProps {
  onAddNode: (type: NodeType) => void;
  onConfigClick: () => void;
  onAutoLayoutClick?: () => void;
}

type Edge = "left" | "top" | "right" | "bottom";

interface Position {
  x: number;
  y: number;
  edge: Edge;
}

export function DraggableToolbar({
  onAddNode,
  onConfigClick,
  onAutoLayoutClick,
}: DraggableToolbarProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState<Position>({
    x: 10,
    y: 10,
    edge: "left",
  });
  const toolbarRef = useRef<HTMLDivElement>(null);
  const dragStartRef = useRef<{
    x: number;
    y: number;
    startPos: Position;
  } | null>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    dragStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      startPos: { ...position },
    };
  };

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !dragStartRef.current) return;

      const reactFlowBounds = document
        .querySelector(".react-flow")
        ?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const toolbarWidth = toolbarRef.current?.offsetWidth || 0;
      const toolbarHeight = toolbarRef.current?.offsetHeight || 0;

      // Calculate distances to each edge
      const distanceToLeft = e.clientX - reactFlowBounds.left;
      const distanceToRight = reactFlowBounds.right - e.clientX;
      const distanceToTop = e.clientY - reactFlowBounds.top;
      const distanceToBottom = reactFlowBounds.bottom - e.clientY;

      // Find the nearest edge
      const distances = [
        { edge: "left" as Edge, distance: distanceToLeft },
        { edge: "right" as Edge, distance: distanceToRight },
        { edge: "top" as Edge, distance: distanceToTop },
        { edge: "bottom" as Edge, distance: distanceToBottom },
      ];

      const nearestEdge = distances.reduce((prev, curr) =>
        curr.distance < prev.distance ? curr : prev
      );

      let newX = dragStartRef.current.startPos.x;
      let newY = dragStartRef.current.startPos.y;

      // Calculate position based on nearest edge
      switch (nearestEdge.edge) {
        case "left":
          newX = 10;
          newY = Math.max(
            10,
            Math.min(
              reactFlowBounds.height - toolbarHeight - 10,
              e.clientY - reactFlowBounds.top
            )
          );
          break;
        case "right":
          newX = reactFlowBounds.width - toolbarWidth - 10;
          newY = Math.max(
            10,
            Math.min(
              reactFlowBounds.height - toolbarHeight - 10,
              e.clientY - reactFlowBounds.top
            )
          );
          break;
        case "top":
          newX = Math.max(
            10,
            Math.min(
              reactFlowBounds.width - toolbarWidth - 10,
              e.clientX - reactFlowBounds.left
            )
          );
          newY = 10;
          break;
        case "bottom":
          newX = Math.max(
            10,
            Math.min(
              reactFlowBounds.width - toolbarWidth - 10,
              e.clientX - reactFlowBounds.left
            )
          );
          newY = reactFlowBounds.height - toolbarHeight - 10;
          break;
      }

      setPosition({ x: newX, y: newY, edge: nearestEdge.edge });
    },
    [isDragging]
  );

  const handleMouseUp = () => {
    setIsDragging(false);
    dragStartRef.current = null;
  };

  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
    }
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, handleMouseMove]);

  const isVertical = position.edge === "left" || position.edge === "right";

  return (
    <div
      ref={toolbarRef}
      className={`select-none ${
        isDragging ? "cursor-grabbing" : "cursor-grab active:cursor-grabbing"
      }`}
      style={{
        position: "absolute",
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: "translate(0, 0)",
        transition: isDragging ? "none" : "all 0.2s ease",
        zIndex: 9999, // Ensure it's always on top
      }}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
    >
      <div
        className={`bg-white rounded-lg shadow-lg ${
          isVertical ? "flex flex-col" : "flex flex-row"
        }`}
      >
        <NodeToolbar
          onAddNode={(nodeType: string) => onAddNode(nodeType as NodeType)}
          onNodeAdd={(nodeType: string) => onAddNode(nodeType as NodeType)}
          onConfigClick={onConfigClick}
          onAutoLayoutClick={onAutoLayoutClick}
          orientation={isVertical ? "vertical" : "horizontal"}
        />
      </div>
    </div>
  );
}
