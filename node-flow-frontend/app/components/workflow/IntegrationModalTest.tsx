'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { IntegrationConnectModal } from './IntegrationConnectModal';

// Sample integration for testing
const sampleIntegration = {
  id: 'openai',
  name: 'OpenAI',
  description: 'Connect to OpenAI for AI capabilities',
  icon: 'openai',
  category: 'ai',
  authType: 'api_key',
  status: 'not_connected' as const
};

export function IntegrationModalTest() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastConnectedIntegration, setLastConnectedIntegration] = useState<string | null>(null);
  
  const handleConnect = (integrationId: string, credentials: Record<string, string>) => {
    console.log(`Connected to ${integrationId} with credentials:`, credentials);
    setLastConnectedIntegration(integrationId);
    setIsModalOpen(false);
  };
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Integration Modal Test</h1>
      
      <Button 
        onClick={() => setIsModalOpen(true)}
        className="text-blue-600 border-blue-200 hover:bg-blue-50"
        variant="outline"
      >
        Open OpenAI Connection Modal
      </Button>
      
      {lastConnectedIntegration && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-800">
            Successfully connected to integration: <strong>{lastConnectedIntegration}</strong>
          </p>
        </div>
      )}
      
      <IntegrationConnectModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        integration={sampleIntegration}
        onConnect={handleConnect}
      />
    </div>
  );
}
