'use client';

import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { LogEntry } from '@/types/nodes';

interface LogWindowProps {
  logs: LogEntry[];
  onClose: () => void;
}

export function LogWindow({ logs, onClose }: LogWindowProps) {
  const getIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white rounded-lg shadow-lg border border-gray-200">
      <div className="flex justify-between items-center p-4 border-b">
        <h3 className="font-semibold">Execution Logs</h3>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          ×
        </button>
      </div>
      <ScrollArea className="h-96 p-4">
        {logs.map((log, index) => (
          <div key={index} className="mb-4">
            <div className="flex items-center gap-2">
              {getIcon(log.type)}
              <span className="text-sm text-gray-500">
                {new Date(log.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <p className="ml-6 text-sm mt-1">{log.message}</p>
          </div>
        ))}
      </ScrollArea>
    </div>
  );
} 