"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import React<PERSON>low, {
  Background,
  BackgroundVariant,
  Controls,
  Edge,
  Connection,
  addEdge,
  Node,
  NodeChange,
  EdgeChange,
  applyNodeChanges,
  applyEdgeChanges,
  ReactFlowProvider,
  useReactFlow,
} from "reactflow";
import "reactflow/dist/style.css";

import { NodeFactory } from "@/components/nodes/NodeFactory";
import { NodeToolbar } from "./NodeToolbar";
import { ConfigModal } from "./ConfigModal";
import { EdgeContextMenu } from "./EdgeContextMenu";
import { NodeContextMenu } from "./NodeContextMenu";
import { SaveFlowModal } from "./SaveFlowModal";
import { LoadFlowModal } from "./LoadFlowModal";
import { getAllFlows, getFlowById, saveFlow } from "@/lib/flows";

import { BaseNode, Terminal, NodeType } from "@/types/nodes";
import { FlowConfig } from "@/lib/flows";

const nodeTypesConfig = {
  start: NodeFactory,
  event: NodeFactory,
  action: NodeFactory,
  integration: NodeFactory,
  decision: NodeFactory,
  approval: NodeFactory,
  dataInput: NodeFactory,
  dataOutput: NodeFactory,
  dataStore: NodeFactory,
  fork: NodeFactory,
  join: NodeFactory,
  end: NodeFactory,
  fallback: NodeFactory,
  humanTask: NodeFactory,
  notification: NodeFactory,
  audit: NodeFactory,
  tools: NodeFactory,
} as const;

// Move the Flow component and hook outside of Canvas
function Flow({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onEdgesDelete,
  onEdgeContextMenu,
  onNodeAdd,
  onConfigClick,
  onSaveClick,
  onLoadClick,
  onNodesDelete,
  onAutoLayout,
}: {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect: (connection: Connection) => void;
  onEdgesDelete: (edges: Edge[]) => void;
  onEdgeContextMenu: (event: React.MouseEvent, edge: Edge) => void;
  onNodeAdd: (nodeType: string) => void;
  onConfigClick: () => void;
  onSaveClick: () => void;
  onLoadClick: () => void;
  onNodesDelete: (nodes: Node[]) => void;
  onAutoLayout: () => void;
}) {
  const { fitView, getViewport } = useReactFlow();
  const [zoomLevel, setZoomLevel] = useState(0.8);
  const ZOOM_OFFSET = 0.8; // 80% is the new 100%

  // Handle viewport changes to update zoom level
  const onMove = useCallback((_: unknown, viewport: { zoom: number }) => {
    setZoomLevel(parseFloat(viewport.zoom.toFixed(2)));
  }, []);

  // Calculate displayed zoom level (0.8 = 100%)
  const displayZoomLevel = useMemo(() => {
    return zoomLevel / ZOOM_OFFSET;
  }, [zoomLevel]);

  // Set initial viewport on mount
  useEffect(() => {
    // Don't use fitView as it will override our zoom settings
    // Just expose the functions through the global instance
    window.flowInstance = { fitView, getViewport };

    return () => {
      delete window.flowInstance;
    };
  }, [fitView, getViewport]);

  // Note: getConnectionState is not being used, but keeping for future use
  // const getConnectionState = useCallback(
  //   (nodeId: string, terminalId: string, type: "input" | "output") => {
  //     return edges.some((edge) =>
  //       type === "input"
  //         ? edge.targetHandle === terminalId
  //         : edge.sourceHandle === terminalId
  //     );
  //   },
  //   [edges]
  // );

  return (
    <div className="w-full h-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onEdgesDelete={onEdgesDelete}
        onEdgeContextMenu={onEdgeContextMenu}
        nodeTypes={nodeTypesConfig}
        defaultEdgeOptions={{
          type: "smoothstep",
          animated: true,
          style: {
            stroke: "#22c55e",
            strokeWidth: 1,
            opacity: 0.8,
          },
        }}
        fitView={false}
        preventScrolling={true}
        panOnScroll={true}
        zoomOnScroll={false}
        minZoom={0.4} // 0.5 * 0.8
        maxZoom={1.6} // 2 * 0.8
        defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
        style={{ width: "100%", height: "100%" }}
        className="bg-white reactflow-wrapper"
        proOptions={{ hideAttribution: true }}
        panOnDrag={[0, 2]}
        zoomOnPinch={true}
        selectNodesOnDrag={false}
        onNodeClick={(_, node) => {
          // Handle node updates here
          console.log("Node clicked:", node);
        }}
        onNodesDelete={onNodesDelete}
        onMove={onMove}
      >
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color="#e5e7eb"
          className="bg-white"
          style={{
            backgroundColor: "white",
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        />
        <Controls />
        <div className="absolute bottom-4 right-4 bg-white/80 backdrop-blur-sm rounded-md px-3 py-1.5 text-sm text-gray-600 shadow-sm border border-gray-200 z-10">
          {Math.round(displayZoomLevel * 100)}%
        </div>
        <NodeToolbar
          onNodeAdd={onNodeAdd}
          onConfigClick={onConfigClick}
          onSaveClick={onSaveClick}
          onLoadClick={onLoadClick}
          onAutoLayoutClick={onAutoLayout}
        />
      </ReactFlow>
    </div>
  );
}

// Declare the window.flowInstance type
declare global {
  interface Window {
    flowInstance?: {
      fitView: (options?: { padding?: number }) => void;
      getViewport: () => { x: number; y: number; zoom: number };
    };
  }
}

export function Canvas() {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [flows, setFlows] = useState<FlowConfig[]>([]);
  // Note: logs is not being used, but keeping for future use
  // const [logs] = useState<unknown[]>([]);
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [config, setConfig] = useState<{
    prompt: string;
    transcript: string;
    config: string;
  }>({
    prompt: "",
    transcript: "",
    config: "",
  });
  const [edgeContextMenu, setEdgeContextMenu] = useState<{
    x: number;
    y: number;
    edge: Edge;
  } | null>(null);
  // Note: connectedTerminals is not being used, but keeping for future use
  // const [connectedTerminals] = useState<Set<string>>(new Set());
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [isLoadModalOpen, setIsLoadModalOpen] = useState(false);

  // Note: nodeTypes is not being used, but keeping for future use
  // const nodeTypes = useMemo(() => nodeTypesConfig, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setFlows(getAllFlows());
    }
  }, []);

  const onNodesChange = useCallback(
    (changes: NodeChange[]) =>
      setNodes((nds) => applyNodeChanges(changes, nds)),
    []
  );

  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((eds) => applyEdgeChanges(changes, eds)),
    []
  );

  const onConnect = useCallback(
    (connection: Connection) => {
      if (!connection.source || !connection.target) return;

      // Find the target node to check its type
      const targetNode = nodes.find((node) => node.id === connection.target);

      // Prevent connections to data nodes and tool nodes
      if (
        targetNode &&
        targetNode.type &&
        ["dataInput", "dataOutput", "dataStore", "tools"].includes(
          targetNode.type
        )
      ) {
        console.log(
          `Cannot connect to ${targetNode.type} node as they are output-only`
        );
        return;
      }

      // Note: connectedTerminals is not being used, but keeping the logic for future use
      // setConnectedTerminals((prev) => {
      //   const newSet = new Set(prev);
      //   if (connection.sourceHandle) newSet.add(connection.sourceHandle);
      //   if (connection.targetHandle) newSet.add(connection.targetHandle);
      //   return newSet;
      // });

      setEdges((eds) =>
        addEdge(
          {
            ...connection,
            type: "smoothstep",
            animated: true,
            style: { stroke: "#22c55e", strokeWidth: 2 },
            className: "animated",
          },
          eds
        )
      );
    },
    [nodes]
  );

  const onAddNode = useCallback(
    (type: NodeType, position?: { x: number; y: number }) => {
      const reactFlowInstance = window.flowInstance;
      let nodePosition = { x: 0, y: 0 };

      if (position) {
        // For right-click context menu - use the clicked position
        nodePosition = position;
      } else {
        // For toolbar clicks - use the center of the current viewport
        if (reactFlowInstance) {
          const { x, y, zoom } = reactFlowInstance.getViewport();
          const centerX = -x / zoom + window.innerWidth / (2 * zoom);
          const centerY = -y / zoom + window.innerHeight / (2 * zoom);
          nodePosition = { x: centerX, y: centerY };
        }
      }

      // Create node data based on type
      let nodeData = {
        label: `${type} Node`,
        description: "",
        inputs:
          type === "start"
            ? []
            : [
                {
                  id: `${type}-${nodes.length + 1}-input-1`,
                  type: "input" as const,
                  name: "Input",
                  position: "left" as const,
                },
              ],
        outputs:
          type === "end"
            ? []
            : [
                {
                  id: `${type}-${nodes.length + 1}-output-1`,
                  type: "output" as const,
                  name: "Output",
                  position: "right" as const,
                },
              ],
      };

      if (type === "tools") {
        nodeData = {
          label: "Tools Node",
          description: "",
          inputs: [
            {
              id: `${type}-${nodes.length + 1}-input-1`,
              type: "input" as const,
              name: "Input",
              position: "left" as const,
            },
          ],
          outputs: [
            {
              id: `${type}-${nodes.length + 1}-output-1`,
              type: "output" as const,
              name: "Output",
              position: "right" as const,
            },
          ],
        };
      }

      const newNode = {
        id: `${type}-${nodes.length + 1}`,
        type,
        position: nodePosition,
        data: nodeData,
      };

      setNodes((nds) => [...nds, newNode]);
    },
    [nodes]
  );

  const onEdgeContextMenu = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      event.preventDefault();
      setEdgeContextMenu({
        x: event.clientX,
        y: event.clientY,
        edge,
      });
    },
    []
  );

  const updateEdgeLabel = useCallback((edgeId: string, newLabel: string) => {
    setEdges((eds) =>
      eds.map((edge) =>
        edge.id === edgeId ? { ...edge, label: newLabel } : edge
      )
    );
  }, []);

  const deleteEdge = useCallback((edgeId: string) => {
    setEdges((eds) => eds.filter((edge) => edge.id !== edgeId));
  }, []);

  const calculateNodePositions = useCallback(
    (nodes: BaseNode[], connections: { from: string; to: string }[]) => {
      const HORIZONTAL_SPACING = 300;
      const VERTICAL_SPACING = 180;
      const NODE_WIDTH = 250;
      const NODE_HEIGHT = 100;
      const MAX_NODES_PER_ROW = 3;

      // Create a map for levels (columns) and their nodes
      const levels = new Map<number, { id: string; level: number }[]>();
      const nodeLevels = new Map<string, number>();

      // First pass: Assign levels using BFS
      const startNodes = nodes.filter(
        (node) => !connections.some((conn) => conn.to === node.id)
      );

      const queue = startNodes.map((node) => ({ id: node.id, level: 0 }));
      const visited = new Set<string>();

      queue.forEach((node) => nodeLevels.set(node.id, 0));

      while (queue.length > 0) {
        const currentNode = queue.shift()!;
        const currentLevel = nodeLevels.get(currentNode.id)!;

        if (!levels.has(currentLevel)) {
          levels.set(currentLevel, []);
        }
        levels.get(currentLevel)!.push(currentNode);

        // Find children
        const childConnections = connections.filter(
          (conn) => conn.from === currentNode.id
        );
        for (const conn of childConnections) {
          const childNode = nodes.find((n) => n.id === conn.to);
          if (childNode && !visited.has(childNode.id)) {
            visited.add(childNode.id);
            nodeLevels.set(childNode.id, currentLevel + 1);
            queue.push({ id: childNode.id, level: currentLevel + 1 });
          }
        }
      }

      // Create connection maps for quick lookup
      const outgoingConnections: { [key: string]: string[] } = {};
      const incomingConnections: { [key: string]: string[] } = {};

      nodes.forEach((node) => {
        outgoingConnections[node.id] = connections
          .filter((conn) => conn.from === node.id)
          .map((conn) => conn.to);

        incomingConnections[node.id] = connections
          .filter((conn) => conn.to === node.id)
          .map((conn) => conn.from);
      });

      // Group nodes by level
      const nodesByLevel: string[][] = [];
      for (let i = 0; i < levels.size; i++) {
        nodesByLevel[i] = (levels.get(i) || []).map((n) => n.id);
      }

      // Create a more structured layout with snake pattern
      // but prioritize keeping connected nodes aligned
      const rows: string[][] = [];
      let currentRow: string[] = [];
      let currentRowWidth = 0;

      // Process nodes level by level
      for (let level = 0; level < nodesByLevel.length; level++) {
        const nodesInLevel = nodesByLevel[level];

        // If this level would make the row too wide, start a new row
        if (
          currentRowWidth + nodesInLevel.length > MAX_NODES_PER_ROW &&
          currentRowWidth > 0
        ) {
          rows.push([...currentRow]);
          currentRow = [];
          currentRowWidth = 0;
        }

        // Add all nodes from this level to the current row
        nodesInLevel.forEach((nodeId) => {
          currentRow.push(nodeId);
        });

        currentRowWidth += nodesInLevel.length;
      }

      // Add any remaining nodes
      if (currentRow.length > 0) {
        rows.push(currentRow);
      }

      // Position nodes based on their row and column
      const nodePositions: { [key: string]: { x: number; y: number } } = {};

      rows.forEach((row, rowIdx) => {
        // For even rows, sort left to right by level
        // For odd rows, sort right to left by level
        const isEvenRow = rowIdx % 2 === 0;

        // Sort nodes within the row by their level
        row.sort((a, b) => {
          const levelA = nodeLevels.get(a) || 0;
          const levelB = nodeLevels.get(b) || 0;

          // Primary sort by level
          if (levelA !== levelB) {
            return isEvenRow ? levelA - levelB : levelB - levelA;
          }

          // Secondary sort: try to minimize edge crossings by considering connections
          const aIncoming = incomingConnections[a] || [];
          const bIncoming = incomingConnections[b] || [];

          // If both have incoming connections, try to position them to minimize crossings
          if (aIncoming.length > 0 && bIncoming.length > 0) {
            // Calculate average position of incoming nodes
            const aAvgPos =
              aIncoming.reduce((sum, id) => {
                const pos = nodePositions[id];
                return pos ? sum + pos.x : sum;
              }, 0) / (aIncoming.length || 1);

            const bAvgPos =
              bIncoming.reduce((sum, id) => {
                const pos = nodePositions[id];
                return pos ? sum + pos.x : sum;
              }, 0) / (bIncoming.length || 1);

            return isEvenRow ? aAvgPos - bAvgPos : bAvgPos - aAvgPos;
          }

          return 0;
        });

        // Position nodes in the row
        row.forEach((nodeId, colIdx) => {
          const x = isEvenRow
            ? colIdx * (NODE_WIDTH + HORIZONTAL_SPACING)
            : (row.length - 1 - colIdx) * (NODE_WIDTH + HORIZONTAL_SPACING);

          const y = rowIdx * (NODE_HEIGHT + VERTICAL_SPACING);

          nodePositions[nodeId] = { x, y };
        });
      });

      // Apply positions to nodes
      return nodes.map((node) => {
        const position = nodePositions[node.id] || { x: 0, y: 0 };

        return {
          ...node,
          position,
        };
      });
    },
    []
  );

  const handleConfigSave = useCallback(
    (newConfig: { prompt: string; transcript: string; config: string }) => {
      setConfig((prev) => ({
        ...prev,
        ...newConfig,
        prompt: newConfig.prompt || prev.prompt,
      }));
      if (newConfig.config) {
        try {
          const workflowConfig = JSON.parse(newConfig.config);
          const positionedNodes = calculateNodePositions(
            workflowConfig.nodes,
            workflowConfig.connections
          );

          const newNodes = positionedNodes.map((node) => ({
            id: node.id,
            type: node.type,
            position: node.position,
            data: {
              label: node.data?.label || "Unnamed Node",
              description: node.data?.description || "",
              owner: node.data?.owner || "",
              action: node.data?.action || "",
              conditions: node.data?.conditions || [],
              validation: node.data?.validation || {
                requiredFields: [],
                fieldTypes: {},
              },
              inputs:
                node.type === "start"
                  ? []
                  : [
                      {
                        id: `${node.id}-input-1`,
                        type: "input" as const,
                        name: "Input",
                        position: "left" as const,
                      },
                    ],
              outputs:
                node.type === "end"
                  ? []
                  : [
                      {
                        id: `${node.id}-output-1`,
                        type: "output" as const,
                        name: "Output",
                        position: "right" as const,
                      },
                    ],
            },
          }));

          const newEdges = workflowConfig.connections.map(
            (
              conn: { from: string; to: string; label?: string },
              index: number
            ) => ({
              id: `edge-${index}`,
              source: conn.from,
              target: conn.to,
              sourceHandle: `${conn.from}-output-1`,
              targetHandle: `${conn.to}-input-1`,
              type: "smoothstep",
              animated: true,
              style: { stroke: "#22c55e", strokeWidth: 2 },
              label: conn.label || "sequence",
            })
          );

          const nodesWithConnections = newNodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              inputs:
                node.data.inputs?.map(
                  (input: Terminal & { isConnected?: boolean }) => ({
                    ...input,
                    isConnected: newEdges.some(
                      (edge: Edge) => edge.targetHandle === input.id
                    ),
                  })
                ) || [],
              outputs:
                node.data.outputs?.map(
                  (output: Terminal & { isConnected?: boolean }) => ({
                    ...output,
                    isConnected: newEdges.some(
                      (edge: Edge) => edge.sourceHandle === output.id
                    ),
                  })
                ) || [],
            },
          }));

          setNodes(nodesWithConnections);
          setEdges(newEdges);
        } catch (error) {
          console.error("Failed to process config:", error);
        }
      }
    },
    [calculateNodePositions]
  );

  const onEdgesDelete = useCallback((edgesToDelete: Edge[]) => {
    // Note: connectedTerminals is not being used, but keeping the logic for future use
    // setConnectedTerminals((prev) => {
    //   const newSet = new Set(prev);
    //   edgesToDelete.forEach((edge) => {
    //     if (edge.sourceHandle) newSet.delete(edge.sourceHandle);
    //     if (edge.targetHandle) newSet.delete(edge.targetHandle);
    //   });
    //   return newSet;
    // });

    setEdges((eds) => eds.filter((edge) => !edgesToDelete.includes(edge)));
  }, []);

  // Note: handleAddNode is not being used, but keeping for future use
  // const handleAddNode = useCallback(
  //   (nodeType: string) => {
  //     onAddNode(nodeType as NodeType, { x: 100, y: 100 });
  //   },
  //   [onAddNode]
  // );

  const handleSaveFlow = (name: string, flowId?: string) => {
    const flowConfig = {
      id: flowId || `flow-${Date.now()}`,
      name,
      description: "User created flow",
      config: {
        nodes: nodes.map((node) => {
          console.log("Saving node data:", node.data); // Debug log
          return {
            id: node.id,
            type: node.type,
            position: { x: node.position.x, y: node.position.y },
            data: {
              ...node.data, // Spread all existing data
              label: node.data.label,
              description: node.data.description,
              owner: node.data.owner,
              action: node.data.action,
              context: node.data.context,
              expectedBehavior: node.data.expectedBehavior,
              conditions: node.data.conditions,
              validation: node.data.validation,
              inputs: node.data.inputs,
              outputs: node.data.outputs,
              parameters: node.data.parameters,
            },
          };
        }),
        connections: edges.map((edge) => ({
          from: edge.source,
          to: edge.target,
          type: edge.type || "sequence",
          label: edge.label || "sequence",
        })),
      },
      createdAt: flowId
        ? getFlowById(flowId)?.createdAt || new Date().toISOString()
        : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    console.log("Saving complete flow config:", flowConfig); // Debug log
    saveFlow(flowConfig);
    setFlows(getAllFlows());
  };

  // Use the custom hook
  const handleLoadFlow = useCallback(
    (flowId: string) => {
      const flow = getFlowById(flowId);
      console.log("Loading flow config:", flow); // Debug log

      if (flow && flow.config) {
        try {
          const loadedNodes =
            flow.config.nodes?.map((node) => {
              const nodeData = (node.data as Record<string, unknown>) || {};
              return {
                id: node.id as string,
                type: node.type as string,
                position: node.position as { x: number; y: number },
                data: {
                  ...nodeData,
                  label: nodeData.label || "Unnamed Node",
                  description: nodeData.description || "",
                  owner: nodeData.owner || "",
                  action: nodeData.action || "",
                  context: nodeData.context || "",
                  expectedBehavior: nodeData.expectedBehavior || "",
                  conditions: nodeData.conditions || [],
                  validation: nodeData.validation || {
                    requiredFields: [],
                    fieldTypes: {},
                  },
                  inputs:
                    node.type === "start"
                      ? []
                      : [
                          {
                            id: `${node.id}-input-1`,
                            type: "input" as const,
                            name: "Input",
                            position: "left" as const,
                            isConnected: false,
                          },
                        ],
                  outputs:
                    node.type === "end"
                      ? []
                      : [
                          {
                            id: `${node.id}-output-1`,
                            type: "output" as const,
                            name: "Output",
                            position: "right" as const,
                            isConnected: false,
                          },
                        ],
                  parameters: nodeData.parameters || {},
                },
              };
            }) || [];

          console.log("Processed nodes for loading:", loadedNodes); // Debug log

          const loadedEdges =
            flow.config.connections?.map((conn, index) => {
              const connection = conn as Record<string, unknown>;
              return {
                id: `edge-${index}`,
                source: connection.from as string,
                target: connection.to as string,
                sourceHandle: `${connection.from}-output-1`,
                targetHandle: `${connection.to}-input-1`,
                type: "smoothstep",
                animated: true,
                style: { stroke: "#22c55e", strokeWidth: 2 },
                label: (connection.label as string) || "sequence",
              };
            }) || [];

          setNodes(loadedNodes);
          setEdges(loadedEdges);

          setTimeout(() => {
            window.flowInstance?.fitView({ padding: 0.2 });
          }, 100);
        } catch (error) {
          console.error("Error processing flow data:", error);
        }
      }
    },
    [setNodes, setEdges]
  );

  const handleDeleteFlow = (flowId: string) => {
    if (typeof window === "undefined") return;

    try {
      const savedFlows = JSON.parse(localStorage.getItem("flows") || "{}");
      delete savedFlows[flowId];
      localStorage.setItem("flows", JSON.stringify(savedFlows));
      setFlows(getAllFlows());
    } catch (error) {
      console.error("Error deleting flow:", error);
    }
  };

  const getCurrentFlowConfig = useCallback(() => {
    return {
      nodes: nodes.map((node) => ({
        id: node.id,
        type: node.type as NodeType,
        position: { x: node.position.x, y: node.position.y },
        data: {
          label: node.data.label,
          description: node.data.description,
          owner: node.data.owner,
          action: node.data.action,
          conditions: node.data.conditions,
          validation: node.data.validation,
          context: node.data.context,
          expectedBehavior: node.data.expectedBehavior,
          inputs: node.data.inputs,
          outputs: node.data.outputs,
          parameters: node.data.parameters,
        },
      })) as BaseNode[],
      connections: edges.map((edge) => ({
        from: edge.source,
        to: edge.target,
        type: edge.type || "sequence",
        label: edge.label || "sequence",
      })),
    };
  }, [nodes, edges]);

  const onNodesDelete = useCallback((nodesToDelete: Node[]) => {
    // First remove any connected edges
    const nodeIds = new Set(nodesToDelete.map((n) => n.id));
    setEdges((eds) =>
      eds.filter(
        (edge) => !nodeIds.has(edge.source) && !nodeIds.has(edge.target)
      )
    );

    // Then remove the nodes
    setNodes((nds) => nds.filter((node) => !nodeIds.has(node.id)));
  }, []);

  const handleAutoLayout = useCallback(() => {
    const currentConfig = getCurrentFlowConfig();
    const layoutedNodes = calculateNodePositions(
      currentConfig.nodes,
      currentConfig.connections
    );

    // Apply the new positions to the nodes
    setNodes((nds) =>
      nds.map((node) => {
        const layoutNode = layoutedNodes.find((n) => n.id === node.id);
        if (layoutNode) {
          return {
            ...node,
            position: layoutNode.position,
          };
        }
        return node;
      })
    );

    // Fit the view to show all nodes after layout
    setTimeout(() => {
      window.flowInstance?.fitView({ padding: 0.2 });
    }, 100);
  }, [calculateNodePositions, getCurrentFlowConfig]);

  return (
    <div className="relative w-full h-full overflow-hidden">
      <ReactFlowProvider>
        <NodeContextMenu
          onAddNode={(type, event) => {
            if (event) {
              onAddNode(type as NodeType, {
                x: event.clientX,
                y: event.clientY,
              });
            } else {
              onAddNode(type as NodeType);
            }
          }}
          onDeleteNode={(nodeId) => {
            const nodeToDelete = nodes.find((n) => n.id === nodeId);
            if (nodeToDelete) {
              onNodesDelete([nodeToDelete]);
            }
          }}
        >
          <Flow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onEdgesDelete={onEdgesDelete}
            onEdgeContextMenu={onEdgeContextMenu}
            onNodeAdd={(type) => onAddNode(type as NodeType)}
            onConfigClick={() => setIsConfigOpen(true)}
            onSaveClick={() => setIsSaveModalOpen(true)}
            onLoadClick={() => setIsLoadModalOpen(true)}
            onNodesDelete={onNodesDelete}
            onAutoLayout={handleAutoLayout}
          />
        </NodeContextMenu>
      </ReactFlowProvider>
      {edgeContextMenu && (
        <EdgeContextMenu
          x={edgeContextMenu.x}
          y={edgeContextMenu.y}
          edge={edgeContextMenu.edge}
          onClose={() => setEdgeContextMenu(null)}
          onRename={updateEdgeLabel}
          onDelete={deleteEdge}
        />
      )}
      <ConfigModal
        isOpen={isConfigOpen}
        onClose={() => setIsConfigOpen(false)}
        onSave={handleConfigSave}
        initialConfig={config}
        currentFlow={getCurrentFlowConfig()}
      />
      <SaveFlowModal
        isOpen={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        onSave={handleSaveFlow}
        existingFlows={flows}
      />
      <LoadFlowModal
        isOpen={isLoadModalOpen}
        onClose={() => setIsLoadModalOpen(false)}
        onLoad={handleLoadFlow}
        onDelete={handleDeleteFlow}
        flows={flows}
      />
    </div>
  );
}
