'use client';

import React, { useState } from 'react';
import { Di<PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FlowConfig } from '@/lib/flows';
import { Trash2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface LoadFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (flowId: string) => void;
  onDelete: (flowId: string) => void;
  flows: FlowConfig[];
}

export function LoadFlowModal({ isOpen, onClose, onLoad, onDelete, flows }: LoadFlowModalProps) {
  const [flowToDelete, setFlowToDelete] = useState<string | null>(null);

  const handleDelete = (flowId: string) => {
    onDelete(flowId);
    setFlowToDelete(null);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
            <DialogTitle>Load Flow Configuration</DialogTitle>
            <p id="load-flow-description" className="text-sm text-gray-500">
              Select a flow configuration to load or delete.
            </p>
          </DialogHeader>
          <ScrollArea className="h-[300px] pr-4">
            <div className="space-y-2">
              {flows?.map((flow) => (
                <div key={flow.id} className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    className="flex-1 justify-start"
                    onClick={() => {
                      if (flow.id) {
                        onLoad(flow.id);
                        onClose();
                      }
                    }}
                  >
                    <div className="flex flex-col items-start">
                      <span className="font-medium">{flow.name}</span>
                      <span className="text-xs text-gray-500">
                        Last modified: {new Date(flow.updatedAt).toLocaleString()}
                      </span>
                    </div>
                  </Button>
                  {flow.id !== 'example-flow' && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="shrink-0 text-red-500 hover:text-red-600 hover:bg-red-50"
                      onClick={() => setFlowToDelete(flow.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      <AlertDialog 
        open={!!flowToDelete} 
        onOpenChange={(open) => !open && setFlowToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the flow configuration.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-500 hover:bg-red-600"
              onClick={() => flowToDelete && handleDelete(flowToDelete)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 