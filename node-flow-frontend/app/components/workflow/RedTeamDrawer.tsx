'use client';

import React from 'react';
import { Drawer } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';

interface RedTeamDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function RedTeamDrawer({ isOpen, onClose }: RedTeamDrawerProps) {
  return (
    <Drawer isOpen={isOpen} onClose={onClose} side="right" width="40%" topOffset="64px">
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center border-b pb-4 mb-6">
          <h2 className="text-2xl font-bold text-red-600">REDTEAM</h2>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onClose} 
            className="h-8 px-3 rounded-md flex items-center gap-1 border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 6 6 18" /><path d="m6 6 12 12" />
            </svg>
            <span>Close</span>
          </Button>
        </div>
        
        <div className="flex-1 overflow-y-auto pr-2">
          <div className="space-y-6">
            <section>
              <h3 className="text-lg font-semibold mb-3 text-red-500">Security Assessment</h3>
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">Run Scan</Button>
                  <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">Generate Report</Button>
                </div>
              </div>
            </section>
            
            <section>
              <h3 className="text-lg font-semibold mb-3 text-red-500">Vulnerability Status</h3>
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Critical:</span>
                    <span className="font-medium text-red-600">3</span>
                  </div>
                  <div className="flex justify-between">
                    <span>High:</span>
                    <span className="font-medium text-orange-500">7</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Medium:</span>
                    <span className="font-medium text-yellow-500">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Low:</span>
                    <span className="font-medium text-green-500">5</span>
                  </div>
                </div>
              </div>
            </section>
            
            <section>
              <h3 className="text-lg font-semibold mb-3 text-red-500">Recent Alerts</h3>
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <ul className="space-y-2">
                  <li className="pb-2 border-b border-red-200 dark:border-red-800">
                    <div className="text-sm font-medium text-red-600">Unauthorized access attempt detected</div>
                    <div className="text-xs text-red-500">10 minutes ago</div>
                  </li>
                  <li className="pb-2 border-b border-red-200 dark:border-red-800">
                    <div className="text-sm font-medium text-red-600">Suspicious data flow pattern identified</div>
                    <div className="text-xs text-red-500">25 minutes ago</div>
                  </li>
                  <li className="pb-2 border-b border-red-200 dark:border-red-800">
                    <div className="text-sm font-medium text-red-600">Input validation failure in Node #12</div>
                    <div className="text-xs text-red-500">45 minutes ago</div>
                  </li>
                  <li>
                    <div className="text-sm font-medium text-red-600">Security scan completed</div>
                    <div className="text-xs text-red-500">1 hour ago</div>
                  </li>
                </ul>
              </div>
            </section>
            
            <section>
              <h3 className="text-lg font-semibold mb-3 text-red-500">Security Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">Penetration Test</Button>
                <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">Threat Model</Button>
                <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">Fix Vulnerabilities</Button>
                <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">Security Report</Button>
              </div>
            </section>
          </div>
        </div>
      </div>
    </Drawer>
  );
}
