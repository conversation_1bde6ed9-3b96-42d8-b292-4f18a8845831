'use client';

import React, { useState, useMemo } from 'react';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Search, FileText, FileImage, FileSpreadsheet, File, 
  FolderOpen, Plus, Upload, MoreHorizontal, ChevronDown, ChevronRight, Folder 
} from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface ResourcesDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

type FileType = 'document' | 'image' | 'spreadsheet' | 'other';

interface Resource {
  id: string;
  name: string;
  description: string;
  type: 'file' | 'folder';
  path: string;
  parentId: string | null;
  modified: string;
  source: string;
  fileType?: FileType;
  size?: string;
}

// Mock data for resources
const mockResources: Resource[] = [
  {
    id: 'folder-1',
    name: 'Documents',
    description: 'Project documentation',
    type: 'folder',
    path: '/Documents',
    parentId: null,
    modified: '2023-09-15',
    source: 'Local'
  },
  {
    id: 'folder-2',
    name: 'Images',
    description: 'Project images and assets',
    type: 'folder',
    path: '/Images',
    parentId: null,
    modified: '2023-09-10',
    source: 'Local'
  },
  {
    id: 'folder-3',
    name: 'Reports',
    description: 'Financial and project reports',
    type: 'folder',
    path: '/Documents/Reports',
    parentId: 'folder-1',
    modified: '2023-09-12',
    source: 'Google Drive'
  },
  {
    id: 'file-1',
    name: 'Annual Report 2023.docx',
    description: 'Annual financial report for 2023',
    type: 'file',
    fileType: 'document',
    path: '/Documents/Reports/Annual Report 2023.docx',
    parentId: 'folder-3',
    size: '2.4 MB',
    modified: '2023-09-05',
    source: 'Google Drive'
  },
  {
    id: 'file-2',
    name: 'Q3 Financial Summary.xlsx',
    description: 'Q3 financial data and projections',
    type: 'file',
    fileType: 'spreadsheet',
    path: '/Documents/Reports/Q3 Financial Summary.xlsx',
    parentId: 'folder-3',
    size: '1.8 MB',
    modified: '2023-09-08',
    source: 'Google Drive'
  },
  {
    id: 'file-3',
    name: 'Project Roadmap.docx',
    description: 'Strategic roadmap for the project',
    type: 'file',
    fileType: 'document',
    path: '/Documents/Project Roadmap.docx',
    parentId: 'folder-1',
    size: '1.2 MB',
    modified: '2023-09-01',
    source: 'Dropbox'
  },
  {
    id: 'file-4',
    name: 'Company Logo.png',
    description: 'Official company logo in PNG format',
    type: 'file',
    fileType: 'image',
    path: '/Images/Company Logo.png',
    parentId: 'folder-2',
    size: '0.5 MB',
    modified: '2023-08-20',
    source: 'OneDrive'
  },
  {
    id: 'file-5',
    name: 'Team Photo.jpg',
    description: 'Team photo from company retreat',
    type: 'file',
    fileType: 'image',
    path: '/Images/Team Photo.jpg',
    parentId: 'folder-2',
    size: '3.2 MB',
    modified: '2023-08-15',
    source: 'OneDrive'
  },
  {
    id: 'folder-4',
    name: 'Shared',
    description: 'Shared project resources',
    type: 'folder',
    path: '/Shared',
    parentId: null,
    modified: '2023-09-18',
    source: 'Dropbox'
  },
  {
    id: 'file-6',
    name: 'Meeting Notes.docx',
    description: 'Notes from the last project meeting',
    type: 'file',
    fileType: 'document',
    path: '/Shared/Meeting Notes.docx',
    parentId: 'folder-4',
    size: '0.8 MB',
    modified: '2023-09-17',
    source: 'Dropbox'
  }
];

// Helper function to get the appropriate icon for a file type
const getFileIcon = (fileType: FileType | undefined) => {
  switch (fileType) {
    case 'document':
      return <FileText className="h-4 w-4 text-blue-500" />;
    case 'image':
      return <FileImage className="h-4 w-4 text-purple-500" />;
    case 'spreadsheet':
      return <FileSpreadsheet className="h-4 w-4 text-green-500" />;
    default:
      return <File className="h-4 w-4 text-gray-500" />;
  }
};

export function ResourcesDrawer({ isOpen, onClose }: ResourcesDrawerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({
    'folder-1': true, // Documents folder expanded by default
  });

  // Toggle folder expansion
  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };

  // Build hierarchical structure
  const resourcesHierarchy = useMemo(() => {
    // First, filter by search query if present
    const filteredResources = searchQuery
      ? mockResources.filter(resource =>
          resource.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          resource.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockResources;

    // If searching, return flat list of results
    if (searchQuery) {
      return filteredResources;
    }

    // Otherwise, build hierarchical structure
    const rootResources = filteredResources.filter(r => r.parentId === null);
    
    // Sort resources: folders first, then alphabetically by name
    return rootResources.sort((a, b) => {
      if (a.type === 'folder' && b.type !== 'folder') return -1;
      if (a.type !== 'folder' && b.type === 'folder') return 1;
      return a.name.localeCompare(b.name);
    });
  }, [searchQuery]);

  // Get children of a folder
  const getChildrenOfFolder = (folderId: string) => {
    const children = mockResources.filter(r => r.parentId === folderId);
    
    // Sort resources: folders first, then alphabetically by name
    return children.sort((a, b) => {
      if (a.type === 'folder' && b.type !== 'folder') return -1;
      if (a.type !== 'folder' && b.type === 'folder') return 1;
      return a.name.localeCompare(b.name);
    });
  };

  // Handle resource actions
  const handleResourceAction = (action: string, resource: Resource) => {
    switch (action) {
      case 'open':
        console.log(`Opening ${resource.name}`);
        break;
      case 'download':
        console.log(`Downloading ${resource.name}`);
        break;
      case 'connect':
        console.log(`Connecting to ${resource.name}`);
        break;
      default:
        break;
    }
  };

  // Render a resource row with its children if expanded
  const renderResourceRow = (resource: Resource, depth = 0) => {
    const isFolder = resource.type === 'folder';
    const isExpanded = expandedFolders[resource.id] || false;
    const children = isFolder ? getChildrenOfFolder(resource.id) : [];
    
    return (
      <React.Fragment key={resource.id}>
        <TableRow className={cn(
          "group hover:bg-muted/50",
          depth > 0 && "border-t-0"
        )}>
          <TableCell className="py-2 pl-4">
            <div className="flex items-center space-x-2" style={{ paddingLeft: `${depth * 16}px` }}>
              {isFolder ? (
                <button 
                  onClick={() => toggleFolder(resource.id)} 
                  className="p-1 rounded-sm hover:bg-muted"
                >
                  {isExpanded ? 
                    <ChevronDown className="h-4 w-4 text-muted-foreground" /> : 
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  }
                </button>
              ) : (
                <div className="w-6"></div>
              )}
              
              <div className="flex items-center space-x-2">
                {isFolder ? (
                  isExpanded ? 
                    <FolderOpen className="h-4 w-4 text-yellow-500" /> : 
                    <Folder className="h-4 w-4 text-yellow-500" />
                ) : (
                  getFileIcon(resource.fileType)
                )}
                <span className="font-medium">{resource.name}</span>
              </div>
            </div>
          </TableCell>
          <TableCell className="py-2">{resource.description}</TableCell>
          <TableCell className="py-2 text-right">
            <div className="flex items-center justify-end space-x-2">
              {resource.source && (
                <Badge variant="outline" className="text-xs">
                  {resource.source}
                </Badge>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleResourceAction('open', resource)}>
                    Open
                  </DropdownMenuItem>
                  {resource.type === 'file' && (
                    <DropdownMenuItem onClick={() => handleResourceAction('download', resource)}>
                      Download
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={() => handleResourceAction('connect', resource)}>
                    Connect
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </TableCell>
        </TableRow>
        
        {/* Render children if folder is expanded */}
        {isFolder && isExpanded && children.map(child => (
          renderResourceRow(child, depth + 1)
        ))}
      </React.Fragment>
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={open => !open && onClose()}>
      <SheetContent side="right" className="w-[40%] p-0 sm:max-w-none">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-5 border-b">
            <h2 className="text-lg font-semibold">Resources</h2>
            <div className="flex items-center space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" className="text-blue-600 border-blue-200">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add new resource</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" className="text-blue-600 border-blue-200">
                      <Upload className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Upload resource</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          <div className="p-5 border-b">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search resources..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex-1 overflow-auto p-5">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[40%]">Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {resourcesHierarchy.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4 text-muted-foreground">
                      No resources found
                    </TableCell>
                  </TableRow>
                ) : (
                  resourcesHierarchy.map(resource => renderResourceRow(resource))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
