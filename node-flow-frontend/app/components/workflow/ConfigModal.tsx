"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Copy, Wand2, X, <PERSON>rkles } from "lucide-react";
import { getAllFlows } from "@/lib/flows";
import { cn } from "@/lib/utils";

interface ConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: {
    prompt: string;
    transcript: string;
    config: string;
  }) => void;
  initialConfig?: { prompt: string; transcript: string; config: string };
  currentFlow?: Record<string, unknown>;
}

export function ConfigModal({
  isOpen,
  onClose,
  onSave,
  initialConfig,
  currentFlow,
}: ConfigModalProps) {
  const [prompt, setPrompt] = useState(
    initialConfig?.prompt ||
      "From the transcript, extract out the key business processes from our action-input perspective, and then generate the json output."
  );
  const [transcript, setTranscript] = useState(initialConfig?.transcript || "");
  const [config, setConfig] = useState(initialConfig?.config || "");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showRainbow, setShowRainbow] = useState(false);

  // Update config when modal opens with current flow
  useEffect(() => {
    if (isOpen && currentFlow) {
      setConfig(JSON.stringify(currentFlow, null, 2));
    }
  }, [isOpen, currentFlow]);

  const handleSave = () => {
    try {
      JSON.parse(config); // Validate JSON
      onSave({ prompt, transcript, config });
      onClose();
    } catch (error) {
      console.error("Invalid JSON configuration:", error);
    }
  };

  const generateGenericConfig = () => {
    const flows = getAllFlows();
    const exampleFlow = flows.find((flow) => flow.id === "example-flow");
    if (exampleFlow) {
      setConfig(JSON.stringify(exampleFlow.config, null, 2));
    }
  };

  const handleCopyConfig = () => {
    navigator.clipboard.writeText(config);
  };

  const handleClearConfig = () => {
    setConfig("");
  };

  const handleGenerateConfig = async () => {
    if (!transcript || !prompt) {
      console.error("Prompt and transcript are required");
      return;
    }

    setIsGenerating(true);
    try {
      // Simulate API call with timeout
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Dummy response
      const dummyConfig = {
        message: "Hello World",
        timestamp: new Date().toISOString(),
      };

      setConfig(JSON.stringify(dummyConfig, null, 2));

      // Trigger rainbow animation
      setShowRainbow(true);
      setTimeout(() => {
        setShowRainbow(false);
      }, 3000);
    } catch (error) {
      console.error("Error generating config:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1000px] h-[600px] flex flex-col">
        <DialogHeader>
          <DialogTitle>Workflow Configuration</DialogTitle>
        </DialogHeader>
        <div className="flex-1 flex gap-4 min-h-0">
          <div className="flex-1 flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <Label htmlFor="prompt">Prompt</Label>
              <Textarea
                id="prompt"
                placeholder="Enter your prompt here..."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="resize-none h-24"
              />
            </div>
            <div className="flex flex-col gap-2 flex-1">
              <Label htmlFor="transcript">Transcript</Label>
              <Textarea
                id="transcript"
                placeholder="Paste your transcript here..."
                value={transcript}
                onChange={(e) => setTranscript(e.target.value)}
                className="flex-1 min-h-0 resize-none"
              />
            </div>
          </div>
          <div className="flex-1 flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="config">Configuration</Label>
            </div>
            <div className="flex-1 relative">
              <div className="absolute top-2 right-2 flex gap-2 z-10">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyConfig}
                  className="flex items-center gap-2 bg-white hover:bg-gray-100"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearConfig}
                  className="flex items-center gap-2 bg-white hover:bg-gray-100"
                >
                  <X className="h-4 w-4" />
                  Clear
                </Button>
              </div>
              <Textarea
                id="config"
                placeholder="Enter your configuration here..."
                value={config}
                onChange={(e) => setConfig(e.target.value)}
                className={cn(
                  "h-full w-full resize-none font-mono transition-all duration-300",
                  showRainbow && "animate-rainbow border-2 border-rainbow"
                )}
              />
            </div>
          </div>
        </div>
        <DialogFooter className="flex w-full items-center">
          <div className="flex-1">
            <Button
              variant="outline"
              onClick={handleGenerateConfig}
              className="flex items-center gap-2 bg-amber-500 hover:bg-amber-600 text-white"
              disabled={isGenerating}
            >
              <Sparkles className="h-4 w-4" />
              {isGenerating ? "Generating..." : "Generate Config"}
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={generateGenericConfig}
              className="flex items-center gap-2"
            >
              <Wand2 className="h-4 w-4" />
              Generate Generic Config
            </Button>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
