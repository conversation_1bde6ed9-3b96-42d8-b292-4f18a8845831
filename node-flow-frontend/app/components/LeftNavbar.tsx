import React, { useState, useEffect } from "react";
import Link from "next/link";
import {
  Workflow,
  LayoutDashboard,
  BarChart2,
  Link as LinkIcon,
  Settings,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  Plus,
  Folder,
  FileText,
  Library,
  ChevronDown,
} from "lucide-react";
import { cn } from "@/lib/utils";
import ConfigModal from "./ConfigModal";
import { useWorkflows } from "@/contexts/WorkflowContext";

interface NavItemProps {
  icon: React.ReactNode;
  text: string;
  href: string;
  isCollapsed: boolean;
  isActive?: boolean;
  subItems?: { text: string; href: string; icon: React.ReactNode }[];
}

const NavItem: React.FC<NavItemProps> = ({
  icon,
  text,
  href,
  isCollapsed,
  subItems,
  isActive,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const hasSubItems = subItems && subItems.length > 0;

  useEffect(() => {
    if (isActive && hasSubItems) {
      setIsExpanded(true);
    }
  }, [isActive, hasSubItems]);

  return (
    <div className="relative">
      <Link
        href={href}
        className={cn(
          "group flex items-center p-2 text-sm font-medium rounded-lg transition-all duration-200 w-[80%] mx-auto",
          isCollapsed ? "justify-center" : "pl-3 pr-1.5",
          hasSubItems && "pr-1.5",
          isActive
            ? "bg-gradient-to-r from-purple-50 to-blue-50 text-purple-700 shadow-sm shadow-purple-100/50"
            : "text-gray-600 hover:bg-gray-50 hover:text-purple-600",
          "hover:translate-x-1"
        )}
      >
        <span
          className={cn(
            "transition-colors",
            isActive
              ? "text-purple-600"
              : "text-gray-400 group-hover:text-purple-500",
            !isCollapsed && "mr-3"
          )}
        >
          {React.cloneElement(icon as React.ReactElement, { size: 20 })}
        </span>
        {!isCollapsed && <span className="flex-1">{text}</span>}
        {hasSubItems && !isCollapsed && (
          <ChevronDown
            className={cn(
              "h-4 w-4 text-gray-400 transition-transform duration-200",
              isExpanded ? "rotate-180" : ""
            )}
          />
        )}
      </Link>
      {hasSubItems && isExpanded && !isCollapsed && (
        <div className="ml-8 mt-1 space-y-1">
          {subItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="flex items-center py-1.5 px-3 text-sm text-gray-500 rounded-md hover:bg-gray-50 hover:text-purple-600 transition-all duration-200 hover:pl-4"
            >
              <div className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 group-focus-within:text-purple-500 transition-colors" />
              <span className="mr-2 text-gray-300">
                {React.cloneElement(item.icon as React.ReactElement, {
                  size: 16,
                })}
              </span>
              {item.text}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

interface LeftNavbarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

const LeftNavbar: React.FC<LeftNavbarProps> = ({ isCollapsed, onToggle }) => {
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const { workflows } = useWorkflows();
  const [activeItem, setActiveItem] = useState<string | null>(null);

  const navItems = [
    {
      icon: <Workflow className="group-hover:scale-110 transition-transform" />,
      text: "Workflows",
      href: "/workflows",
      subItems: [
        {
          icon: <Plus className="text-blue-500" />,
          text: "Create Workflow",
          href: "/workflows/create",
        },
        {
          icon: <Folder className="text-purple-500" />,
          text: "My Workflows",
          href: "/workflows/my",
        },
        {
          icon: <FileText className="text-green-500" />,
          text: "Templates",
          href: "/workflows/templates",
        },
        {
          icon: <Library className="text-amber-500" />,
          text: "Library",
          href: "/workflows/library",
        },
        ...(workflows?.map((workflow) => ({
          icon: (
            <span className="h-1.5 w-1.5 rounded-full bg-gray-300 group-hover:bg-purple-500" />
          ),
          text: workflow.name,
          href: `/workflows/${workflow.id}`,
        })) || []),
      ],
    },
    {
      icon: (
        <LayoutDashboard className="group-hover:scale-110 transition-transform" />
      ),
      text: "Processes",
      href: "/process-management",
    },
    {
      icon: (
        <BarChart2 className="group-hover:scale-110 transition-transform" />
      ),
      text: "Analytics",
      href: "/reports",
    },
    {
      icon: <LinkIcon className="group-hover:scale-110 transition-transform" />,
      text: "Integrations",
      href: "/integrations",
    },
    {
      icon: <Settings className="group-hover:scale-110 transition-transform" />,
      text: "Settings",
      href: "/settings",
    },
    {
      icon: (
        <HelpCircle className="group-hover:scale-110 transition-transform" />
      ),
      text: "Help",
      href: "/help",
    },
  ];

  const toggleItem = (text: string) => {
    setActiveItem(activeItem === text ? null : text);
  };

  const handleItemClick = (
    e: React.MouseEvent,
    text: string,
    hasSubItems: boolean
  ) => {
    if (hasSubItems) {
      e.preventDefault();
      toggleItem(text);
    }
  };

  return (
    <div className="relative h-full">
      <nav
        className={`bg-white/95 backdrop-blur-sm border-r border-gray-100 h-full transition-all duration-200 flex flex-col z-30 ${
          isCollapsed ? "w-14" : "w-64"
        }`}
      >
        <div className="flex-1 overflow-y-auto py-2">
          <div className="h-16"></div> {/* Spacer for the header area */}
          <div className="space-y-1.5 px-3 py-2">
            {navItems.map((item, index) => (
              <div key={index}>
                <div
                  onClick={(e) =>
                    handleItemClick(e, item.text, !!item.subItems?.length)
                  }
                  className="cursor-pointer"
                >
                  <NavItem
                    {...item}
                    isCollapsed={isCollapsed}
                    isActive={activeItem === item.text}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </nav>

      {/* Toggle button centered on the navbar edge */}
      <div
        className="absolute top-6 -right-4 z-40 flex h-8 w-8 items-center justify-center rounded-full bg-white border border-gray-200 shadow-lg shadow-gray-300/50 transition-all hover:shadow-purple-200/50 hover:shadow-xl group"
        style={{ transform: "translateX(50%)" }}
      >
        <button
          onClick={onToggle}
          className="w-full h-full flex items-center justify-center text-gray-500 hover:text-purple-600 transition-colors"
        >
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      </div>

      <ConfigModal
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
        config='{"meta":{"title":"","description":"","lastUpdated":""},"processFlow":[],"connections":[]}'
        onUpdateConfig={() => {}}
      />
    </div>
  );
};

export default LeftNavbar;
