'use client';

import { useState } from 'react';
import { Integration } from '@/types/integration';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

export function IntegrationCard({ integration }: { integration: Integration }) {
  const [isApiKeyDialogOpen, setIsApiKeyDialogOpen] = useState(false);
  const [apiKeyName, setApiKeyName] = useState('');
  const [apiKeyValue, setApiKeyValue] = useState('');
  const [apiKeys, setApiKeys] = useState<Array<{
    id: string;
    name: string;
    key: string;
    createdAt: string;
    isActive: boolean;
  }>>([]);
  const [isActive, setIsActive] = useState(integration.isActive);

  const handleAddApiKey = (e: React.FormEvent) => {
    e.preventDefault();
    if (!apiKeyName.trim() || !apiKeyValue.trim()) return;
    
    const newApiKey = {
      id: `key-${Date.now()}`,
      name: apiKeyName,
      key: apiKeyValue,
      createdAt: new Date().toISOString(),
      isActive: true,
    };
    
    setApiKeys(prev => [...prev, newApiKey]);
    setApiKeyName('');
    setApiKeyValue('');
    setIsApiKeyDialogOpen(false);
    
    // If this is the first API key, activate the integration
    if (apiKeys.length === 0) {
      setIsActive(true);
    }
  };

  const handleToggleApiKey = (keyId: string, isKeyActive: boolean) => {
    setApiKeys(prev => 
      prev.map(key => 
        key.id === keyId ? { ...key, isActive: isKeyActive } : key
      )
    );
  };

  const handleDeleteApiKey = (keyId: string) => {
    const newApiKeys = apiKeys.filter(key => key.id !== keyId);
    setApiKeys(newApiKeys);
    
    // If no more API keys, deactivate the integration
    if (newApiKeys.length === 0 && isActive) {
      setIsActive(false);
    }
  };

  return (
    <div className="border rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="p-5">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gray-100 text-2xl">
              {integration.icon}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{integration.name}</h3>
              <p className="text-sm text-gray-500">{integration.description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 text-xs rounded-full ${
              isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
            }`}>
              {isActive ? 'Active' : 'Inactive'}
            </span>
            <Switch 
              checked={isActive} 
              onCheckedChange={setIsActive}
              disabled={apiKeys.length === 0}
            />
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium text-gray-700">API Keys</h4>
            <Dialog open={isApiKeyDialogOpen} onOpenChange={setIsApiKeyDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  Add API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add {integration.name} API Key</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleAddApiKey} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="key-name">Key Name</Label>
                    <Input
                      id="key-name"
                      placeholder="e.g., Production, Development"
                      value={apiKeyName}
                      onChange={(e) => setApiKeyName(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="key-value">API Key</Label>
                    <Input
                      id="key-value"
                      type="password"
                      placeholder="Enter your API key"
                      value={apiKeyValue}
                      onChange={(e) => setApiKeyValue(e.target.value)}
                      required
                    />
                  </div>
                  <div className="flex justify-end space-x-2 pt-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsApiKeyDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">Save API Key</Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          
          {apiKeys.length > 0 ? (
            <div className="space-y-2">
              {apiKeys.map((key) => (
                <div key={key.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <div className="text-sm font-medium">{key.name}</div>
                    <div className="text-xs text-gray-500">
                      {key.key.substring(0, 4)}...{key.key.substring(key.key.length - 4)}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={key.isActive}
                      onCheckedChange={(checked) => handleToggleApiKey(key.id, checked)}
                      className="data-[state=checked]:bg-green-500"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-red-500 hover:bg-red-50"
                      onClick={() => handleDeleteApiKey(key.id)}
                      type="button"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 bg-gray-50 rounded">
              <p className="text-sm text-gray-500">No API keys added yet</p>
              <p className="text-xs text-gray-400 mt-1">Add an API key to enable this integration</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Simple Trash2 icon component since we don't have the icon library imported
function Trash2(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 6h18" />
      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
      <line x1="10" y1="11" x2="10" y2="17" />
      <line x1="14" y1="11" x2="14" y2="17" />
    </svg>
  );
}
