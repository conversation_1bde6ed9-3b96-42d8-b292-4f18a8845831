'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface DrawerProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  side?: 'left' | 'right';
  width?: string;
  topOffset?: string; // Add top offset prop to position drawer below ribbon
}

export function Drawer({
  children,
  isOpen,
  onClose,
  side = 'left',
  width = '40%',
  topOffset = '0px', // Default to 0 if not provided
}: DrawerProps) {
  const [mounted, setMounted] = React.useState(false);
  
  // Only render on client side to avoid hydration issues
  React.useEffect(() => {
    setMounted(true);
  }, []);
  
  // Handle ESC key to close drawer
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Prevent scrolling when drawer is open
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!mounted) {
    return null;
  }

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-40 transition-opacity"
          onClick={onClose}
          aria-hidden="true"
        />
      )}

      {/* Drawer */}
      <div
        className={cn(
          'fixed bottom-0 z-50 flex flex-col bg-white dark:bg-gray-800 shadow-lg transition-transform duration-300 ease-in-out',
          side === 'left' ? 'left-0' : 'right-0',
          isOpen 
            ? 'translate-x-0' 
            : side === 'left' 
              ? '-translate-x-full' 
              : 'translate-x-full'
        )}
        style={{ width, top: topOffset }}
      >
        <div className="flex-1 overflow-auto p-6 relative">
          {children}
        </div>
      </div>
    </>
  );
}
