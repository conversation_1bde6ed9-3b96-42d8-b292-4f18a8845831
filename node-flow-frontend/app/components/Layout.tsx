import React, { useState } from 'react';
import Header from './Header';
import Footer from './Footer';
import LeftNavbar from './LeftNavbar';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isNavbarCollapsed, setIsNavbarCollapsed] = useState(false);

  const toggleNavbar = () => setIsNavbarCollapsed(!isNavbarCollapsed);

  return (
    <div className="flex flex-col h-screen">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <LeftNavbar isCollapsed={isNavbarCollapsed} onToggle={toggleNavbar} />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
      <Footer />
    </div>
  );
};

export default Layout;
