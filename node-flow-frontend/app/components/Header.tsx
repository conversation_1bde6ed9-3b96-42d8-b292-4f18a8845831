import React from 'react';
import { Workflow, Bell, Search, User, Settings as SettingsIcon } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';

const Header: React.FC = () => {
  return (
    <header className="sticky top-0 z-40 border-b border-gray-100 bg-white/80 backdrop-blur-lg">
      <div className="flex h-16 items-center justify-between px-6">
        <div className="flex items-center space-x-2">
          <div className="flex h-9 w-9 items-center justify-center rounded-lg bg-gradient-to-br from-purple-600 to-blue-500">
            <Workflow className="h-5 w-5 text-white" />
          </div>
          <h1 className="bg-gradient-to-r from-purple-600 to-blue-500 bg-clip-text text-xl font-bold text-transparent">TeamCraft.AI</h1>
        </div>
        
        <div className="relative w-1/3">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            type="search"
            placeholder="Search workflows, integrations, or settings..."
            className="w-full rounded-full border-0 bg-gray-50 pl-10 pr-4 text-sm focus-visible:ring-2 focus-visible:ring-purple-500/20"
          />
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="ghost" size="icon" className="text-gray-500 hover:bg-gray-100 hover:text-gray-700">
            <Bell className="h-5 w-5" />
            <span className="sr-only">Notifications</span>
          </Button>
          <Button variant="ghost" size="icon" className="text-gray-500 hover:bg-gray-100 hover:text-gray-700">
            <SettingsIcon className="h-5 w-5" />
            <span className="sr-only">Settings</span>
          </Button>
          <Button variant="outline" size="sm" className="group flex items-center space-x-2 rounded-full border-gray-200 bg-white px-3 hover:bg-gray-50">
            <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gradient-to-br from-purple-600 to-blue-500 text-white">
              <User className="h-4 w-4" />
            </div>
            <span className="text-sm font-medium text-gray-700">Account</span>
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
