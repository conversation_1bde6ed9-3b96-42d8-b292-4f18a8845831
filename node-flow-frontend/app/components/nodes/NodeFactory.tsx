'use client';

import React from 'react';
import { NodeProps, useReactFlow } from 'reactflow';
import { NodeData } from '@/types/nodes';
import { NodeCard } from './NodeCard';
import { memo } from 'react';

export const NodeFactory = memo(({ type, data, id }: NodeProps) => {
  const { setNodes } = useReactFlow();

  const handleNodeUpdate = (updatedData: NodeData) => {
    console.log('NodeFactory handling update:', id, updatedData);
    setNodes((nodes) => 
      nodes.map((node) => 
        node.id === id 
          ? {
              ...node,
              data: {
                ...node.data,
                ...updatedData,
                // Always preserve these fields
                inputs: node.data.inputs,
                outputs: node.data.outputs,
              },
            }
          : node
      )
    );
  };

  return (
    <NodeCard 
      key={id}
      type={type}
      data={data}
      onNodeUpdate={handleNodeUpdate}
    />
  );
});

NodeFactory.displayName = 'NodeFactory';