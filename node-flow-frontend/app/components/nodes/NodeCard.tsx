"use client";

import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { NodeData } from "@/types/nodes";
import { Hand<PERSON>, Position } from "reactflow";
import { nodeCategories } from "@/lib/nodeTypes";
import { Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface NodeCardProps {
  type: string;
  data: NodeData & {
    context?: string;
    expectedBehavior?: string;
  };
  onNodeUpdate?: (updatedData: NodeData) => void;
}

export function NodeCard({ type, data, onNodeUpdate }: NodeCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedData, setEditedData] = useState<NodeData>({
    ...data,
    label: data.label || "",
    description: data.description || "",
    owner: data.owner || "",
    action: data.action || "",
    context: data.context || "",
    expectedBehavior: data.expectedBehavior || "",
    conditions: data.conditions || [],
  });

  useEffect(() => {
    setEditedData({
      ...data,
      label: data.label || "",
      description: data.description || "",
      owner: data.owner || "",
      action: data.action || "",
      context: data.context || "",
      expectedBehavior: data.expectedBehavior || "",
      conditions: data.conditions || [],
    });
  }, [data]);

  const handleSave = () => {
    if (onNodeUpdate) {
      const updatedData = {
        ...data,
        ...editedData,
      };

      console.log("NodeCard saving:", updatedData);
      onNodeUpdate(updatedData);
      setIsEditing(false);
    }
  };

  // Get node config for styling
  let finalConfig;

  // First check if this is specifically a 'tools' type node
  if (type === "tools" && nodeCategories.Tools) {
    finalConfig = nodeCategories.Tools[0];
  } else {
    // Otherwise look through all categories
    finalConfig = Object.values(nodeCategories)
      .flat()
      .find((node) => node.type === type);
  }

  const cardStyle = {
    borderColor: finalConfig?.color || "#e5e7eb",
    backgroundColor: finalConfig?.color ? `${finalConfig.color}10` : "white",
  };

  const handleStyle = (
    index: number,
    total: number,
    position: "left" | "right" | "bottom"
  ) => {
    // Base styles
    const baseStyle = {
      width: "24px",
      height: "24px",
      borderRadius: "12px",
      borderWidth: "2px",
      borderColor: finalConfig?.color || "#e5e7eb",
      backgroundColor: finalConfig?.color || "#e5e7eb", // Fill with the node's color
    };

    // Position-specific styles
    switch (position) {
      case "left":
        return {
          ...baseStyle,
          left: "-12px",
          top: `${25 + index * 50}%`,
        };
      case "right":
        return {
          ...baseStyle,
          right: "-12px",
          top: `${25 + index * 50}%`,
        };
      case "bottom":
        return {
          ...baseStyle,
          bottom: "-12px",
          left: "50%",
          transform: "translateX(-50%)",
        };
      default:
        return baseStyle;
    }
  };

  // Card content remains the same until DialogContent
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Card
          className="min-w-[200px] cursor-pointer hover:shadow-md transition-shadow relative"
          style={cardStyle}
        >
          {/* Tool Node: Special handling for tools node */}
          {type === "tools" ? (
            <>
              {/* Tools node should only have output connection points */}
              {/* Input handle removed as per requirement */}
              <Handle
                type="source"
                position={Position.Right}
                id="tool-output"
                style={handleStyle(0, 1, "right")}
                className="!border-solid"
              />
            </>
          ) : (
            <>
              {/* Input Handles - Not rendered for data nodes */}
              {data.inputs?.map(
                (input, index) =>
                  // Skip rendering input handles for data nodes
                  !["dataInput", "dataOutput", "dataStore"].includes(type) && (
                    <Handle
                      key={input.id}
                      type="target"
                      position={Position.Left}
                      id={input.id}
                      style={handleStyle(
                        index,
                        data.inputs?.length || 0,
                        "left"
                      )}
                      className="!border-solid"
                    />
                  )
              )}

              {/* Output Handles */}
              {data.outputs?.map((output, index) => (
                <Handle
                  key={output.id}
                  type="source"
                  position={Position.Right}
                  id={output.id}
                  style={handleStyle(index, data.outputs?.length || 0, "right")}
                  className="!border-solid"
                />
              ))}

              {/* Dynamic Bottom Handle for Tool Connections */}
              {/* Note: toolConnections is not defined in NodeData interface, commenting out for now */}
              {/* {data.toolConnections?.map((conn, index) => (
                <Handle
                  key={`tool-${conn.id}`}
                  type="target"
                  position={Position.Bottom}
                  id={`tool-input-${conn.id}`}
                  style={handleStyle(
                    index,
                    data.toolConnections?.length || 0,
                    "bottom"
                  )}
                  className="!border-solid"
                />
              ))} */}
            </>
          )}

          {/* Add padding to prevent terminal overlap */}
          <div className="p-6">
            {/* Node Icon */}
            <div className="absolute -left-3 -top-3 bg-white rounded-full p-1 shadow-sm border">
              {finalConfig && (
                <finalConfig.icon
                  className="w-5 h-5"
                  style={{ color: finalConfig.color }}
                />
              )}
            </div>

            {/* Node Title */}
            <div className="font-semibold text-lg mb-2 mt-2">
              {data.label || finalConfig?.label || type}
            </div>

            {/* Actor/Owner */}
            <div className="text-sm text-gray-600">
              {data.owner || "Unassigned"}
            </div>
          </div>
        </Card>
      </DialogTrigger>

      <DialogContent className="max-w-2xl h-[80vh] flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            {finalConfig && (
              <finalConfig.icon
                className="w-5 h-5"
                style={{ color: finalConfig.color }}
              />
            )}
            {isEditing ? (
              <Input
                value={editedData.label || ""}
                onChange={(e) =>
                  setEditedData((prev) => ({
                    ...prev,
                    label: e.target.value,
                  }))
                }
                className="text-lg font-semibold"
                placeholder={finalConfig?.label || type}
              />
            ) : (
              data.label || finalConfig?.label || type
            )}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 overflow-auto">
          <div className="space-y-4 px-6 pb-6">
            {/* Description */}
            <div>
              <h4 className="font-medium mb-2 text-gray-600">Description</h4>
              {isEditing ? (
                <Textarea
                  value={editedData.description || ""}
                  onChange={(e) =>
                    setEditedData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  className="text-sm min-h-[100px]"
                  placeholder="Describe the purpose of this node..."
                />
              ) : (
                <p className="text-sm">
                  {data.description || "No description provided"}
                </p>
              )}
            </div>

            <div className="space-y-4 border-x px-6">
              <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
                Configuration
              </h3>

              {/* Context */}
              <div>
                <h4 className="font-medium mb-2 text-gray-600">Context</h4>
                {isEditing ? (
                  <Textarea
                    value={editedData.context || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        context: e.target.value,
                      }))
                    }
                    className="text-sm min-h-[100px]"
                    placeholder="Describe the context of this node..."
                  />
                ) : (
                  <p className="text-sm">
                    {data.context || "No context provided"}
                  </p>
                )}
              </div>

              {/* Expected Behavior */}
              <div>
                <h4 className="font-medium mb-2 text-gray-600">
                  Expected Behavior
                </h4>
                {isEditing ? (
                  <Textarea
                    value={editedData.expectedBehavior || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        expectedBehavior: e.target.value,
                      }))
                    }
                    className="text-sm min-h-[100px]"
                    placeholder="Describe the expected behavior..."
                  />
                ) : (
                  <p className="text-sm">
                    {data.expectedBehavior || "No expected behavior defined"}
                  </p>
                )}
              </div>

              {/* Actor/Owner */}
              <div>
                <h4 className="font-medium mb-2 text-gray-600">Actor</h4>
                {isEditing ? (
                  <Input
                    value={editedData.owner || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        owner: e.target.value,
                      }))
                    }
                    className="text-sm"
                  />
                ) : (
                  <p className="text-sm">{data.owner || "Unassigned"}</p>
                )}
              </div>

              {/* Action */}
              <div>
                <h4 className="font-medium mb-2 text-gray-600">Action</h4>
                {isEditing ? (
                  <Input
                    value={editedData.action || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        action: e.target.value,
                      }))
                    }
                    className="text-sm"
                  />
                ) : (
                  <p className="text-sm">{data.action || "No action"}</p>
                )}
              </div>

              {/* Conditions */}
              {data.conditions && (
                <div>
                  <h4 className="font-medium mb-2 text-gray-600">Conditions</h4>
                  {isEditing ? (
                    <Textarea
                      value={
                        editedData.conditions
                          ?.map((condition) =>
                            typeof condition === "string"
                              ? condition
                              : `${condition.field} ${condition.operator} ${condition.value}`
                          )
                          .join("\n") || ""
                      }
                      onChange={(e) =>
                        setEditedData({
                          ...editedData,
                          conditions: e.target.value
                            .split("\n")
                            .filter((line) => line.trim())
                            .map((line) => {
                              const [
                                field = "",
                                operator = "equals",
                                value = "",
                              ] = line.split(" ");
                              return {
                                field,
                                operator: operator as
                                  | "equals"
                                  | "not_equals"
                                  | "contains"
                                  | "greater_than"
                                  | "less_than",
                                value,
                              };
                            }),
                        })
                      }
                      className="text-sm min-h-[100px]"
                      placeholder="Enter conditions (format: field operator value)"
                    />
                  ) : (
                    <ul className="list-disc list-inside text-sm">
                      {data.conditions.map((condition, index) => (
                        <li key={index}>{condition.toString()}</li>
                      ))}
                    </ul>
                  )}
                </div>
              )}

              {/* Parameters */}
              <div>
                <h4 className="font-medium mb-2 text-gray-600">Parameters</h4>
                {isEditing ? (
                  <div className="space-y-2">
                    {/* Add parameter editing UI here */}
                  </div>
                ) : (
                  <div className="text-sm text-gray-500">
                    No parameters configured
                  </div>
                )}
              </div>
            </div>
          </div>
        </ScrollArea>

        {isEditing && (
          <div className="flex justify-end gap-2 px-6 py-4 border-t mt-auto">
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        )}

        {!isEditing && (
          <div className="flex justify-end px-6 py-4 border-t mt-auto">
            <Button onClick={() => setIsEditing(true)}>
              <Pencil className="w-4 h-4 mr-2" />
              Edit
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
