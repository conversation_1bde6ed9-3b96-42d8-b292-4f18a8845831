"use client";

import React from "react";
import { BaseNode, Terminal } from "@/types/nodes";
import {
  Sheet,
  Network,
  Brain,
  User,
  AlertCircle,
  RotateCw,
  Play,
  GitBranch,
  Bell,
  Link,
  Clock,
  Zap,
  Square,
} from "lucide-react";
import { Handle, Position } from "reactflow";

interface NodeComponentProps {
  data: BaseNode["data"];
  onConfigClick?: () => void;
  onRetry?: () => void;
  onConnect: (terminalId: string, type: "input" | "output") => void;
}

// Base styles for different node types
const baseStyles = {
  node: "p-4 rounded-lg shadow-md border min-w-[200px]",
  title: "font-semibold mb-2",
  content: "text-sm",
  iconWrapper:
    "absolute -left-3 -top-3 bg-white rounded-full p-1 shadow-sm border",
};

// Update terminal styles
const terminalStyles = {
  source:
    "w-3 h-3 rounded-full bg-white hover:bg-green-100 border-2 border-gray-300",
  target:
    "w-3 h-3 rounded-full bg-white hover:bg-green-100 border-2 border-gray-300",
  connected: "bg-green-500 hover:bg-green-600 border-2 border-green-600",
};

interface TerminalProps {
  terminal: Terminal & { isConnected?: boolean };
  type: "source" | "target";
  position: Position;
}

const TerminalPoint: React.FC<TerminalProps> = ({
  terminal,
  type,
  position,
}) => {
  return (
    <Handle
      type={type}
      position={position}
      id={terminal.id}
      className={`${terminalStyles[type]} ${
        terminal.isConnected ? terminalStyles.connected : ""
      }`}
      style={{
        width: "12px",
        height: "12px",
        transition: "all 0.2s ease",
        backgroundColor: terminal.isConnected ? "#22c55e" : "#ffffff",
        borderColor: terminal.isConnected ? "#16a34a" : "#d1d5db",
      }}
    />
  );
};

export const StartNodeComponent: React.FC<NodeComponentProps> = ({ data }) => (
  <div className={`${baseStyles.node} bg-blue-50 border-blue-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Zap className="w-5 h-5 text-blue-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label || "Untitled Flow"}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const ActionNodeComponent: React.FC<NodeComponentProps> = ({ data }) => (
  <div className={`${baseStyles.node} bg-green-50 border-green-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Play className="w-5 h-5 text-green-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const DecisionNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-purple-50 border-purple-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <GitBranch className="w-5 h-5 text-purple-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const APINodeComponent: React.FC<NodeComponentProps> = ({ data }) => (
  <div className={`${baseStyles.node} bg-yellow-50 border-yellow-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Network className="w-5 h-5 text-yellow-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const GoogleSheetsNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-green-50 border-green-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Sheet className="w-5 h-5 text-green-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const CrewAINodeComponent: React.FC<NodeComponentProps> = ({ data }) => (
  <div className={`${baseStyles.node} bg-indigo-50 border-indigo-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Brain className="w-5 h-5 text-indigo-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const HumanTaskNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-orange-50 border-orange-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <User className="w-5 h-5 text-orange-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const ParallelNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-cyan-50 border-cyan-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <RotateCw className="w-5 h-5 text-cyan-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const ApprovalNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-pink-50 border-pink-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <AlertCircle className="w-5 h-5 text-pink-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const NotificationNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-red-50 border-red-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Bell className="w-5 h-5 text-red-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const IntegrationNodeComponent: React.FC<NodeComponentProps> = ({
  data,
}) => (
  <div className={`${baseStyles.node} bg-violet-50 border-violet-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Link className="w-5 h-5 text-violet-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const DelayNodeComponent: React.FC<NodeComponentProps> = ({ data }) => (
  <div className={`${baseStyles.node} bg-gray-50 border-gray-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Clock className="w-5 h-5 text-gray-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
    {data.outputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="source"
        position={Position.Right}
      />
    ))}
  </div>
);

export const EndNodeComponent: React.FC<NodeComponentProps> = ({ data }) => (
  <div className={`${baseStyles.node} bg-slate-50 border-slate-200 relative`}>
    <div className={baseStyles.iconWrapper}>
      <Square className="w-5 h-5 text-slate-500" />
    </div>
    <h3 className={baseStyles.title}>{data.label}</h3>
    <p className={baseStyles.content}>{data.description}</p>
    {data.inputs?.map((terminal) => (
      <TerminalPoint
        key={terminal.id}
        terminal={terminal}
        type="target"
        position={Position.Left}
      />
    ))}
  </div>
);
