import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Path to the workflow rules YAML file
    // This assumes the rules file is accessible from the API route
    // You may need to adjust this path based on your project structure
    const rulesPath = path.join(process.cwd(), 'public', 'workflow_rules.yaml');
    
    // Read the file
    const rulesContent = fs.readFileSync(rulesPath, 'utf8');
    
    // Return the rules as plain text with YAML content type
    return new NextResponse(rulesContent, {
      headers: {
        'Content-Type': 'text/yaml',
      },
    });
  } catch (error) {
    console.error('Error reading workflow rules:', error);
    return NextResponse.json(
      { error: 'Failed to load workflow rules' },
      { status: 500 }
    );
  }
}
