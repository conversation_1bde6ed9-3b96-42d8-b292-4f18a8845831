export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // Call your Django backend
    const response = await fetch('http://your-django-backend/api/generate-config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error('Failed to generate config');
    }

    const data = await response.json();
    return Response.json(data);
    
  } catch (error) {
    console.error('Error in generate-config route:', error);
    return Response.json({ error: 'Failed to generate config' }, { status: 500 });
  }
} 