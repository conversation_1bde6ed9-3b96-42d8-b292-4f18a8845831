import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Forward the request to the Django backend
    const backendUrl =
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
    const response = await fetch(`${backendUrl}/api/validate-workflow/`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    // Get the response from the backend
    const data = await response.json();

    // Return the response to the client
    return NextResponse.json(data, {
      status: response.status,
    });
  } catch (error) {
    console.error("Error in validate-workflow API route:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      {
        isValid: false,
        report: `❌ Error: ${errorMessage}`,
        errors: [
          {
            id: "API_ERROR",
            name: "api_error",
            description: `API error: ${errorMessage}`,
          },
        ],
        warnings: [],
      },
      { status: 500 }
    );
  }
}
