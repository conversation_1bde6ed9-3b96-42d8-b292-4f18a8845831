export type NodeType = 
  | 'start' 
  | 'event' 
  | 'action' 
  | 'integration' 
  | 'decision' 
  | 'approval' 
  | 'dataInput' 
  | 'dataOutput' 
  | 'dataStore' 
  | 'fork' 
  | 'join' 
  | 'end' 
  | 'fallback' 
  | 'humanTask' 
  | 'notification' 
  | 'audit' 
  | 'tools';

export interface Terminal {
  id: string;
  type: 'input' | 'output';
  label?: string;
  dataType?: string;
}

export interface NodeCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  value: string | number | boolean;
}

export interface NodeValidation {
  requiredFields: string[];
  fieldTypes: Record<string, string>;
}

export interface NodeData {
  label: string;
  description?: string;
  owner?: string;
  action?: string;
  context?: string;
  expectedBehavior?: string;
  conditions?: NodeCondition[];
  validation?: NodeValidation;
  inputs?: Terminal[];
  outputs?: Terminal[];
  parameters?: Record<string, unknown>;
}

export interface BaseNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
}

export interface NodeConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: 'default' | 'condition' | 'data';
}

export interface FlowConfig {
  id: string;
  name: string;
  description: string;
  config: {
    nodes: BaseNode[];
    connections: NodeConnection[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowConfig {
  nodes: BaseNode[];
  connections: NodeConnection[];
}
