export interface ApiKey {
  id: string;
  name: string;
  key: string;
  createdAt: string;
  lastUsed?: string;
  isActive: boolean;
}

export interface Integration {
  id: string;
  name: string;
  description: string;
  icon: string; // Icon component name from react-icons
  apiKeys: ApiKey[];
  isActive: boolean;
  category: 'ai' | 'cloud' | 'productivity' | 'communication' | 'database' | 'other';
  authType: 'api_key' | 'oauth2' | 'basic_auth' | 'custom';
  createdAt: string;
  updatedAt: string;
}

export const integrationCategories = [
  { id: 'ai', name: 'AI & Machine Learning' },
  { id: 'cloud', name: 'Cloud Storage' },
  { id: 'productivity', name: 'Productivity' },
  { id: 'communication', name: 'Communication' },
  { id: 'database', name: 'Database' },
  { id: 'other', name: 'Other' },
] as const;

export const availableIntegrations: Integration[] = [
  // Cloud Storage
  {
    id: 'google-drive',
    name: 'Google Drive',
    description: 'Connect to Google Drive for file storage and retrieval',
    icon: 'google-drive',
    apiKeys: [],
    isActive: false,
    category: 'cloud',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    description: 'Connect to Dropbox for file storage and sharing',
    icon: 'dropbox',
    apiKeys: [],
    isActive: false,
    category: 'cloud',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'azure-blob',
    name: 'Azure Blob Storage',
    description: 'Connect to Microsoft Azure Blob Storage',
    icon: 'azure-blob',
    apiKeys: [],
    isActive: false,
    category: 'cloud',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'aws-s3',
    name: 'AWS S3',
    description: 'Connect to Amazon S3 for object storage',
    icon: 'aws-s3',
    apiKeys: [],
    isActive: false,
    category: 'cloud',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  // Productivity
  {
    id: 'google-sheets',
    name: 'Google Sheets',
    description: 'Connect to Google Sheets for data management',
    icon: 'google-sheets',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'google-calendar',
    name: 'Google Calendar',
    description: 'Connect to Google Calendar for scheduling',
    icon: 'google-calendar',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'xero',
    name: 'Xero',
    description: 'Connect to Xero for accounting and bookkeeping',
    icon: 'xero',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'zoho',
    name: 'Zoho',
    description: 'Connect to Zoho for CRM and business applications',
    icon: 'zoho',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Connect to Notion for notes and documentation',
    icon: 'notion',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'asana',
    name: 'Asana',
    description: 'Connect to Asana for project management',
    icon: 'asana',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'trello',
    name: 'Trello',
    description: 'Connect to Trello for kanban boards and task management',
    icon: 'trello',
    apiKeys: [],
    isActive: false,
    category: 'productivity',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  // Communication
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    description: 'Connect to WhatsApp for messaging',
    icon: 'whatsapp',
    apiKeys: [],
    isActive: false,
    category: 'communication',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'telegram',
    name: 'Telegram',
    description: 'Connect to Telegram for messaging and bots',
    icon: 'telegram',
    apiKeys: [],
    isActive: false,
    category: 'communication',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'instagram',
    name: 'Instagram',
    description: 'Connect to Instagram for social media integration',
    icon: 'instagram',
    apiKeys: [],
    isActive: false,
    category: 'communication',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'facebook',
    name: 'Facebook',
    description: 'Connect to Facebook for social media integration',
    icon: 'facebook',
    apiKeys: [],
    isActive: false,
    category: 'communication',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Connect to Slack for team communication',
    icon: 'slack',
    apiKeys: [],
    isActive: false,
    category: 'communication',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Connect to Discord for community communication',
    icon: 'discord',
    apiKeys: [],
    isActive: false,
    category: 'communication',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  // AI
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'Connect to OpenAI for AI capabilities',
    icon: 'openai',
    apiKeys: [],
    isActive: false,
    category: 'ai',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    description: 'Connect to Anthropic for Claude AI capabilities',
    icon: 'anthropic',
    apiKeys: [],
    isActive: false,
    category: 'ai',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  // Database
  {
    id: 'postgres',
    name: 'PostgreSQL',
    description: 'Connect to PostgreSQL database',
    icon: 'postgres',
    apiKeys: [],
    isActive: false,
    category: 'database',
    authType: 'basic_auth',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'mongodb',
    name: 'MongoDB',
    description: 'Connect to MongoDB database',
    icon: 'mongodb',
    apiKeys: [],
    isActive: false,
    category: 'database',
    authType: 'basic_auth',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'supabase',
    name: 'Supabase',
    description: 'Connect to Supabase backend',
    icon: 'supabase',
    apiKeys: [],
    isActive: false,
    category: 'database',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // Other
  {
    id: 'n8n',
    name: 'n8n',
    description: 'Connect to n8n for workflow automation',
    icon: 'n8n',
    apiKeys: [],
    isActive: false,
    category: 'other',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'make',
    name: 'Make (Integromat)',
    description: 'Connect to Make for workflow automation',
    icon: 'make',
    apiKeys: [],
    isActive: false,
    category: 'other',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'zapier',
    name: 'Zapier',
    description: 'Connect to Zapier for workflow automation',
    icon: 'zapier',
    apiKeys: [],
    isActive: false,
    category: 'other',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Connect to Stripe for payment processing',
    icon: 'stripe',
    apiKeys: [],
    isActive: false,
    category: 'other',
    authType: 'api_key',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Connect to Shopify for e-commerce',
    icon: 'shopify',
    apiKeys: [],
    isActive: false,
    category: 'other',
    authType: 'oauth2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];
