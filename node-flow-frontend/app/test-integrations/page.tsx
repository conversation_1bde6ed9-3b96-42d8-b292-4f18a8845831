'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { IntegrationsDrawer } from '@/components/workflow/IntegrationsDrawer';
import { IntegrationConnectModal } from '@/components/workflow/IntegrationConnectModal';

// Sample integration for testing modal directly
const sampleIntegration = {
  id: 'openai',
  name: 'OpenAI',
  description: 'Connect to OpenAI for AI capabilities',
  icon: 'openai',
  category: 'ai',
  authType: 'api_key',
  status: 'not_connected' as const
};

export default function TestIntegrationsPage() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Integration Components Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="border rounded-lg p-6 bg-white shadow-sm">
          <h2 className="text-xl font-bold mb-4">Test Integrations Drawer</h2>
          <p className="text-gray-600 mb-4">
            Opens the integrations drawer with all available integrations.
            The drawer follows user preferences with 40% width and border-bottom tab indicators.
          </p>
          <Button 
            onClick={() => setIsDrawerOpen(true)}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
            variant="outline"
          >
            Open Integrations Drawer
          </Button>
          
          <IntegrationsDrawer 
            isOpen={isDrawerOpen} 
            onClose={() => setIsDrawerOpen(false)} 
          />
        </div>
        
        <div className="border rounded-lg p-6 bg-white shadow-sm">
          <h2 className="text-xl font-bold mb-4">Test Integration Connect Modal</h2>
          <p className="text-gray-600 mb-4">
            Opens a specific integration connection modal directly.
            All buttons use the blue outline style as per user preference.
          </p>
          <Button 
            onClick={() => setIsModalOpen(true)}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
            variant="outline"
          >
            Open OpenAI Connection Modal
          </Button>
          
          <IntegrationConnectModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            integration={sampleIntegration}
            onConnect={(id, credentials) => {
              console.log(`Connected to ${id} with credentials:`, credentials);
              setIsModalOpen(false);
            }}
          />
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h3 className="font-medium text-blue-800 mb-2">User Preferences Applied</h3>
        <ul className="list-disc pl-5 text-blue-700">
          <li>Drawer width set to 40%</li>
          <li>Tabs use border-bottom style for active tab highlight</li>
          <li>Buttons use blue outline variant with text-blue-600 and border-blue-200</li>
          <li>Padding in drawer content area set to p-5</li>
        </ul>
      </div>
    </div>
  );
}
