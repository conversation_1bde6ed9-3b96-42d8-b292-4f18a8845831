'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { BaseNode, NodeConnection } from '@/types/nodes';

interface Workflow {
  id: string;
  name: string;
  nodes: BaseNode[];
  connections: NodeConnection[];
  config: string;
}

interface WorkflowContextType {
  workflows: Workflow[];
  addWorkflow: (workflow: Workflow) => void;
  removeWorkflow: (id: string) => void;
  updateWorkflow: (id: string, workflow: Partial<Workflow>) => void;
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(undefined);

export function useWorkflows() {
  const context = useContext(WorkflowContext);
  if (context === undefined) {
    throw new Error('useWorkflows must be used within a WorkflowProvider');
  }
  return context;
}

export function WorkflowProvider({ children }: { children: React.ReactNode }) {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);

  useEffect(() => {
    const savedWorkflows = localStorage.getItem('workflows');
    if (savedWorkflows) {
      setWorkflows(JSON.parse(savedWorkflows));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('workflows', JSON.stringify(workflows));
  }, [workflows]);

  const addWorkflow = (workflow: Workflow) => {
    setWorkflows(prev => [...prev, workflow]);
  };

  const removeWorkflow = (id: string) => {
    setWorkflows(prev => prev.filter(w => w.id !== id));
  };

  const updateWorkflow = (id: string, workflow: Partial<Workflow>) => {
    setWorkflows(prev => prev.map(w => w.id === id ? { ...w, ...workflow } : w));
  };

  return (
    <WorkflowContext.Provider value={{ workflows, addWorkflow, removeWorkflow, updateWorkflow }}>
      {children}
    </WorkflowContext.Provider>
  );
}
