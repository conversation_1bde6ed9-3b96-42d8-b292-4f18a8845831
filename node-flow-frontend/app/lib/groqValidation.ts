import { Node, Edge } from 'reactflow';

interface ValidationResult {
  isValid: boolean;
  report: string;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

interface ValidationError {
  id: string;
  name: string;
  description: string;
  affectedNodes?: string[];
}

interface ValidationWarning {
  id: string;
  name: string;
  description: string;
  affectedNodes?: string[];
}

export const validateWorkflowWithGroq = async (
  nodes: Node[],
  edges: Edge[]
): Promise<ValidationResult> => {
  try {
    // Prepare workflow data for the backend API
    const workflowData = {
      nodes: nodes.map(node => ({
        id: node.id,
        type: node.type,
        data: node.data,
        position: node.position
      })),
      edges: edges.map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle
      }))
    };

    // Call the backend API for validation
    const response = await fetch('/api/validate-workflow/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(workflowData),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const validationResult = await response.json();
    
    return {
      isValid: validationResult.isValid,
      report: validationResult.report,
      errors: validationResult.errors || [],
      warnings: validationResult.warnings || []
    };
  } catch (error) {
    console.error('Error validating workflow:', error);
    return {
      isValid: false,
      report: `❌ Error: Failed to validate workflow. ${error.message}`,
      errors: [{
        id: 'API_ERROR',
        name: 'api_error',
        description: `Failed to call validation API: ${error.message}`
      }],
      warnings: []
    };
  }
};

// The report formatting is now handled by the backend
