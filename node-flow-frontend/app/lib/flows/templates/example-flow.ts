import { FlowConfig } from '../index';

export const exampleFlow: FlowConfig = {
  id: 'example-flow',
  name: 'UU Consultative Sales Process',
  description: 'A consultative sales process that guides clients through needs assessment to proposal generation',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  config: {
    nodes: [
      {
        id: "start-1",
        type: "start",
        position: { x: 0, y: 0 },
        data: {
          label: "Start Process",
          description: "Start Process",
          owner: "Internal (System)",
          action: "Initiate process at Monday, 12 PM",
          conditions: [
            "Trigger happens based on the system clock"
          ],
          validation: {
            requiredFields: [
              "startTime",
              "dayOfWeek"
            ],
            fieldTypes: {
              "startTime": "time",
              "dayOfWeek": "string"
            }
          }
        }
      },
      {
        id: "action-1",
        type: "action",
        position: { x: 250, y: 0 },
        data: {
          label: "Initiate Consultative Session",
          description: "Initiate Consultative Session",
          owner: "Internal (UU System)",
          action: "Start a consultative session for client needs assessment",
          conditions: [
            "Client interaction is needed to provide input"
          ],
          validation: {
            requiredFields: [
              "sessionId",
              "clientId"
            ],
            fieldTypes: {
              "sessionId": "string",
              "clientId": "string"
            }
          }
        }
      },
      {
        id: "humanTask-1",
        type: "humanTask",
        position: { x: 500, y: 0 },
        data: {
          label: "Ask Clarifying Questions",
          description: "Ask Clarifying Questions",
          owner: "Internal (UU System, AI Bot)",
          action: "Send series of clarifying questions to client",
          conditions: [
            "Requires client interaction",
            "Questions tailored based on previous responses"
          ],
          validation: {
            requiredFields: [
              "questions",
              "clientResponses"
            ],
            fieldTypes: {
              "questions": "array",
              "clientResponses": "object"
            }
          }
        }
      },
      {
        id: "dataStore-1",
        type: "dataStore",
        position: { x: 750, y: 0 },
        data: {
          label: "Analyze Client Needs",
          description: "Analyze Client Needs",
          owner: "Internal (UU System)",
          action: "Process client responses and identify key needs",
          conditions: [
            "Internal Logic: Based on client input"
          ],
          validation: {
            requiredFields: [
              "clientInput",
              "analysisResult"
            ],
            fieldTypes: {
              "clientInput": "object",
              "analysisResult": "object"
            }
          }
        }
      },
      {
        id: "decision-1",
        type: "decision",
        position: { x: 1000, y: 0 },
        data: {
          label: "Need More Information?",
          description: "Need More Information?",
          owner: "Internal (UU System)",
          action: "Determine if additional information is needed",
          conditions: [
            "Based on completeness of client data"
          ],
          validation: {
            requiredFields: [
              "dataCompleteness"
            ],
            fieldTypes: {
              "dataCompleteness": "boolean"
            }
          }
        }
      },
      {
        id: "humanTask-2",
        type: "humanTask",
        position: { x: 1250, y: 0 },
        data: {
          label: "Additional Clarifying Questions",
          description: "Additional Clarifying Questions",
          owner: "Internal (UU System, AI Bot)",
          action: "Ask follow-up questions for missing information",
          conditions: [
            "Client Data Required"
          ],
          validation: {
            requiredFields: [
              "additionalQuestions",
              "responses"
            ],
            fieldTypes: {
              "additionalQuestions": "array",
              "responses": "object"
            }
          }
        }
      },
      {
        id: "action-2",
        type: "action",
        position: { x: 1500, y: 0 },
        data: {
          label: "Needs Assessment Completed",
          description: "Needs Assessment Completed",
          owner: "Internal (UU System)",
          action: "Finalize client's needs assessment",
          conditions: [
            "Data Completeness Required"
          ],
          validation: {
            requiredFields: [
              "assessmentData"
            ],
            fieldTypes: {
              "assessmentData": "object"
            }
          }
        }
      },
      {
        id: "action-3",
        type: "action",
        position: { x: 1750, y: 0 },
        data: {
          label: "Generate Proposal A/B",
          description: "Generate Proposal A/B",
          owner: "Internal (UU System)",
          action: "Generate customized proposals based on needs assessment",
          conditions: [
            "Image Storage Repository Access Required",
            "Client Inputs Required"
          ],
          validation: {
            requiredFields: [
              "proposalA",
              "proposalB",
              "images"
            ],
            fieldTypes: {
              "proposalA": "object",
              "proposalB": "object",
              "images": "array"
            }
          }
        }
      },
      {
        id: "action-4",
        type: "action",
        position: { x: 2000, y: 0 },
        data: {
          label: "Send Proposal A/B to Client",
          description: "Send Proposal A/B to Client",
          owner: "Internal (UU System)",
          action: "Send generated proposals to the client for review",
          conditions: [
            "Client Data Required"
          ],
          validation: {
            requiredFields: [
              "clientEmail"
            ],
            fieldTypes: {
              "clientEmail": "string"
            }
          }
        }
      },
      {
        id: "action-5",
        type: "action",
        position: { x: 2250, y: 0 },
        data: {
          label: "Confirm Vendor Availability",
          description: "Confirm Vendor Availability",
          owner: "Internal (UU System)",
          action: "Send availability requests to multiple vendors and wait for confirmation",
          conditions: [
            "Vendor Availability Confirmation Required"
          ],
          validation: {
            requiredFields: [
              "vendorAvailability"
            ],
            fieldTypes: {
              "vendorAvailability": "array"
            }
          }
        }
      },
      {
        id: "action-6",
        type: "action",
        position: { x: 2500, y: 0 },
        data: {
          label: "Confirm Payment Received",
          description: "Confirm Payment Received",
          owner: "Internal (Admin)",
          action: "Admin verifies payment receipt before proceeding",
          conditions: [
            "Payment Confirmation from Admin Required"
          ],
          validation: {
            requiredFields: [
              "paymentStatus"
            ],
            fieldTypes: {
              "paymentStatus": "boolean"
            }
          }
        }
      },
      {
        id: "end-1",
        type: "end",
        position: { x: 2750, y: 0 },
        data: {
          label: "End Process",
          description: "End Process",
          owner: "Internal (System)",
          action: "Complete the workflow process",
          conditions: [
            "All steps completed"
          ],
          validation: {
            requiredFields: [
              "finalStatus"
            ],
            fieldTypes: {
              "finalStatus": "string"
            }
          }
        }
      }
    ],
    connections: [
      {
        from: "start-1",
        to: "action-1",
        type: "sequence",
        label: "Start"
      },
      {
        from: "action-1",
        to: "humanTask-1",
        type: "sequence",
        label: "Human Task"
      },
      {
        from: "humanTask-1",
        to: "dataStore-1",
        type: "sequence",
        label: "Data Store"
      },
      {
        from: "dataStore-1",
        to: "decision-1",
        type: "sequence",
        label: "Decision"
      },
      {
        from: "decision-1",
        to: "humanTask-2",
        type: "condition",
        label: "Need More Info"
      },
      {
        from: "decision-1",
        to: "action-2",
        type: "condition",
        label: "Info Complete"
      },
      {
        from: "humanTask-2",
        to: "dataStore-1",
        type: "sequence",
        label: "Data Store"
      },
      {
        from: "action-2",
        to: "action-3",
        type: "sequence",
        label: "Action"
      },
      {
        from: "action-3",
        to: "action-4",
        type: "sequence",
        label: "Action"
      },
      {
        from: "action-4",
        to: "action-5",
        type: "sequence",
        label: "Action"
      },
      {
        from: "action-5",
        to: "action-6",
        type: "sequence",
        label: "Action"
      },
      {
        from: "action-6",
        to: "end-1",
        type: "sequence",
        label: "End"
      }
    ]
  }
};


