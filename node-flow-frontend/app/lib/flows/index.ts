import { exampleFlow } from "./templates/example-flow";
// Note: getItem and setItem are not being used, but keeping for future use
// import { getItem, setItem } from '../storage';

export interface FlowConfig {
  id: string;
  name: string;
  description: string;
  config: {
    nodes: Record<string, unknown>[];
    connections: Record<string, unknown>[];
  };
  createdAt: string;
  updatedAt: string;
}

export const staticFlowConfigs: Record<string, FlowConfig> = {
  "example-flow": exampleFlow,
};

export function getFlowById(id: string): FlowConfig | null {
  if (typeof window === "undefined") return null;

  try {
    const savedFlows = JSON.parse(localStorage.getItem("flows") || "{}");
    console.log("Loading flow:", id);
    console.log("Found flow:", savedFlows[id]);
    return savedFlows[id] || null;
  } catch (error) {
    console.error("Error loading flow:", error);
    return null;
  }
}

export function getAllFlows(): FlowConfig[] {
  // Check if we're on the client side
  if (typeof window === "undefined") {
    return [];
  }

  try {
    const savedFlows = JSON.parse(
      localStorage.getItem("flows") || "{}"
    ) as Record<string, FlowConfig>;
    const staticFlows = Object.values(staticFlowConfigs);

    return [...staticFlows, ...Object.values(savedFlows)];
  } catch (error) {
    console.error("Error loading flows:", error);
    return [];
  }
}

export function saveFlow(flow: FlowConfig) {
  if (typeof window === "undefined") return;

  try {
    const savedFlows = JSON.parse(localStorage.getItem("flows") || "{}");
    console.log("Current saved flows:", savedFlows);
    console.log("Saving new flow:", flow);

    savedFlows[flow.id] = flow;
    localStorage.setItem("flows", JSON.stringify(savedFlows));

    // Verify save
    const verifyFlow = JSON.parse(localStorage.getItem("flows") || "{}")[
      flow.id
    ];
    console.log("Verified saved flow:", verifyFlow);
  } catch (error) {
    console.error("Error saving flow:", error);
  }
}
