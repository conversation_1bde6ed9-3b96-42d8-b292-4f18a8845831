"use client";

import { BaseNode, NodeConnection } from "@/types/nodes";

interface ExecutionContext {
  nodes: BaseNode[];
  connections: NodeConnection[];
  onNodeStatusChange: (
    nodeId: string,
    status: string,
    message?: string
  ) => void;
  onNodeComplete: (nodeId: string, output: unknown) => void;
  onError: (nodeId: string, error: Error) => void;
}

export class NodeExecutionEngine {
  private context: ExecutionContext;
  private executedNodes: Set<string> = new Set();
  private nodeOutputs: Map<string, unknown> = new Map();
  private loopCounts: Map<string, number> = new Map();

  constructor(context: ExecutionContext) {
    this.context = context;
  }

  async executeFlow(startNodeId: string) {
    try {
      await this.executeNode(startNodeId);
    } catch (error) {
      console.error("Flow execution failed:", error);
    }
  }

  private async executeNode(nodeId: string): Promise<void> {
    const node = this.context.nodes.find((n) => n.id === nodeId);
    if (!node) return;

    // Check for infinite loops
    const loopCount = (this.loopCounts.get(nodeId) || 0) + 1;
    this.loopCounts.set(nodeId, loopCount);

    if (loopCount > 10) {
      // Maximum loop count
      throw new Error(`Maximum loop count exceeded for node ${nodeId}`);
    }

    try {
      this.context.onNodeStatusChange(nodeId, "processing");

      // Execute node based on type
      const output = await this.executeNodeLogic(node);

      this.nodeOutputs.set(nodeId, output);
      this.executedNodes.add(nodeId);

      this.context.onNodeComplete(nodeId, output);
      this.context.onNodeStatusChange(nodeId, "completed");

      // Find and execute next nodes
      await this.executeNextNodes(nodeId, output);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      this.context.onNodeStatusChange(nodeId, "failed", errorMessage);
      this.context.onError(
        nodeId,
        error instanceof Error ? error : new Error(String(error))
      );
      throw error;
    }
  }

  private async executeNodeLogic(node: BaseNode): Promise<unknown> {
    switch (node.type) {
      case "api":
        return this.executeAPINode(node);
      case "googleSheets":
        return this.executeGoogleSheetsNode(node);
      case "crewAI":
        return this.executeCrewAINode(node);
      case "decision":
        return this.executeDecisionNode(node);
      case "humanTask":
        return this.executeHumanTaskNode(node);
      default:
        return null;
    }
  }

  private async executeAPINode(node: BaseNode): Promise<unknown> {
    // Implement API call logic
    const { url, method, body } = node.data;
    try {
      const response = await fetch(url, {
        method,
        body: JSON.stringify(body),
        headers: { "Content-Type": "application/json" },
      });
      return await response.json();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      throw new Error(`API call failed: ${errorMessage}`);
    }
  }

  private async executeGoogleSheetsNode(_node: BaseNode): Promise<unknown> {
    // Mock Google Sheets integration
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ data: "Mock Google Sheets data" });
      }, 1000);
    });
  }

  private async executeCrewAINode(_node: BaseNode): Promise<unknown> {
    // Mock CrewAI integration
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ result: "Mock CrewAI response" });
      }, 1500);
    });
  }

  private async executeDecisionNode(node: BaseNode): Promise<unknown> {
    const inputData = this.getNodeInputs(node.id);
    // Evaluate conditions
    const conditions = node.data.conditions || [];
    return conditions.some((condition) =>
      this.evaluateCondition(condition, inputData)
    );
  }

  private async executeHumanTaskNode(_node: BaseNode): Promise<unknown> {
    // Implement human task logic
    return new Promise((resolve) => {
      // In a real implementation, this would wait for human input
      setTimeout(() => {
        resolve({ status: "approved" });
      }, 2000);
    });
  }

  private async executeNextNodes(
    nodeId: string,
    output: unknown
  ): Promise<void> {
    const connections = this.context.connections.filter(
      (c) => c.sourceNodeId === nodeId
    );

    for (const connection of connections) {
      if (connection.type === "condition" && !output) continue;
      await this.executeNode(connection.targetNodeId);
    }
  }

  private getNodeInputs(nodeId: string): Record<string, unknown> {
    const inputConnections = this.context.connections.filter(
      (c) => c.targetNodeId === nodeId
    );
    return inputConnections.reduce((inputs, connection) => {
      inputs[connection.sourceNodeId] = this.nodeOutputs.get(
        connection.sourceNodeId
      );
      return inputs;
    }, {});
  }

  private evaluateCondition(condition: unknown, _data: unknown): boolean {
    // Implement condition evaluation logic
    // Note: Using eval is dangerous and should be replaced with a proper condition evaluator
    try {
      // For now, return false as a safe default
      return false;
    } catch {
      return false;
    }
  }
}
