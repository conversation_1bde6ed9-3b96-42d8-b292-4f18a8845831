import { NodeType, NodeConnection, BaseNode, NodeValidationResult } from '@/types/nodes';

const nodeConnectionRules: Record<NodeType, { maxIncoming: number; maxOutgoing: number }> = {
  start: { maxIncoming: 0, maxOutgoing: 1 },
  action: { maxIncoming: 1, maxOutgoing: 1 },
  decision: { maxIncoming: 1, maxOutgoing: -1 },
  api: { maxIncoming: 1, maxOutgoing: 1 },
  googleSheets: { maxIncoming: 1, maxOutgoing: 1 },
  crewAI: { maxIncoming: 1, maxOutgoing: 1 },
  humanTask: { maxIncoming: 1, maxOutgoing: 1 },
  parallel: { maxIncoming: 1, maxOutgoing: -1 },
  approval: { maxIncoming: 1, maxOutgoing: 2 },
  notification: { maxIncoming: 1, maxOutgoing: 1 },
  integration: { maxIncoming: 1, maxOutgoing: 1 },
  delay: { maxIncoming: 1, maxOutgoing: 1 },
  end: { maxIncoming: 1, maxOutgoing: 0 }
};

export function validateNodeConnections(
  node: BaseNode,
  connections: NodeConnection[]
): NodeValidationResult {
  const rules = nodeConnectionRules[node.type as NodeType];
  if (!rules) return { isValid: false, error: 'Invalid node type' };

  const incoming = connections.filter(conn => conn.targetNodeId === node.id);
  const outgoing = connections.filter(conn => conn.sourceNodeId === node.id);

  if (rules.maxIncoming !== -1 && incoming.length > rules.maxIncoming) {
    return {
      isValid: false,
      error: `Node can only have ${rules.maxIncoming} incoming connection(s)`
    };
  }

  if (rules.maxOutgoing !== -1 && outgoing.length > rules.maxOutgoing) {
    return {
      isValid: false,
      error: `Node can only have ${rules.maxOutgoing} outgoing connection(s)`
    };
  }

  return { isValid: true };
} 