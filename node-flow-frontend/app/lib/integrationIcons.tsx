import React from "react";
// Import common icons that we know exist in react-icons
import {
  SiGoogledrive,
  SiDropbox,
  SiAmazon,
  SiGooglesheets,
  SiNotion,
  SiGithub,
  SiAsana,
  SiTrello,
  SiWhatsapp,
  SiTelegram,
  SiInstagram,
  SiFacebook,
  SiSlack,
  SiGmail,
  SiDiscord,
  SiOpenai,
  SiPostgresql,
  SiMongodb,
  SiSupabase,
  SiZapier,
  SiStripe,
  SiShopify,
} from "react-icons/si";

// For any icons not available in Simple Icons or as fallbacks
import {
  FaRobot, // For Groq, Anthropic
  FaTools, // For Make (Integromat)
  FaCalendar, // For calendar services
  FaEnvelope, // For email/communication services
  FaMicrosoft, // For Microsoft services
  FaCode, // For development services
} from "react-icons/fa";

// Map integration IDs to their respective icon components
// Using a simplified approach with fallbacks to avoid import errors
export const integrationIconMap: Record<
  string,
  React.ComponentType<{ style?: React.CSSProperties }>
> = {
  // Cloud Storage
  "google-drive": SiGoogledrive,
  dropbox: SiDropbox,
  "azure-blob": FaMicrosoft,
  "aws-s3": SiAmazon,

  // Productivity
  "google-sheets": SiGooglesheets,
  "google-calendar": FaCalendar,
  xero: FaTools,
  zoho: FaTools,
  notion: SiNotion,
  calendly: FaCalendar,
  github: SiGithub,
  asana: SiAsana,
  trello: SiTrello,

  // Communication
  whatsapp: SiWhatsapp,
  telegram: SiTelegram,
  instagram: SiInstagram,
  facebook: SiFacebook,
  slack: SiSlack,
  gmail: SiGmail,
  twilio: FaEnvelope,
  discord: SiDiscord,

  // AI
  groq: FaRobot,
  openai: SiOpenai,
  anthropic: FaRobot,
  huggingface: FaCode,

  // Database
  postgres: SiPostgresql,
  mongodb: SiMongodb,
  supabase: SiSupabase,

  // Other
  n8n: FaTools,
  make: FaTools,
  zapier: SiZapier,
  stripe: SiStripe,
  shopify: SiShopify,
};

// Default icon color by category
export const categoryColors: Record<string, string> = {
  cloud: "#4285F4", // Google Blue
  productivity: "#34A853", // Google Green
  communication: "#EA4335", // Google Red
  ai: "#8E44AD", // Purple
  database: "#F39C12", // Orange
  other: "#7F8C8D", // Gray
};

// Function to get icon component for an integration
export const getIntegrationIcon = (
  integrationId: string,
  size: number = 24,
  color?: string
) => {
  const IconComponent = integrationIconMap[integrationId] || FaTools;
  return <IconComponent style={{ width: size, height: size, color }} />; // Using style prop instead of size
};
