import { NodeType } from '@/lib/nodeTypes';

interface BasicNode {
  id: string;
  type: string;
  position: { x: number; y: number };
}

interface DetailedNode extends Omit<BasicNode, 'position'> {
  from: { nodeId: string | null; type: string }[];
  to?: { nodeId: string | null; type: string }[];
  muted?: boolean;
  description: string;
  owner: string;
  action: string;
  conditions: string[];
  altFlow: { condition: string; action: string; nextNode: string }[];
  validation: {
    requiredFields: string[];
    fieldTypes: { [key: string]: string };
  };
}

interface BasicConnection {
  id: string;
  from: { nodeId: string; portType: string };
  to: { nodeId: string; portType: string };
}

interface DetailedConnection {
  from: string;
  to: string;
  type: string;
}

interface BasicFlowConfig {
  nodes: BasicNode[];
  connections: BasicConnection[];
}

interface DetailedFlowConfig {
  processFlow: DetailedNode[];
  connections: DetailedConnection[];
}

export function mergeFlowConfig(basicConfig: BasicFlowConfig, nodeTypes: NodeType[]): DetailedFlowConfig {
  const detailedNodes: DetailedNode[] = basicConfig.nodes.map(node => {
    const nodeType = nodeTypes.find(nt => nt.type === node.type);
    return {
      id: node.id,
      type: node.type,
      from: [],
      description: nodeType?.label || '',
      owner: '',
      action: '',
      conditions: [],
      altFlow: [],
      validation: {
        requiredFields: [],
        fieldTypes: {}
      },
      muted: false
    };
  });

  const detailedConnections: DetailedConnection[] = basicConfig.connections.map(conn => ({
    from: conn.from.nodeId,
    to: conn.to.nodeId,
    type: 'outputToInput'
  }));

  // Update 'from' and 'to' properties for each node
  detailedNodes.forEach(node => {
    node.from = detailedConnections
      .filter(conn => conn.to === node.id)
      .map(conn => ({ nodeId: conn.from, type: 'mandatory' }));

    if (node.type === 'terminationNode') {
      node.to = [{ nodeId: null, type: 'null' }];
    }
  });

  return {
    processFlow: detailedNodes,
    connections: detailedConnections
  };
}
