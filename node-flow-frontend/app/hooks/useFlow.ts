import { useCallback } from "react";
import { Node, Edge } from "reactflow";
// Note: FlowConfig is not being used, but keeping for future use
// import { FlowConfig } from "@/types/nodes";
import { getAllFlows, getFlowById, saveFlow } from "@/lib/flows";

export function useFlow() {
  const handleSaveFlow = useCallback(
    (name: string, nodes: Node[], edges: Edge[], flowId?: string) => {
      const flowConfig = {
        id: flowId || `flow-${Date.now()}`,
        name,
        description: "User created flow",
        config: {
          nodes: nodes.map((node) => ({
            ...node,
            position: { x: node.position.x, y: node.position.y },
            data: {
              ...node.data,
              inputs: node.data.inputs || [],
              outputs: node.data.outputs || [],
            },
          })),
          connections: edges.map((edge) => ({
            from: edge.source,
            to: edge.target,
            type: edge.type || "default",
            sourceHandle: edge.sourceHandle,
            targetHandle: edge.targetHandle,
            label: edge.label,
          })),
        },
        createdAt: flowId
          ? getFlowById(flowId)?.createdAt || new Date().toISOString()
          : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      saveFlow(flowConfig);
    },
    []
  );

  return {
    handleSaveFlow,
    getAllFlows,
    getFlowById,
  };
}
