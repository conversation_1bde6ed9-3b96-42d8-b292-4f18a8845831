{"name": "teamcraft.ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true next build"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "groq-sdk": "^0.23.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.452.0", "next": "14.2.15", "react": "^18", "react-dom": "^18", "react-icons": "^5.5.0", "reactflow": "^11.11.4", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}