# Workflow Validation Rules
# This file contains rules for validating workflow configurations
# Rules are categorized as errors (must be fixed) or warnings (recommended to fix)

# Definitions
definitions:
  workflow:
    description: "A directed graph of connected nodes representing a process flow"
    
  node:
    description: "A single step in a workflow with a specific type and purpose"
    
  connection:
    description: "A directed link between two nodes representing flow of execution"
    
  trigger_node:
    description: "A node that initiates a workflow (types: start, event)"
    
  termination_node:
    description: "A node that ends a workflow path (types: end, fallback)"
    
  reachable:
    description: "A node is reachable if there exists at least one path from a trigger node to it"
    
  cycle:
    description: "A path in the workflow that returns to a previously visited node"
    
  isolated_subgraph:
    description: "A connected group of nodes that has no connection to the main workflow"

# Error Rules - Critical issues that must be fixed
errors:
  - id: "E001"
    name: "missing_trigger"
    description: "Every workflow must have at least one trigger node"
    mechanics: "Check if the workflow contains at least one node of type 'start' or 'event'"
    
  - id: "E002"
    name: "missing_termination"
    description: "Every workflow must have at least one termination node"
    mechanics: "Check if the workflow contains at least one node of type 'end' or 'fallback'"
    
  - id: "E003"
    name: "dangling_node"
    description: "Non-termination nodes must have at least one outgoing connection"
    mechanics: "For each node that is not a termination node, check if it has at least one outgoing connection"
    
  - id: "E004"
    name: "unreachable_node"
    description: "All nodes must be reachable from a trigger node"
    mechanics: "Perform a graph traversal from all trigger nodes and verify all nodes are visited"
    
  - id: "E005"
    name: "invalid_connection_count"
    description: "Node connections must follow the defined connection rules for incoming/outgoing counts"
    mechanics: "For each node, check if the number of incoming and outgoing connections matches the rules for its type"
    
  - id: "E006"
    name: "duplicate_node_id"
    description: "All node IDs must be unique within a workflow"
    mechanics: "Check if any node ID appears more than once in the workflow"
    
  - id: "E007"
    name: "invalid_node_type"
    description: "All nodes must have a valid node type"
    mechanics: "Check if each node's type is in the list of defined node types"
    
  - id: "E008"
    name: "missing_required_data"
    description: "All nodes must have their required data fields populated"
    mechanics: "For each node, check if all required data fields for its type are present and non-empty"

# Warning Rules - Issues that should be addressed but don't prevent execution
warnings:
  - id: "W001"
    name: "isolated_subgraph"
    description: "Workflow contains isolated subgraphs"
    mechanics: "Identify connected components in the graph that are not connected to any trigger node"
    
  - id: "W002"
    name: "default_node_label"
    description: "Nodes should have descriptive custom labels"
    mechanics: "Check if node labels match default labels or are empty"
    
  - id: "W003"
    name: "complex_decision_node"
    description: "Decision nodes with many outgoing paths may be hard to maintain"
    mechanics: "Identify decision nodes with more than 3 outgoing connections"
    
  - id: "W004"
    name: "long_sequential_chain"
    description: "Long sequential chains of nodes may indicate need for restructuring"
    mechanics: "Identify linear sequences of more than 10 nodes without branching"
    
  - id: "W005"
    name: "missing_error_handling"
    description: "Critical nodes should have error handling paths"
    mechanics: "Check if action, integration, and data nodes have paths to fallback nodes"
    
  - id: "W006"
    name: "fork_without_join"
    description: "Fork nodes should have corresponding join nodes"
    mechanics: "For each fork node, verify there is at least one join node that collects its paths"
    
  - id: "W007"
    name: "potential_deadlock"
    description: "Join nodes may cause deadlocks if not all incoming paths can be activated"
    mechanics: "Check if join nodes have incoming paths that may not always be activated"
    
  - id: "W008"
    name: "missing_node_documentation"
    description: "Nodes should have descriptions explaining their purpose"
    mechanics: "Check if nodes have empty or default description fields"

# Connection Rules by Node Type
# These define the valid connection patterns for each node type
connection_rules:
  # Trigger nodes - start workflow execution
  trigger:
    types: ["start", "event"]
    max_incoming: 0  # Cannot have incoming connections
    max_outgoing: 1  # Must have exactly one outgoing connection
    
  # Processing nodes - perform actions
  processing:
    types: ["action", "integration"]
    max_incoming: 1  # Must have exactly one incoming connection
    max_outgoing: 1  # Must have exactly one outgoing connection
    
  # Decision nodes - branch workflow based on conditions
  decision:
    types: ["decision", "approval"]
    max_incoming: 1    # Must have exactly one incoming connection
    max_outgoing: -1   # Can have multiple outgoing connections (-1 = unlimited)
    min_outgoing: 2    # Should have at least two outgoing connections
    
  # Data nodes - handle data operations
  data:
    types: ["dataInput", "dataOutput", "dataStore"]
    max_incoming: 1  # Must have exactly one incoming connection
    max_outgoing: 1  # Must have exactly one outgoing connection
    
  # Flow control - split execution into parallel paths
  fork:
    types: ["fork"]
    max_incoming: 1    # Must have exactly one incoming connection
    max_outgoing: -1   # Can have multiple outgoing connections (-1 = unlimited)
    min_outgoing: 2    # Should have at least two outgoing connections
    
  # Flow join - synchronize parallel paths
  join:
    types: ["join"]
    min_incoming: 2    # Should have at least two incoming connections
    max_incoming: -1   # Can have multiple incoming connections (-1 = unlimited)
    max_outgoing: 1    # Must have exactly one outgoing connection
    
  # Termination nodes - end workflow execution
  termination:
    types: ["end", "fallback"]
    max_incoming: -1   # Can have multiple incoming connections (-1 = unlimited)
    max_outgoing: 0    # Cannot have outgoing connections
    
  # Special function nodes - perform specialized tasks
  special:
    types: ["humanTask", "notification", "audit"]
    max_incoming: 1  # Must have exactly one incoming connection
    max_outgoing: 1  # Must have exactly one outgoing connection

# Required Data Fields by Node Type
# These define the data fields that must be present for each node type
required_data:
  # Trigger nodes
  start:
    - label  # Display name for the node
    
  event:
    - label       # Display name for the node
    - parameters  # Event parameters (e.g., event type, conditions)
    
  # Processing nodes
  action:
    - label   # Display name for the node
    - action  # Action to perform
    
  integration:
    - label       # Display name for the node
    - integration # Integration type
    - parameters  # Integration parameters
    
  # Decision nodes
  decision:
    - label       # Display name for the node
    - conditions  # Decision conditions
    
  approval:
    - label  # Display name for the node
    - owner  # Person/role responsible for approval
    
  # Data nodes
  dataInput:
    - label   # Display name for the node
    - inputs  # Input data specifications
    
  dataOutput:
    - label    # Display name for the node
    - outputs  # Output data specifications
    
  dataStore:
    - label       # Display name for the node
    - operation   # Storage operation (read/write/update/delete)
    - parameters  # Storage parameters
    
  # Special nodes
  humanTask:
    - label        # Display name for the node
    - owner        # Person/role responsible for the task
    - description  # Detailed description of the task
