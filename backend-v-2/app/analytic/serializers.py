from rest_framework import serializers
import app.analytic.models as analytic_models


class IPAddressRecordSerializer:
    class Get(serializers.ModelSerializer):
        created_at = serializers.DateTimeField()
        updated_at = serializers.DateTimeField()

        class Meta:
            model = analytic_models.IPAddressRecord
            fields = [
                "id",
                "ip_address",
                "country",
                "created_by",
                "created_at",
                "updated_at",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = analytic_models.IPAddressRecord
            fields = ["ip_address", "country", "created_by", "created_at"]
