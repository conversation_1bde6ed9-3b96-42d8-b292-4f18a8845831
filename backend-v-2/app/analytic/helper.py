from django.db.models import Count
from django.utils import timezone

from datetime import datetime, timedelta
import app.utility.date_helper as utility_date_helper
from django.db.models.functions import TruncDate, TruncWeek


def get_dict_for_message(iar):
    iar_result = (
        iar.order_by("created_at")
        .values(
            "date",
            "messages_length",
        )
        .annotate(conversation_count=Count("id", distinct=True))
    )

    iar_result_dict = {}

    for res in iar_result:
        date = res["date"].date() if isinstance(res["date"], datetime) else res["date"]
        if iar_result_dict.get((date), None):
            iar_result_dict[date]["conversation_count"] += 1
            iar_result_dict[date]["messages_count"] += res["messages_length"]
        else:
            iar_result_dict[date] = {
                "conversation_count": 1,
                "messages_count": res["messages_length"],
            }

    # print("iar_result_dict: ", iar_result_dict)
    return iar_result_dict


def get_dict_for_cta(iar):
    iar_result = (
        iar.order_by("created_at")
        .values(
            "date",
            "reach_cta_count",
            "click_cta_count",
        )
        .annotate(conversation_count=Count("id", distinct=True))
    )
    # print("iar_result: ", iar_result)

    iar_result_dict = {}

    for res in iar_result:
        date = res["date"].date() if isinstance(res["date"], datetime) else res["date"]

        if iar_result_dict.get(date, None):
            iar_result_dict[date]["reach_cta_count"] += res["reach_cta_count"]
            iar_result_dict[date]["click_cta_count"] += res["click_cta_count"]
        else:
            iar_result_dict[date] = {
                "reach_cta_count": res["reach_cta_count"],
                "click_cta_count": res["click_cta_count"],
            }

    # iar_result_dict = {
    #     result["date"].date()
    #     if isinstance(result["date"], datetime.datetime)
    #     else result["date"]: {
    #         "reach_cta_count": result["reach_cta_count"],
    #         "click_cta_count": result["click_cta_count"],
    #     }
    #     for result in iar_result
    # }
    return iar_result_dict


def filter_ip_address_record(is_public, ip_address_records):
    if is_public == "true" or is_public == True:
        ip_address_records = ip_address_records.filter(created_by__isnull=True)
    else:
        ip_address_records = ip_address_records.filter(created_by__isnull=False)

    return ip_address_records


def filter_conversation_instances(is_public, conversation_instances):
    if is_public == "true" or is_public == True:
        conversation_instances = conversation_instances.filter(created_by__isnull=True)
    else:
        conversation_instances = conversation_instances.filter(created_by__isnull=False)

    return conversation_instances


def get_start_end_date_object(start_datetime, end_datetime):
    start_date = utility_date_helper.convert_datetime_to_desired_timezone_datetime(
        timezone.make_aware(
            datetime.strptime(start_datetime, utility_date_helper.FE_DATE_TIME_FORMAT),
            timezone=timezone.utc,
        )
    ).date()

    end_date = utility_date_helper.convert_datetime_to_desired_timezone_datetime(
        timezone.make_aware(
            datetime.strptime(end_datetime, utility_date_helper.FE_DATE_TIME_FORMAT),
            timezone=timezone.utc,
        )
        + timedelta(minutes=1)
    ).date()

    return start_date, end_date


def get_date_analytic_vars(start_datetime, end_datetime, is_start_end_only=False):
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=7)
    if start_datetime and end_datetime:
        start_date, end_date = get_start_end_date_object(start_datetime, end_datetime)
    if is_start_end_only:
        return start_date, end_date, "", [], []

    day_gap_count = (end_date - start_date).days
    truncate_method = (
        TruncWeek if int(day_gap_count) > 30 else TruncDate
    )  # truncweek always return Monday as start of date

    # Create a date range
    # 2024-02-02
    extra_end_date_list = []
    if truncate_method == TruncDate:
        date_range = [
            start_date + timedelta(days=i) for i in range(day_gap_count)
        ]  # no need +1 for the day_gap_count
    else:
        adjusted_start_date = start_date - timezone.timedelta(days=start_date.weekday())
        date_range = []
        for i in range(0, (end_date - adjusted_start_date).days // 7 + 1):
            date_range.append(adjusted_start_date + timezone.timedelta(weeks=i))
            extra_end_date_list.append(
                adjusted_start_date
                + timezone.timedelta(weeks=i + 1)
                - timezone.timedelta(days=1)
            )
    return start_date, end_date, truncate_method, date_range, extra_end_date_list
