from django.urls import path
import app.analytic.views as views

urlpatterns = [
    path("ip-address-record/list", views.IPAddressRecordView.List.as_view()),
    path("ip-address-record/create", views.IPAddressRecordView.Create.as_view()),
    path(
        "ip-address-record/custom-create",
        views.IPAddressRecordView.CustomCreate.as_view(),
    ),
    path("ip-address-record/<int:pk>", views.IPAddressRecordView.Get.as_view()),
    path(
        "ip-address-record/<int:pk>/delete", views.IPAddressRecordView.Delete.as_view()
    ),
    path(
        "ip-address-record/get-visitor-count",
        views.IPAddressRecordView.GetVisitorCount.as_view(),
    ),
    path(
        "ip-address-record/get-master-information",
        views.IPAddressRecordView.GetMasterInformation.as_view(),
    ),
    path(
        "ip-address-record/export-master-information",
        views.IPAddressRecordView.ExportMasterInformation.as_view(),
    ),
    path(
        "user/new-returning-user-count",
        views.UserView.GetReturningAndNewUserCount.as_view(),
    ),
    path("user/get-created-count", views.UserView.GetUserCreated.as_view()),
    path(
        "conversation/get-created-per-day-count",
        views.ConversationView.GetConversationCreatedPerDayCount.as_view(),
    ),
    path(
        "conversation/get-length-distribution-count",
        views.ConversationView.GetConversationLengthDistributionCount.as_view(),
    ),
    path(
        "conversation/get-appt-made-per-source",
        views.ConversationView.GetAppointmentMadePerSource.as_view(),
    ),
    # path(
    #     "conversation/get-word-cloud-topic",
    #     views.ConversationView.GetWordCloudTopic.as_view(),
    # ),
    path(
        "conversation/get-question-distribution-count",
        views.ConversationView.GetQuestionDistributionCount.as_view(),
    ),
    path(
        "conversation/get-positive-sentiment-analysis-heatmap-data",
        views.ConversationView.GetPositiveSentimentAnalysisHeatMapData.as_view(),
    ),
    path(
        "conversation/get-negative-sentiment-analysis-heatmap-data",
        views.ConversationView.GetNegativeSentimentAnalysisHeatMapData.as_view(),
    ),
    path(
        "conversation/topic-frequency-analysis",
        views.ConversationView.GetTopicFrequencyAnalysisData.as_view(),
    ),
    path(
        "conversation/get-top-keywords-for-positive-sentiment",
        views.ConversationView.GetTopKeywordsForPositiveSentiment.as_view(),
    ),
    path(
        "conversation/get-top-keywords-for-negative-sentiment",
        views.ConversationView.GetTopKeywordsForNegativeSentiment.as_view(),
    ),
    path(
        "conversation/get-conversation-over-time-box-plot",
        views.ConversationView.GetConversationBoxPlot.as_view(),
    ),
    path(
        "conversation/get-pop-up-conversation-table",
        views.MessageAnalyticsView.List.as_view(),
    ),
    path(
        "conversation/get-pop-up-conversation-table-csv-file",
        views.MessageAnalyticsView.GetMessageAnalyticsCsvFile.as_view(),
    ),
    path(
        "conversation/get-main-conversation-table",
        views.ConversationAnalyticsView.List.as_view(),
    ),
    path(
        "conversation/get-main-conversation-table-csv-file",
        views.ConversationAnalyticsView.GetConversationAnalyticsCsvFile.as_view(),
    ),
    path("ip-address-record/get-countries", views.IPAddressRecordView.GetCountryList.as_view()),
]
