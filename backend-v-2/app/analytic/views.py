from collections import Counter, defaultdict
import re
import string
import pandas as pd
import app.analytic.helper as analytic_helper
import app.analytic.models as analytic_models
import app.analytic.serializers as analytic_serializers

import app.core.authapi.permission as authapi_permission
import app.core.authapi.helper as authapi_helper

# import app.core.authapi.serializers as authapi_serializers
import app.chats.chat.models as chat_models
import app.chats.chat_action.models as chat_action_models
import app.chats.chat_action_tracker.models as chat_action_tracker_models

import app.chats.chat.serializers as chat_serializers
import app.documents.document.models as document_models

import app.integrations.facebook_integration.models as facebook_integration_models

from rest_framework import generics, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response

import app.utility.models as utility_models
import app.utility.common_class as utility_common_class

from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.db.models import (
    <PERSON>,
    Count,
    Min,
    Max,
    OuterRef,
    Subquery,
    F,
    fields,
    Count,
    DateField,
    ExpressionWrapper,
    Case,
    When,
    Value,
    F,
    Count,
    Char<PERSON>ield,
)

from django.db.models.functions import (
    TruncDate,
    TruncWeek,
    ExtractSecond,
    ExtractDay,
    Concat,
    Coalesce,
    LTrim
)
from django.utils import timezone

from datetime import datetime, timedelta
from nltk.corpus import stopwords
from rest_framework.filters import SearchFilter, OrderingFilter
from django.contrib.postgres.aggregates import ArrayAgg, StringAgg 
import requests
import json
import ipaddress
import csv
import geoip2.database

User = get_user_model()


def get_ip_address(request):
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        # proxies can forward multiple IP addresses
        ip_address = x_forwarded_for.split(",")[0]
    else:
        # if no proxy, this is the user's IP
        ip_address = request.META.get("REMOTE_ADDR")
    return ip_address


def is_valid_ip(ip):
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False


# def get_country(ip_address):
#     try:
#         response = requests.get(
#             f"https://geolocation-db.com/jsonp/{ip_address}", timeout=1
#         )  # Set timeout to 1 seconds
#         json_text = response.text.split("(")[1].strip(")")
#         data = json.loads(json_text)
#         return data["country_name"]
#     except requests.exceptions.Timeout:
#         print("Geolocation: The request timed out after 1 seconds.")
#         return "No Record"
#     except Exception as e:
#         print(f"An error occurred: {e}")
#         return "No Record"

def get_country(ip_address):
    try:
        if ip_address == "127.0.0.1":
            print("Skipping the process to get country.")
            return "No Record"
        
        # Get Country By IP
        database_path = './analytic/ip_database/GeoLite2-Country.mmdb'
        reader = geoip2.database.Reader(database_path)
        response = reader.country(ip_address)
        reader.close()
        
        return response.country.name
    
    except Exception as e:
        print("An unexpected error occurred: %s" % str(e))
        return "No Record"

# triggered on 1 place:
# 1. When create new conversation through new message


def get_user_id_ip_address_country(request):
    ip_address = get_ip_address(request)
    if request.user.is_authenticated:
        user_id = request.user.id
    else:
        user_id = None

    if is_valid_ip(ip_address):
        country = get_country(ip_address)
        if country == "Not found":
            country = "No Record"

    return [user_id, ip_address, country]


def create_ip_address_record(request):
    user_id, ip_address, country = get_user_id_ip_address_country(request)

    res = analytic_models.IPAddressRecord.objects.create(
        created_by_id=user_id,
        ip_address=ip_address,
        country=country,
    )
    return res


def update_ip_address_record(request, conversation_id):
    user_id, ip_address, country = get_user_id_ip_address_country(request)
    # print("IP address: ", ip_address)

    ip_address_record_instance = analytic_models.IPAddressRecord.objects.filter(
        ip_address=ip_address, conversation_id=conversation_id
    ).first()

    if not ip_address_record_instance:
        ip_address_record_instance = analytic_models.IPAddressRecord.objects.filter(
            ip_address=ip_address, conversation__isnull=True
        ).first()
        if not ip_address_record_instance:
            print("creating a brand new ip address")
            ip_address_record_instance = create_ip_address_record(request)

    # print("res here: ", res.id)
    ip_address_record_instance.conversation = chat_models.Conversation.objects.get(
        id=conversation_id
    )
    ip_address_record_instance.save()

    return ip_address_record_instance


# Create your views here.
class IPAddressRecordView:
    class IPAddressRecordBaseView(generics.GenericAPIView):
        queryset = analytic_models.IPAddressRecord.objects.all()
        serializer_class = analytic_serializers.IPAddressRecordSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]
        pagination_class = utility_common_class.CustomPageNumberPagination

    class List(IPAddressRecordBaseView, generics.ListAPIView):
        def get_queryset(self):
            queryset = super().get_queryset()
            today = timezone.now()
            if last_n_days := self.request.query_params.get("last_n_days", None):
                queryset = queryset.filter(
                    created_at__gte=today - timezone.timedelta(days=int(last_n_days))
                )

            return queryset

    class Get(IPAddressRecordBaseView, generics.RetrieveAPIView):
        pass
    
    class GetCountryList(IPAddressRecordBaseView, generics.GenericAPIView):
        def get(self, request, *args, **kwargs):
            queryset = super().get_queryset()
            country_list = sorted(
                set(queryset.values_list("country", flat=True)),
                key=lambda x: (x == "No Record", x)
            )
            return Response(list(country_list))

    # Probably will get triggered through automation without triggering this
    class Create(IPAddressRecordBaseView, generics.CreateAPIView):
        permission_classes = [AllowAny]
        serializer_class = analytic_serializers.IPAddressRecordSerializer.Post

        def post(self, request, *args, **kwargs):
            ip_address = get_ip_address(request)
            if self.request.user.is_authenticated:
                request.data["created_by"] = self.request.user.id

            if is_valid_ip(ip_address):
                country = get_country(ip_address)
                if country == "Not found":
                    country = "No Record"
                request.data["country"] = country

            return super().post(request, *args, **kwargs)

    class CustomCreate(APIView):
        permission_classes = [AllowAny]

        def post(self, request, *args, **kwargs):
            instance = create_ip_address_record(request)

            return Response(
                data=analytic_serializers.IPAddressRecordSerializer.Get(instance).data,
                status=status.HTTP_200_OK,
            )

    class GetVisitorCount(APIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def get(self, request, *args, **kwargs):
            """
            Return:
            [
                {
                    "date": "2023-08-12",
                    "countries": [
                        {
                            "name": string,
                            "count": number,
                        }
                    ]
                },
            ]
            """
            today = timezone.now()
            ip_address_records = analytic_models.IPAddressRecord.objects.all()
            last_n_days = self.request.query_params.get("last_n_days", None)

            if last_n_days:
                start_date = today - timezone.timedelta(days=int(last_n_days))
                ip_address_records = ip_address_records.filter(
                    created_at__gte=start_date
                )
            else:
                start_date = (
                    ip_address_records.order_by("created_at").first().created_at
                )

            ip_address_records = ip_address_records.annotate(
                date=TruncDate("created_at")
            )

            results = (
                ip_address_records.values("date", "country")
                .annotate(count=Count("id", distinct=True))
                .order_by("date", "country")
            )

            # Create a dictionary from results for easy access and a set of countries
            results_dict = {
                (result["date"], result["country"]): result["count"]
                for result in results
            }
            countries = {result["country"] for result in results}

            # Generate list of dates from `start_date` to `today`
            date_list = [
                start_date.date() + timezone.timedelta(days=i)
                for i in range((today - start_date).days + 1)
            ]

            # Combine `date_list` with results
            combined_results = []

            for date in date_list:
                date_record = {"date": date, "countries": []}
                for country in countries:
                    country_record = {
                        "name": country if country else "No Record",
                        "count": results_dict.get((date, country), 0),
                    }
                    date_record["countries"].append(country_record)
                combined_results.append(date_record)

            return Response(data=combined_results, status=status.HTTP_200_OK)

    class GetMasterInformation(APIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def get(self, request, *args, **kwargs):
            """
            Return:
            [
                {
                    "date": "2023-08-12",
                    "landing_chat_count": number,
                    "start_chat_count": number,
                    "reach_cta_count": number,
                    "click_cta_count": number,
                },
            ]
            """
            today = timezone.now()
            ip_address_records = analytic_models.IPAddressRecord.objects.all()
            last_n_days = self.request.query_params.get("last_n_days", None)
            is_public = self.request.query_params.get("is_public", True)
            ip_address_records = analytic_helper.filter_ip_address_record(
                is_public, ip_address_records
            )

            if last_n_days:
                start_date = today - timezone.timedelta(days=int(last_n_days))
                ip_address_records = ip_address_records.filter(
                    created_at__gte=start_date
                )
            else:
                start_date = (
                    ip_address_records.order_by("created_at").first().created_at
                )
                last_n_days = (today - start_date).days

            truncate_method = (
                TruncWeek if int(last_n_days) > 30 else TruncDate
            )  # truncweek always return Monday as start of date

            iar_for_cta = ip_address_records.annotate(
                date=truncate_method("created_at"),
                reach_cta_count=Count(
                    "download_material_button_record",
                ),
                click_cta_count=Count(
                    "download_material_button_record",
                    filter=Q(download_material_button_record__clicked_at__isnull=False),
                ),
            )

            iar_for_msg = ip_address_records.annotate(
                date=truncate_method("created_at"),
                messages_length=Count(
                    "conversation__messages",
                    filter=Q(
                        conversation__messages__message_type=utility_models.USER_TEXT
                    )
                    | Q(
                        conversation__messages__message_type=utility_models.ANONYMOUS_TEXT
                    ),
                ),
            )

            iar_with_zero_message = iar_for_msg.filter(messages_length=0)
            iar_with_more_than_zero_message = iar_for_msg.filter(messages_length__gt=0)

            zero_message_dict = analytic_helper.get_dict_for_message(
                iar_with_zero_message
            )
            one_message_dict = analytic_helper.get_dict_for_message(
                iar_with_more_than_zero_message
            )
            cta_dict = analytic_helper.get_dict_for_cta(iar_for_cta)

            # Generate list of dates from `start_date` to `today`

            if truncate_method == TruncDate:
                periods = [
                    (start_date + timezone.timedelta(days=i)).date()
                    for i in range((today - start_date).days + 1)
                ]
            else:
                adjusted_start_date = start_date - timezone.timedelta(
                    days=start_date.weekday()
                )
                periods = [
                    (
                        (adjusted_start_date + timezone.timedelta(weeks=i)).date(),
                        (
                            adjusted_start_date
                            + timezone.timedelta(weeks=i + 1)
                            - timezone.timedelta(days=1)
                        ).date(),
                    )
                    for i in range(0, (today - adjusted_start_date).days // 7 + 1)
                ]

            # date_list = [
            #     start_date.date() + timezone.timedelta(days=i)
            #     for i in range((today - start_date).days + 1)
            # ]

            # Combine `date_list` with results
            combined_results = []
            for period in periods:
                if isinstance(period, tuple):  # if it's a week
                    start_week, end_week = period
                    week_key = start_week
                    date_str = f"{start_week} - {end_week}"
                else:  # if it's a day
                    week_key = period
                    date_str = week_key

                landing_chat_count = zero_message_dict.get(week_key, {}).get(
                    "conversation_count", 0
                )
                start_chat_count = one_message_dict.get(week_key, {}).get(
                    "conversation_count", 0
                )
                total_message_count = one_message_dict.get(week_key, {}).get(
                    "messages_count", 0
                )
                reach_cta_count = cta_dict.get(week_key, {}).get("reach_cta_count", 0)
                click_cta_count = cta_dict.get(week_key, {}).get("click_cta_count", 0)

                date_record = {
                    "date": date_str,
                    "landing_chat_count": landing_chat_count,  # not related to them
                    "start_chat_count": start_chat_count - reach_cta_count,
                    "reach_cta_count": reach_cta_count - click_cta_count,
                    "click_cta_count": click_cta_count,
                    "total_message_count": total_message_count,
                }
                combined_results.append(date_record)
            return Response(data=combined_results, status=status.HTTP_200_OK)

    class ExportMasterInformation(APIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def get(self, request, *args, **kwargs):
            """
            Return:
            CSV: Date (Day), Landing Chat Count, Start Chat Count, Reach CTA Count, Click CTA Count, Total Question Asked
            """
            today = timezone.now()
            ip_address_records = analytic_models.IPAddressRecord.objects.all()

            is_public = self.request.query_params.get("is_public", True)
            ip_address_records = analytic_helper.filter_ip_address_record(
                is_public, ip_address_records
            )

            last_n_days = self.request.query_params.get("last_n_days", None)

            if last_n_days:
                start_date = today - timezone.timedelta(days=int(last_n_days))
                ip_address_records = ip_address_records.filter(
                    created_at__gte=start_date
                )
            else:
                start_date = (
                    ip_address_records.order_by("created_at").first().created_at
                )
                last_n_days = (today - start_date).days

            iar_for_cta = ip_address_records.annotate(
                date=TruncDate("created_at"),
                reach_cta_count=Count(
                    "download_material_button_record",
                ),
                click_cta_count=Count(
                    "download_material_button_record",
                    filter=Q(download_material_button_record__clicked_at__isnull=False),
                ),
            )

            iar_for_msg = ip_address_records.annotate(
                date=TruncDate("created_at"),
                messages_length=Count(
                    "conversation__messages",
                    filter=Q(
                        conversation__messages__message_type=utility_models.USER_TEXT
                    )
                    | Q(
                        conversation__messages__message_type=utility_models.ANONYMOUS_TEXT
                    ),
                ),
            )

            iar_with_zero_message = iar_for_msg.filter(messages_length=0)
            iar_with_more_than_zero_message = iar_for_msg.filter(messages_length__gt=0)

            zero_message_dict = analytic_helper.get_dict_for_message(
                iar_with_zero_message
            )
            one_message_dict = analytic_helper.get_dict_for_message(
                iar_with_more_than_zero_message
            )
            cta_dict = analytic_helper.get_dict_for_cta(iar_for_cta)

            # Generate list of dates from `start_date` to `today`

            periods = [
                (start_date + timezone.timedelta(days=i)).date()
                for i in range((today - start_date).days + 1)
            ]

            # Export to csv
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                'attachment; filename="question_asked_list.csv"'
            )

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Date",
                    "Landing Chat Count",
                    "Start Chat Count",
                    "Reach CTA Count",
                    "Click CTA Count",
                    "Total Question Asked",
                ]
            )

            # Initialize counters
            total_landing_chat_count = 0
            total_start_chat_count = 0
            total_total_message_count = 0
            total_reach_cta_count = 0
            total_click_cta_count = 0

            for period in periods:
                week_key = period
                date_str = week_key

                landing_chat_count = zero_message_dict.get(week_key, {}).get(
                    "conversation_count", 0
                )
                start_chat_count = one_message_dict.get(week_key, {}).get(
                    "conversation_count", 0
                )
                total_message_count = one_message_dict.get(week_key, {}).get(
                    "messages_count", 0
                )
                reach_cta_count = cta_dict.get(week_key, {}).get("reach_cta_count", 0)
                click_cta_count = cta_dict.get(week_key, {}).get("click_cta_count", 0)

                # Increment counters
                total_landing_chat_count += landing_chat_count
                total_start_chat_count += start_chat_count
                total_total_message_count += total_message_count
                total_reach_cta_count += reach_cta_count
                total_click_cta_count += click_cta_count

                writer.writerow(
                    [
                        date_str,
                        landing_chat_count,
                        start_chat_count,
                        reach_cta_count,
                        click_cta_count,
                        total_message_count,
                    ]
                )

            writer.writerow(
                [
                    "Total",
                    total_landing_chat_count,
                    total_start_chat_count,
                    total_reach_cta_count,
                    total_click_cta_count,
                    total_total_message_count,
                ]
            )

            return response

    class Delete(IPAddressRecordBaseView, generics.DestroyAPIView):
        pass


class UserView:
    class GetUserBaseView(generics.GenericAPIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class GetUserCreated(GetUserBaseView, APIView):
        def get(self, request, *args, **kwargs):
            today = timezone.now()
            # user_objects = User.objects.all()
            # if last_n_days := self.request.query_params.get('last_n_days', None):
            #     user_objects = user_objects.filter(created_at__gte=today-timezone.timedelta(days=int(last_n_days)))

            user_objects = User.objects.all()
            comparison_user_objects = User.objects.all()
            if last_n_days := self.request.query_params.get("last_n_days", None):
                last_n_days = int(last_n_days)
                user_objects = user_objects.filter(
                    created_at__gte=today - timezone.timedelta(days=last_n_days)
                )
                comparison_user_objects = comparison_user_objects.filter(
                    created_at__gte=today - timezone.timedelta(days=last_n_days * 2),
                    created_at__lt=today - timezone.timedelta(days=last_n_days),
                )

            user_objects_count = user_objects.count()
            comparison_user_objects_count = comparison_user_objects.count()
            percentage_diff = (
                0
                if comparison_user_objects_count == 0
                else round(
                    (
                        (user_objects_count - comparison_user_objects_count)
                        / comparison_user_objects_count
                    )
                    * 100,
                    2,
                )
            )

            # return Response(data=user_objects.count(), status=status.HTTP_200_OK)
            return Response(
                data={
                    "count": user_objects_count,
                    "percentage_diff": 0 if percentage_diff == 0 else percentage_diff,
                },
                status=status.HTTP_200_OK,
            )

    class GetReturningAndNewUserCount(GetUserBaseView, APIView):
        def get(self, request, *args, **kwargs):
            """
            Return:
              [
                {name: "date", returning_user_count: number, new_user_count: number, total_count: number},
                ...,
            ]
            """
            today = timezone.now()
            last_n_days = self.request.query_params.get("last_n_days", None)

            user_objects = User.objects.all()
            if last_n_days := self.request.query_params.get("last_n_days", None):
                start_date = today - timezone.timedelta(days=int(last_n_days))
                user_objects = user_objects.filter(
                    created_at__gte=today - timezone.timedelta(days=int(last_n_days))
                )
            else:
                start_date = user_objects.order_by("created_at").first().created_at

            # Annotate users with their IP address record count and the dates of their earliest and latest records
            user_objects = user_objects.annotate(
                first_ip_record_date=Min("ipaddressrecord_created_by__created_at"),
                last_ip_record_date=Max("ipaddressrecord_created_by__created_at"),
                date=TruncDate("created_at"),
            )

            # Filter out the returning users: those with latest and earliest records are at least one day apart
            returning_users = user_objects.filter(
                last_ip_record_date__date__gt=F("first_ip_record_date__date")
                + timedelta(days=1)
            )

            # Filter out the new users: those with exactly 1 IP address record
            new_users = user_objects.exclude(
                id__in=returning_users.values_list("id", flat=True)
            )

            returning_users_result = (
                returning_users.values("date")
                .annotate(count=Count("id", distinct=True))
                .order_by("date")
            )

            new_users_result = (
                new_users.values("date")
                .annotate(count=Count("id", distinct=True))
                .order_by("date")
            )

            # Determine the maximum length between the two lists
            max_length = max(len(returning_users_result), len(new_users_result))
            results_dict = {}
            for i in range(max_length):
                date = None
                # Get returning user count or 0 if not present
                if i < len(returning_users_result):
                    returning_count = returning_users_result[i].get("count", 0)
                    date = returning_users_result[i]["date"]
                else:
                    returning_count = 0

                # Get new user count or 0 if not present
                if i < len(new_users_result):
                    new_count = new_users_result[i].get("count", 0)

                    # If 'date' is not set from returning_users_result, set it from new_users_result
                    if not date:
                        date = new_users_result[i]["date"]
                else:
                    new_count = 0
                # Add to results dictionary
                results_dict[date] = [returning_count, new_count]

            # print("results_dict: ", results_dict)

            date_list = [
                start_date.date() + timezone.timedelta(days=i)
                for i in range((today - start_date).days + 1)
            ]

            # Combine `date_list` with results
            combined_results = []
            for date in date_list:
                date_record = {
                    "date": date,
                    "returning_user_count": results_dict.get(date, [0, 0])[0],
                    "new_user_count": results_dict.get((date), [0, 0])[1],
                }
                combined_results.append(date_record)

            return Response(
                data=combined_results,
                status=status.HTTP_200_OK,
            )


class ConversationView:
    class ConversationBaseView(generics.GenericAPIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class GetConversationCreatedPerDayCount(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # start_date = conversation_instances.last().created_at
            # if last_n_days := self.request.query_params.get("last_n_days", None):
            #     last_n_days = int(last_n_days)
            #     conversation_instances = conversation_instances.filter(
            #         created_at__gte=today - timezone.timedelta(days=last_n_days)
            #     )
            #     start_date = today - timedelta(days=last_n_days)

            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)

            # Query to group by date and sub_source
            conversation_instances = (
                chat_models.Conversation.objects.filter(
                    created_at__gte=start_date, created_at__lte=end_date
                )
                .annotate(date=truncate_method("created_at", output_field=DateField()))
                .values(
                    "date", "source", "sub_source"
                )  # Include sub_source in the values clause
                .annotate(count=Count("id"))
                .order_by("date", "source", "sub_source")
            )
            
            if country_lists:
                countries = country_lists.split(",")
                conversation_instances = conversation_instances.filter(
                    ip_address_record__country__in=countries
                )
            
            # Apply filter only if is_verified is not an empty string
            if is_verified:
                if is_verified.lower() == "true":
                    conversation_instances = conversation_instances.filter(
                        metadata__is_verified=True
                    )
                else:
                    conversation_instances = conversation_instances.filter(
                        Q(metadata__is_verified=False) | Q(metadata__is_verified__isnull=True)
                    )
                
            # Initialize the dictionary with dates as keys and empty dictionaries as values
            conversation_counts = {date: {} for date in date_range}
            schema_name = authapi_helper.get_schema_name()

            # Facebook only for now
            sub_source_to_page_name = {
                credential.PAGE_ID: credential.PAGE_NAME
                for credential in facebook_integration_models.Credential.objects.filter(
                    tenant__schema_name=schema_name
                )
            }

            # Update the nested dictionary with actual conversation counts, grouped by sub_source
            for conversation in conversation_instances:
                date = conversation["date"]
                source = conversation["source"]
                sub_source = sub_source_to_page_name.get(
                    conversation["sub_source"], conversation["sub_source"]
                )  # only fb use sub_source now, use raw sub_source if can't find match
                count = conversation["count"]

                is_facebook_stuff = source == utility_models.FACEBOOK

                if source not in conversation_counts[date]:
                    if is_facebook_stuff:
                        conversation_counts[date][source] = {}
                    else:
                        conversation_counts[date][source] = 0

                # Ensure the sub_source key exists for the given date
                if is_facebook_stuff:
                    conversation_counts[date][source][sub_source] = count
                    continue

                conversation_counts[date][source] = count

            # Preparing response data
            """
            [{
                "label": "2024-01-30",
                "Facebook": {
                    "Page 1": 2,
                    "Page 2": 2,
                    "Page 4": 1,
                    "Page 6": 1
                },
                "Web Application": 1,
                ...
            },
            ...
            ]...            
            """
            response_data = [
                {
                    "label": f"{date.strftime('%Y-%m-%d')}{(' to ' + extra_end_date_list[index].strftime('%Y-%m-%d')) if extra_end_date_list else ''}",
                    **conversation_counts[date],
                }
                for index, date in enumerate(date_range)
            ]

            return Response(
                data=response_data,
                status=status.HTTP_200_OK,
            )

    class GetQuestionDistributionCount(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            """
            Return:
              [
                {label: "<=2 Questions", value: number},
                {label: "3-5 Questions", value: number},
                {label: "6-8 Questions", value: number},
                {label: "9-11 Questions", value: number},
                {label: ">= 11 Questions", value: number},
            ]
            """
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, _, _, _ = analytic_helper.get_date_analytic_vars(
                start_datetime, end_datetime, is_start_end_only=True
            )
            country_lists = self.request.query_params.get("countries", None)

            conversation_instances = chat_models.Conversation.objects.filter(
                created_at__gte=start_date, created_at__lte=end_date
            ).annotate(
                question_count=Count(
                    "messages",
                    filter=Q(messages__message_type=utility_models.USER_TEXT)
                    | Q(messages__message_type=utility_models.ANONYMOUS_TEXT),
                ),
            )
            
            if country_lists:
                countries = country_lists.split(",")
                conversation_instances = conversation_instances.filter(
                    ip_address_record__country__in=countries
                )
                
            # Apply filter only if is_verified is not an empty string
            if is_verified:
                if is_verified.lower() == "true":
                    conversation_instances = conversation_instances.filter(
                        metadata__is_verified=True
                    )
                else:
                    conversation_instances = conversation_instances.filter(
                        Q(metadata__is_verified=False) | Q(metadata__is_verified__isnull=True)
                    )
                
            intervals = [
                (0, 2, "<=2 questions"),
                (3, 5, "3-5 questions"),
                (6, 8, "6-8 questions"),
                (9, 11, "9-11 questions"),
                (
                    11,
                    999,
                    ">=11 questions",
                ),  # if continue the session after 12 hours  then no need to consider
            ]
            master_data = []
            for start, end, label in intervals:
                temp_data = {
                    "label": label,
                    "value": conversation_instances.filter(
                        Q(question_count__gte=start) & Q(question_count__lte=end)
                    ).count(),
                }
                master_data.append(temp_data)
            # master_data = [
            #     {"label": "<=2 questions", "value": 500},
            #     {"label": "3-5 questions", "value": 300},
            #     {"label": "6-8 questions", "value": 200},
            #     {"label": "9-11 questions", "value": 100},
            #     {"label": ">=11 questions", "value": 80},
            # ]
            return Response(
                data=master_data,
                status=status.HTTP_200_OK,
            )

    class GetConversationLengthDistributionCount(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            """
            Return:
              [
                {label: "Quick Queries (<2 minutes)", value: number},
                {label: "Standard Interactions(2-10 minutes)", value: number},
                {label: "In-depth Conversations(10-30 minutes)", value: number},
                {label: "Extended Engagements (30-60 minutes)", value: number},
                {label: "Marathon Sessions (>60 minutes)", value: number},
            ]
            """
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, _, _, _ = analytic_helper.get_date_analytic_vars(
                start_datetime, end_datetime, is_start_end_only=True
            )
            country_lists = self.request.query_params.get("countries", None)

            conversation_instances = chat_models.Conversation.objects.filter(
                created_at__gte=start_date, created_at__lte=end_date
            )

            if country_lists:
                countries = country_lists.split(",")
                conversation_instances = conversation_instances.filter(
                    ip_address_record__country__in=countries
                )
                
            # Apply filter only if is_verified is not an empty string
            if is_verified:
                if is_verified.lower() == "true":
                    conversation_instances = conversation_instances.filter(
                        metadata__is_verified=True
                    )
                else:
                    conversation_instances = conversation_instances.filter(
                        Q(metadata__is_verified=False) | Q(metadata__is_verified__isnull=True)
                    )
                
            first_message_time = (
                chat_models.Message.objects.filter(conversation=OuterRef("pk"))
                .order_by("created_at")
                .values("created_at")[:1]
            )

            last_message_time = (
                chat_models.Message.objects.filter(conversation=OuterRef("pk"))
                .order_by("-created_at")
                .values("created_at")[:1]
            )
            # Annotate with the start_time and end_time
            conversation_instances = (
                conversation_instances.annotate(
                    start_time=Subquery(
                        first_message_time, output_field=fields.DateTimeField()
                    ),
                    end_time=Subquery(
                        last_message_time, output_field=fields.DateTimeField()
                    ),
                )
                .annotate(
                    duration=ExpressionWrapper(
                        F("end_time") - F("start_time"),
                        output_field=fields.DurationField(),
                    )
                )
                .annotate(
                    session_length_seconds=ExpressionWrapper(
                        ExtractDay(F("duration")) * 86400
                        + ExtractSecond(  # Days to seconds
                            F("duration")
                        ),  # Add the remainder seconds
                        output_field=fields.IntegerField(),  # Or IntegerField, depending on the precision you need
                    )
                )
            )

            # for c in conversation_instances:
            #     print(f"{c.id} - {c.session_length_seconds}")

            intervals = [
                (0, 2, "<2 minutes"),
                (2, 10, "2-10 minutes"),
                (10, 30, "10-30 minutes"),
                (30, 60, "30-60 minutes"),
                (
                    60,
                    720,
                    ">60 minutes",
                ),  # if continue the session after 12 hours  then no need to consider
            ]
            master_data = []
            for start, end, label in intervals:
                temp_data = {
                    "label": label,
                    "value": conversation_instances.filter(
                        Q(session_length_seconds__gt=start * 60)
                        & Q(session_length_seconds__lte=end * 60)
                    ).count(),
                }
                master_data.append(temp_data)
            # master_data = [
            #     {"label": "<2 minutes", "value": 200},
            #     {"label": "2-10 minutes", "value": 400},
            #     {"label": "10-30 minutes", "value": 300},
            #     {"label": "30-60 minutes", "value": 100},
            #     {"label": ">60 minutes", "value": 75},
            # ]
            return Response(
                data=master_data,
                status=status.HTTP_200_OK,
            )

    class GetAppointmentMadePerSource(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # start_date = conversation_instances.last().created_at
            # if last_n_days := self.request.query_params.get("last_n_days", None):
            #     last_n_days = int(last_n_days)
            #     conversation_instances = conversation_instances.filter(
            #         created_at__gte=today - timezone.timedelta(days=last_n_days)
            #     )
            #     start_date = today - timedelta(days=last_n_days)

            # dates
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, _, _, _ = analytic_helper.get_date_analytic_vars(
                start_datetime, end_datetime, is_start_end_only=True
            )

            schema_name = authapi_helper.get_schema_name()
            # Facebook only for now
            sub_source_to_page_name = {
                credential.PAGE_ID: credential.PAGE_NAME
                for credential in facebook_integration_models.Credential.objects.filter(
                    tenant__schema_name=schema_name
                )
            }

            appointment_instance_list = (
                chat_action_tracker_models.Appointment.objects.filter(
                    created_at__gte=start_date,
                    created_at__lte=end_date,
                    appointment_chat_feature_location__isnull=False,
                )
            )
            location_name_list = (
                chat_action_models.AppointmentChatFeatureLocation.objects.values_list(
                    "name", flat=True
                )
            )

            # Adjusting the query to handle empty sub_source and add brackets if it exists
            annotated_appointments = (
                appointment_instance_list.annotate(
                    source=F("conversation__source"),
                    sub_source=Case(
                        When(conversation__sub_source__isnull=True, then=Value("")),
                        When(conversation__sub_source__exact="", then=Value("")),
                        default=F("conversation__sub_source"),
                        output_field=CharField(),
                    ),
                    appt_label=F("appointment_chat_feature_location__name"),
                )
                .values("source", "sub_source", "appt_label")
                .annotate(total=Count("id"))  # group by happens in the Count there
            )
            TOTAL = "Total"
            SOURCE = "Source"
            # Convert to desired format
            appt_made_per_source = {}
            total_appt_dict = {TOTAL: 0}
            for appt in annotated_appointments:
                sub_source = sub_source_to_page_name.get(
                    appt["sub_source"], appt["sub_source"]
                )
                source_sub_source_cat = (
                    appt["source"] + f" ({sub_source})" if sub_source else ""
                )
                appt_label = appt["appt_label"]
                total = appt["total"]

                if source_sub_source_cat not in appt_made_per_source:
                    appt_made_per_source[source_sub_source_cat] = {}
                appt_made_per_source[source_sub_source_cat][appt_label] = total

                if TOTAL not in appt_made_per_source[source_sub_source_cat]:
                    appt_made_per_source[source_sub_source_cat][TOTAL] = total
                else:
                    appt_made_per_source[source_sub_source_cat][TOTAL] += total

                if appt_label not in total_appt_dict:
                    total_appt_dict[appt_label] = total
                else:
                    total_appt_dict[appt_label] += total

            # add 0
            for location_name in location_name_list:
                for key in appt_made_per_source.keys():
                    if location_name not in appt_made_per_source[key]:
                        appt_made_per_source[key][location_name] = 0
                if location_name not in total_appt_dict:
                    total_appt_dict[location_name] = 0

                total_appt_dict[TOTAL] += total_appt_dict[location_name]

            # Convert the dictionary to a list format expected by MUI table
            appt_made_per_source_list = [
                {
                    SOURCE: key,
                    **value,
                }
                for key, value in appt_made_per_source.items()
            ] + [{SOURCE: TOTAL, **total_appt_dict}]

            return Response(
                data=appt_made_per_source_list,
                status=status.HTTP_200_OK,
            )
    class GetPositiveSentimentAnalysisHeatMapData(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)
            
            # Prepare the filter for is_verified
            verified_filter = Q()
            if is_verified.lower() == 'true':
                verified_filter = Q(conversation__metadata__is_verified=True)
            elif is_verified.lower() == 'false':
                verified_filter = Q(conversation__metadata__is_verified=False) | Q(conversation__metadata__is_verified__isnull=True)

            message_instances = (
                chat_models.Message.objects.filter(
                    created_at__gte=start_date, created_at__lte=end_date,sentiment__isnull=False,topics__isnull=False,message_type=utility_models.ANONYMOUS_TEXT, topics__gt=""
                )
                .filter(verified_filter)
                .annotate(date=truncate_method("created_at", output_field=DateField()))
                .values("date", "sentiment", "topics")  # Fetch only required fields 
            )
            
            # Apply country filter if provided
            if country_lists:
                countries = country_lists.split(",")
                message_instances = message_instances.filter(
                    conversation__ip_address_record__country__in=countries
                )
            
            final_dict = {}
            if message_instances:
                message_data = list(message_instances)
                
                df = pd.DataFrame(message_data)
                
                df = df.assign(topic=df["topics"].str.split(", ")).explode("topic").reset_index(drop=True)
                
                sentiment_counts = df.groupby(['topic', 'sentiment']).size().unstack(fill_value=0).drop(columns=['neutral', 'negative'], errors='ignore')

                
                if len(sentiment_counts.columns) > 0:
                
                    top_10_positive = sentiment_counts.sort_values(by='positive', ascending=False).head(10)
                    
                    top_10_positive_dict = top_10_positive.to_dict()['positive']
                    
                    positive_df = df[df['sentiment'] == 'positive']
                    positive_df = positive_df[positive_df['topic'].isin(top_10_positive_dict.keys())]
                    positive_df['count'] = 1
                    
                    
                    def adjust_date_column(df, start_date, end_date):
                        df['date'] = pd.to_datetime(df['date'], errors='coerce')
                        
                        # Calculate the difference between start_date and end_date
                        date_diff = (end_date - start_date).days
                        
                        # Check if the difference is more than 30 days
                        if date_diff > 30:
                            # Change the date column to show the start of the week (Monday)
                            df['date'] = df['date'] - pd.to_timedelta(df['date'].dt.weekday, unit='d')
                            
                        # Convert the datetime to date
                        df['date'] = df['date'].dt.date
                        
                        return df

                    # Assuming positive_df is your DataFrame and start_date, end_date are the date range you're working with
                    positive_df = adjust_date_column(positive_df, start_date, end_date)

                    positive_df = positive_df.groupby(['date', 'topic']).agg({'count': 'sum'}).reset_index()
                    
                    max_count = positive_df['count'].max()
                    
                    # extract the date from df
                    data_date = set(positive_df['date'].unique())
                    date_range_set = set(date_range)
                    missing_dates = date_range_set - data_date
                    
                    for date in missing_dates:
                        # Create the df and rows for the missing date
                        missing_df = pd.DataFrame([{'date': date, 'topic': topic, 'count': 0} for topic in top_10_positive_dict.keys()])
                        positive_df = pd.concat([positive_df, missing_df], ignore_index=True)
                    
                    # Sort the df by date in ascending order
                    positive_df = positive_df.sort_values(by='date')
                    
                    # Group By the date
                    positive_df = positive_df.groupby('date')
                    
                    result = {
                        "topics": list(top_10_positive_dict.keys()),
                        "date": [],
                        "value": []
                    }
                    
                    for date, df in positive_df:
                        
                        # Get the set of topics in df
                        topics = set(df['topic'])
                        full_topics = set(top_10_positive_dict.keys())

                        # Get the missing topics
                        missing_topics = full_topics - topics
                        
                        # Add the missing topics to the df
                        missing_df = pd.DataFrame([{'date': date, 'topic': topic, 'count': 0} for topic in missing_topics])
                        df = pd.concat([df, missing_df], ignore_index=True)
                            
                        # Append the date to the result
                        result['date'].append(date.strftime('%Y-%m-%d'))
                        
                        # Convert 'topic' column to categorical type with specified order
                        df['topic_ordered'] = pd.Categorical(df['topic'], categories=top_10_positive_dict.keys(), ordered=True)

                        # Sort the DataFrame by the 'topic' column
                        df = df.sort_values(by='topic_ordered')
                        
                        # Append the count to the result
                        result['value'].append(df['count'].tolist())
                
                    final_dict = {
                        "max_value": max_count,
                        "min_value": 0,
                        "result": result
                    }
                
            return Response(
                data=final_dict,
                status=status.HTTP_200_OK,
            )
    class GetNegativeSentimentAnalysisHeatMapData(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)

            # Prepare the filter for is_verified
            verified_filter = Q()
            if is_verified.lower() == 'true':
                verified_filter = Q(conversation__metadata__is_verified=True)
            elif is_verified.lower() == 'false':
                verified_filter = Q(conversation__metadata__is_verified=False) | Q(conversation__metadata__is_verified__isnull=True)
                
            message_instances = (
                chat_models.Message.objects.filter(
                    created_at__gte=start_date, created_at__lte=end_date,sentiment__isnull=False,topics__isnull=False,message_type=utility_models.ANONYMOUS_TEXT, topics__gt=""
                )
                .filter(verified_filter)
                .annotate(date=truncate_method("created_at", output_field=DateField()))
                .values("date", "sentiment", "topics")  # Fetch only required fields 
            )
            
            # Apply country filter if provided
            if country_lists:
                countries = country_lists.split(",")
                message_instances = message_instances.filter(
                    conversation__ip_address_record__country__in=countries
                )
                
            final_dict = {}
            if message_instances:
                message_data = list(message_instances)
                
                df = pd.DataFrame(message_data)
                
                df = df.assign(topic=df["topics"].str.split(", ")).explode("topic").reset_index(drop=True)
                
                df = df.drop(columns=["topics"])
                
                sentiment_counts = df.groupby(['topic', 'sentiment']).size().unstack(fill_value=0).drop(columns=['neutral', 'positive'], errors='ignore')
                
                if len(sentiment_counts.columns) > 0:
                
                    top_10_negative = sentiment_counts.sort_values(by='negative', ascending=False).head(10)
                    
                    top_10_negative_dict = top_10_negative.to_dict()['negative']
                                
                    negative_df = df[df['sentiment'] == 'negative']
                    negative_df = negative_df[negative_df['topic'].isin(top_10_negative_dict.keys())]
                    negative_df['count'] = 1
                    
                    def adjust_date_column(df, start_date, end_date):
                        df['date'] = pd.to_datetime(df['date'], errors='coerce')
                        
                        # Calculate the difference between start_date and end_date
                        date_diff = (end_date - start_date).days
                        
                        # Check if the difference is more than 30 days
                        if date_diff > 30:
                            # Change the date column to show the start of the week (Monday)
                            df['date'] = df['date'] - pd.to_timedelta(df['date'].dt.weekday, unit='d')
                            
                        # Convert the datetime to date
                        df['date'] = df['date'].dt.date
                        
                        return df

                    # Assuming negative_df is your DataFrame and start_date, end_date are the date range you're working with
                    negative_df = adjust_date_column(negative_df, start_date, end_date)

                    negative_df = negative_df.groupby(['date', 'topic']).agg({'count': 'sum'}).reset_index()
                    
                    max_count = negative_df['count'].max()
                    
                    # extract the date from df
                    data_date = set(negative_df['date'].unique())
                    date_range_set = set(date_range)
                    missing_dates = date_range_set - data_date
                    
                    for date in missing_dates:
                        # Create the df and rows for the missing date
                        missing_df = pd.DataFrame([{'date': date, 'topic': topic, 'count': 0} for topic in top_10_negative_dict.keys()])
                        negative_df = pd.concat([negative_df, missing_df], ignore_index=True)
                    
                    # Sort the df by date in ascending order
                    negative_df = negative_df.sort_values(by='date')
                    
                    # Group By the date
                    negative_df = negative_df.groupby('date')
                    
                    result = {
                        "topics": list(top_10_negative_dict.keys()),
                        "date": [],
                        "value": []
                    }
                    
                    for date, df in negative_df:
                        
                        # Get the set of topics in df
                        topics = set(df['topic'])
                        full_topics = set(top_10_negative_dict.keys())
                        # Get the missing topics
                        missing_topics = full_topics - topics
                        
                        # Add the missing topics to the df
                        missing_df = pd.DataFrame([{'date': date, 'topic': topic, 'count': 0} for topic in missing_topics])
                        df = pd.concat([df, missing_df], ignore_index=True)
                            
                        # Append the date to the result
                        result['date'].append(date.strftime('%Y-%m-%d'))
                        
                        # Convert 'topic' column to categorical type with specified order
                        df['topic_ordered'] = pd.Categorical(df['topic'], categories=top_10_negative_dict.keys(), ordered=True)

                        # Sort the DataFrame by the 'topic' column
                        df = df.sort_values(by='topic_ordered')
                        
                        # Append the count to the result
                        result['value'].append(df['count'].tolist())
                    
                    final_dict = {
                        "max_value": max_count,
                        "min_value": 0,
                        "result": result
                    }
                
            return Response(
                data=final_dict,
                status=status.HTTP_200_OK,
            )
    class GetTopicFrequencyAnalysisData(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)
            
            # Prepare the filter for is_verified
            verified_filter = Q()
            if is_verified.lower() == 'true':
                verified_filter = Q(conversation__metadata__is_verified=True)
            elif is_verified.lower() == 'false':
                verified_filter = Q(conversation__metadata__is_verified=False) | Q(conversation__metadata__is_verified__isnull=True)

            message_instances = (
                chat_models.Message.objects.filter(
                    created_at__gte=start_date, created_at__lte=end_date,sentiment__isnull=False,topics__isnull=False,message_type=utility_models.ANONYMOUS_TEXT
                ).exclude(topics="")
                .filter(verified_filter)
                .annotate(date=truncate_method("created_at", output_field=DateField()))
                .values("date", "sentiment", "topics")  # Fetch only required fields
            )
            
            # Apply country filter if provided
            if country_lists:
                countries = country_lists.split(",")
                message_instances = message_instances.filter(
                    conversation__ip_address_record__country__in=countries
                )
                
            result = []
            if message_instances:
                message_data = list(message_instances)
                
                df = pd.DataFrame(message_data)
                
                df = df.assign(topic=df["topics"].str.split(", ")).explode("topic").reset_index(drop=True)
                
                df = df.drop(columns=["topics"])
                
                sentiment_counts = df.groupby(['topic', 'sentiment']).size().unstack(fill_value=0)
                
                final_df = sentiment_counts.sum(axis=1).reset_index(name='count')
                
                top_10_df = final_df.nlargest(10, 'count')
                
                result = top_10_df.apply(lambda x: {"label": x['topic'], "value": x['count']}, axis=1).tolist()

            return Response(
                data=result,
                status=status.HTTP_200_OK,
            )
    class GetTopKeywordsForPositiveSentiment(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)

            # Prepare the filter for is_verified
            verified_filter = Q()
            if is_verified.lower() == 'true':
                verified_filter = Q(conversation__metadata__is_verified=True)
            elif is_verified.lower() == 'false':
                verified_filter = Q(conversation__metadata__is_verified=False) | Q(conversation__metadata__is_verified__isnull=True)
                
            message_instances = (
                chat_models.Message.objects.filter(
                    created_at__gte=start_date, created_at__lte=end_date,sentiment__isnull=False,topics__isnull=False,message_type=utility_models.ANONYMOUS_TEXT
                )
                .filter(verified_filter)
                .annotate(date=truncate_method("created_at", output_field=DateField()))
                .values("sentiment", "message")
            )
            
            # Apply country filter if provided
            if country_lists:
                countries = country_lists.split(",")
                message_instances = message_instances.filter(
                    conversation__ip_address_record__country__in=countries
                )
                
            result = []
            if message_instances:
                neutral_messages = [
                    message['message'] for message in message_instances
                    if message['sentiment'] == 'positive'
                ]
                
                # Combine all neutral messages into one large text
                combined_text = ' '.join(neutral_messages)
                
                # Split the combined text into words
                words = combined_text.split()
                
                # Remove Empty Strings and Email Addresses
                words = [word for word in words if word and not re.match(r'^[\w\.-]+@[\w\.-]+\.\w+$', word)]
                
                # Remove Punctuation Marks
                words = [word.translate(str.maketrans('', '', string.punctuation)) for word in words]
                
                # Remove stop words
                stop_words = set(stopwords.words('english')).union(utility_models.STOP_WORDS)
                filtered_words = [word for word in words if word.lower() not in stop_words]
                
                # Count the frequency of each word
                word_counts = Counter(filtered_words)
                
                # Get the top 10 most common keywords, excluding any empty labels
                top_keywords = [item for item in word_counts.most_common() if item[0]][:10]
                
                # Prepare the result in the desired format
                result = [{"label": keyword, "value": frequency} for keyword, frequency in top_keywords]
                
            return Response(
                data=result,
                status=status.HTTP_200_OK,
            )
    class GetTopKeywordsForNegativeSentiment(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)
            
            # Prepare the filter for is_verified
            verified_filter = Q()
            if is_verified.lower() == 'true':
                verified_filter = Q(conversation__metadata__is_verified=True)
            elif is_verified.lower() == 'false':
                verified_filter = Q(conversation__metadata__is_verified=False) | Q(conversation__metadata__is_verified__isnull=True)

            message_instances = (
                chat_models.Message.objects.filter(
                    created_at__gte=start_date, created_at__lte=end_date,
                    sentiment__isnull=False, topics__isnull=False,
                    message_type=utility_models.ANONYMOUS_TEXT
                )
                .filter(verified_filter)
                .annotate(date=truncate_method("created_at", output_field=DateField()))
                .values("sentiment", "message", "conversation_id")
            )
            
            # Apply country filter if provided
            if country_lists:
                countries = country_lists.split(",")
                message_instances = message_instances.filter(
                    conversation__ip_address_record__country__in=countries
                )
                
            result = []
            if message_instances:
                neutral_messages = [
                    message['message'] for message in message_instances
                    if message['sentiment'] == 'negative'
                ]
                
                # Combine all neutral messages into one large text
                combined_text = ' '.join(neutral_messages)
                
                # Split the combined text into words
                words = combined_text.split()                
                
                # Remove Empty Strings and Email Addresses
                words = [word for word in words if word and not re.match(r'^[\w\.-]+@[\w\.-]+\.\w+$', word)]
                
                # Remove Punctuation Marks
                words = [word.translate(str.maketrans('', '', string.punctuation)) for word in words]
                
                # Remove stop words
                stop_words = set(stopwords.words('english')).union(utility_models.STOP_WORDS)
                filtered_words = [word for word in words if word.lower() not in stop_words]
                
                # Count the frequency of each word
                word_counts = Counter(filtered_words)
                
                # Get the top 10 most common keywords
                top_keywords = [item for item in word_counts.most_common() if item[0]][:10]
                
                # Prepare the result in the desired format
                result = [{"label": keyword, "value": frequency} for keyword, frequency in top_keywords]
                
            return Response(
                data=result,
                status=status.HTTP_200_OK,
            )
    class GetConversationBoxPlot(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            # dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, truncate_method, date_range, extra_end_date_list = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)
            
            conversation_instances = chat_models.Conversation.objects.filter(
                created_at__gte=start_date, created_at__lte=end_date,
            )
            
            if country_lists:
                countries = country_lists.split(",")
                conversation_instances = conversation_instances.filter(
                    ip_address_record__country__in=countries
                )
                
            # Apply filter only if is_verified is not an empty string
            if is_verified:
                if is_verified.lower() == "true":
                    conversation_instances = conversation_instances.filter(
                        metadata__is_verified=True
                    )
                else:
                    conversation_instances = conversation_instances.filter(
                        Q(metadata__is_verified=False) | Q(metadata__is_verified__isnull=True)
                    )


            conversation_instances = conversation_instances.annotate(
                date=truncate_method("created_at", output_field=DateField()),
            ).values("id", "date")
            
            result = []
            
            # Convert the queryset to a list of dictionaries
            conversation_list = list(conversation_instances)
            
            if conversation_list:
                # Assuming you have a list of conversation IDs in conversation_list
                conversation_ids = [conv["id"] for conv in conversation_list]

                # Query to get the count of messages for each conversation in the list
                message_counts = chat_models.Message.objects.filter(
                    conversation_id__in=conversation_ids,
                    message_type=utility_models.ANONYMOUS_TEXT
                ).values("conversation_id").annotate(message_count=Count("id"))

                # Convert the queryset to a list of dictionaries
                message_counts_list = list(message_counts)

                # Create DataFrame from the conversation list
                conversation_df = pd.DataFrame(conversation_list)

                # If message_counts_list is not empty, create DataFrame, otherwise create an empty DataFrame
                if message_counts_list:
                    message_counts_df = pd.DataFrame(message_counts_list)
                else:
                    # Create an empty DataFrame with the same structure if there are no messages
                    message_counts_df = pd.DataFrame(columns=['conversation_id', 'message_count'])

                # Merge conversation DataFrame with message counts DataFrame on conversation_id
                result_df = pd.merge(
                    conversation_df,
                    message_counts_df,
                    left_on="id",
                    right_on="conversation_id",
                    how="left"
                )

                # Drop the 'conversation_id' column from the right DataFrame (message_counts_df)
                result_df.drop(columns=["conversation_id"], inplace=True)

                # Rename the remaining columns for clarity
                result_df.rename(columns={"id": "conversation_id", "date": "date", "message_count": "number_of_messages"}, inplace=True)

                # Ensure that the number_of_messages column is set to 0 explicitly if there are no messages
                result_df["number_of_messages"] = result_df["number_of_messages"].fillna(0).astype(int)
            else:
                # If no conversation instances found, create an empty result_df
                result_df = pd.DataFrame(columns=["date", "conversation_id", "number_of_messages"])

            # Implement the date adjustment logic
            for i in range(1, len(date_range)):
                # Check if the difference between consecutive dates is 7 days
                if (date_range[i] - date_range[i - 1]).days == 7:
                    # Update dates in result_df to match the previous date in date_range if they fall within this range
                    result_df.loc[(result_df['date'] >= date_range[i-1]) & (result_df['date'] < date_range[i]), 'date'] = date_range[i-1]
            
            # Include all dates in the range, even those with no data
            # Create a DataFrame to ensure all dates are included
            all_dates_df = pd.DataFrame(date_range, columns=['date'])

            # Merge the all_dates_df with result_df to ensure all dates are included
            result_df = pd.merge(all_dates_df, result_df, on="date", how="left")

            # Fill missing values with 0 (for dates with no conversations)
            result_df["conversation_id"] = result_df["conversation_id"].fillna(0)
            result_df["number_of_messages"] = result_df["number_of_messages"].fillna(0).astype(int)

            # Group by 'date' and calculate the min and max
            boxplot_data = result_df.groupby('date').agg(
                min=('number_of_messages', 'min'),
                max=('number_of_messages', 'max')
            ).reset_index()

            # Calculate lower quartile (Q1), median (Q2), and upper quartile (Q3) based on min and max
            boxplot_data['lowerQuartile'] = boxplot_data['min'] + 0.25 * (boxplot_data['max'] - boxplot_data['min'])
            boxplot_data['median'] = (boxplot_data['min'] + boxplot_data['max']) / 2
            boxplot_data['upperQuartile'] = boxplot_data['min'] + 0.75 * (boxplot_data['max'] - boxplot_data['min'])

            # Calculate average as the mean of min and max
            boxplot_data['average'] = boxplot_data['median']

            # Round the values to 2 decimal places
            boxplot_data = boxplot_data.round({'lowerQuartile': 2, 'median': 2, 'upperQuartile': 2, 'average': 2})

            result = [
                {
                    "min": row["min"],
                    "lowerQuartile": row["lowerQuartile"],
                    "median": row["median"],
                    "upperQuartile": row["upperQuartile"],
                    "max": row["max"],
                    "average": row["average"],
                    "label": row["date"].strftime("%Y-%m-%d")
                }
                for index, row in boxplot_data.iterrows()
            ]
                
            return Response(
                data=result,
                status=status.HTTP_200_OK,
            )
class DocumentView:
    class DocumentBaseView:
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class GetDocumentCreated(DocumentBaseView, APIView):
        def get(self, request, *args, **kwargs):
            today = timezone.now()
            document_objects = document_models.Document.objects.all()
            comparison_document_objects = document_models.Document.objects.all()

            if last_n_days := self.request.query_params.get("last_n_days", None):
                last_n_days = int(last_n_days)
                document_objects = document_objects.filter(
                    created_at__gte=today - timezone.timedelta(days=last_n_days)
                )
                comparison_document_objects = comparison_document_objects.filter(
                    created_at__gte=today - timezone.timedelta(days=last_n_days * 2),
                    created_at__lt=today - timezone.timedelta(days=last_n_days),
                )

            document_objects_count = document_objects.count()
            comparison_document_objects_count = comparison_document_objects.count()
            percentage_diff = (
                0
                if comparison_document_objects_count == 0
                else round(
                    (
                        (document_objects_count - comparison_document_objects_count)
                        / comparison_document_objects_count
                    )
                    * 100,
                    2,
                )
            )

            return Response(
                data={
                    "count": document_objects_count,
                    "percentage_diff": 0 if percentage_diff == 0 else percentage_diff,
                },
                status=status.HTTP_200_OK,
            )
            
class MessageAnalyticsView:
    class MessageAnalyticsBaseView(generics.GenericAPIView):
        queryset = chat_models.Conversation.objects.all()
        serializer_class = chat_serializers.MessageAnalyticsSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]
        pagination_class = utility_common_class.CustomPageNumberPagination
        
    class List(MessageAnalyticsBaseView, generics.ListAPIView):
        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "email",
            "topics",
            "updated_at"
        ]
        ordering_fields = [
            "email",
            "topics",
            "updated_at"
        ]
        ordering = ["-updated_at"]

        def get_queryset(self):
            # Dates
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            email = self.request.query_params.get("email", "")
            start_date, end_date, _, _, _ = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )

            queryset = super().get_queryset()
            
            if start_date and end_date:
                queryset = queryset.filter(created_at__gte=start_date, created_at__lte=end_date, metadata__email=email)

                queryset = queryset.annotate(
                    email=F('metadata__email'),
                    topics=Coalesce(
                        LTrim(
                            StringAgg(
                                'messages__topics',
                                delimiter=', ',
                                distinct=True,
                                output_field=CharField()  # Ensure consistent type for the aggregated topics
                            )
                        ),
                        Value(''),
                        output_field=CharField()  # Ensure the final output is a CharField
                    )
                ).exclude(
                    topics__exact=''  # Exclude if topics are empty
                ).exclude(
                    email__exact=''  # Exclude if email is missing
                )
                
            return queryset
    class GetMessageAnalyticsCsvFile(List, APIView):
        def get(self, request, *args, **kwargs):
            """
            Export filtered messages analytics data to CSV.
            """
            queryset = self.get_queryset().order_by('-updated_at')
            email = self.request.query_params.get("email", "")

            # Export to CSV
            response = HttpResponse(content_type="text/csv")
            response[
                "Content-Disposition"
            ] = f'attachment; filename="conversation_analytics_{email}_{timezone.now().strftime("%d.%m.%Y")}.csv"'

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Email",
                    "Date",
                    "Topics"
                ]
            )

            # Get data from serializer
            message_data = chat_serializers.MessageAnalyticsSerializer.Get(
                queryset, many=True
            ).data

            # Write data to CSV
            for data in message_data:
                # Parse the string back to a datetime object
                updated_at = datetime.fromisoformat(data["updated_at"])
                # Format the datetime object to the desired format
                date = updated_at.strftime("%d %B %Y %-I:%M%p")
                # Ensure that the time period (AM/PM) is in lowercase
                date = date[:-2] + date[-2:].lower()
                writer.writerow(
                    [
                        data["email"],
                        date,
                        data["topics"]
                    ]
                )

            return response
        
class ConversationAnalyticsView:
    class ConversationAnalyticsBaseView(generics.GenericAPIView):
        queryset = chat_models.Conversation.objects.all()
        serializer_class = chat_serializers.ConversationAnalyticsSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]
        pagination_class = utility_common_class.CustomPageNumberPagination
        
    class List(ConversationAnalyticsBaseView, generics.ListAPIView):
        def get_queryset(self):
            def group_conversations(conversations):
                grouped_data = defaultdict(lambda: {'topics': defaultdict(int), 'last_message_date': None})

                for item in conversations:
                    email = item.get('email')
                    topics = item.get('topic_list', '').split(', ')
                    last_message_date = item.get('last_message_date')
                    
                    unique_topics = set(topics)

                    for topic in unique_topics:
                        if topic != "":
                            grouped_data[email]['topics'][topic] += 1

                    if (grouped_data[email]['last_message_date'] is None or 
                            last_message_date > grouped_data[email]['last_message_date']):
                        grouped_data[email]['last_message_date'] = last_message_date

                result = []
                for email, data in grouped_data.items():
                    # Sort topics by count in descending order
                    sorted_topics = sorted(data['topics'].items(), key=lambda x: x[1], reverse=True)
                    topics_list = [f"{topic} ({count})" for topic, count in sorted_topics]
                    topics_str = ', '.join(topics_list)
                    result.append({
                        "email": email,
                        "topics": topics_str,
                        "last_message_date": data['last_message_date']
                    })

                return result
            # Dates
            is_verified = self.request.query_params.get("is_verified", "")
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")
            start_date, end_date, _, _, _ = (
                analytic_helper.get_date_analytic_vars(start_datetime, end_datetime)
            )
            country_lists = self.request.query_params.get("countries", None)
            search = self.request.query_params.get("search", "")
            ordering = self.request.query_params.get("ordering", "")

            queryset = super().get_queryset()
            
            if start_date and end_date:
                queryset = queryset.filter(created_at__gte=start_date, created_at__lte=end_date, metadata__email__isnull=False, messages__topics__gt="",messages__topics__isnull=False)
                
                if country_lists:
                    countries = country_lists.split(",")
                    queryset = queryset.filter(
                        ip_address_record__country__in=countries
                    )
                
                # Apply filter only if is_verified is not an empty string
                if is_verified:
                    if is_verified.lower() == "true":
                        queryset = queryset.filter(
                            metadata__is_verified=True
                        )
                    else:
                        queryset = queryset.filter(
                            Q(metadata__is_verified=False) | Q(metadata__is_verified__isnull=True)
                        )

                if search:
                    queryset = queryset.filter(
                        Q(metadata__email__icontains=search) | 
                        Q(messages__topics__icontains=search)
                    )

                if ordering:
                    if ordering == 'topics':
                        ordering = 'topic_list'
                    elif ordering == '-topics':
                        ordering = '-topic_list'
                    # Group by email and count unique conversation IDs
                    queryset = queryset.annotate(
                        email=F('metadata__email'),
                        topic_list=StringAgg('messages__topics', delimiter=', ', distinct=True),
                        conversation_count=Count('id', distinct=True),
                        last_message_date=Max('updated_at')
                    ).values('email', 'topic_list', 'conversation_count', 'last_message_date').order_by(ordering)
                else:
                    # Group by email and count unique conversation IDs
                    queryset = queryset.annotate(
                        email=F('metadata__email'),
                        topic_list=StringAgg('messages__topics', delimiter=', ', distinct=True),
                        conversation_count=Count('id', distinct=True),
                        last_message_date=Max('updated_at')
                    ).values('email', 'topic_list', 'conversation_count', 'last_message_date').order_by('-last_message_date')
                
                queryset = group_conversations(queryset)

            return queryset
    class GetConversationAnalyticsCsvFile(List, APIView):
        def get(self, request, *args, **kwargs):
            """
            Export filtered messages analytics data to CSV.
            """
            queryset = self.get_queryset()

            # Export to CSV
            response = HttpResponse(content_type="text/csv")
            response[
                "Content-Disposition"
            ] = f'attachment; filename="conversation_analytics_{timezone.now().strftime("%d.%m.%Y")}.csv"'

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Email",
                    "Date",
                    "Topics"
                ]
            )

            # Get data from serializer
            message_data = chat_serializers.ConversationAnalyticsSerializer.Get(
                queryset, many=True
            ).data

            # Write data to CSV
            for data in message_data:
                # Parse the string back to a datetime object
                last_message_date = datetime.fromisoformat(data["last_message_date"])
                # Format the datetime object to the desired format
                date = last_message_date.strftime("%d %B %Y %-I:%M%p")
                # Ensure that the time period (AM/PM) is in lowercase
                date = date[:-2] + date[-2:].lower()
                writer.writerow(
                    [
                        data["email"],
                        date,
                        data["topics"]
                    ]
                )

            return response
        