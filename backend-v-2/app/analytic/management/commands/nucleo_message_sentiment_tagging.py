import traceback
import os
import time
import json
import logging
import pytz

import app.chats.chat.models as chat_models

from datetime import timedelta
from tenant_schemas.utils import schema_context
from django.utils import timezone
from django.core.management.base import BaseCommand

from utility.tone_analysis import analyze_tone

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Tag sentiment and topics for nucleo messages"

    def handle(self, *args, **options):
        def get_openai_completion(prompt, model="gpt-4o", format=None, max_retries:int = 5):
            from openai import OpenAI
            # Define your OpenAI API key
            client = OpenAI(
                # This is the default and can be omitted
                api_key=os.environ.get("OPENAI_API_KEY"),
            )
            
            retries = 0
            while retries < max_retries:
                try:

                    if format:
                        # Call the OpenAI API
                        response = client.chat.completions.create(
                            model=model,  # Or any other engine you prefer
                            messages=prompt,
                            temperature=0,
                            response_format=format
                        )
                        completion_text = response.choices[0].message.content
                    else:
                        # Call the OpenAI API
                        response = client.chat.completions.create(
                            model=model,  # Or any other engine you prefer
                            messages=prompt,
                            temperature=0
                        )
                        completion_text = str(response.choices[0].message.content).strip()

                    return completion_text
                
                except Exception as e:
                    print(e)
                    print(f"Request timed out. Retrying... {retries + 1}/{max_retries}")
                    retries += 1
                    time.sleep(retries * 2)
            
            # Need to raise an exception here
            raise Exception("Request to Openai server timed out")

        def get_episodes(messages_list):
            prompt = [
                {
                    "role": "system",
                    "content": """
                    You are an elite expert in conversation segmentation. Your task is to analyze a sequence of chat messages and group them into distinct, non-overlapping episodes with absolute precision.

                    **Definitions and Requirements:**
                    - An *episode* is a continuous series of messages that revolve around the same topic or discussion.
                    - A new episode begins only when there is a clear shift in topic, a change in participants, or a significant time gap between messages.

                    **Key Instructions:**
                    1. **Grouping:** Ensure all related messages are grouped within the same episode, with no message appearing in more than one episode.
                    2. **Chronological Order:** Episodes must be listed in strict chronological order based on `chat_message_id`.
                    3. **Unique Boundaries:** Assign unique `start_id` and `end_id` values for each episode. The `start_id` of any episode must be strictly greater than the `end_id` of the preceding episode, eliminating any possibility of overlap.
                    4. **Continuous Conversations:** If the entire sequence of messages forms a single, continuous conversation, group them into one episode, clearly identifying the `start_id` and `end_id` for that episode.

                    **Output Format:**
                    Provide the output as a JSON object structured as follows:
                    {
                        "total_episodes": <number_of_episodes>,
                        "episodes": [
                            {
                                "start_id": <chat_message_id>,
                                "end_id": <chat_message_id>
                            },
                            ...
                        ]
                    }
                    Return only the JSON object with no additional text or formatting. (THIS IS VERY IMPORTANT)
                    """
                },
                {
                    "role": "user",
                    "content": f"""Analyze the following chat messages, which include message content, the time sent, and the message ID. Segment them into distinct, non-overlapping episodes according to the guidelines. The episodes must be listed in strict chronological order by `chat_message_id`, with each `start_id` being greater than the `end_id` of the previous episode. Return the results strictly as a JSON object in the specified format:
                    
                    Messages: {messages_list}
                    """
                }
            ]

            try:
                response = get_openai_completion(prompt=prompt, model="gpt-4o-mini")
                logger.info("get_episodes - gpt completion call", extra={"chat_message": str(messages_list), "episodes": response})
                return json.loads(response)
            except Exception as e:
                cleaned_json_string = response.replace('```json\n', '').replace('\n```', '')
                return json.loads(cleaned_json_string)

        def get_topics(messages_list):
            prompt = [
                {
                    "role": "system",
                    "content": """
                        You are a seasoned marketing executive with 10 years of experience. Your task is to analyze chat messages and extract only the most relevant and actionable topics or keywords, producing a single JSON object. The goal is to identify insights that can directly inform and enhance marketing strategies based on what the Users are asking for.

                        **Key Objectives:**
                        - Extract core subjects or content that are directly relevant to marketing efforts, focusing on user needs, product features, service improvements, and technological solutions.
                        - Emphasize topics that can drive marketing decisions, campaign strategies, or customer engagement insights, particularly in the context of technology, customer support, and product usage.
                        - Exclude any keywords or topics that do not contribute to actionable marketing insights, such as generic instructions, common phrases, or non-specific engagement metrics.

                        **Instructions:**
                        1. **Identify Marketing-Centric Content:** Focus on extracting topics that provide value for marketing, such as specific user issues, product features (e.g., password reset process), technology use cases, and user needs.
                        2. **Exclude Non-Actionable Keywords:** Avoid including keywords that do not directly support marketing decisions, such as basic steps or instructions that are not unique or insightful for marketing purposes.
                        3. **Understand Context and Intent:** Capture the essence of the user's request or issue, identifying what they are truly asking for or struggling with, and how this can inform marketing strategies.
                        4. **Create a Precise JSON Object:** Consolidate the relevant keywords into a single JSON object with one key, `topics`, ensuring the list contains only unique and marketing-relevant entries.
                        5. In the message conversation, focus on extracting the actionable insights from the User's messages, while considering the context provided by You.

                        **Output Format:**
                        - The JSON object must be structured as `{"topics": [<keyword_1>, <keyword_2>, ...]}`.
                        - Ensure there is no duplication of topics in the list.
                        - Return only the JSON object, with no additional text or formatting. (THIS IS VERY IMPORTANT)

                        **Strict Compliance:** Follow these instructions rigorously, focusing only on marketing-relevant topics and excluding any non-actionable content.
                    """
                },
                {
                    "role": "user",
                    "content": f"""
                    Below is a sequence of chat messages. Your task is to identify and extract the most essential topics or keywords that are directly relevant to marketing strategies. Exclude any keywords that do not contribute to actionable marketing insights. Return the results as a single JSON object under the `topics` key.

                    Messages: {messages_list}
                    """
                }
            ]

            try:
                response = get_openai_completion(prompt=prompt, model="gpt-4o-mini")
                logger.info("get_topics - gpt completion call", extra={"chat_message": str(messages_list), "topics": response})
                return json.loads(response)
            except Exception as e:
                logger.error("Error processing topics: %s", str(e))
                cleaned_json_string = response.replace('```json\n', '').replace('\n```', '')
                return json.loads(cleaned_json_string)
    
        try:
            logger.info("Starting nucleo_message_sentiment_tagging")
            NUCLEO_SCHEMA_NAME = "nucleo"
            with schema_context(NUCLEO_SCHEMA_NAME):
                # Start time always yesterday 12:00am to today 12:00am, if updated at betwwen the range, take id out
                gmt_plus_8 = pytz.timezone('Asia/Singapore')
                
                # Get the current time (today at 12:00 AM)
                end_time = timezone.now().astimezone(gmt_plus_8).replace(hour=0, minute=0, second=0, microsecond=0)
                # Calculate start time (yesterday at 12:00 AM)
                start_time = end_time - timedelta(days=1)
                
                logger.info("Message Tagging Date Range", extra={"start_date_time": str(start_time), "end_date_time": str(end_time)})
                
                # Filter the conversations within the range of yesterday 12:00 AM to today 12:00 AM
                conversations_in_range = chat_models.Conversation.objects.filter(updated_at__range=(start_time, end_time)).order_by('id')
                
                # Get conversation IDs from the filtered conversations
                conversation_ids = conversations_in_range.values_list('id', flat=True)
                logger.info("Retrieved Conversation Lists", extra={"conversation_ids": str(conversation_ids)})
                
                if len(conversation_ids) > 0:
                    conversations = chat_models.Conversation.objects.filter(id__in=conversation_ids).order_by('id')
                             
                    for conversation in conversations:
                        messages_list = []
                        # Get all messages for this conversation
                        chat_messages = chat_models.Message.objects.filter(conversation_id=conversation.id, sentiment__isnull=True, topics__isnull=True).order_by('id')

                        # Loop through each message in this conversation
                        for chat_message in chat_messages:
                            
                            logger.info("Tagging Chat Message", extra={"chat_message_id": chat_message.id})
                            
                            # Sentiment Analysis
                            message_tone = analyze_tone(chat_message.message)
                            sentiment = message_tone['label'].lower()
                            
                            # Update sentiment column
                            chat_models.Message.objects.filter(id=chat_message.id).update(sentiment=sentiment)
                            
                            # Topics
                            # Create a dictionary for each message
                            message_dict = {
                                'message': chat_message.message,
                                'time': chat_message.created_at,
                                'id': chat_message.id
                            }

                            # Add the dictionary to the list
                            messages_list.append(message_dict)
                        # Identify how many episodes in message
                        episodes = get_episodes(messages_list)['episodes']
                    
                        for episode in episodes:
                            episode_list = []
                            # Get the start and end message IDs for this episode
                            start_id = episode['start_id']
                            end_id = episode['end_id']
                            
                        
                            # Get chat messages between start and end message IDs
                            episode_messages = chat_models.Message.objects.filter(
                                conversation_id=conversation.id,
                                id__gte=start_id,
                                id__lte=end_id
                            )
                            
                            for episode_message in episode_messages:
                                # Create a dictionary for each message
                                episode_dict = {
                                    'message': episode_message.message,
                                    'role': "User" if episode_message.message_type == "anonymous" else "You",
                                }

                                # Add the dictionary to the list
                                episode_list.append(episode_dict)
                                
                            # Call GPT to generate topics for this episode
                            topics_response = get_topics(episode_list)
                            
                            # Extract the list of topics from the response
                            topics = topics_response.get("topics", [])
                            
                            # Form topics string
                            topics_string = ", ".join(topics)
                            
                            # Update topics column
                            chat_models.Message.objects.filter(
                                conversation_id=conversation.id,
                                id__gte=start_id,
                                id__lte=end_id
                            ).update(topics=topics_string)
            logger.info("Finished nucleo_message_sentiment_tagging")
        except Exception as e:
            logger.error(f"Error in tag_message_task: {traceback.format_exc()}")
