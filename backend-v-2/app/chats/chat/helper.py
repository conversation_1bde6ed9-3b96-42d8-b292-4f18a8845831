import app.chats.chat.models as chat_models

import re
import app.utility.models as utility_models


def split_by_last(content, keyword):
    matches = list(re.finditer(keyword, content, re.IGNORECASE))

    # If no matches found, return the original content and an empty string
    if not matches:
        return content, ""

    # Otherwise, split by the last match
    match = matches[-1]
    return (content[: match.start()], content[match.end() :])


def split_sentence_by_period(sentence, word_per_chat_bubble, schema_name):
    chunks = []

    # 1. Identify and temporarily replace URLs and emails with placeholders
    url_pattern = r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+"
    email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
    urls = re.findall(url_pattern, sentence)
    emails = re.findall(email_pattern, sentence)

    placeholders = []

    for i, url in enumerate(urls):
        placeholder = f"{{URL_{i}}}"
        placeholders.append((placeholder, url))
        sentence = sentence.replace(url, placeholder)

    for i, email in enumerate(emails):
        placeholder = f"{{EMAIL_{i}}}"
        placeholders.append((placeholder, email))
        sentence = sentence.replace(email, placeholder)

    # 2. Split the sentence by periods
    sentence_word_list = sentence.split(" ")
    sentence_word_count = len(sentence_word_list)
    while sentence_word_count > word_per_chat_bubble:
        new_word_per_chat_bubble = word_per_chat_bubble

        partial_sentence_word_count = 0
        while partial_sentence_word_count < sentence_word_count:
            partial_sentence = " ".join(sentence_word_list[:new_word_per_chat_bubble])

            # Find the position of the '.' that isn't followed by a digit (to avoid splitting lists)
            # Here, positive lookbehind is used to find a '.' that isn't preceded by a digit
            if schema_name == "amelio":
                matches = [
                    m
                    for m in re.finditer(
                        r"(?<=\D)(?<!Dr)(?<!DR)\. |\n\n|\? |\! ",
                        partial_sentence,
                    )
                ]
            else:
                matches = [
                    m
                    for m in re.finditer(
                        r"(?<=\D)(?<!No)(?<!Ind)\. |\n\n|\? |\! ",
                        partial_sentence,
                    )
                ]

            if matches:
                # means it found a way to split
                break
            else:
                partial_sentence_word_count = len(partial_sentence.split(" "))
                # extend more to see if there's splitting point
                new_word_per_chat_bubble += 5

        if matches:
            index_to_split = matches[-1].end()
        else:
            # If no splitting point is found, split at the last word
            index_to_split = len(partial_sentence)
        # Append the chunk to the result list and continue with the rest
        chunks.append(sentence[:index_to_split].strip())
        sentence = sentence[index_to_split:].strip()

        # update the list
        sentence_word_list = sentence.split(" ")
        sentence_word_count = len(sentence_word_list)

    # Append the remaining sentence (or the whole sentence if it's short enough)
    if sentence:
        chunks.append(sentence)

    # 3. Restore the URLs and emails from the placeholders
    for placeholder, original in placeholders:
        chunks = [chunk.replace(placeholder, original) for chunk in chunks]

    return chunks


def get_historical_messages(conversation, chat_length=25):
    previous_messages: list[chat_models.Message] = conversation.messages.all().order_by(
        "-created_at"
    )[:chat_length][::-1]
    historical_messages = []
    for prev_msg in previous_messages:
        # Check if there's attachments in the message; yes then append at the end
        attachments_str = ""
        attachment_instance_list: list[chat_models.Attachment] = (
            prev_msg.attachments.all()
        )
        if attachment_instance_list:
            attachments_str = "\nAttachments urls: "
            attachments_str += ", ".join(
                [attachment.file.url for attachment in attachment_instance_list]
            )

        if (
            prev_msg.message_type == utility_models.MACHINE_TEXT
            or prev_msg.message_type == utility_models.PAGE_ADMIN_TEXT
        ):
            historical_messages.append(
                {
                    "role": "assistant",
                    "content": prev_msg.message.rsplit("SOURCES:", 1)[0]
                    + attachments_str,
                }
            )
        else:
            historical_messages.append(
                {"role": "user", "content": prev_msg.message + attachments_str}
            )

    return historical_messages


##Legacy
# def get_historical_messages(previous_messages):
#     i = 0
#     chat_history = []
#     while i + 1 < len(previous_messages):
#         user_message = previous_messages[i]
#         machine_message = previous_messages[i + 1]
#         # Check if user didn't get the machine reply, if yes then skip
#         if (
#             machine_message.message_type == utility_models.USER_TEXT
#             or machine_message.message_type == utility_models.ANONYMOUS_TEXT
#         ):
#             i += 1
#             continue
#         # Clean up the sources to be returned there
#         chat_history.append(
#             (
#                 user_message.message,
#                 machine_message.message.rsplit("SOURCES:", 1)[0],
#             )
#         )
#         i += 1

#     historical_messages = []
#     for ch in chat_history:
#         historical_messages.append({"role": "user", "content": ch[0]})
#         historical_messages.append({"role": "assistant", "content": ch[1]})

#     return historical_messages
