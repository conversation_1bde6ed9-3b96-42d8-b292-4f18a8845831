import os
import whisper
import time
import torch
import gc

AUDIO_FILE_DIRECTORY = "temp_resources/audio_file"
ACCEPTABLE_FORMATS = [".wav"]


def write_audio_file(output_file, audio_file):
    with open(output_file, "wb") as destination_file:
        for chunk in audio_file.chunks():
            destination_file.write(chunk)


def transcribe(audio_file):
    """
    Take in file > save in local > process > delete in local
    """
    MODEL_FP32 = whisper.load_model(
        "base", device="cpu", download_root=".cache/whisper"
    )
    # check the file type
    start_time = time.time()
    file_extension = os.path.splitext(audio_file.name)[-1]
    if file_extension not in ACCEPTABLE_FORMATS:
        raise Exception(
            f"Audio file format not accepted. Accepted options are {ACCEPTABLE_FORMATS}"
        )

    # save file locally
    if not os.path.exists(AUDIO_FILE_DIRECTORY):
        os.makedirs(AUDIO_FILE_DIRECTORY)

    local_file_path = f"{AUDIO_FILE_DIRECTORY}/temp_audio_file{file_extension}"
    write_audio_file(local_file_path, audio_file)

    # model = whisper.load_model("tiny.en")

    model = torch.quantization.quantize_dynamic(
        MODEL_FP32, {torch.nn.Linear}, dtype=torch.qint8
    )
    result = model.transcribe(local_file_path, fp16=False, language="English")
    # remove the temp audio file
    os.remove(local_file_path)
    print("result: ", result.get("text", ""))
    print(f"Time taken for transcribe: {time.time() - start_time:.2f} seconds")

    # Delete the model to free up memory
    del model
    del MODEL_FP32
    gc.collect()

    return result.get("text", "")
