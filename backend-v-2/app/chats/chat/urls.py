from django.urls import path
import app.chats.chat.views as views

urlpatterns = [
    path("conversation/list", views.ConversationView.List.as_view()),
    path(
        "conversation/admin-view-list", views.ConversationView.AdminViewList.as_view()
    ),
    path("conversation/create", views.ConversationView.Create.as_view()),
    path("conversation/<int:pk>", views.ConversationView.Get.as_view()),
    path(
        "conversation/get-latest",
        views.ConversationView.GetLatestConversationId.as_view(),
    ),
    # path("conversation/<int:pk>/message", views.MessageView.List.as_view()),
    path("conversation/<int:pk>/update", views.ConversationView.Update.as_view()),
    path("conversation/<int:pk>/delete", views.ConversationView.Delete.as_view()),
    path(
        "conversation/whatsapp/<str:number>",
        views.ConversationView.GetWhatsAppConversationByNumber.as_view(),
    ),
    path("conversation/message/<int:pk>/update", views.MessageView.Update.as_view()),
    path("conversation/message/create", views.MessageView.Create.as_view()),
    path(
        "conversation/message/create-page-admin-reply",
        views.MessageView.CreateReply.as_view(),
    ),
    path(
        "conversation/message/<int:pk>/check-status",
        views.MessageView.CheckMessageSentStatus.as_view(),
    ),
    path(
        "conversation/message/get-lateset-whatsapp-message",
        views.MessageView.GetLatesetWhatsappMessage.as_view(),
    ),
    path(
        "conversation/message/<str:third_party_id>/check-attachments-by-message-id",
        views.MessageView.CheckAttachmentsByMessageId.as_view(),
    ),
    path(
        "conversation/message/whatsapp/<int:pk>/update-third-party-id",
        views.MessageView.UpdateWhatsAppMessageThirdPartyId.as_view(),
    ),
    path(
        "conversation/get-question-asked-csv-file",
        views.ConversationView.GetQuestionAskedCsvFile.as_view(),
    ),
    path(
        "conversation/message/feedback/list", views.MessageView.FeedBackList.as_view()
    ),
    path("conversation/message/audio/create", views.MessageAudioView.Create.as_view()),
    path(
        "conversation/message/attachment/create", views.AttachmentView.Create.as_view()
    ),
    path(
        "conversation/whatsapp/create", views.WhatsAppQRChat.as_view()
    ),  # webhook for whatsapp ec2 instance
]
