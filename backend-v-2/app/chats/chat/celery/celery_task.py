import logging
import pickle
from celery import shared_task, Celery
import redis
import requests

from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.conf import settings
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from datetime import datetime
from asgiref.sync import async_to_sync
from tenant_schemas.utils import schema_context
from app.core.tenants.models import Tenant
from django.db.models import OuterRef, Subquery

from backend.settings import REDIS
from app.utility.websocket_heartbeat_template import get_heartbeat_response
from app.chats.chat.models import Message, Conversation, ConversationQueue

logger = logging.getLogger(__name__)

# Add this import
from django.db import transaction


@shared_task
def send_heartbeat_messages() -> None:
    """
    This function is used to send heartbeat messages to the clients.
    """
    # Get channel layer
    channel_layer = get_channel_layer()

    response = get_heartbeat_response()

    async_to_sync(channel_layer.group_send)(
        "heartbeat_room", {"type": "message", "message": response}
    )


@shared_task
def close_inactive_connections() -> None:
    """
    This function is used to close inactive connections.
    """

    # Connect to redis client
    redis_config = REDIS
    redis_config["decode_responses"] = (
        False  # The key and values are in bytes. Cannot be decoded in here.
    )
    redis_client = redis.Redis(**redis_config)

    # Get all the keys
    active_connection_dict_name = b"active_connection_dict"
    active_connection_dict = redis_client.hgetall(active_connection_dict_name)

    # Get channel layer
    channel_layer = get_channel_layer()

    # For each key, check if the last heartbeat is more than 30 seconds ago
    for key in active_connection_dict:
        # Value will need to use pickle.loads
        value = active_connection_dict[key]
        value = pickle.loads(value)

        # Get the last_active in epoch time
        last_heartbeat = int(value["last_active"])

        # Get the current time in epoch time in integer
        current_time = int(datetime.now().timestamp())

        async_to_sync(channel_layer.group_discard)("heartbeat_room", key)

        # Check if the difference is more than 1 hour
        if current_time - last_heartbeat > 60 * 60:
            logger.info(
                "Closing inactive connection from the backend.",
                extra={
                    "account_id": value.get("account_id", ""),
                    "townie_id": value.get("townie_id", ""),
                    "channel_name": key,
                    "duration": current_time - last_heartbeat,
                    "last_heartbeat": last_heartbeat,
                },
            )

            # Close the connection
            async_to_sync(channel_layer.send)(
                key,
                {
                    "type": "websocket.disconnect",
                    "code": 1000,
                },
            )

            # Remove channel from active connection dict
            redis_client.hdel(active_connection_dict_name, key)


@shared_task
def process_unread_messages():
    """
    Process messages that haven't been read by the bot yet across all tenants.
    Only processes messages that are at least 2 seconds old and in 'pending' status.
    Skips conversations that have any messages 'in_process'.
    """
    try:
        # Get all active tenants
        tenants = Tenant.objects.exclude(schema_name="public")

        for tenant in tenants:
            # logger.info(f"Processing messages for tenant: {tenant.schema_name}")

            with schema_context(tenant.schema_name):
                with transaction.atomic():
                    # Get timestamp for 2 seconds ago
                    two_seconds_ago = timezone.now() - timedelta(seconds=2)

                    # Find conversations with in-process messages
                    conversations_with_in_process = (
                        Conversation.objects.filter(
                            messages__bot_reply_status="in_process"
                        )
                        .prefetch_related("messages")
                        .values_list("id", flat=True)
                    )

                    # Get all the conversations where the last message is pending and older than 2 seconds
                    conversations_with_pending_messages = (
                        Conversation.objects.prefetch_related("messages")
                        .annotate(
                            last_message_created=Subquery(
                                Message.objects.filter(
                                    conversation=OuterRef("pk"), message_type="user"
                                )
                                .order_by("-created_at")
                                .values("created_at")[:1]
                            )
                        )
                        .filter(last_message_created__lte=two_seconds_ago)
                        .distinct()
                    )

                    # First, get conversations where the last message is pending and older than 2 seconds
                    eligible_conversations = (
                        conversations_with_pending_messages.exclude(
                            id__in=conversations_with_in_process
                        ).distinct()
                    )

                    # Then get all pending messages from these eligible conversations
                    pending_messages = Message.objects.filter(
                        conversation__in=eligible_conversations,
                        bot_reply_status="pending",
                        message_type="user",
                    ).select_related("conversation")

                    # Group messages by conversation
                    conversation_messages = {}
                    for message in pending_messages:
                        if message.conversation_id not in conversation_messages:
                            conversation_messages[message.conversation_id] = []
                        conversation_messages[message.conversation_id].append(message)

                    # Process each conversation's messages
                    for conv_id, messages in conversation_messages.items():
                        message_ids = [str(msg.id) for msg in messages]

                        # Create conversation queue entry
                        queue_entry = ConversationQueue.objects.create(
                            conversation_id=conv_id,
                            messages_in_processing=message_ids,
                            is_completed=False,
                        )

                        # Update messages to in_process
                        Message.objects.filter(id__in=message_ids).update(
                            bot_reply_status="in_process"
                        )

                        payload = {
                            "conversation_id": str(conv_id),
                            "queue_id": str(queue_entry.id),
                            "message_ids": message_ids,
                            "tenant": tenant.schema_name,
                            "api_key": settings.BOT_API_KEY,
                        }

                        # Make synchronous request to processing service using requests
                        try:
                            print("sending message to bot: ", payload)
                            response = requests.post(
                                f"{settings.PROCESSING_SERVICE_URL}/org/{tenant.schema_name.replace('_', '-')}/chat/v1/process_messages/",
                                json=payload,
                                timeout=1.0,  # Short timeout since we don't wait for response
                            )
                        except requests.RequestException as e:
                            logger.error(
                                f"Failed to send messages to processing service: {e}",
                                extra={
                                    "tenant": tenant.schema_name,
                                    "conversation_id": str(conv_id),
                                    "message_ids": message_ids,
                                    "queue_id": str(queue_entry.id),
                                },
                            )

    except Exception as e:
        logger.error(f"Error in process_unread_messages task: {str(e)}", exc_info=True)
        raise
