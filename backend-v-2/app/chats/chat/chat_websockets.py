"""
This script contains the chat class for the websocket user.
"""

import logging
import pickle
import re
import traceback
import uuid
from datetime import datetime
from typing import List

import redis
from asgiref.sync import sync_to_async
from channels.exceptions import StopConsumer
from channels.generic.websocket import AsyncJsonWebsocketConsumer
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from tenant_schemas.utils import schema_context

from app.chats.chat.models import (
    Attachment,
    Conversation,
    ConversationQueue,
    Message,
    StateMachine,
    MessageAudio,
)
from app.chats.chat.serializers import MessageSerializer
from app.chats.chat.static.status_code import WebSocketStatusCode
from app.documents.document.models import KnowledgeImage
from app.integrations.integration import models as integration_models
from app.marketing.marketing.models import ClientCustomer
from app.utility import models as utility_models
from app.utility.common import datetime_to_epoch
from app.utility.custom_exception import CustomException
from app.utility.models import CONVERSATION_SOURCE_CHOICES
from app.utility.response_template import ws_error_response
from app.utility.websocket_authentication import validate_ws_token
from app.utility.websocket_payload_checker import (
    validate_bot_api_key,
    validate_conversation_source,
    validate_message_type,
)
from backend.settings import AWS_STORAGE_BUCKET_NAME, REDIS

logger = logging.getLogger(__name__)


class ChatWebsocket(AsyncJsonWebsocketConsumer):
    """
    This is the websocket class for the managing chats.

    Args:
        AsyncJsonWebsocketConsumer (AsyncJsonWebsocketConsumer): The base class for the websocket chat.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Setting up the redis client to track the active users
        self.redis_client = redis.Redis(**REDIS)

        # Default Heartbeat Room for tracking all the active users.
        self.heartbeat_overall_room_name = "heartbeat_room"
        self.active_connection_dict_name = "active_connection_dict"

        # Setting up the max message size
        DEFAULT_MAX_MESSAGE_SIZE = 2**20  # 1 MB
        channel_config = settings.CHANNEL_LAYERS["default"]["CONFIG"].get(
            "channel_capacity", {}
        )
        self.max_message_size = channel_config.get(
            "websocket.send", DEFAULT_MAX_MESSAGE_SIZE
        )

    @staticmethod
    def add_device_id_to_active_connection_dict(
        self, channel: str, account_id: str
    ) -> None:
        """
        This function is used to add the device id mapped to account.
        """
        # Get the current time and convert it to epoch time
        current_time = datetime.now()
        current_time_in_epoch = datetime_to_epoch(current_time)

        # Active connection dict
        device_dict = {"account_id": account_id, "last_active": current_time_in_epoch}

        # Convert dict into pickle object as redis only supports string
        device_dict = pickle.dumps(device_dict)

        self.redis_client.hset(
            name=self.active_connection_dict_name, key=channel, value=device_dict
        )

    @staticmethod
    def remove_device_id_from_active_connection_dict(self, channel: str) -> None:
        """
        This function is used to remove the user from the active connection dictionary.
        """
        self.redis_client.hdel(self.active_connection_dict_name, channel)

    async def add_user_to_room(self, room_name: str, session_uuid: str) -> None:
        """
        This function is used to add the user to the room.

        Args:
            room_name (str): The room name.

        Raises:
            CustomException: If the user is already subscribed to the room.
        """
        # Adding user to the given room
        await self.channel_layer.group_add(room_name, self.channel_name)

        logger.info(
            "User is added to room",
            extra={"user_session": session_uuid, "room_name": room_name},
        )

    async def remove_user_from_room(self, room_name: str) -> None:
        """
        This function is used to remove the user from the room.

        Args:
            room_name (str): The room name.

        Raises:
            CustomException: If the user is not subscribed to the room.
        """
        # Removing user from room
        await self.channel_layer.group_discard(room_name, self.channel_name)

        logger.info(
            "User is removed from room",
            extra={"user": self.user, "room_name": room_name},
        )

    def check_if_device_is_still_active(self, device_id: str) -> bool:
        """
        This function is used to check if the device_id is still active.

        Returns:
            bool: True if the device_id is active, else False.
        """
        # Get the current time and convert it to epoch time
        current_time = datetime.now()
        current_time_in_epoch = datetime_to_epoch(current_time)

        device_info = self.redis_client.hget(
            name=self.active_connection_dict_name, key=device_id
        )

        # If device info is None, then the device is not active
        if device_info is None:
            return False

        # If the device is active, then check if the device is active for more than 10 minutes
        last_active = device_info.get("last_active")
        if current_time_in_epoch - last_active < 600:
            return True

        return False

    async def connect(self) -> None:
        """
        This function is called when frontend is attempting to connect to the websocket.

        Raises:
            Exception: If the user does not exist.

        Returns:
            str: The session UUID generated for this connection
        """
        try:
            # Generate an unique session uuid
            session_uuid = str(uuid.uuid4())

            logger.info(
                "User is attempting to connect to the Websocket.",
                extra={"user_session": session_uuid},
            )

            # Adding user to the default heartbeat room
            await self.add_user_to_room(self.heartbeat_overall_room_name, session_uuid)
            # Adding channel to the account room using uuid as the unique key
            await self.add_user_to_room(session_uuid, session_uuid)
            # Add the channel to the active connection dictionary
            self.add_device_id_to_active_connection_dict(
                self, self.channel_name, session_uuid
            )
            await self.accept()

            logger.info(
                "User has connected successfully to the Websocket.",
                extra={"user_session": session_uuid},
            )

            # Send session UUID back to client
            await self.send_json(
                {
                    "header": {"type": "connection_established"},
                    "body": {"session_uuid": session_uuid},
                }
            )

        except CustomException as e:
            logger.error(
                e.args[0],
                exc_info=True,
                extra={"scope": self.scope, "exception": e.args[0], "code": e.args[1]},
            )
            await self.disconnect(code=e.args[1])

        except IndexError as e:
            logger.error(
                e.args[0],
                exc_info=True,
                extra={"scope": self.scope, "exception": e.args[0]},
            )
            await self.disconnect(code=WebSocketStatusCode.INTERNAL_ERROR)

        except Exception as e:
            logger.error(
                e.args[0],
                exc_info=True,
                extra={"scope": self.scope, "exception": e.args[0]},
            )
            await self.disconnect(code=WebSocketStatusCode.INTERNAL_ERROR)

    async def disconnect(self, code: int) -> None:
        """
        This function is called when frontend is attempting to disconnect from the websocket.

        Args:
            close_code (int): The close code.
        """
        try:
            logger.info(
                "User is attempting to disconnect from the Websocket",
                extra={"user": self.user, "channel_name": self.channel_name},
            )

            # Removing user from the default heartbeat room
            await self.remove_user_from_room(self.heartbeat_overall_room_name)
            # Removing channel from the account room using cognito_id as the unique key
            await self.remove_user_from_room(self.user.cognito_id)
            # Remove the channel from the active connection dictionary
            self.remove_device_id_from_active_connection_dict(self, self.channel_name)

            # Closing the connection
            await self.close(code=code)

        except Exception as e:
            logger.error(
                e.args[0],
                exc_info=True,
                extra={"user": self.user, "scope": self.scope, "exception": e.args[0]},
            )

        finally:
            raise StopConsumer()

    async def receive_json(self, content: dict, **kwargs) -> None:
        """
        This function is called when the websocket receives a message.

        Args:
            content (dict): This is the message content.
        """

        try:
            logger.info("Content received", extra={"content": content})
            print("content: ", content)

            header = content.get("header", None)
            if header is None:
                raise CustomException(
                    "Header not found", WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA
                )

            body = content.get("body", {})

            # Decrypt the encrypted token
            encrypted_token = body.get("encrypted_token", "")
            decrypted_token = validate_ws_token(encrypted_token)

            # If the decrypted token is empty, then raise an exception
            if not decrypted_token:
                raise CustomException(
                    "Invalid encrypted token",
                    WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                )

            # Get the uuid and chat_id from the decrypted token
            session_uuid = decrypted_token.get("uuid", "")
            chat_id = decrypted_token.get("chat_id", None)
            bot_api_key = decrypted_token.get("bot_api_key", None)

            print(
                f"session_uuid: {session_uuid}, chat_id: {chat_id}, bot_api_key: {bot_api_key}"
            )

            # Get the tenant
            tenant = self.scope["tenant"].schema_name

            message_type = header.get("type", None)

            logger.info(
                "Message type received",
                extra={
                    "message_type": message_type,
                    "session_uuid": session_uuid,
                    "tenant": tenant,
                },
            )

            # If message_type is None, then raise an exception
            if message_type is None:
                raise CustomException(
                    "Message type not found",
                    WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                )

            # If message_type is heartbeat response, then update the last_active field in the active connection dictionary
            elif message_type == "heartbeat_response":
                cognito_id = content.get("body", {}).get("cognito_id", "")
                self.add_device_id_to_active_connection_dict(
                    self, self.channel_name, cognito_id
                )

                logger.info(
                    "Heartbeat response received",
                    extra={"session_uuid": session_uuid, "cognito_id": cognito_id},
                )

            elif message_type == "user_send_message":
                source = body.get("source", "")
                if source == utility_models.WEB_APPLICATION:
                    message_text = body.get("message", "").strip()
                    attachment_ids = body.get("attachment_ids", [])

                    if not message_text and not attachment_ids:
                        raise CustomException(
                            "Message cannot be empty",
                            WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                        )

                    # Handle message creation
                    if chat_id is None:
                        source = body.get("source", "")
                        validate_conversation_source(source)
                        conversation = await create_new_conversation(
                            session_uuid, source, tenant
                        )
                    else:
                        conversation = await get_conversation(
                            chat_id, session_uuid, tenant
                        )

                    # Validate the message type
                    user_message_type = body.get("message_type", "")
                    validate_message_type(user_message_type)

                    # Create the message
                    message = await create_message(
                        conversation, message_text, user_message_type, tenant
                    )
                    await tag_attachments(message.id, attachment_ids, tenant)

                    # Format response
                    message_data = await format_message_response(message, tenant)

                    response = {
                        "header": {"type": "user_message_received"},
                        "body": message_data,
                    }

                    # Send response back to the user
                    await self.channel_layer.send(
                        self.channel_name, {"type": "message", "message": response}
                    )

                    logger.info(
                        "User message received.",
                        extra={
                            "session_uuid": session_uuid,
                            "conversation_id": str(conversation.id),
                            "message_id": str(message.id),
                        },
                    )
                elif source == utility_models.WHATSAPP_QR:
                    message_objs = body.get("message", [])
                    if not message_objs:
                        raise CustomException(
                            "Message cannot be empty",
                            WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                        )

                    user_message_type = body.get("message_type", "")
                    validate_message_type(user_message_type)
                    conversation_name = body.get("conversation_name", "")
                    third_party_id = body.get("third_party_id", "")
                    is_group_chat = message_objs[0].get("group_id", "") != ""
                    conversation_defaults = {
                        "name": conversation_name,
                        "source": source,
                        "third_party_id": third_party_id,
                        "is_group_chat": is_group_chat,
                        "session_uuid": session_uuid,
                    }
                    conversation_instance, _ = await create_or_update_conversation(
                        source=source,
                        third_party_id=third_party_id,
                        conversation_defaults={**conversation_defaults},
                        tenant_name=tenant,
                    )
                    qr_connection = await update_whatsapp_qr_session_uuid(
                        session_uuid, tenant
                    )
                    is_auto_reply = (
                        conversation_instance.is_auto_reply and qr_connection.auto_reply
                    )
                    logger.info(
                        "Auto reply status: ",
                        extra={
                            "is_auto_reply": is_auto_reply,
                            "conversation_instance.is_auto_reply": conversation_instance.is_auto_reply,
                            "qr_connection.auto_reply": qr_connection.auto_reply,
                        },
                    )
                    message_instances = []
                    latest_timestamp = None
                    for message in message_objs:
                        message_text = message.get("text", "")
                        third_party_id = message.get("message_id", "")
                        third_party_sender = message.get("name", "")
                        attachment_ids = message.get("attachment_id", [])
                        timestamp = message.get("timestamp", None)
                        # Update latest_timestamp if current message's timestamp is more recent
                        if timestamp:
                            if not latest_timestamp or timestamp > latest_timestamp:
                                latest_timestamp = timestamp
                        bot_reply_status = message.get("bot_reply_status", "pending")
                        message_type = message.get("message_type", "")
                        logger.info(
                            "Original bot_reply_status: ",
                            extra={"bot_reply_status": bot_reply_status},
                        )
                        bot_reply_status = (
                            bot_reply_status if is_auto_reply else "completed"
                        )
                        logger.info(
                            "Updated bot_reply_status: ",
                            extra={"bot_reply_status": bot_reply_status},
                        )
                        is_audio = message.get("is_audio", False)
                        message_instance = await get_or_create_message(
                            conversation_instance,
                            message_text,
                            message_type,
                            tenant,
                            third_party_id,
                            third_party_sender,
                            timestamp,
                            bot_reply_status,
                            is_audio,
                        )
                        if is_audio:
                            await tag_audio_file(
                                message_instance.id, attachment_ids, tenant
                            )
                        else:
                            await tag_attachments(
                                message_instance.id, attachment_ids, tenant
                            )
                        message_instances.append(message_instance)
                    await update_lateset_whatsapp_message(latest_timestamp, tenant)
                    message_data = await format_message_response(
                        message_instances, tenant, many=True
                    )

                    response = {
                        "header": {"type": "user_message_received"},
                        "body": message_data,
                    }

                    await self.channel_layer.send(
                        self.channel_name, {"type": "message", "message": response}
                    )
                    logger.info(
                        "User message received.",
                        extra={
                            "session_uuid": session_uuid,
                            "conversation_id": str(conversation_instance.id),
                            "message_id": str(message_instance.id),
                        },
                    )

            elif message_type == "bot_response":
                # Validate bot API key
                await validate_bot_api_key(bot_api_key)

                # Extract data from body
                message_ids = body.get("message_ids", [])
                chat_response = body.get("bot_reply", {})
                conversation_id = body.get("conversation_id")
                queue_id = body.get("queue_id", "")

                if not all([message_ids, chat_response, conversation_id]):
                    raise CustomException(
                        "Missing required fields",
                        WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                    )

                # Extract bot message and state info from chat_response
                bot_message = chat_response.get("bot_message", [])
                state_info = chat_response.get("state_info", {})

                if not bot_message:
                    raise CustomException(
                        "Bot message cannot be empty",
                        WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                    )

                # Check if there are any changes in the processing queue
                queue_matches = await check_conversation_queue(
                    queue_id, message_ids, tenant
                )

                state_machine = None

                if not queue_matches:
                    # Reset messages that were in process
                    await reset_processing_messages(conversation_id, tenant)

                    response = {
                        "header": {"type": "bot_reply_rejected"},
                        "body": {
                            "reason": "Message queue changed, please retry with updated queue"
                        },
                    }
                else:
                    # Validate state info
                    if not state_info:
                        raise CustomException(
                            "Missing state information",
                            WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                        )

                    should_reply = await check_to_reply_whatsapp_message(
                        conversation_id, tenant
                    )
                    # Create the bot message
                    message_array = await create_bot_message(
                        conversation_id, bot_message, tenant, should_reply
                    )

                    # Update the conversation metadata
                    metadata = chat_response.get("chat_metadata", {})
                    await update_conversation_metadata(
                        conversation_id, metadata, tenant
                    )

                    # Update state machine
                    state_machine = await update_state_machine(
                        state_info, tenant, message_array, should_reply
                    )

                    # Update all the in-process messages to completed
                    await update_in_process_messages(
                        conversation_id, message_ids, tenant
                    )

                    # Update the conversation queue
                    await update_conversation_queue(queue_id, tenant)

                    # Get the conversation to find the session UUID
                    conversation = await get_conversation(conversation_id, None, tenant)

                    # Check that the bot message is not [""]
                    if not should_reply:
                        logger.info(f"rejecting reply as auto reply is disabled")
                    elif len(bot_message) > 0 and bot_message[0] != "":
                        # Format the message response
                        for message in message_array:
                            message_data = await format_message_response(
                                message, tenant
                            )

                            response = {
                                "header": {"type": "message"},
                                "body": message_data,
                            }

                            # Broadcast to the session's channel
                            if conversation.source == utility_models.WHATSAPP_QR:
                                session_uuid_from_whatsapp_qr_connection = (
                                    await get_session_uuid_from_whatsapp_qr_connection(
                                        tenant
                                    )
                                )
                                if session_uuid_from_whatsapp_qr_connection:
                                    logger.info(
                                        f"sending reply to : {session_uuid_from_whatsapp_qr_connection}"
                                    )
                                    await self.channel_layer.group_send(
                                        str(session_uuid_from_whatsapp_qr_connection),
                                        {"type": "message", "message": response},
                                    )

                            else:
                                if conversation.session_uuid:
                                    logger.info(
                                        f"sending reply to : {conversation.session_uuid}"
                                    )
                                    await self.channel_layer.group_send(
                                        str(conversation.session_uuid),
                                        {"type": "message", "message": response},
                                    )

                logger.info(
                    "Bot reply processed",
                    extra={
                        "conversation_id": conversation_id,
                        "queue_matched": queue_matches,
                        "message_ids": message_ids,
                        "state_machine_id": (
                            str(state_machine.id) if state_machine else None
                        ),
                    },
                )

            elif message_type == "mid_chat_processing_bot_response":
                # Validate bot API key
                await validate_bot_api_key(bot_api_key)

                # Extract data from body
                chat_response = body.get("bot_reply", {})
                conversation_id = body.get("conversation_id")

                if not all([chat_response, conversation_id]):
                    raise CustomException(
                        "Missing required fields",
                        WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                    )

                # Extract bot message and state info from chat_response
                bot_message = chat_response.get("bot_message", [])

                if not bot_message:
                    raise CustomException(
                        "Bot message cannot be empty",
                        WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
                    )

                should_reply = await check_to_reply_whatsapp_message(
                    conversation_id, tenant
                )
                # Create the bot message
                message_array = await create_bot_message(
                    conversation_id, bot_message, tenant, should_reply
                )

                # Get the conversation to find the session UUID
                conversation = await get_conversation(conversation_id, None, tenant)

                # Check that the bot message is not [""]
                if not should_reply:
                    logger.info(f"rejecting reply as auto reply is disabled")
                    
                elif len(bot_message) > 0 and bot_message[0] != "":
                    # Format the message response
                    for message in message_array:
                        message_data = await format_message_response(
                            message, tenant
                        )

                        response = {
                            "header": {"type": "message"},
                            "body": message_data,
                        }

                        # Broadcast to the session's channel
                        if conversation.source == utility_models.WHATSAPP_QR:
                            session_uuid_from_whatsapp_qr_connection = (
                                await get_session_uuid_from_whatsapp_qr_connection(
                                    tenant
                                )
                            )
                            if session_uuid_from_whatsapp_qr_connection:
                                logger.info(
                                    f"sending reply to : {session_uuid_from_whatsapp_qr_connection}"
                                )
                                await self.channel_layer.group_send(
                                    str(session_uuid_from_whatsapp_qr_connection),
                                    {"type": "message", "message": response},
                                )

                        else:
                            if conversation.session_uuid:
                                logger.info(
                                    f"sending reply to : {conversation.session_uuid}"
                                )
                                await self.channel_layer.group_send(
                                    str(conversation.session_uuid),
                                    {"type": "message", "message": response},
                                )

                logger.info(
                    "Mid Chat Processing Bot reply processed",
                    extra={
                        "conversation_id": conversation_id,
                    },
                )

            else:
                raise CustomException(
                    "Message type not supported", WebSocketStatusCode.FORBIDDEN
                )

        except CustomException as e:
            logger.error(
                e.args[0],
                exc_info=True,
                extra={"scope": self.scope, "exception": e.args[0]},
            )
            response = ws_error_response(
                e, WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA
            )
            await self.channel_layer.send(
                self.channel_name, {"type": "message", "message": response}
            )

        except Exception as e:
            traceback.print_exc()
            # logger.error(
            #     e.args[0],
            #     exc_info=True,
            #     extra={"scope": self.scope, "exception": e.args[0]},
            # )
            response = ws_error_response(e, WebSocketStatusCode.INTERNAL_ERROR)
            await self.channel_layer.send(
                self.channel_name, {"type": "message", "message": response}
            )

    async def message(self, event: dict) -> None:
        """
        This function is called after the websocket receives and processes a message.

        Args:
            event (dict): This is the event content.
        """
        await self.send_json(event["message"])

    async def send_json(self, content: dict, close: bool = False) -> None:
        """
        Override the send_json function from the base class.
        This function is used to send json to the client.

        Args:
            content (dict): The content to be sent.
            close (bool, optional): Whether to close the connection. Defaults to False.
        """

        message = await self.encode_json(content)

        if len(message) > self.max_message_size:
            error = CustomException(
                "Message size exceeds maximum allowed.",
                WebSocketStatusCode.MESSAGE_TOO_BIG,
            )
            content = ws_error_response(error, WebSocketStatusCode.MESSAGE_TOO_BIG)

        await super().send_json(content, close=close)


@sync_to_async
def get_session_uuid_from_whatsapp_qr_connection(schema_name):
    with schema_context(schema_name):
        whatsapp_qr_connection_instance = (
            integration_models.WhatsAppQRConnection.objects.first()
        )
        if whatsapp_qr_connection_instance:
            return whatsapp_qr_connection_instance.session_uuid
        return None


@sync_to_async
def update_lateset_whatsapp_message(timestamp, schema_name):

    with schema_context(schema_name):
        whatsapp_qr_connection = integration_models.WhatsAppQRConnection.objects.first()
        if whatsapp_qr_connection:
            # Convert string timestamp to datetime object
            timestamp_dt = datetime.fromisoformat(timestamp)
            current_timestamp = whatsapp_qr_connection.latest_message_timestamp

            if current_timestamp is None:
                whatsapp_qr_connection.latest_message_timestamp = timestamp_dt
                whatsapp_qr_connection.save()
            elif timestamp_dt > current_timestamp:
                whatsapp_qr_connection.latest_message_timestamp = timestamp_dt
                whatsapp_qr_connection.save()
        return whatsapp_qr_connection


@sync_to_async
def create_new_conversation(session_uuid, source, tenant_name):
    """
    Create a new conversation with the given session UUID and source.

    Args:
        session_uuid: UUID for the session
        source: Source of the conversation (must be one of CONVERSATION_SOURCE_CHOICES)
        tenant_name: Schema name for the tenant

    Returns:
        Conversation: The newly created conversation
    """
    # Validate source is one of the allowed choices
    valid_sources = [choice[0] for choice in CONVERSATION_SOURCE_CHOICES]
    if source not in valid_sources:
        raise CustomException(
            f"Invalid source. Must be one of: {', '.join(valid_sources)}",
            WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA,
        )

    with schema_context(tenant_name):
        conversation = Conversation.objects.create(
            session_uuid=session_uuid,
            source=source,
            name="",  # blank=True
            is_auto_reply=True,  # default=True
            is_group_chat=False,  # default=False
            metadata={},  # default=dict
        )
        return conversation


@sync_to_async
def create_or_update_conversation(
    source, third_party_id, conversation_defaults, tenant_name
):
    """ """
    with schema_context(tenant_name):
        conversation = Conversation.objects.update_or_create(
            source=source,
            third_party_id=third_party_id,
            defaults={**conversation_defaults},
        )
        return conversation


@sync_to_async
def update_whatsapp_qr_session_uuid(session_uuid, tenant_name):
    with schema_context(tenant_name):
        qr_connection = integration_models.WhatsAppQRConnection.objects.first()
        if qr_connection:
            qr_connection.session_uuid = session_uuid
            qr_connection.save()
        return qr_connection


@sync_to_async
def get_conversation(chat_id, session_uuid=None, tenant_name=None):
    """
    Get conversation by ID and update session UUID if different

    Args:
        chat_id: ID of conversation to retrieve
        session_uuid: Optional session UUID to update if different
        tenant_name: Schema name for the tenant
    """
    try:
        with schema_context(tenant_name):
            conversation = Conversation.objects.get(id=chat_id)
            if session_uuid and conversation.session_uuid != session_uuid:
                conversation.session_uuid = session_uuid
                conversation.save()
            return conversation
    except ObjectDoesNotExist:
        raise CustomException("Chat not found", WebSocketStatusCode.NOT_FOUND)


@sync_to_async
def create_message(
    conversation,
    message_text,
    message_type,
    tenant_name,
    third_party_id="",
    third_party_sender="",
    timestamp=None,
    bot_reply_status="pending",
):
    timestamp = timestamp or timezone.now()
    """Create a new message in the conversation"""

    logging.info(f"create message triggered: {locals()}")
    with schema_context(tenant_name):
        message = Message.objects.create(
            conversation=conversation,
            created_at=timestamp,
            message_type=message_type,
            feedback=utility_models.NEUTRAL,
            is_audio=False,
            chat_language=utility_models.ENGLISH_US,
            bot_reply_status=bot_reply_status,
            message=message_text,
            sender=None,
            third_party_id=third_party_id,
            third_party_sender=third_party_sender,
            timestamp=timestamp,
            feedback_reason="",
            message_audio=None,
            sentiment=None,
            topics=None,
            metadata={},
        )
        return message


@sync_to_async
def get_or_create_message(
    conversation,
    message_text,
    message_type,
    tenant_name,
    third_party_id="",
    third_party_sender="",
    timestamp=None,
    bot_reply_status="pending",
    is_audio=False,
):
    with schema_context(tenant_name):
        message, _ = Message.objects.get_or_create(
            third_party_id=third_party_id,
            conversation=conversation,
            defaults={
                "conversation": conversation,
                "created_at": timestamp,
                "message_type": message_type,
                "feedback": utility_models.NEUTRAL,
                "is_audio": is_audio,
                "chat_language": utility_models.ENGLISH_US,
                "bot_reply_status": bot_reply_status,
                "message": message_text,
                "sender": None,
                "third_party_id": third_party_id,
                "third_party_sender": third_party_sender,
                "timestamp": timestamp,
                "feedback_reason": "",
                "message_audio": None,
                "sentiment": None,
                "topics": None,
                "metadata": {},
            },
        )
        return message


@sync_to_async
def update_conversation_metadata(conversation_id, metadata, tenant_name):
    """Update the conversation metadata"""
    with schema_context(tenant_name):
        conversation_obj = Conversation.objects.filter(id=conversation_id).first()

        if conversation_obj:
            current_metadata = conversation_obj.metadata

            # Loop through the new metadata and update the existing metadata
            for key, value in metadata.items():
                current_metadata[key] = value

            conversation_obj.metadata = current_metadata
            conversation_obj.save(update_fields=["metadata"])

            # Update the marketing client customer
            try:
                client_customer = ClientCustomer.objects.get(
                    conversation=conversation_obj
                )
            except ClientCustomer.DoesNotExist:
                client_customer = ClientCustomer.objects.create(
                    conversation=conversation_obj
                )

            # If Customer name exists
            to_save = False
            if not client_customer.name:
                client_customer.name = metadata.get("name", "")
                to_save = True
            if not client_customer.email:
                client_customer.email = metadata.get("email", "")
                if metadata.get("email") is None:
                    client_customer.email = ""
                to_save = True
            if not client_customer.contact:
                client_customer.contact = metadata.get("contact_number", "")
                to_save = True

            if to_save:
                client_customer.save(update_fields=["name", "email", "contact"])


@sync_to_async
def tag_audio_file(message_id, attachment_ids, tenant_name):
    """Tag audio file to the message"""
    with schema_context(tenant_name):
        message = Message.objects.get(id=message_id)
        audio_object = MessageAudio.objects.filter(id__in=attachment_ids).first()
        message.message_audio = audio_object
        message.save()


@sync_to_async
def tag_attachments(message_id, attachment_ids, tenant_name):
    """Tag attachments to the message"""
    with schema_context(tenant_name):
        message = Message.objects.get(id=message_id)
        attachments = Attachment.objects.filter(id__in=attachment_ids)
        message.attachments.add(*attachments)
        message.save()


@sync_to_async
def format_message_response(message, tenant_name, many=False):
    """Format message object for response"""
    with schema_context(tenant_name):
        return MessageSerializer.Get(message, many=many).data


@sync_to_async
def check_conversation_queue(
    queue_id: str, message_ids: List[str], tenant_name: str
) -> bool:
    """
    Check if the provided message IDs match all pending and in-process messages for the conversation.

    Args:
        queue_id: ID of the conversation queue
        message_ids: List of message IDs to validate
        tenant_name: Schema name for the tenant

    Returns:
        bool: True if message_ids match exactly with pending/in-process messages, False otherwise
    """
    with schema_context(tenant_name):
        try:
            # Get conversation ID from queue
            queue = ConversationQueue.objects.get(id=queue_id)
            conversation_id = queue.conversation_id

            # Get all pending and in-process messages for this conversation
            current_messages = Message.objects.filter(
                conversation_id=conversation_id,
                bot_reply_status__in=["pending", "in_process"],
            ).values_list("id", flat=True)

            # Convert to sets for comparison
            # Using sets ensures order-independent comparison since sets are unordered collections
            current_message_set = set(str(msg_id) for msg_id in current_messages)
            new_message_set = set(message_ids)

            # Return True only if sets contain exactly the same elements
            # Set equality (==) compares elements without regard to order
            return current_message_set == new_message_set

        except ConversationQueue.DoesNotExist:
            return False


@sync_to_async
def reset_processing_messages(conversation_id: str, tenant_name: str) -> None:
    """Reset all in-process messages back to pending"""
    print(f"conversation_id: {conversation_id}")
    print(f"tenant_name: {tenant_name}")
    with schema_context(tenant_name):
        Message.objects.filter(
            conversation_id=conversation_id, bot_reply_status="in_process"
        ).update(bot_reply_status="pending")


@sync_to_async
def check_to_reply_whatsapp_message(conversation_id, tenant_name):
    with schema_context(tenant_name):
        whatsapp_qr_connection_instance = (
            integration_models.WhatsAppQRConnection.objects.first()
        )
        conversation_instance = Conversation.objects.get(id=conversation_id)

        should_reply = True
        if (
            whatsapp_qr_connection_instance
            and whatsapp_qr_connection_instance.auto_reply == False
            and conversation_instance.source == utility_models.WHATSAPP_QR
        ):
            should_reply = False
        if should_reply and conversation_instance.is_auto_reply == False:
            should_reply = False

    return should_reply


@sync_to_async
def create_bot_message(
    conversation_id: str,
    message_list: list,
    tenant_name: str,
    should_reply: bool = True,
) -> Message:
    """Create a new bot message"""
    message_array = []
    with schema_context(tenant_name):
        for message_text in message_list:
            # message_text must be in string
            message_text = str(message_text)

            # Find all image markdown patterns with optional leading slash
            image_pattern = rf"!\[Image\]\(?/?{tenant_name}/[^)]+\)"
            matches = re.finditer(image_pattern, message_text)

            # Process each match
            for match in matches:
                full_match = match.group(0)
                # Extract the path between parentheses, handling optional leading slash
                path_match = re.search(rf"\(?/?{tenant_name}/([^)]+)\)", full_match)

                print(f"full_match: {full_match}")
                print(f"path_match: {path_match}")
                if path_match:
                    image_path = path_match.group(1)
                    try:
                        # Get the KnowledgeImage object based on the file path
                        knowledge_image = KnowledgeImage.objects.get(
                            file=f"{tenant_name}/{image_path}"
                        )
                        # Get the S3 URL from the file field
                        s3_url = knowledge_image.file.url

                        if str(s3_url).startswith("/"):
                            # If the S3 URL starts with a slash, remove it
                            s3_url = (
                                f"https://{AWS_STORAGE_BUCKET_NAME}/{str(s3_url)[1:]}"
                            )
                        elif str(s3_url).startswith("https://"):
                            # If the S3 URL starts with "https://", keep it as is
                            pass
                        else:
                            # If the S3 URL does not start with "https://", prepend it
                            s3_url = f"https://{AWS_STORAGE_BUCKET_NAME}/{s3_url}"

                        # Replace the original path with S3 URL
                        message_text = message_text.replace(
                            path_match.group(0), f"({s3_url})"
                        )

                        print(f"image_path: {image_path}")
                        print(f"s3_url: {s3_url}")

                    except KnowledgeImage.DoesNotExist:
                        # If image not found, leave the path as is
                        pass

            message = Message(
                conversation_id=conversation_id,
                message=message_text,
                message_type="bot_text",
                bot_reply_status="completed",
            )
            message_array.append(message)

        if should_reply:
            Message.objects.bulk_create(message_array)

    return message_array


@sync_to_async
def update_conversation_queue(queue_id: str, tenant_name: str) -> None:
    """Update conversation queue is_completed status"""
    with schema_context(tenant_name):
        ConversationQueue.objects.filter(id=queue_id).update(is_completed=True)


@sync_to_async
def update_in_process_messages(
    conversation_id: str, message_ids: List[str], tenant_name: str
) -> None:
    """Update all in-process messages to completed status"""
    with schema_context(tenant_name):
        Message.objects.filter(
            conversation_id=conversation_id,
            id__in=message_ids,
            bot_reply_status="in_process",
        ).update(bot_reply_status="completed")


@sync_to_async
def update_state_machine(
    state_info: dict,
    tenant_name: str,
    message_array: List[Message],
    should_reply: bool = True,
) -> StateMachine:
    """Update or create a state machine entry for the last bot message in the conversation"""
    last_message = message_array[-1]
    if not should_reply:
        return None

    with schema_context(tenant_name):
        state_machine = StateMachine.objects.create(
            message=last_message, context=state_info
        )

        return state_machine
