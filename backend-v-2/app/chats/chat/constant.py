import os

chroma_db_folder = "./all_chroma_db/"

persist_directory_internal = chroma_db_folder + "chroma_db_internal"
persist_directory_external = chroma_db_folder + "chroma_db_external"

internal_collection = "internal"
external_collection = "external"


def get_directory(persist_directory, schema_name):
    return persist_directory + "_" + schema_name


def get_collection_name(internal_or_external_collection, schema_name):
    return internal_or_external_collection + "_" + schema_name


# asc
#GPT_35 meant for cheaper and faster option, the model will update accordingly, hence we keep the variable name, but actual model name will be updated accordingly
GPT_35_MODEL_CHOICES = [
    os.environ.get("GPT_35_MODEL_SHORTER_CONTEXT", "gpt-4o-mini"),
    os.environ.get("GPT_35_MODEL_LONGER_CONTEXT", "gpt-4o-mini"),
]  # as per 19/2/2024, gpt-3.5-turbo supporting 16k input tokens + 4k output (using 0125) (the second option meant to be stronger one)
GPT_4_MODEL_CHOICES = [
    os.environ.get("GPT_4_MODEL_SHORTER_CONTEXT", "gpt-4o"),
    os.environ.get("GPT_4_MODEL_LONGER_CONTEXT", "gpt-4o"),
]  # 32k not usable
