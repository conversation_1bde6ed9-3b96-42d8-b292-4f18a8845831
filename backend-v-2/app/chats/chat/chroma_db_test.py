from langchain import OpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.document_loaders import TextLoader, PyPDFLoader
from langchain.vectorstores.chroma import Chroma

# from django.db import connections

persist_directory_internal = "./all_chroma_db/chroma_db_internal_nie"
persist_directory_external = "./all_chroma_db/chroma_db_external_nie"
# persist_directory_internal_external = "./chroma_db_internal_external"
import dotenv

dotenv.load_dotenv()

import chromadb
from chromadb.config import Settings
from langchain.embeddings import OpenAIEmbeddings

import os

OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
embeddings = OpenAIEmbeddings()


def get_chromadb_client():
    client = chromadb.HttpClient(
        host="127.0.0.1",
        port="4000",
        settings=Settings(allow_reset=True),
    )
    return client


def list_collection_names(client):
    collections = client.list_collections()
    names = [collection.name for collection in collections]
    return names


# def get_document_list(file_urls):
#     documents = []
#     for file_url in file_urls:
#         # print("\n"*10)
#         # print("file_url: ", file_url)
#         # print("\n"*10)
#         if file_url.endswith(".pdf"):
#             loader = PyPDFLoader(file_url)
#             # print('\n' *10)
#             # for attribute, value in vars(loader).items():
#             #     print(f"loader.{attribute} = {value}")
#             # temp_documents = loader.load()

#             # for temp_doc in temp_documents:
#             #     for attribute, value in vars(temp_doc).items():
#             #         print(f"temp_doc.{attribute} = {value}")
#             # print('\n' *10)
#             # print('\n' *10)
#             temp_documents = loader.load()
#             for temp_doc in temp_documents:
#                 temp_doc.metadata = {
#                     **temp_doc.metadata,
#                     "source": f"{file_url}#page={temp_doc.metadata['page'] + 1}",
#                 }

#             # for temp_doc in temp_documents:
#             #     print('temp_doc meta: ', temp_doc.metadata)
#             # print(temp_doc)
#             # print('\n' *10)
#             documents.extend(temp_documents)

#         elif file_url.endswith(".txt"):
#             loader = TextLoader(file_url)
#             documents.extend(loader.load())

#     text_splitter = RecursiveCharacterTextSplitter(
#         chunk_size=1000,
#         chunk_overlap=20,
#         separators=[" ", ",", "\n"],
#         add_start_index=True,
#     )
#     document_list = text_splitter.split_documents(documents)
#     # print('\n' * 10)
#     # print("document_list: ", document_list)
#     # print('\n' * 10)

#     return document_list


# for persist_directory in [
#     persist_directory_internal,
#     persist_directory_external,
#     # persist_directory_internal_external,
# ]:
#     chroma_db_collection = Chroma(
#         embedding_function=embeddings, persist_directory=persist_directory
#     )
#     print("=" * 30, f"in {persist_directory}")
#     print(
#         "chroma_db_collection.get before",
#         str(chroma_db_collection.get(include=["metadatas"]))[:6000],
#         "\n" * 2,
#     )

collection_name = "external_demo_furnishing"
client = get_chromadb_client()
chroma_db_collection = Chroma(
    embedding_function=embeddings,
    client=client,
    collection_name=collection_name,
)
print(chroma_db_collection.get())


# all_collection_names = list_collection_names(client)
# for collection_name in all_collection_names:
#     chroma_db_collection = Chroma(
#         embedding_function=embeddings,
#         client=client,
#         collection_name=collection_name,
#     )
#     print("chroma_db_collection ", chroma_db_collection)


# get db items:
"""
    print('chroma_db_collection.get before', chroma_db_collection.get(include=[]))
"""

# Delete file by giving in file name:
"""
    ids = chroma_db_collection.get(include=[])['ids']
    ids = [id for id in ids if '{your-file-name}' in id]
    chroma_db_collection.delete(ids=ids)
"""
# Update file

"""
    document_list = get_document_list(["https://s3-chiwi-dev.s3.amazonaws.com/Info_Deck_on_PreceptsGroup_Services_Full_Range_2023.04.06_l4H8Ggg.pdf"])
    doc_ids = [document.metadata["source"] + str(uuid.uuid4()) for document in document_list]
    Chroma.from_documents(document_list, embedding=embeddings, persist_directory=persist_directory, ids=doc_ids)#to update with new files
"""

# print('chroma_db_collection.get after', chroma_db_collection.get(include=[]))
# print('cleaned id', chroma_db_collection.get(ids=ids))

# `{$contains: {"text": "hello"}}`
