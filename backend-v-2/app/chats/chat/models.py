from django.contrib.auth import get_user_model

from app.chats.chat.abstract_models import *

User = get_user_model()


class MessageAudio(AbstractMessageAudio):
    pass


class Conversation(AbstractConversation):
    pass


class Message(AbstractMessage):
    conversation = models.ForeignKey(
        Conversation, related_name="messages", on_delete=models.CASCADE
    )
    sender = models.ForeignKey(
        User, related_name="messages", on_delete=models.CASCADE, null=True, blank=True
    )
    message_audio = models.OneToOneField(
        MessageAudio,
        related_name="message",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )  # from user, changed from foreign to one to one

    def get_previous_message(self):
        return (
            Message.objects.filter(
                conversation=self.conversation, created_at__lt=self.created_at
            )
            .order_by("-created_at")
            .only("id")
            .first()
        )

    def get_next_message(self):
        return (
            Message.objects.filter(
                conversation=self.conversation, created_at__gt=self.created_at
            )
            .order_by("created_at")
            .only("id")
            .first()
        )


class ConversationQueue(AbstractConversationQueue):
    conversation = models.ForeignKey(
        Conversation, related_name="conversation_queue", on_delete=models.CASCADE
    )


class Attachment(AbstractAttachment):
    message = models.ForeignKey(
        Message,
        related_name="attachments",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )


class StateMachine(AbstractStateMachine):
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name="state_machines",
        null=True,
        blank=True,
    )


class FlowMapping(AbstractFlowMapping):
    pass
