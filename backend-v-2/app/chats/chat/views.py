import app.core.authapi.permission as authapi_permission
import app.core.authapi.models as authapi_models
import app.core.authapi.helper as authapi_helper

import app.analytic.helper as analytic_helper
import app.analytic.views as analytic_views

import app.chats.bot.chatbot_helper as bot_chatbot_helper
import app.chats.bot.models as bot_models

import app.chats.chat.models as chat_models
import app.chats.chat_action_tracker.models as chat_action_tracker_models
import app.chats.chat.serializers as chat_serializers
import app.chats.chat.helper as chat_helper
import app.chats.chat.constant as chat_constant
import app.chats.chat.transcribe_helper as transcribe_helper

import app.integrations.facebook_integration.helper as facebook_integration_helper

import app.utility.models as utility_models
import app.utility.common_class as utility_common_class
import app.utility.general_helper as utility_general_helper


import app.marketing.marketing.models as marketing_models
import app.integrations.integration.models as integration_models
import app.integrations.integration.serializers as integration_serializers
import app.integrations.integration.telegram_helper as telegram_helper

import asyncio
import csv
import datetime
import logging
import base64
import requests
from io import BytesIO

from django.http import HttpResponse
from django.utils import timezone
from django.db import transaction
from django.db.models import Max, Q
from django.core.files.base import ContentFile

from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.filters import SearchFilter, OrderingFilter

import traceback

logger = logging.getLogger(__name__)


# Create your views here.
class ConversationView:
    class ConversationBaseView(generics.GenericAPIView):
        queryset = chat_models.Conversation.objects.all()
        serializer_class = chat_serializers.ConversationSerializer.List
        permission_classes = [IsAuthenticated]
        pagination_class = utility_common_class.CustomPageNumberPagination

        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "name",
            "created_at",
            "created_by__full_name",
            "metadata__email",
        ]
        ordering_fields = [
            "name",
            "created_at",
            "created_by__full_name",
            "metadata__email",
        ]
        ordering = ["-messages_created_at"]

        def get_queryset(self):
            queryset = super().get_queryset()
            messages_created_at = Max("messages__created_at")
            queryset = (
                queryset.prefetch_related("messages")
                .annotate(messages_created_at=messages_created_at)
                .filter(messages_created_at__isnull=False)
            )  # latest display at the highest.
            return queryset

    class List(ConversationBaseView, generics.ListAPIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def get_queryset(self):
            queryset = super().get_queryset()

            if source_filters := self.request.query_params.get("source", ""):
                source_filters = source_filters.split(",")
                q_objects = Q()
                for source_filter in source_filters:
                    q_objects |= Q(source__icontains=source_filter)
                queryset = queryset.filter(q_objects)

            if auto_reply := self.request.query_params.get("auto_reply", ""):
                auto_reply = str(auto_reply).lower() == "true"
                queryset = queryset.filter(is_auto_reply=auto_reply)

            return queryset

    class AdminViewList(ConversationBaseView, generics.ListAPIView):
        serializer_class = chat_serializers.ConversationSerializer.AdminViewList
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def get_queryset(self):
            queryset = super().get_queryset()
            is_public = (
                self.request.query_params.get("is_public", "true").lower() == "true"
            )
            queryset = queryset.filter(created_by__isnull=is_public)

            return queryset

    class Get(ConversationBaseView, generics.RetrieveAPIView):
        permission_classes = [AllowAny]
        serializer_class = chat_serializers.ConversationSerializer.Get

        def get(self, request, *args, **kwargs):
            conversation = chat_models.Conversation.objects.get(id=self.kwargs["pk"])

            saved_schema = authapi_helper.get_schema_name()
            if self.request.user.is_authenticated:
                tenant_information = request.user.user_tenant_bridges.values_list(
                    "tenant__schema_name", "role"
                )
                tenant_dict = {
                    schema_name: role for schema_name, role in tenant_information
                }

                if (
                    conversation.created_by == self.request.user
                    or self.request.user.role == authapi_models.ADMIN
                    or tenant_dict.get(saved_schema, "None") == authapi_models.ADMIN
                ):
                    return super().get(request, *args, **kwargs)

            message_id = self.request.query_params.get("message_id", -1)

            if not message_id:  # Checking if message_id is provided in query_params
                return Response(
                    status=status.HTTP_400_BAD_REQUEST
                )  # Or any other appropriate error response

            message_ids = conversation.messages.all().values_list("id", flat=True)
            if int(message_id) in message_ids:
                return super().get(request, *args, **kwargs)
            return Response(
                data={"body": "Not authorized"}, status=status.HTTP_401_UNAUTHORIZED
            )

    class GetLatestConversationId(ConversationBaseView, APIView):
        def get(self, request, *args, **kwargs):
            saved_schema = authapi_helper.get_schema_name()
            if self.request.user.is_authenticated:
                tenant_information = request.user.user_tenant_bridges.values_list(
                    "tenant__schema_name", "role"
                )
                tenant_dict = {
                    schema_name: role for schema_name, role in tenant_information
                }

                if (
                    self.request.user.role == authapi_models.ADMIN
                    or tenant_dict.get(saved_schema, "None") == authapi_models.ADMIN
                ):
                    latest_conversation_instance = (
                        chat_models.Conversation.objects.first()
                    )
                    if latest_conversation_instance:
                        return Response(
                            latest_conversation_instance.id,
                            status=status.HTTP_200_OK,
                        )

            return Response(-1, status=status.HTTP_404_NOT_FOUND)

    class GetWhatsAppConversationByNumber(
        ConversationBaseView, generics.RetrieveAPIView
    ):
        permission_classes = [AllowAny]
        serializer_class = chat_serializers.ConversationSerializer.Get
        lookup_field = "number"

        def get(self, request, *args, **kwargs):
            number = self.kwargs.get("number")
            conversation = chat_models.Conversation.objects.filter(
                third_party_id=number,
                source=utility_models.WHATSAPP_QR,
            ).first()
            if conversation:
                return Response(
                    {"conversation_id": conversation.id}, status=status.HTTP_200_OK
                )
            return Response({"conversation_id": None}, status=status.HTTP_200_OK)

    class Create(ConversationBaseView, generics.CreateAPIView):
        serializer_class = chat_serializers.ConversationSerializer.Update

        def post(self, request, *args, **kwargs):
            request.data["created_by"] = self.request.user.id
            return super().post(request, *args, **kwargs)

    class Update(ConversationBaseView, generics.UpdateAPIView):
        serializer_class = chat_serializers.ConversationSerializer.Update

    class Delete(ConversationBaseView, generics.DestroyAPIView):
        pass

    class GetQuestionAskedCsvFile(APIView):
        permission_classes = [IsAuthenticated]

        def get(self, request, *args, **kwargs):
            """
            Return:
            {
                .csv files that has column: user_email, user_ip, country, created_at, question_asked
            }
            """
            conversation_instances = chat_models.Conversation.objects.all()

            is_public = self.request.query_params.get("is_public", True)
            conversation_instances = analytic_helper.filter_conversation_instances(
                is_public, conversation_instances
            )

            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                'attachment; filename="question_asked_list.csv"'
            )

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "User Email",
                    "User IP",
                    "Country",
                    "Conversation Created At",
                    "Question Asked",
                ]
            )

            # Write data to CSV
            for conversation_instance in conversation_instances:
                email = "anonymous"
                if conversation_instance.created_by:
                    email = conversation_instance.created_by.email

                ip_address = country = created_at = "No Record"
                try:
                    ip_address = conversation_instance.ip_address_record.ip_address
                    country = conversation_instance.ip_address_record.country
                    created_at = conversation_instance.ip_address_record.created_at
                except:
                    pass
                    # print("ip address record doesn't exist in the conversation.")

                writer.writerow(
                    [
                        email,
                        ip_address,
                        country,
                        created_at,
                        ";;".join(
                            conversation_instance.messages.exclude(
                                message_type=utility_models.MACHINE_TEXT
                            ).values_list("message", flat=True)
                        ),
                    ]
                )

            return response


class MessageView:
    class MessageBaseView(generics.GenericAPIView):
        queryset = chat_models.Message.objects.all()
        serializer_class = chat_serializers.MessageSerializer.Get
        pagination_class = utility_common_class.CustomPageNumberPagination

    class Update(MessageBaseView, generics.UpdateAPIView):
        pass

    class Create(MessageBaseView, generics.CreateAPIView):
        def post(self, request, *args, **kwargs):
            """
            This endpoint takes in user question, generate the answer and reply (so 2 message instances are created.)
            FE need to pass in:
            {
            conversation: [number] Conversation Id
            message: [string] Message Content
            audio_file: [File] Audio File
            source: [string] Where does this message send from
            email: [string, optional, public only] Customer Client Email
            is_signup_newsletter: [boolean, optional, public only] Boolean
            }

            Flow:
            1. User ask the question, and backend take in information
            1.1 (Optional) Translate audio file
            1.2 (Optional) Save User Image / File
            2. Create or Get conversation
            2.1 (Optional) Create Client Customer
            2.2 (Optional) Update IP address record if conversation created
            3. Get response from chatgpt on the user question
            4. Save user message.
            5. Save machine message.
            6. Return the response.

            """
            # return super().post(request, *args, **kwargs)
            # Get the chat session
            conversation_id = request.data.get("conversation")
            source = request.data.get("source", utility_models.WEB_APPLICATION)
            audio_duration = datetime.timedelta(
                seconds=float(request.data.get("duration", 0))
            )

            incoming_message = request.data.get("message", "")
            incoming_audio_file = request.data.get("audio_file", "")
            incoming_attachments = request.FILES.getlist("files")

            chat_language = request.data.get("chat_language", utility_models.ENGLISH_US)

            customer_client_email = request.data.get("email", "")
            customer_client_is_signup_newsletter = (
                str(request.data.get("is_signup_newsletter", "")).lower() == "true"
            )

            is_audio = False
            if incoming_audio_file:  # audio
                is_audio = True
                incoming_message = transcribe_helper.transcribe(incoming_audio_file)

            # 1. Create conversation
            conversation_defaults = {"name": incoming_message, "source": source}

            if self.request.user.is_authenticated:
                conversation_defaults["created_by"] = self.request.user

            conversation, conversation_created = (
                chat_models.Conversation.objects.get_or_create(
                    id=conversation_id,
                    defaults=conversation_defaults,
                )
            )

            if conversation_created:
                # print("updating ip address now...")
                analytic_views.update_ip_address_record(request, conversation.id)

            # 2. Create customer client object
            if customer_client_email:
                # print("creating client customer...")
                marketing_models.ClientCustomer.objects.get_or_create(
                    conversation=conversation,
                    defaults={
                        "email": customer_client_email,
                        "is_signup_newsletter": customer_client_is_signup_newsletter,
                    },
                )
                # Now update the conversation_instance metadata with the merged information
                conversation.metadata.update({"email": customer_client_email})
                conversation.save()

            # 3. Create audio file if any
            message_audio_object = None
            if incoming_audio_file:
                message_audio_object = chat_models.MessageAudio.objects.create(
                    file=incoming_audio_file, duration=audio_duration
                )

            # 4. Create the user's message
            message_content_dict = {
                "conversation": conversation,
                "message": incoming_message,
                "message_audio": message_audio_object,
                "is_audio": is_audio,
            }
            if self.request.user.is_authenticated:
                message_content_dict["sender"] = self.request.user
                message_content_dict["created_by"] = self.request.user
            else:
                message_content_dict["sender"] = None
                message_content_dict["message_type"] = utility_models.ANONYMOUS_TEXT

            with transaction.atomic():
                user_message = chat_models.Message(**message_content_dict)
                user_message.save()

                # 3.1 Create user uploaded files
                for incoming_attachment in incoming_attachments:
                    attachment_type = utility_general_helper.categorize_file(
                        incoming_attachment.name
                    )
                    chat_models.Attachment.objects.create(
                        message=user_message,
                        file=incoming_attachment,
                        attachment_type=attachment_type,
                    )

                if conversation.is_auto_reply == False:
                    return Response(
                        "User Msg Saved. No reply is required.",
                        status=status.HTTP_201_CREATED,
                    )

                # 5. Get & Create the chatbot's message
                historical_messages = chat_helper.get_historical_messages(conversation)
                schema_name = authapi_helper.get_schema_name()
                external_collection = chat_constant.get_collection_name(
                    chat_constant.external_collection, schema_name
                )
                bot_setting_instance = bot_models.BotSetting.objects.get(
                    bot_type=utility_models.EXTERNAL_TEXT
                )

                # 5.1 Check if there's delivery info path to be fed in
                # nova_delivery_csv_file_path = (
                #     nova_related_helper.get_delivery_csv_file_path(
                #         "Nova Furnishing", schema_name
                #     )
                # )

                # 5.2 Get Chatbot reply
                response_message_text = bot_chatbot_helper.get_chatbot_response(
                    incoming_message,
                    historical_messages,
                    external_collection,
                    bot_setting_instance,
                    schema_name,
                    chat_language,
                    conversation.id,
                    # delivery_csv_file_path=nova_delivery_csv_file_path,
                    # chatbot_extra_source_name="Ang Mo Kio",
                )

                if not response_message_text:
                    # No reply needed or failed
                    serializer = chat_serializers.MessageSerializer.Get(user_message)
                    return Response(
                        data=serializer.data,
                        status=status.HTTP_201_CREATED,
                    )

                # 5.3 Create the chatbot's message
                response_message = chat_models.Message(
                    conversation=conversation,
                    sender=None,
                    message_type=utility_models.MACHINE_TEXT,
                    message=response_message_text,
                    is_audio=is_audio,
                    chat_language=chat_language,
                )
                response_message.save()

                # Return the chatbot's message as a response
                serializer = chat_serializers.MessageSerializer.Get(response_message)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

    class CreateReply(MessageBaseView, generics.CreateAPIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def post(self, request, *args, **kwargs):
            """
            This endpoint reply to the user in a particular conversation. Only 1 msg is generated.
            FE need to pass in:
            {
            conversation: [number] Conversation Id
            message: [string] Message Content
            message_id_to_reply: [string] Only for telegram, no need now.
            }
            source: [string] can be derived from conversation

            Flow:
            1. Admin click "Send" in the FE
            2. Backend handle the send (diff source diff method) + save to db

            """
            # return super().post(request, *args, **kwargs)
            # Get the chat session
            reply_to_user_message = request.data.get("message", "")
            message_id_to_reply = request.data.get(
                "message_id_to_reply", ""
            )  # for telegram only

            # assert 1 == 2
            conversation_id = request.data.get("conversation")
            conversation = chat_models.Conversation.objects.get(
                id=conversation_id,
            )
            source = conversation.source  # third party, if uu then it's none

            def handle_message_from_diff_third_party_sources(source):
                """
                Reply to third party platform from diff sources.
                Return the third party id
                """
                match source:
                    case utility_models.FACEBOOK:
                        page_id = conversation.sub_source
                        sender_psid = conversation.sender_psid
                        if not (page_id and sender_psid):
                            print(
                                "Page ID or sender_psid is not found in this conversation!"
                            )
                            return ""

                        facebook_integration_helper_instance = (
                            facebook_integration_helper.FacebookIntegrationAPICall(
                                page_id
                            )
                        )
                        msg_res = facebook_integration_helper_instance.send_message(
                            post_data={
                                "message": reply_to_user_message,
                                "customer_psid": sender_psid,
                            }
                        )
                        print("Facebook msg_res: ", msg_res)
                        return msg_res.get("message_id", False)
                    case utility_models.TELEGRAM:
                        bot_username = conversation.sub_source
                        if not bot_username:
                            print("Bot Username is not found in this conversation!")
                            return ""

                        telegram_connection_instance = (
                            integration_models.TelegramConnection.objects.filter(
                                bot_username=bot_username
                            ).first()
                        )

                        reply_message_id = asyncio.run(
                            telegram_helper.send_reply(
                                telegram_connection_instance.token,
                                conversation.third_party_id,
                                message_id_to_reply,
                                reply_to_user_message,
                            )
                        )
                        # false or id
                        return reply_message_id
                    case utility_models.WHATSAPP_QR:
                        # check whatsapp qr
                        whatsapp_qr_connection_instance = (
                            integration_models.WhatsAppQRConnection.objects.first()
                        )
                        if (
                            whatsapp_qr_connection_instance
                            and whatsapp_qr_connection_instance.status
                            == utility_models.ACTIVE
                            and conversation.third_party_id
                        ):
                            msg_to_send_instance = (
                                chat_action_tracker_models.MessageToSend.objects.create(
                                    contact=conversation.third_party_id,
                                    message=reply_to_user_message,
                                    target_source=utility_models.WHATSAPP_QR,
                                    message_type=utility_models.PAGE_ADMIN_TEXT,
                                    to_be_sent_at=timezone.now(),
                                )
                            )
                            # TODO-yc: Create msg to send attachment objects here in future
                            return msg_to_send_instance.id
                    case utility_models.WHATSAPP:
                        # whatsapp api
                        return ""
                    case utility_models.WECHAT:
                        return ""
                    case utility_models.INSTAGRAM:
                        return ""
                    case _:
                        return ""

            uu_sources = [
                utility_models.WEB_APPLICATION,
                utility_models.FLOATING_CHAT,
                utility_models.SHOPIFY,
                utility_models.WIX,
                utility_models.WOOCOMMERCE,
            ]
            if source in uu_sources:  # UU itself
                third_party_message_id = ""
            else:
                third_party_message_id = handle_message_from_diff_third_party_sources(
                    source
                )
                if not third_party_message_id:
                    return Response(
                        {
                            "error": f"Something wrong when sending message via {source}. Please try again or contact the web admin for help."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Create the page-admin's message
            page_admin_message = chat_models.Message.objects.create(
                third_party_id=third_party_message_id,
                conversation=conversation,
                message_type=utility_models.PAGE_ADMIN_TEXT,
                message=reply_to_user_message,
                sender=self.request.user,
                created_by=self.request.user,
                bot_reply_status="completed",
            )

            if source == utility_models.WHATSAPP_QR and third_party_message_id:
                # add msg to send
                msg_to_send_instance = (
                    chat_action_tracker_models.MessageToSend.objects.get(
                        id=third_party_message_id
                    )
                )
                if msg_to_send_instance:
                    msg_to_send_instance.uu_message = page_admin_message
                    msg_to_send_instance.save()

            # Return the message as a response
            serializer = chat_serializers.MessageSerializer.Get(page_admin_message)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

    class FeedBackList(MessageBaseView, generics.ListAPIView):
        serializer_class = chat_serializers.MessageSerializer.FeedbackList

        def get_queryset(self):
            queryset = super().get_queryset().filter(feedback__isnull=False)
            # queryset = queryset.filter(created_by=self.request.user)
            return queryset

    class CheckMessageSentStatus(MessageBaseView, APIView):

        def get(self, request, *args, **kwargs):
            """
            Check whether the message has been sent to the third party platform
            """
            message_id = self.kwargs.get("pk")
            if not message_id:
                return Response(
                    data={"error": "No message id found."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            message_instance = chat_models.Message.objects.get(id=message_id)
            message_status = utility_models.COMPLETED
            if hasattr(message_instance, "message_to_send"):
                is_msg_sent = (
                    message_instance.message_to_send.status == utility_models.COMPLETED
                )
                if not is_msg_sent:
                    message_status = utility_models.PENDING
            return Response(data={"status": message_status}, status=status.HTTP_200_OK)

    class GetLatesetWhatsappMessage(APIView):
        def get(self, request, *args, **kwargs):
            whatsapp_qr_connection = (
                integration_models.WhatsAppQRConnection.objects.first()
            )
            if not whatsapp_qr_connection:
                return Response(
                    data={"error": "No connection found."},
                    status=status.HTTP_404_NOT_FOUND,
                )
            latest_message_timestamp = whatsapp_qr_connection.latest_message_timestamp
            if not latest_message_timestamp:
                return Response(
                    data={"error": "No message found."},
                    status=status.HTTP_404_NOT_FOUND,
                )
            serializer_data = integration_serializers.WhatsAppQRConnectionSerializer(
                whatsapp_qr_connection
            ).data
            data = {"created_at": serializer_data["latest_message_timestamp"]}
            return Response(data=data, status=status.HTTP_200_OK)

    class CheckAttachmentsByMessageId(APIView):
        def get(self, request, *args, **kwargs):
            third_party_id = self.kwargs.get("third_party_id")
            if not third_party_id:
                return Response(
                    data={"error": "No message id found."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            try:
                message_instance = chat_models.Message.objects.get(
                    third_party_id=third_party_id
                )
            except chat_models.Message.DoesNotExist:
                return Response(
                    data={"error": "Message not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )
            if message_instance.is_audio:
                return Response(
                    data={"attachment_count": 1},
                    status=status.HTTP_200_OK,
                )
            attachments = message_instance.attachments.all()
            if attachments:
                return Response(
                    data={"attachment_count": attachments.count()},
                    status=status.HTTP_200_OK,
                )
            return Response(data={"attachment_count": 0}, status=status.HTTP_200_OK)

    class UpdateWhatsAppMessageThirdPartyId(APIView):
        def post(self, request, *args, **kwargs):
            message_id = self.kwargs.get("pk")
            if not message_id:
                return Response(
                    data={"error": "No message id found."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            message_instance = chat_models.Message.objects.get(id=message_id)
            message_instance.third_party_id = request.data.get("third_party_id")
            message_instance.save()
            return Response(data={"status": "success"}, status=status.HTTP_200_OK)


class MessageAudioView:
    class MessageAudioBaseView(generics.GenericAPIView):
        serializer_class = chat_serializers.MessageAudioSerializer

    class Create(MessageAudioBaseView, generics.CreateAPIView):
        pass


class AttachmentView:
    class AttachmentBaseView(generics.GenericAPIView):
        serializer_class = chat_serializers.AttachmentSerializer.Create

    class Create(AttachmentBaseView, generics.CreateAPIView):
        pass


class WhatsAppQRChat(APIView):
    """
    TO BE TRANSFER TO INTEGRATION > WHATSAPP QR
    1. This function will have only 1 whatsapp connection (per tenant)
    2. in the whatsapp connection we can check whether trigger send live to whatsapp, and return it in response so that ec2 can capture it
    3. if conversation not live or whatsapp connection not live, restrict the reply or add in "(Draft)"
    """

    def post(self, request):
        """
        Derived from utter_unicorn_bot.py > check_and_reply > formatted msgs
        {
          'conversation_name': '+60 xx-xxx xxxx',
          'source': 'WhatsApp QR',
          'messages': [
            {
              'phone_number': '60xxxxxxxxx',
              'role':'receiving' / 'sending',
              'group_id': '120363304329976582' / ''
              'message_id': '3EB0A6E1E2C06E7869C05F',
              'name': '+60 xx-xxx xxxx' / 'Contact Name',
              'type': 'text' / 'image' / 'voice',
              'timestamp': '2024-05-06T10:40:00+08:00',
              'is_mentioned_user': False / True,
              'message': 'hi there',
              'media': None,
              'media_base64': None
            }
          ]
        }

        """
        try:
            logger.info("Start websapp message received.")
            logger.info(f"Request data: {request.data}")
            messages = request.data.get("messages", [])
            source = utility_models.WHATSAPP_QR
            conversation_name = request.data.get(
                "conversation_name"
            )  # username (group) / contact number (individual)
            is_mentioned = False
            for m in messages:
                if m.get("is_mentioned_user", False):
                    is_mentioned = True
                    break
            conversation_third_party_id = messages[0].get("group_id", "")
            is_group_chat = True
            if conversation_third_party_id == "":  # this is individual chat
                conversation_third_party_id = messages[0].get("phone_number", "")
                is_group_chat = False
            # message_type = message

            # TODO:Leon: WhatsAppQRWhiteList is tied to chat action AppointmentChatFeatureLocation, now it is removed from whatsapp qr chat, in case we use the appointment feature again, need to consider next 13 lines of comments

            # white_list = list(
            #     integration_models.WhatsAppQRWhiteList.objects.all().values_list(
            #         "name", flat=True
            #     )
            # )

            ## If is groupchat + not (conversation in white_list) = error
            # if is_group_chat and conversation_name not in white_list:
            #     logger.error(
            #         f"Group chat not in white list: {conversation_name} {contact_number}"
            #     )
            #     return Response(
            #         data={"send": False}, status=status.HTTP_400_BAD_REQUEST
            #     )

            ## TAKING IN MESSAGES from the group
            chat_language = utility_models.ENGLISH_US  # TODO: detect language here

            conversation_defaults = {
                "name": conversation_name,
                "source": source,
                "third_party_id": conversation_third_party_id,
                "is_group_chat": is_group_chat,
            }

            (
                conversation,
                conversation_created,
            ) = chat_models.Conversation.objects.update_or_create(
                source=source,
                third_party_id=conversation_third_party_id,
                # is_group_chat=is_group_chat,
                defaults={
                    **conversation_defaults,
                },
            )
            schema_name = authapi_helper.get_schema_name()

            if conversation_created:
                # print("updating ip address now...")
                analytic_views.update_ip_address_record(request, conversation.id)

            latest_created_message_obj = None
            attachments = []
            for m in messages:
                message_third_party_id = m["message_id"]
                # if m["media_base64"] != None:
                #     m["media_base64"], m["media"], schema_name, conversation_name
                # )
                # if m["message"] == None:
                #     m["message"] = file_url
                # else:
                #     m["message"] = m["message"] + "\n" + file_url
                message_type = m.get("type", "text")
                if message_type == "voice":
                    m["message"] = "# Voice Message #"

                if m["message"] and m["message"].strip() != "":
                    data = {
                        "conversation": conversation,
                        "message": m["message"],
                        "third_party_id": message_third_party_id,
                        "third_party_sender": m["name"],
                        "created_at": m["timestamp"],
                    }
                    message_obj = chat_models.Message(**data)
                    message_obj.save()
                    latest_created_message_obj = message_obj
                    for attachment in attachments:
                        if not hasattr(attachment, "message"):
                            attachment.message = latest_created_message_obj

                if m["media_base64"]:
                    if m["media_base64"].startswith("http"):
                        decoded_data = requests.get(m["media_base64"]).content
                    else:
                        decoded_data = base64.b64decode(m["media_base64"])
                    file = BytesIO(decoded_data)
                    if message_type == "voice":
                        audio_message_obj = chat_models.MessageAudio.objects.create(
                            file=ContentFile(file.read(), name=m["media"]),
                            duration=timezone.timedelta(seconds=2),
                        )
                        latest_created_message_obj.message_audio = audio_message_obj
                        latest_created_message_obj.is_audio = True
                        latest_created_message_obj.save()
                    else:
                        attachment_obj = chat_models.Attachment(
                            message=latest_created_message_obj,
                            file=ContentFile(file.read(), name=m["media"]),
                            attachment_type=message_type,
                        )
                        attachments.append(attachment_obj)
            if latest_created_message_obj == None:
                m = messages[0]
                data = {
                    "conversation": conversation,
                    "message": "",
                    "third_party_id": message_third_party_id,
                    "third_party_sender": m["name"],
                    "created_at": m["timestamp"],
                }
                message_obj = chat_models.Message(**data)
                message_obj.save()
                for attachment in attachments:
                    attachment.message = message_obj
            for attachment in attachments:
                attachment.save()

            # do not generate a reply if it is a group chat and the user is not mentioned
            if is_group_chat and (not is_mentioned):
                return Response(data={"send": False}, status=status.HTTP_201_CREATED)

            # check if is_send
            whatsapp_qr_connection_instance = (
                integration_models.WhatsAppQRConnection.objects.first()
            )

            is_send = False
            if (
                conversation.is_auto_reply
                and whatsapp_qr_connection_instance
                and whatsapp_qr_connection_instance.auto_reply
            ):
                is_send = True

            if not is_send:
                return Response(data={"send": False}, status=status.HTTP_201_CREATED)

            ## GENERATING REPLY
            historical_messages = chat_helper.get_historical_messages(conversation)
            external_collection_name = chat_constant.get_collection_name(
                chat_constant.external_collection, schema_name
            )

            # Get the chatbot's response
            collection_name = external_collection_name
            bot_setting_instance = bot_models.BotSetting.objects.get(
                bot_type=utility_models.EXTERNAL_TEXT
            )
            incoming_message = ("\n").join(
                [m["message"] if m["message"] else " " for m in messages]
            )

            response_message_text = bot_chatbot_helper.get_chatbot_response(
                incoming_message,
                historical_messages,
                collection_name,
                bot_setting_instance,
                schema_name,
                chat_language,
                conversation.id,
                is_source=False,
            )

            if not response_message_text:
                # No reply needed or failed
                return Response(
                    data={"send": False},
                    status=status.HTTP_201_CREATED,
                )

            # Create the chatbot's message
            response_message = chat_models.Message(
                conversation=conversation,
                sender=None,
                message_type=utility_models.MACHINE_TEXT,
                message=response_message_text,
                is_audio=False,
                chat_language=chat_language,
            )
            response_message.save()
            serializer = chat_serializers.MessageSerializer.Get(response_message)
            response_data = serializer.data
            response_data["send"] = is_send
            return Response(response_data, status=status.HTTP_201_CREATED)

        except BaseException as e:
            logger.error(f"Error in WhatsAppQRChat: {traceback.format_exc()}")
            return Response(
                data={"error": "Something went wrong. Please try again."},
                status=status.HTTP_400_BAD_REQUEST,
            )
