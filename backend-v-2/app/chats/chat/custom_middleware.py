import logging
import jwt

from channels.db import database_sync_to_async
from urllib.parse import parse_qs
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from channels.security.websocket import Websocket<PERSON>enier

from app.utility.custom_exception import CustomException
from app.chats.chat.static.status_code import WebSocketStatusCode
from backend.settings import SECRET_KEY
from app.core.tenants.models import Tenant


logger = logging.getLogger(__name__)

@database_sync_to_async
def get_user(id_token):
    """
    Get the user by decoding the JWT id_token and querying the custom user model.
    """
    try:
        # Decode the JWT token
        decoded_payload = jwt.decode(id_token, SECRET_KEY, algorithms=["HS256"])
        
        # Get the user_id from the decoded payload
        user_id = decoded_payload.get("user_id")
        
        if not user_id:
            raise ValueError("user_id not found in token payload")

        # Get the custom user model
        User = get_user_model()
        
        # Query the user model using the user_id
        user = User.objects.get(id=user_id)
        
        return user
    except jwt.InvalidTokenError:
        logger.error("Invalid token", exc_info=True)
        raise CustomException("Invalid token", errors=None, status_code=WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA)
    except ObjectDoesNotExist:
        logger.error(f"User with id {user_id} not found", exc_info=True)
        raise CustomException("User not found", errors=None, status_code=WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA)
    except Exception as e:
        logger.error(f"Error getting user: {str(e)}", exc_info=True)
        raise CustomException("Error authenticating user", errors=None, status_code=WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA)


class WebsocketAuthMiddleware:
    """
    Custom middleware that takes authorization bearer token from the headers.
    """
    def __init__(self, app):
        """
        Store the ASGI application we were passed        
        """
        self.app = app

    async def __call__(self, scope, receive, send):
        """
        Look up authorization header from headers and get the bearer token for authentication purpose.

        Args:
            scope (dict): The scope of the websocket connection.
            receive (function): The receive function.
            send (function): The send function.

        Raises:
            CustomException: If the authorization header is not found or if any other CustomException exception occurs.
            Exception: If any other exception occurs.

        Returns:
            function: The ASGI application.
        """
        try:
            # Retrieve the bearer token from the headers
            encoded_headers = dict(scope['headers'])
            headers = { key.decode('ascii').lower(): encoded_headers.get(key).decode('ascii') for key in encoded_headers.keys() }

            # Retrieve the bearer token from the query string
            encoded_query_string = scope['query_string']
            bearer_token = encoded_query_string.decode('ascii')

            scope, headers = self.set_host(scope, headers)

            # Only for the path ending with /chat, then we dont need to check for the user
            if not scope['path'].endswith('/chat/'):
                # Check if authorization header is parsed
                authorization_key = 'authorization'
                if authorization_key not in headers and authorization_key not in bearer_token.lower():
                    raise CustomException("Authorization header not found", errors=None, status_code=WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA)
                
                # If authorization is in headers
                elif authorization_key in headers:
                    bearer_token = headers[authorization_key].split(" ")[1]
                
                # If authorization is in query string
                else:
                    bearer_token = parse_qs(bearer_token.replace("Authorization", "authorization"))
                    bearer_token = bearer_token[authorization_key][0].split(" ")[1]

                scope['user'] = await get_user(bearer_token)

            else:
                # Append anonymous user to the scope
                scope['user'] = "Anonymous User"

            # Check if the user is allowed to access the tenant
            await self.check_organization(scope, headers)

        except CustomException as e:
            logger.error(e.args[0], exc_info=True, extra={"scope": scope, "exception": e.args[0], "code": e.status_code})
            # Deny the connection
            denier = WebsocketDenier()
            return await denier(scope, receive, send)

        except Exception as e:
            logger.error(e.args[0], exc_info=True, extra={"scope": scope, "exception": e.args[0]})
            # Deny the connection
            denier = WebsocketDenier()
            return await denier(scope, receive, send)

        return await self.app(scope, receive, send)
    
    @staticmethod
    def set_host(scope, headers):
        host = headers['host']
        path = scope['path'].lstrip("/")
        print("host: ", host)  # localhost:8000
        print("path: ", path)  # org/reluvate/chat/conversation/list

        # Optionally, you might want to adjust the path or do additional handling here
        if path.startswith("org/"):
            # elif path.startswith("org/"):
            tenant_slug = path.split("/")[1]
            new_host = f"{tenant_slug}.{host}"

            # Changing the host of the request
            headers['host'] = new_host

            # Update path_info to remove the organization segment
            scope['path'] = "/" + "/".join(path.split("/")[2:])
        else:
            new_host = f"public.{host}"

        return scope, headers

    @database_sync_to_async
    def check_organization(self, scope, headers):
        host = headers.get('host', '')
        user = scope.get('user')

        if not user and not scope['user'] == "Anonymous User":
            raise CustomException("User not authenticated", errors=None, status_code=WebSocketStatusCode.INVALID_FRAME_PAYLOAD_DATA)

        # Extract tenant slug from the host
        tenant_slug = host.split('.')[0] if '.' in host else None

        if not tenant_slug or tenant_slug == 'public':
            # Allow access to public routes
            return
                
        try:
            print("Tenant slug: ", tenant_slug)
            print("User: ", user)
            # Get the organization based on the tenant slug
            tenant = Tenant.objects.get(domain_url__icontains=tenant_slug)

            # Check if the user is a member of the organization
            if not scope['user'] == "Anonymous User" and not tenant.user_tenant_bridges.filter(id=user.id).exists():
                raise CustomException("User not authorized for this tenant", errors=None, status_code=WebSocketStatusCode.UNAUTHORIZED)

            # Add the organization to the scope for future use
            scope['tenant'] = tenant

        except Tenant.DoesNotExist:
            raise CustomException("Invalid tenant", errors=None, status_code=WebSocketStatusCode.UNAUTHORIZED)
        
        except Exception as e:
            logger.error(f"Error checking organization: {str(e)}", exc_info=True)
            raise CustomException("Error checking organization access", errors=None, status_code=WebSocketStatusCode.UNAUTHORIZED)
