# Generated by Django 4.2.5 on 2024-10-28 07:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0028_alter_message_chat_language'),
    ]

    operations = [
        migrations.AddField(
            model_name='conversation',
            name='uuid',
            field=models.UUIDField(blank=True, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='message',
            name='bot_reply_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('in_process', 'In Process'), ('completed', 'Completed')], default='pending', max_length=15),
        ),
        migrations.CreateModel(
            name='ConversationQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('messages_in_processing', models.J<PERSON><PERSON>ield(blank=True, default=list, help_text='List of messages currently being processed')),
                ('is_completed', models.BooleanField(default=False)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_queue', to='chat.conversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
