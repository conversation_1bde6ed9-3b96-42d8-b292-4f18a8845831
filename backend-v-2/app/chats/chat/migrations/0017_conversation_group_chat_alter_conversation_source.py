# Generated by Django 4.2.5 on 2023-12-08 09:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0016_alter_conversation_source'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='conversation',
            name='group_chat',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='conversation',
            name='source',
            field=models.CharField(choices=[('Web Application', 'Web Application'), ('Floating Chat', 'Floating Chat'), ('WhatsApp', 'WhatsApp'), ('WhatsApp QR', 'WhatsApp QR'), ('WeChat', 'WeChat'), ('Telegram', 'Telegram'), ('Instagram', 'Instagram'), ('Facebook', 'Facebook'), ('Shopify', 'Shopify'), ('Wix', 'Wix'), ('WooCommerce', 'WooCommerce')], default='Web Application', max_length=63),
        ),
    ]
