import uuid

from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone

from app.utility import models as utility_models

# Create your models here.

User = get_user_model()


class AbstractMessageAudio(utility_models.BaseModel):
    file = models.FileField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, null=True, blank=True
    )
    duration = models.DurationField(default=0)

    def __str__(self):
        return self.file.url

    class Meta:
        abstract = True


class AbstractConversation(utility_models.BaseModel):
    name = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    third_party_id = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # conversation id, unique, can be contact number in whatsapp
    sender_psid = models.CharField(
        max_length=127, blank=True
    )  # if from external, from fb. Also possibly from WeChat (source). Basically the sender id (user).
    # the id of the other person the bot is talking to
    # blank for group chats
    source = models.CharField(
        max_length=63,
        choices=utility_models.CONVERSATION_SOURCE_CHOICES,
        default=utility_models.WEB_APPLICATION,
    )
    sub_source = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # (facebook -> page_id; telegram -> bot_username; whatsapp -> phone_number_id; )
    # whatsapp -> bot number
    is_auto_reply = models.BooleanField(default=True)  # is chatbot auto reply
    is_group_chat = models.BooleanField(default=False)

    metadata = models.JSONField(default=dict, blank=True)

    session_uuid = models.UUIDField(null=True, blank=True)

    def __str__(self):
        return f"{self.pk}"

    class Meta:
        ordering = ["-created_at"]
        abstract = True


class AbstractMessage(utility_models.BaseModel):
    created_at = models.DateTimeField(default=timezone.now)
    third_party_id = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # message id, unique
    third_party_sender = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # this is third party sender name /email
    timestamp = models.DateTimeField(null=True)  # no use, replaced through "created_at"
    message_type = models.CharField(
        max_length=10,
        choices=utility_models.MESSAGE_TYPES,
        default=utility_models.USER_TEXT,
    )
    message = models.TextField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    feedback = models.CharField(
        max_length=15,
        choices=utility_models.MESSAGE_FEEDBACK_CHOICES,
        default=utility_models.NEUTRAL,
    )
    feedback_reason = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    is_audio = models.BooleanField(default=False)  # for machine

    chat_language = models.CharField(max_length=127, default=utility_models.ENGLISH_US)

    sentiment = models.CharField(
        max_length=15,
        null=True,
        blank=True,
    )

    topics = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True, null=True
    )

    metadata = models.JSONField(default=dict, blank=True)

    BOT_REPLY_STATUS_CHOICES = [
        ("pending", "Pending"),
        ("in_process", "In Process"),
        ("completed", "Completed"),
    ]

    bot_reply_status = models.CharField(
        max_length=15,
        choices=BOT_REPLY_STATUS_CHOICES,
        default="pending",
    )

    def __str__(self):
        return f"{self.pk}"

    class Meta:
        ordering = ["-created_at"]
        abstract = True


class AbstractConversationQueue(utility_models.BaseModel):
    messages_in_processing = models.JSONField(
        default=list, blank=True, help_text="List of messages currently being processed"
    )

    is_completed = models.BooleanField(default=False)

    class Meta:
        abstract = True


class AbstractAttachment(utility_models.BaseModel):
    file = models.FileField(
        upload_to="messages_attachments/",
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        null=True,
        blank=True,
    )
    google_drive_url = models.URLField(
        max_length=255, blank=True, null=True, default=None
    )
    attachment_type = models.CharField(
        max_length=10,
        choices=utility_models.ATTACHMENT_TYPES,
        default=utility_models.IMAGE,
    )
    tags = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )

    def __str__(self):
        return self.file.url

    class Meta:
        abstract = True


# TODO-yc: Create this model
# class MessageToSendAttachment(utility_models.BaseModel):
#     message_to_send = models.ForeignKey(
#         "chat_action_tracker.MessageToSend",
#         related_name="attachments",
#         on_delete=models.CASCADE,
#     )

#     file = models.FileField(
#         upload_to="message_to_send_attachments/",
#         max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
#         null=True,
#         blank=True,
#     )
#     attachment_type = models.CharField(
#         max_length=10,
#         choices=utility_models.ATTACHMENT_TYPES,
#         default=utility_models.IMAGE,
#     )

#     def __str__(self):
#         return self.file.url


class AbstractStateMachine(utility_models.BaseModel):
    """
    Enhanced StateMachine model that tracks individual topics within a conversation,
    stores all necessary information for context, and manages decision-making history.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    context = models.JSONField(default=dict, blank=True)

    class Meta:
        abstract = True


class AbstractFlowMapping(utility_models.BaseModel):
    """
    Stores the JSON configuration for state mappings with version control.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    version = models.IntegerField()
    mapping_json = models.JSONField()
    is_active = models.BooleanField(
        default=False
    )  # Allows for easy retrieval of the active version

    class Meta:
        unique_together = ("version",)
        ordering = ["-version"]
        abstract = True

    def __str__(self):
        return (
            f"FlowMapping version {self.version} {'(Active)' if self.is_active else ''}"
        )
