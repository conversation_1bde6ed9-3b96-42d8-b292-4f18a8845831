from collections import defaultdict
from rest_framework import serializers


import app.core.authapi.serializers as authapi_serializers

from app.chats.bot.helper import (
    get_page_number,
    extract_file_name_and_page,
    get_sources,
)
import app.chats.bot.models as bot_models

import app.chats.chat.models as chat_models
import app.chats.chat.helper as chat_helper

import app.integrations.facebook_integration.models as facebook_integration_models


import app.utility.models as utility_models
import re
from datetime import timedelta, datetime
from django.utils import timezone

import app.core.authapi.helper as authapi_helper


class ConversationSerializerList(serializers.ModelSerializer):
    sender_name = serializers.SerializerMethodField()

    def get_sender_name(self, conversation_instance):
        if hasattr(conversation_instance, "client_customer"):
            client_customer_name = conversation_instance.client_customer.name
            return client_customer_name if client_customer_name else ""

        message = conversation_instance.messages.filter(
            message_type=utility_models.USER_TEXT
        ).first()
        if message and message.third_party_sender:
            return message.third_party_sender

        return ""

    email = serializers.SerializerMethodField()

    def get_email(self, conversation_instance):
        if hasattr(conversation_instance, "client_customer"):
            client_customer_email = conversation_instance.client_customer.email
            return client_customer_email if client_customer_email else ""
        return ""

    contact = serializers.SerializerMethodField()

    def get_contact(self, conversation_instance):
        if hasattr(conversation_instance, "client_customer"):
            client_customer_contact = conversation_instance.client_customer.contact
            return client_customer_contact if client_customer_contact else ""
        return ""

    last_message = serializers.SerializerMethodField()

    def get_last_message(self, conversation_instance):
        message_instance = conversation_instance.messages.first()
        if message_instance:
            return message_instance.message.rsplit("SOURCES:", 1)[0]
        return ""

    last_message_created_at = serializers.SerializerMethodField()

    def get_last_message_created_at(self, conversation_instance):
        message_instance = conversation_instance.messages.first()
        if message_instance:
            return message_instance.created_at
        return ""

    sub_source = serializers.SerializerMethodField()

    def get_sub_source(self, conversation_instance):
        sub_source = conversation_instance.sub_source
        if conversation_instance.source == utility_models.FACEBOOK and sub_source:
            fb_credential_instance = (
                facebook_integration_models.Credential.objects.filter(
                    PAGE_ID=sub_source
                ).first()
            )
            if fb_credential_instance:
                sub_source = fb_credential_instance.PAGE_NAME

        return sub_source

    class Meta:
        model = chat_models.Conversation
        fields = [
            "id",
            "name",
            "sender_name",
            "third_party_id",
            "source",
            "sub_source",
            "email",
            "contact",
            "is_auto_reply",
            "last_message",
            "last_message_created_at",
            "is_group_chat",
            "created_at",
        ]


class ConversationSerializer:
    class Update(serializers.ModelSerializer):
        class Meta:
            model = chat_models.Conversation
            fields = [
                "id",
                "name",
                "third_party_id",
                "source",
                "is_auto_reply",
                "created_by",
                "created_at",
            ]

    class List(ConversationSerializerList):
        pass

    class Get(ConversationSerializerList, serializers.ModelSerializer):
        messages = serializers.SerializerMethodField()

        def get_messages(self, chat_session_instance):
            return MessageSerializer.Get(chat_session_instance.messages, many=True).data

        sub_source_id = serializers.SerializerMethodField()

        def get_sub_source_id(self, chat_session_instance):
            return chat_session_instance.sub_source

        class Meta(ConversationSerializerList.Meta):
            # Copy all fields from ConversationSerializerList and add extra fields
            fields = ConversationSerializerList.Meta.fields + [
                "messages",
                "sender_psid",  # Facebook user id
                "sub_source_id",  # Facebook page id
            ]

    class AdminViewList(serializers.ModelSerializer):
        created_by = authapi_serializers.ShortCustomUserSerializer()

        class Meta:
            model = chat_models.Conversation
            fields = ["id", "name", "created_by", "created_at"]


class MessageSerializer:
    class Get(serializers.ModelSerializer):
        # conversation = ConversationSerializer.List()
        created_at = serializers.DateTimeField()
        updated_at = serializers.DateTimeField()
        message_audio = serializers.SerializerMethodField()
        conversation_name = (
            serializers.SerializerMethodField()
        )  # only use for whatsapp webscoket connection
        message = serializers.SerializerMethodField()
        message_attachments = serializers.SerializerMethodField()

        def get_message_audio(self, message_instance):
            if hasattr(message_instance, "message_audio"):
                if message_instance.message_audio:
                    return message_instance.message_audio.file.url
            return None

        def get_message_attachments(self, message_instance):
            if hasattr(message_instance, "attachments"):
                return AttachmentSerializer.Get(
                    message_instance.attachments.all(), many=True
                ).data
            else:
                return None

        def get_conversation_name(self, message_instance):
            return message_instance.conversation.name

        def get_message(self, message_instance):
            """
            content: list of strings
            scenario: string
            sources[]: {
                name: string || doc.file.name
                url: string || "" (without pages)
                pages[]: number
            }

            """
            # is_public = message_instance.conversation.created_by == None
            is_public = True
            if is_public:
                bot_setting = bot_models.BotSetting.objects.filter(
                    bot_type=utility_models.EXTERNAL_TEXT
                ).first()
            else:
                bot_setting = bot_models.BotSetting.objects.filter(
                    bot_type=utility_models.INTERNAL_TEXT
                ).first()

            content = message_instance.message
            file_urls = []
            sources = []
            scenario = ""
            if message_instance.message_type == utility_models.MACHINE_TEXT:
                # Split the input string into two parts: content and sources
                splitted_messages = message_instance.message.rsplit("SOURCES", 1)

                if len(splitted_messages) == 1:
                    pass
                else:
                    content = splitted_messages[0].strip()
                    # --- No need to sort, use original threshold score
                    # Sort by pdf page number
                    # file_urls = sorted(
                    #     re.findall(r"https?://[^\s\n]+", splitted_messages[1]),
                    #     key=get_page_number,
                    # )
                    file_urls = re.findall(r"https?://[^\s\n]+", splitted_messages[1])
                    # No need to sort, use original threshold score ---
                    # print("file urls: ", file_urls)
                    cleaned_file_urls = extract_file_name_and_page(file_urls)
                    # print("cleaned_file_urls: ", cleaned_file_urls)
                    sources = get_sources(cleaned_file_urls)
                    # print("sources: ", sources)

                (content, scenario) = chat_helper.split_by_last(content, "scenario:")

                word_per_chat_bubble = 20
                if bot_setting:
                    word_per_chat_bubble = bot_setting.word_per_chat_bubble

                schema_name = authapi_helper.get_schema_name()

                content = chat_helper.split_sentence_by_period(
                    content, word_per_chat_bubble, schema_name
                )

            if type(content) == str:
                content = [content]

            # sources = [source for source in sources if (not "FAQ" in source)] #infuture maybe apply FAQ sources

            return {"content": content, "scenario": scenario, "sources": sources}

        is_display_avatar = serializers.SerializerMethodField()

        def get_is_display_avatar(self, message_instance):
            previous_message = message_instance.get_previous_message()
            if (
                not previous_message
                or previous_message.message_type != message_instance.message_type
            ):
                return True

            # Get raw datetime value from model instance
            message_created_at = message_instance.created_at
            previous_created_at = previous_message.created_at

            if not isinstance(message_created_at, datetime) or not isinstance(
                previous_created_at, datetime
            ):
                return True

            return message_created_at - previous_created_at > timedelta(
                minutes=utility_models.CHAT_MINUTE_SPLIT_VALUE
            )

        is_display_timestamp = serializers.SerializerMethodField()

        def get_is_display_timestamp(self, message_instance):
            next_message = message_instance.get_next_message()
            if (
                not next_message
                or next_message.message_type != message_instance.message_type
            ):
                return True

            # Get raw datetime value from model instance
            message_created_at = message_instance.created_at
            next_created_at = next_message.created_at

            return next_created_at - message_created_at > timedelta(
                minutes=utility_models.CHAT_MINUTE_SPLIT_VALUE
            )

        message_status = serializers.SerializerMethodField()

        def get_message_status(self, message_instance):
            message_status = utility_models.COMPLETED
            if hasattr(message_instance, "message_to_send"):
                status_ = message_instance.message_to_send.status
                if status_ == utility_models.APPT_UPDATED:
                    return utility_models.COMPLETED
                return status_
            return message_status

        class Meta:
            model = chat_models.Message
            fields = [
                "id",
                "sender",
                "is_display_avatar",
                "is_display_timestamp",
                "message",
                "message_audio",
                "message_attachments",
                "message_status",
                "chat_language",
                "is_audio",
                "message_type",
                "conversation",
                "conversation_name",
                "feedback",
                "feedback_reason",
                "created_by",
                "created_at",
                "updated_at",
                "third_party_sender",
                "third_party_id",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = chat_models.Message
            fields = [
                "sender",
                "message",
                "message_type",
                "third_party_sender",
                "third_party_id",
                "timestamp",
                "conversation",
                "feedback",
                "feedback_reason",
                "created_at",
                "created_by",
            ]

    class FeedbackList(serializers.ModelSerializer):
        class Meta:
            model = chat_models.Message
            fields = [
                "message",
                "feedback",
                "feedback_reason",
            ]


class MessageAudioSerializer(serializers.ModelSerializer):
    class Meta:
        model = chat_models.MessageAudio
        fields = "__all__"


class AttachmentSerializer:

    class Get(serializers.ModelSerializer):
        # conversation = ConversationSerializer.List()
        created_at = serializers.DateTimeField()

        class Meta:
            model = chat_models.Attachment
            fields = [
                "id",
                "file",
                "attachment_type",
                "created_at",
            ]

    class Create(serializers.ModelSerializer):
        class Meta:
            model = chat_models.Attachment
            fields = "__all__"


class MessageAnalyticsSerializer:
    class Get(serializers.ModelSerializer):
        email = serializers.SerializerMethodField()
        topics = serializers.SerializerMethodField()

        class Meta:
            model = chat_models.Conversation
            fields = ["id", "email", "topics", "updated_at"]

        def get_email(self, obj):
            return obj.metadata.get("email", "")

        def get_topics(self, obj):
            topics = obj.topics or ""

            # Remove any leading or trailing commas and whitespace
            topics = topics.lstrip(", ")

            # Split the topics into a list, remove duplicates using set, and remove any empty strings
            unique_topics = ", ".join(sorted(set(filter(None, topics.split(", ")))))

            # Return None if unique_topics is empty, to exclude it from the response
            return unique_topics if unique_topics else None


class ConversationAnalyticsSerializer:
    class Get(serializers.Serializer):
        email = serializers.CharField()
        topics = serializers.CharField()
        last_message_date = serializers.DateTimeField()

        class Meta:
            model = chat_models.Conversation
            fields = ["email", "topics", "last_message_date"]
