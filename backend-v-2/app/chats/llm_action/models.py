import app.chats.chat_action.models as chat_action_models

from django.db import models

from app.utility import models as utility_models


# When create CustomChatActionTracker > inside the function need to define the action function, such that when render in FE, it can call correct endpoint?
class CustomLLMAction(utility_models.BaseModel):
    """
    name: function name
    description: description of the function
    function_str: full function str itself (must match with the function name)
    """

    name = models.Char<PERSON>ield(max_length=255)
    description = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    function_str = (
        models.TextField()
    )  # inside this function, it will create instance CustomChatActionTracker.

    # custom_chat_action_setting_name = models.Char<PERSON>ield(max_length=255, blank=True)# the name of the tab, so it can refer back.

    class Meta:
        ordering = ["-created_at"]


class CustomLLMActionCredentials(utility_models.BaseModel):
    """
    name: function name
    description: description of the function
    function_str: full function str itself (must match with the function name)
    """

    name = models.Char<PERSON><PERSON>(max_length=255, unique=True)
    description = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    credential_str = models.Char<PERSON><PERSON>(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )

    # custom_chat_action_setting_name = models.CharField(max_length=255, blank=True)# the name of the tab, so it can refer back.
    class Meta:
        ordering = ["-created_at"]
