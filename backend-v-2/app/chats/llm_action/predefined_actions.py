import app.core.authapi.helper as authapi_helper


import app.chats.bot.chatbot_helper as bot_chatbot_helper
import app.chats.bot.intent_helper as bot_intent_helper
import app.chats.bot.helper as bot_helper
import app.chats.bot.openai_helper as bot_openai_helper
import app.chats.bot.shopify_helper as bot_shopify_helper

# import app.chats.bot.beefy_helper as bot_beefy_helper

import app.chats.chat.constant as chat_constant
import app.chats.chat.models as chat_models
import app.chats.chat_action.models as chat_action_models
import app.chats.chat_action_tracker.models as chat_action_tracker_models
import app.chats.chat_action_tracker.helper as chat_action_tracker_helper
import app.chats.chat.helper as chat_helper

import app.chats.llm_action.execute_helper as llm_action_execute_helper
import app.chats.llm_action.constant as llm_action_constant
import app.chats.llm_action.woo_commerce_helper as llm_action_woo_commerce_helper

import app.utility.general_helper as utility_general_helper
import app.utility.chroma_db_helper as utility_chroma_db_helper
import app.utility.date_helper as utility_date_helper
import app.utility.models as utility_models

from django.utils import timezone
from django.conf import settings

from langchain.embeddings.openai import OpenAIEmbeddings

from datetime import datetime, timedelta
import json
import time
import os
import re

# Note: Becareful when renaming the variable names here; LLM Action helper is associated with it!
# Status: completed,failed,

# All the actions here must return:
# {
#     "status": {status}
#     "output": {result}
# }

# Shopify product / order inquiry


def shopify_product_or_order_inquiry(
    tenant_name, chat_language, historical_messages, incoming_message
):
    shopify_intent_json = bot_shopify_helper.get_shopify_intent_json(
        tenant_name,
        historical_messages,
        chat_language,
        incoming_message,
    )
    print("shopify_intent_json: ", shopify_intent_json)
    if shopify_intent_json["intent"] == bot_intent_helper.OTHER:
        output = "Not able to get information on this inquiry at the moment. Please try again later."
    else:
        output = bot_shopify_helper.execute_shopify_intent(
            tenant_name, historical_messages, incoming_message, shopify_intent_json
        )

    # completed, but maybe not getting the desired info, output described it
    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


def woocommerce_product_inquiry(chat_language, incoming_message):
    # still WIP, no use yet
    output = llm_action_woo_commerce_helper.get_product_info(incoming_message)

    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


# appointment
def get_appointment_location_details_request(appointment_chat_feature_setting_describe):

    output = utility_general_helper.clean_up_prompt(
        f"""
            Here is the available appointment details for you to refer:
            {appointment_chat_feature_setting_describe}

        Please answer the customer, by reformatting the information into a human friendly and readable message, in 1 or 2 sentences without using point form.
        """
    )
    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


# not action
def get_appointment_made_summarization_json(historical_messages, incoming_message):
    """
    Return:
    {
        "service_or_product": "",
        "offer_promised": "",
        "notes": "",
    }
    """
    appointment_conversation_summarized_info_message = historical_messages + [
        {
            "role": "system",
            "content": utility_general_helper.clean_up_prompt(
                f"""
            You have made an appointment with the customer. Now, your task is to provide a summarization about the appointment in JSON format, which includes: 
            1. What services or products that the customer interested in? 
            2. Promises about the offer or voucher in the entire conversation (if any)?
            3. Notes or preferences mentioned by the customer? 
            
            Reply only in the JSON format: {{"service_or_product":"", "offer_promised":"", "notes":""}} and nothing else (no free text, no small talk)"""
            ),
        },
        {
            "role": "user",
            "content": incoming_message,
        },
    ]

    (
        appointment_conversation_summarized_info_response,
        _,
    ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
        appointment_conversation_summarized_info_message
    )
    # self.cost += bot_openai_helper.openai_api_calculate_cost(
    #     appointment_conversation_summarized_info_response.usage,
    #     model=appointment_conversation_summarized_info_model_name,
    # )
    appointment_conversation_summarized_info_str = (
        appointment_conversation_summarized_info_response.choices[0].message.content
    )
    appointment_conversation_summarized_info = {
        "service_or_product": "",
        "offer_promised": "",
        "notes": "",
    }
    appointment_conversation_summarized_info_response_json = (
        utility_general_helper.extract_json_from_str(
            appointment_conversation_summarized_info_str
        )
    )
    try:
        appointment_conversation_summarized_info.update(
            json.loads(appointment_conversation_summarized_info_response_json)
        )
    except Exception as e:
        print(
            f"error loading appointment_conversation_summarized_info_response ({appointment_conversation_summarized_info_str}) to json with error: {e}",
        )

    return appointment_conversation_summarized_info


# not action
def create_appointment_notification(
    created_appointment_instance: chat_action_tracker_models.Appointment,
    schema_name,
    tenant_name,
    historical_messages,
    incoming_message,
):
    """Message to send out after appointment made"""
    appointment_made_summarization_json = get_appointment_made_summarization_json(
        historical_messages, incoming_message
    )

    appointment_email_str = (
        f" <{created_appointment_instance.email}>"
        if created_appointment_instance.email
        else ""
    )
    sales_info_list = [
        part
        for part in [
            appointment_made_summarization_json.get("service_or_product", ""),
            appointment_made_summarization_json.get("offer_promised", ""),
        ]
        if part
    ]
    summarized_str = ", ".join(sales_info_list)
    summarized_str = f"\n{summarized_str}" if summarized_str else ""

    appointment_whatsapp_message = f"""{tenant_name}\n{created_appointment_instance.appointment_chat_feature_location.name}\n{created_appointment_instance.name}\n{created_appointment_instance.contact}{appointment_email_str}\n{utility_date_helper.convert_to_human_friendly_format(created_appointment_instance.datetime)}{summarized_str}"""

    # create msg to send
    chat_action_tracker_models.MessageToSend.objects.create(
        appointment=created_appointment_instance,
        contact=created_appointment_instance.appointment_chat_feature_location.identifier,
        target_source=utility_models.WHATSAPP_QR,
        message=appointment_whatsapp_message,
        to_be_sent_at=created_appointment_instance.datetime - timedelta(hours=6),
    )

    https_app_url_1 = os.environ.get("HTTPS_APP_URL_1")
    base_url = f"{https_app_url_1}/org/{schema_name.replace('_', '-')}/appointment/{created_appointment_instance.code}"

    customer_info_str = f"""{created_appointment_instance.name} {created_appointment_instance.contact}{appointment_email_str}"""
    sale_made_url_str = f"Sale Made: {base_url}/yes"
    absent_url_str = f"No Show/Postponed: {base_url}/absent"
    no_sale_url_str = f"No Sale: {base_url}/no"

    follow_up_message = f"""{tenant_name}\nPls update {created_appointment_instance.datetime.strftime("%I:%M %p")} {customer_info_str}\n\n{sale_made_url_str}\n\n{absent_url_str}\n\n{no_sale_url_str}"""
    # print("follow_up_message: ", follow_up_message)
    chat_action_tracker_models.MessageToSend.objects.create(
        appointment=created_appointment_instance,
        contact=created_appointment_instance.appointment_chat_feature_location.identifier,
        target_source=utility_models.WHATSAPP_QR,
        message=follow_up_message,
        to_be_sent_at=created_appointment_instance.datetime + timedelta(hours=2),
    )


def create_appointment_request(
    conversation_id,
    customer_name,
    customer_email,
    customer_contact_number,
    appointment_location_name,
    appointment_date,
    appointment_time,
    appointment_remarks,
    appointment_chat_feature_setting_describe,
    schema_name="",
    tenant_name="",
    historical_messages="",
    incoming_message="",
):
    """
    schema_name > incoming_message is for notification creation, can be optional
    """
    optional_missing_value_list = []
    missing_value_list = []

    if not customer_name:
        missing_value_list.append("Customer Name")

    if not customer_email:
        optional_missing_value_list.append("Customer Email (Optional)")
        customer_email = ""

    if not customer_contact_number:
        missing_value_list.append("Customer Contact Number")

    appointment_chat_feature_location_instance = (
        llm_action_execute_helper.get_most_similar_location(appointment_location_name)
    )

    extra_location_info = ""
    if not appointment_chat_feature_location_instance:
        missing_value_list.append("Appointment Location")
        extra_location_info = (
            "Tell the customer about the Appointment Location so they can choose."
        )
    else:
        appointment_location_name = (
            appointment_chat_feature_location_instance.name
        )  # overwrite with the correct one

    if not appointment_date:
        missing_value_list.append("Appointment Date")
    else:
        try:
            datetime.strptime(appointment_date, utility_date_helper.DATE_FORMAT)
        except ValueError:
            missing_value_list.append("Appointment Date")

    if not appointment_time:
        missing_value_list.append("Appointment Time")
    else:
        try:
            datetime.strptime(appointment_time, utility_date_helper.TIME_FORMAT)
        except ValueError:
            missing_value_list.append("Appointment Time")

    if appointment_location_name and appointment_date and appointment_time:
        # check timing within opening hour
        check_datetime_details = llm_action_execute_helper.check_appointment_datetime(
            appointment_location_name,
            appointment_date,
            appointment_time,
        )
        if check_datetime_details["is_date_valid"] == "no":
            missing_value_list.append(
                f"Appointment Date: {appointment_date} is not within opening hour."
            )
        if check_datetime_details["is_time_valid"] == "no":
            missing_value_list.append(
                f"Appointment Time: {appointment_time} is not within opening hour"
            )
        if check_datetime_details["is_datetime_valid"] == "no":
            missing_value_list.append(
                f"Appointment Date Time: {appointment_date} {appointment_time} is past today's datetime"
            )

    if missing_value_list:
        extra_information = utility_general_helper.clean_up_prompt(
            f"""
        Available appointment location details to reference the customer inquiry about (use if necessary only):
        {appointment_chat_feature_setting_describe}
        {extra_location_info}
        """
        )

        llm_action_execute_helper.raise_value_error(
            missing_value_list,
            optional_missing_value_list=optional_missing_value_list,
            extra_information=extra_information,
        )

    # create request
    conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
    # print("appointment_date: ", appointment_date)
    # print("appointment_time: ", appointment_time)
    (
        created_appointment_instance,
        is_created_appointment_instance,
    ) = chat_action_tracker_models.Appointment.objects.get_or_create(
        appointment_chat_feature_location=appointment_chat_feature_location_instance,
        name=customer_name,
        contact=customer_contact_number,
        email=customer_email,
        location=appointment_location_name,
        datetime=datetime.strptime(
            appointment_date + "T" + appointment_time,
            utility_date_helper.DATE_TIME_FORMAT,
        ),
        remarks=f"{appointment_remarks}",
        conversation=conversation_instance,
        defaults={
            "message": conversation_instance.messages.first(),
        },
    )

    # create notification
    if (
        is_created_appointment_instance
        and appointment_chat_feature_location_instance.identifier != ""
    ):
        if schema_name and tenant_name and historical_messages and incoming_message:
            create_appointment_notification(
                created_appointment_instance,
                schema_name,
                tenant_name,
                historical_messages,
                incoming_message,
            )

    # -Email: {customer_email}
    output = utility_general_helper.clean_up_prompt(
        f"""
        Appointment request has been created with the following information:    
        -Name: {customer_name}
        -Contact Number: {customer_contact_number}
        -Appointment Location Name: {appointment_location_name}
        -Appointment Date: {appointment_date}
        -Appointment Time: {appointment_time}
        -Remarks: {appointment_remarks}
    """
    )
    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


def reschedule_appointment_request(
    conversation_id,
    customer_name,
    customer_contact_number,
    new_appointment_date,
    new_appointment_time,
    schema_name="",
    tenant_name="",
    historical_messages="",
    incoming_message="",
):
    """
    schema_name > incoming_message is for notification creation, can be optional
    """
    missing_value_list = []

    if not customer_contact_number:
        missing_value_list.append("Customer Contact Number")
        llm_action_execute_helper.raise_value_error(missing_value_list)

    initial_appointment_datetime = ""
    initial_appointment_datetime_str = ""

    appointment_instance = chat_action_tracker_models.Appointment.objects.filter(
        contact=customer_contact_number,
        status=utility_models.PENDING,
    ).first()

    if not appointment_instance:
        missing_value_list.append(
            "Appointment not found for the provided contact number: {customer_contact_number}"
        )
        llm_action_execute_helper.raise_value_error(missing_value_list)

    customer_name = appointment_instance.name
    initial_appointment_datetime = utility_date_helper.convert_to_human_friendly_format(
        appointment_instance.datetime
    )
    # print("initial_appointment_datetime: ", initial_appointment_datetime)
    initial_appointment_datetime_str = utility_general_helper.clean_up_prompt(
        f"""
        -Initial Appointment Date Time: {initial_appointment_datetime}
    """
    )
    try:
        # Check if the new date/time matches the existing appointment; if yes, then it's capturing the old dates
        new_datetime = datetime.strptime(
            f"{new_appointment_date}T{new_appointment_time}",
            utility_date_helper.DATE_TIME_FORMAT,
        )
        if new_datetime == timezone.localtime(appointment_instance.datetime):
            new_appointment_date = ""
            new_appointment_time = ""
    except:
        pass

    if not new_appointment_date:
        missing_value_list.append("New Appointment Date")
    if not new_appointment_time:
        missing_value_list.append("New Appointment Time")

    if new_appointment_date and new_appointment_time:
        check_datetime_details = llm_action_execute_helper.check_appointment_datetime(
            appointment_instance.appointment_chat_feature_location.name,
            new_appointment_date,
            new_appointment_time,
        )

        if check_datetime_details["is_date_valid"] == "no":
            missing_value_list.append(
                f"Appointment Date: {check_datetime_details['date']} is not within opening hour."
            )
        if check_datetime_details["is_time_valid"] == "no":
            missing_value_list.append(
                f"Appointment Time: {check_datetime_details['time']} is not within opening hour"
            )
        if check_datetime_details["is_datetime_valid"] == "no":
            missing_value_list.append(
                f"Appointment Datetime: {new_appointment_date} {new_appointment_time} is past today's datetime"
            )

    if missing_value_list:
        llm_action_execute_helper.raise_value_error(
            missing_value_list, extra_information=initial_appointment_datetime_str
        )

    # reschedule request
    # new appt instance
    conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
    created_appointment_instance, is_created_appointment_instance = (
        chat_action_tracker_models.Appointment.objects.get_or_create(
            appointment_chat_feature_location=appointment_instance.appointment_chat_feature_location,
            name=appointment_instance.name,
            contact=appointment_instance.contact,
            email=appointment_instance.email,
            location=appointment_instance.location,
            datetime=datetime.strptime(
                new_appointment_date + "T" + new_appointment_time,
                utility_date_helper.DATE_TIME_FORMAT,
            ),
            conversation=conversation_instance,
            defaults={
                "message": conversation_instance.messages.first(),
            },
        )
    )

    # create appointment made notification
    if (
        is_created_appointment_instance
        and appointment_instance.appointment_chat_feature_location.identifier != ""
    ):
        if schema_name and tenant_name and historical_messages and incoming_message:
            create_appointment_notification(
                created_appointment_instance,
                schema_name,
                tenant_name,
                historical_messages,
                incoming_message,
            )

    appointment_instance.status = utility_models.ABSENT
    appointment_instance.remarks = "Rescheduled"
    appointment_instance.save()

    # update message to send
    message_to_send_instances = appointment_instance.messages_to_send.filter(
        status=utility_models.PENDING
    )
    for message_to_send_instance in message_to_send_instances:
        message_to_send_instance.status = utility_models.APPT_UPDATED
        message_to_send_instance.save()

    output = utility_general_helper.clean_up_prompt(
        f"""
            Appointment request has been rescheduled with the following information:    
            -Name: {customer_name}
            -Contact Number: {customer_contact_number}
            -Initial Appointment Date Time: {initial_appointment_datetime}
            -Appointment Location Name: {appointment_instance.location}
            -New Appointment Date: {new_appointment_date}
            -New Appointment Time: {new_appointment_time}
        """
    )

    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


# customer support
def create_complaint_request(
    conversation_id,
    tenant_name,
    schema_name,
    customer_name,
    customer_email,
    customer_contact_number,
    customer_complaint_details,
    customer_complaint_invoice_number,
):
    optional_missing_value_list = []
    missing_value_list = []

    if not customer_name:
        missing_value_list.append("Customer Name")

    if not customer_email:
        optional_missing_value_list.append("Customer Email (Optional)")
        customer_email = ""

    if not customer_contact_number:
        missing_value_list.append("Customer Contact Number")

    if not customer_complaint_details:
        missing_value_list.append("Complaint Issue/Reasoning")

    if not customer_complaint_invoice_number:
        optional_missing_value_list.append("Invoice Number (Optional)")
        customer_complaint_invoice_number = ""

    if missing_value_list:
        llm_action_execute_helper.raise_value_error(
            missing_value_list, optional_missing_value_list=optional_missing_value_list
        )

    conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
    customer_support_chat_feature_setting = (
        chat_action_models.CustomerSupportChatFeatureSetting.objects.first()
    )

    customer_support_instance = (
        chat_action_tracker_models.CustomerSupport.objects.create(
            name=customer_name,
            contact=customer_contact_number,
            complaint_description=customer_complaint_details,
            email=customer_email,
            invoice_number=customer_complaint_invoice_number,
            conversation=conversation_instance,
            message=conversation_instance.messages.first(),
        )
    )

    bot_intent_helper.send_customer_support_email(
        customer_support_instance.name,
        customer_support_instance.contact,  # customer contact
        customer_support_instance.complaint_description,
        customer_support_instance.email,  # customer email
        customer_support_instance.invoice_number,
        customer_support_instance.conversation.id,
        tenant_name,
        schema_name,
        customer_support_chat_feature_setting.email_to,
    )

    complaint_detail_list = []
    if customer_support_instance.name:
        complaint_detail_list.append(f"-Name: {customer_support_instance.name}")
    if customer_support_instance.contact:
        complaint_detail_list.append(
            f"-Contact Number: {customer_support_instance.contact}"
        )
    if customer_support_instance.email:
        complaint_detail_list.append(f"-Email: {customer_support_instance.email}")
    if customer_support_instance.invoice_number:
        complaint_detail_list.append(
            f"-Invoice Number: {customer_support_instance.invoice_number}"
        )
    if customer_support_instance.complaint_description:
        complaint_detail_list.append(
            f"-Complaint Description: {customer_support_instance.complaint_description}"
        )
    complaint_detail_str = "\n".join(complaint_detail_list)

    output = utility_general_helper.clean_up_prompt(
        f"""
        A complaint has been made to the customer support team. The details are as follows:    
        {complaint_detail_str}
    """
    )
    print("output: ", output)
    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


# callback
def create_callback_request(
    conversation_id,
    tenant_name,
    schema_name,
    customer_name,
    customer_email,
    customer_contact_number,
    callback_context,
):
    optional_missing_value_list = []
    missing_value_list = []

    if not customer_name:
        missing_value_list.append("Customer Name")

    if not customer_email and schema_name != "nova_servicing":
        optional_missing_value_list.append("Customer Email (Optional)")
        customer_email = ""

    if not customer_contact_number:
        missing_value_list.append("Customer Contact Number")

    if not callback_context:
        missing_value_list.append("Callback Context")

    if missing_value_list:
        llm_action_execute_helper.raise_value_error(
            missing_value_list, optional_missing_value_list=optional_missing_value_list
        )

    conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)

    call_list_action_instance = (
        chat_action_tracker_models.CallListAction.objects.create(
            conversation=conversation_instance,
            message=conversation_instance.messages.first(),
            name=customer_name,
            contact=customer_contact_number,
            email=customer_email,
            context=callback_context,
        )
    )
    callback_chat_feature_setting = (
        chat_action_models.CallbackChatFeatureSetting.objects.first()
    )
    bot_intent_helper.send_direct_contact_request_email(
        call_list_action_instance.name,
        call_list_action_instance.contact,  # customer contact
        call_list_action_instance.email,  # customer email
        call_list_action_instance.context,
        call_list_action_instance.conversation.id,
        tenant_name,
        schema_name,
        callback_chat_feature_setting.email_to,
    )

    callback_detail_list = []
    if call_list_action_instance.name:
        callback_detail_list.append(f"-Name: {call_list_action_instance.name}")
    if call_list_action_instance.contact:
        callback_detail_list.append(
            f"-Contact Number: {call_list_action_instance.contact}"
        )
    if call_list_action_instance.email:
        callback_detail_list.append(f"-Email: {call_list_action_instance.email}")
    if call_list_action_instance.context:
        callback_detail_list.append(
            f"-Callback Context: {call_list_action_instance.context}"
        )
    callback_detail_str = "\n".join(callback_detail_list)
    output = utility_general_helper.clean_up_prompt(
        f"""
        A callback has been made to the customer support team. The details are as follows:    
        {callback_detail_str}
    """
    )
    print("output: ", output)
    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
    }


# general
def answer_business_related_inquiry(
    conversation_id,
    incoming_message,
    intro,
    tenant_name,
    general_inquiry_guide,
    is_missing_info_feature_enabled,
):
    schema_name = authapi_helper.get_schema_name()
    conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
    conversation_metadata_str = re.sub(
        r"\\(?!n)", "", json.dumps(conversation_instance.metadata)
    )
    historical_messages = chat_helper.get_historical_messages(conversation_instance)

    embeddings = OpenAIEmbeddings()

    # External Chroma DB
    external_collection_name = chat_constant.get_collection_name(
        chat_constant.external_collection, schema_name
    )
    external_chroma_db_collection = utility_chroma_db_helper.load_chroma_db_collection(
        embeddings, external_collection_name
    )
    external_retriever = external_chroma_db_collection.as_retriever(
        search_type="similarity_score_threshold",
        search_kwargs={
            "k": 4,  # now it includes Docs + FAQ also
            "score_threshold": 0.6,
        },  # return consine similarity, the higher the more similar
    )
    external_retriever_content = external_retriever.get_relevant_documents(
        bot_helper.extract_chat_history_content(historical_messages[-4:])
        + incoming_message
    )
    master_context = ""
    master_sources_str = ""
    if external_retriever_content:
        # print("retriever_content: ", retriever_content)
        master_context += "\n".join(
            [i.page_content for i in external_retriever_content]
        )
        external_sources = list(
            set([i.metadata.get("source", "") for i in external_retriever_content])
        )
        if external_sources:
            master_sources_str += "SOURCES:" + "\n".join(external_sources)

    # Check if verified; if verified, add in internal collection retriever content
    is_verified_conversation = (
        str(conversation_instance.metadata.get("is_verified", "false")).lower()
        == "true"
    )
    if is_verified_conversation:
        internal_collection_name = chat_constant.get_collection_name(
            chat_constant.internal_collection, schema_name
        )
        internal_chroma_db_collection = (
            utility_chroma_db_helper.load_chroma_db_collection(
                embeddings, internal_collection_name
            )
        )
        internal_retriever = internal_chroma_db_collection.as_retriever(
            search_type="similarity_score_threshold",
            search_kwargs={
                "k": 4,  # now it includes Docs + FAQ also
                "score_threshold": 0.6,
            },  # return consine similarity, the higher the more similar
        )
        internal_retriever_content = internal_retriever.get_relevant_documents(
            bot_helper.extract_chat_history_content(historical_messages[-4:])
            + incoming_message
        )
        if internal_retriever_content:
            master_context += "\n".join(
                [i.page_content for i in internal_retriever_content]
            )
            internal_sources = list(
                set([i.metadata.get("source", "") for i in internal_retriever_content])
            )
            if internal_sources:
                if not ("SOURCES" in master_sources_str):
                    master_sources_str += "SOURCES:"
                master_sources_str += "\n".join(internal_sources)

    appointment_chat_feature_setting = (
        chat_action_models.AppointmentChatFeatureSetting.objects.first()
    )

    appointment_reference_str = context_reference_str = (
        customer_metadata_reference_str
    ) = ""

    if (
        appointment_chat_feature_setting_describe := appointment_chat_feature_setting.describe()
    ):
        appointment_reference_str = utility_general_helper.clean_up_prompt(
            f"""
            Available appointment location details to reference when you generate your reply:
            {appointment_chat_feature_setting_describe}
        """
        )

    if master_context:
        context_reference_str = utility_general_helper.clean_up_prompt(
            f"""
            Relevant information to reference when you generate your reply:
            {master_context}
        """
        )

    if conversation_metadata_str:
        customer_metadata_reference_str = utility_general_helper.clean_up_prompt(
            f"""
            Customer information to reference when you generate your reply:
            {conversation_metadata_str}
        """
        )
    if is_missing_info_feature_enabled:
        chat_action_tracker_helper.check_if_is_missing_info(
            conversation_id,
            historical_messages,
            incoming_message,
            intro,
            tenant_name,
            general_inquiry_guide,
            master_context,
        )

    output = utility_general_helper.clean_up_prompt(
        f"""  
        -tell the customer you need to check back with the team if you do not have the information to reply the inquiry
        {appointment_reference_str}
        {context_reference_str}
        {customer_metadata_reference_str}
    """
    )

    return {
        "status": llm_action_constant.ACTION_COMPLETED,
        "output": output,
        "sources_str": master_sources_str,
    }


def irrelevant_chatter():
    output = utility_general_helper.clean_up_prompt(
        f"""
        Irrelevant Chatter from the customer. Answer politely without any promises or commitments.
        """
    )
    status = "completed"

    return {
        "status": status,
        "output": output,
    }


def needs_clarification():
    output = utility_general_helper.clean_up_prompt(
        f"""
        Unable to understand what the customer says. Ask for clarification politely to understand the customer.
        """
    )
    status = "completed"

    return {
        "status": status,
        "output": output,
    }


def voice_message_received():
    output = utility_general_helper.clean_up_prompt(
        f"""
        Voice message received. Explain to customer politely that we cannot listen to the voice message at the moment.
        """
    )
    status = "completed"
    return {
        "status": status,
        "output": output,
    }


# disable first as it clash with the "what services do you provide"
def get_action_list(available_action_describe: str):
    action_list_str = utility_general_helper.clean_up_prompt(
        f"""
    Action that you can perform:
    {available_action_describe}
"""
    )
    # print("action_list_str: ", action_list_str)
    return {"status": llm_action_constant.ACTION_COMPLETED, "output": action_list_str}


##Backlog
# def get_pet_diagnostic_detail(llm_action_class_object):
#     info_needed = {
#         "contact_number": "",
#         "pet_diagnostic_id": "",
#     }
#     llm_action_helper.update_info_needed(info_needed, metadata)

#     customer_contact_number = info_needed["contact_number"]
#     pet_diagnostic_id = info_needed["pet_diagnostic_id"]

#     is_missing_contact_number = not customer_contact_number
#     is_missing_pet_diagnostic_id = not pet_diagnostic_id

#     if is_missing_contact_number or is_missing_pet_diagnostic_id:
#         output = utility_general_helper.clean_up_prompt(
#             f"""
#             You are a customer service agent of {tenant_name} that is talking with the customer professional and casually via chat.
#             Insufficient information to create appointment request.
#             Please ask the customer to provide the following information:
#             {"-Contact Number" if is_missing_contact_number else ""}
#             {"-Pet Diagnostic ID" if is_missing_pet_diagnostic_id else ""}

#             Please ask the customer by reformatting the information into a human friendly and readable message, in 1 or 2 sentences without using point form.
#         """
#         )
#         status = "missing_info"
#     else:
#         pet_info = bot_vet_helper.get_pet_info(
#             customer_contact_number,
#             pet_diagnostic_id,
#         )
#         output = utility_general_helper.clean_up_prompt(
#             f"""
#             You have retrieved the customer pet information as below:
#             {pet_info}

#             Please reply the customer using the available information above with a human friendly and readable format, in 1 or 2 sentences without using point form.
#         """
#         )
#         status = "completed"

#     print("output: ", output)
#     return {
#         "status": status,
#         "output": output,
#     }
