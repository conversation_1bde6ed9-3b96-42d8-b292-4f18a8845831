# Conversation Metadata avoid key list, cause they refresh everytime a function call, no need to store as metadata
CONVERSATION_METADATA_AVOID_KEY_LIST = [
    "incoming_message",
    "historical_messages",
    "conversation_metadata_str",
]

# Shopify Product Inquiry
SHOPIFY_PRODUCT_OR_ORDER_INQUIRY = "shopify_product_or_order_inquiry"
SHOPIFY_PRODUCT_OR_ORDER_INQUIRY_DESCRIPTION = (
    "Action to take when the customer asked about the products or orders"
)

# WooCommerce Product Inquiry
WOOMERCE_PRODUCT_INQUIRY = "woocommerce_product_inquiry"
WOOMERCE_PRODUCT_INQUIRY_DESCRIPTION = (
    "Action to take when the customer asked about the products or orders"
)

# Appointment
GET_APPOINTMENT_LOCATION_DETAILS_REQUEST = "get_appointment_location_details_request"
CREATE_APPOINTMENT_REQUEST = "create_appointment_request"
RESCHEDULE_APPOINTMENT_REQUEST = "reschedule_appointment_request"

GET_APPOINTMENT_LOCATION_DETAILS_REQUEST_DESCRIPTION = "Action to take when the customer asked for the available appointment location details, such as the appointment address"
CREATE_APPOINTMENT_REQUEST_DESCRIPTION = (
    "Action to take when the customer asked to create a new appointment request"
)
RESCHEDULE_APPOINTMENT_REQUEST_DESCRIPTION = (
    "Action to take when the customer asked to reschedule an existing appointment."
)

# Callback
CREATE_CALLBACK_REQUEST = "create_callback_request"
CREATE_CALLBACK_REQUEST_DESCRIPTION = (
    "Action to take when the customer request to be called back or talk to human."
)
# Customer Support / Complaint
CREATE_COMPLAINT_REQUEST = "create_complaint_request"
CREATE_COMPLAINT_REQUEST_DESCRIPTION = (
    "Action to take when the customer complaints about our services or products"
)

# General Inquiry
ANSWER_BUSINESS_RELATED_INQUIRY = "answer_business_related_inquiry"
ANSWER_BUSINESS_RELATED_INQUIRY_DESCRIPTION = (
    "Action to take when the customer asked about business related inquiry"
)

# Irrelevant Chatter
IRRELEVANT_CHATTER = "irrelevant_chatter"
IRRELEVANT_CHATTER_DESCRIPTION = (
    "Action to take when the customer having irrelevant chatter to the business"
)

# Require follow up question
NEEDS_CLARIFICATION = "needs_clarification"
NEEDS_CLARIFICATION_DESCRIPTION = "Action to take when the user says something that does not make sense or require more clarification to understand what the user is talking about"

# List action
LIST_ACTION = "list_action"
LIST_ACTION_DESCRIPTION = "Action to take when the customer specifically asked about what you (CHATBOT) can do. If the customer asked about the business that you are doing, do not trigger this action."  # still clash with "what services do you provide"

VOICE_MESSAGE_RECEIVED = "voice_message_received"
VOICE_MESSAGE_RECEIVED_DESCRIPTION = "Action to take when the customer send a voice message to the chatbot or the message is # Voice Message #"

# action output
ACTION_COMPLETED = "completed"
ACTION_FAILED = "failed"
