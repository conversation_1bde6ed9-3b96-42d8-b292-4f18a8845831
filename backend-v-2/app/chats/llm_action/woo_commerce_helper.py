import app.integrations.integration.woocommerce_helper as integration_woocommerce_helper
import app.integrations.integration.models as integration_models

import app.utility.models as utility_models 

def get_product_info(customer_query: str) -> str:
    """
    WIP - No use yet
    Process customer query, fetch product information from WooCommerce API,
    and generate a response.
    """
    wc_connection_instance = integration_models.WooCommerceConnection.objects.filter(status=utility_models.ACTIVE).first()
    try:
        wc_api = integration_woocommerce_helper.get_wc_api(wc_connection_instance.url, wc_connection_instance.consumer_key, wc_connection_instance.consumer_secret)
    except Exception as e:
        print(f"Error initializing WooCommerce API: {e}")
        return "I'm sorry, but I can't access the product information at the moment. Please try again later."
    
    # First, try to extract product information from the query
    product_name = extract_product_name(customer_query)
    
    if not product_name:
        return "I'm sorry, but I couldn't understand which product you're asking about. Could you please provide the name of the product you're interested in?"

    # Search for the product in WooCommerce
    try:
        response = wc_api.get("products", params={"search": product_name})
        products = response.json()

        if not products:
            return f"I'm sorry, but I couldn't find any products matching '{product_name}'. Could you please check the spelling or try a different product name?"

        # If multiple products found, ask for clarification
        if len(products) > 1:
            product_list = ", ".join([p['name'] for p in products[:5]])
            return f"I found multiple products matching '{product_name}'. Could you please specify which one you're interested in? Here are some options: {product_list}"

        # If only one product found, provide its details
        product = products[0]
        name = product['name']
        price = product['price']
        description = product['short_description']
        stock_status = product['stock_status']

        response = f"Here's the information for {name}:\n"
        response += f"Price: ${price}\n"
        response += f"Description: {description}\n"
        response += f"Stock Status: {stock_status.capitalize()}\n"

        return response

    except Exception as e:
        print(f"Error fetching product information: {e}")
        return "I'm sorry, but I encountered an error while fetching the product information. Please try again later or contact our support team for assistance."

def extract_product_name(query: str) -> str:
    """
    WIP - No use yet
    Extract the product name from the customer query.
    This is a simple implementation and might need to be improved
    based on the complexity of customer queries.
    """
    # Remove common question words and punctuation
    words_to_remove = ['what', 'is', 'the', 'price', 'of', 'tell', 'me', 'about', '?', '.', ',']
    for word in words_to_remove:
        query = query.replace(word, '')
    
    # Return the cleaned query as the product name
    return query.strip()

