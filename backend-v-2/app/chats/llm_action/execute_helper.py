import app.chats.bot.openai_helper as bot_openai_helper

import app.chats.chat_action.models as chat_action_models
import app.chats.chat.constant as chat_constant

import app.chats.llm_action.helper as llm_action_helper
import app.chats.llm_action.execute_helper as llm_action_execute_helper
import app.chats.llm_action.constant as llm_action_constant

import app.utility.general_helper as utility_general_helper
import app.utility.date_helper as utility_date_helper
import app.utility.models as utility_models


from fuzzywuzzy import process

from typing import List

import json
import traceback


client = bot_openai_helper.get_open_ai_client()


# 3. Execute Action and return JSON Response
def get_action_json_output(
    llm_action_class_instance: "llm_action_helper.LLMAction",
):
    is_rerun = True
    excluded_action_list = []

    while is_rerun:
        available_action_describe = (
            llm_action_class_instance.get_available_action_describe(
                excluded_action_list=excluded_action_list
            )
        )
        selected_action, cost = (
            llm_action_class_instance.get_action_and_update_metadata(
                available_action_describe
            )
        )
        selected_action_dict = llm_action_class_instance.available_action_dict.get(
            selected_action,
            llm_action_class_instance.available_action_dict.get(
                llm_action_constant.IRRELEVANT_CHATTER
            ),
        )
        predefined_args = llm_action_class_instance.get_predefined_args()
        action_to_execute = selected_action_dict["name"]
        fn_to_execute = selected_action_dict["function"]

        required_args = fn_to_execute.__code__.co_varnames[
            : fn_to_execute.__code__.co_argcount
        ]
        required_args_dict = {arg: "" for arg in required_args}
        fn_doc_str = (
            utility_general_helper.clean_up_prompt(
                f"""These are the doc string for the function:
                ```
                {fn_to_execute.__doc__}
                ```
            """
            )
            if fn_to_execute.__doc__
            else ""
        )
        # This final res will be return to "generate_reply_from_action_json_output"
        # ideally it should be in format of {"action": "", "status": "", "output": "", "is_rerun": ""}
        final_res = {"action": action_to_execute}

        # Todo-yc: WIP Filter out historical messages such that it suits only the latest incoming message

        get_arguments_messages = (
            llm_action_class_instance.historical_messages
            + [
                {"role": "user", "content": llm_action_class_instance.incoming_message},
            ]
            + [
                {
                    "role": "system",
                    "content": utility_general_helper.clean_up_prompt(
                        f"""Today is {llm_action_class_instance.today}.
                            These are the list of arguments that you need to provide for {action_to_execute} action:
                            ```
                            {json.dumps(required_args_dict)}
                            ```
                            {fn_doc_str}
                            
                            Note:
                            1. For date related field, always use format {utility_date_helper.DATE_FORMAT}
                            2. For time related field, always use format {utility_date_helper.TIME_FORMAT}

                            Populate the JSON based on the conversation and latest user message (Return "" if can't find from the conversation / latest user message. STRICTLY NO FACTUAL INFORMATION).
                            Give me back the JSON ONLY and nothing else.
                        """
                    ),
                }
            ]
        )

        (
            get_arguments_res,
            _,
        ) = bot_openai_helper.get_chat_completion_response_with_gpt_4(
            get_arguments_messages, model_name=chat_constant.GPT_4_MODEL_CHOICES[1]
        )
        # arguments_response_str = bot_beefy_helper.get_beefy_response(
        #     prompt_messages=get_arguments_messages
        # ).strip()
        arguments_response_str = get_arguments_res.choices[0].message.content.strip()
        response_json = utility_general_helper.extract_json_from_str(
            arguments_response_str
        )
        try:
            llm_processed_args: dict = json.loads(response_json)
        except:
            print(
                "cannot find json format in arguments_response_str: ",
                arguments_response_str,
            )
            llm_processed_args = {}
        # print("llm_processed_args bf:", llm_processed_args)
        llm_processed_args.update(predefined_args)

        # print("llm_processed_args af: ", llm_processed_args)
        required_args = {
            k: v for k, v in llm_processed_args.items() if k in required_args
        }

        # get from conversation metadata if empty
        conversation_metadata = (
            llm_action_class_instance.conversation_instance.metadata.copy()
        )
        for key, value in required_args.items():
            if value == "" and key in conversation_metadata:
                required_args[key] = conversation_metadata[key]

        # update conversation_metadata with the required args for future ref
        non_empty_args = {
            k: v
            for k, v in required_args.items()
            if v
            and (key not in llm_action_constant.CONVERSATION_METADATA_AVOID_KEY_LIST)
        }
        conversation_metadata.update(non_empty_args)
        llm_action_class_instance.conversation_instance.metadata = conversation_metadata
        llm_action_class_instance.conversation_instance.save()

        # print("required_args: ", required_args)
        # execute function

        # function_output will always be: {"action":"","status": "", "output": "", "is_rerun": ""}
        try:
            function_output = fn_to_execute(**required_args)
            final_res.update(function_output)
        except Exception as e:
            print("=========Traceback error here: ==============\n")
            traceback.print_exc()
            print("\n=========Traceback END ==============")
            final_res["status"] = utility_models.FAILED
            final_res["output"] = str(e)
        finally:
            is_rerun = bool(final_res.get("is_rerun"))
            if is_rerun:
                print(
                    f"{selected_action} is triggered but have to be rerun. Rerunning to get the next action..."
                )
                excluded_action_list.append(action_to_execute)

    return final_res


# 4. Process the JSON response and generate a reply msg
def generate_reply_from_action_json_output(
    llm_action_class_instance: "llm_action_helper.LLMAction",
):
    """
    {
        "action": CREATE_COMPLAINT_REQUEST,
        "status": "missing_info",
        "output": missing_info_output,
        "word_limit_str": "The response should be around 100 words, unless presenting a table or list.",
    }
    """
    action_json_output = get_action_json_output(llm_action_class_instance)
    action = action_json_output["action"]
    status = action_json_output["status"]
    output = action_json_output["output"]
    sources_str = (
        action_json_output.get("sources_str", "")
        if llm_action_class_instance.is_source
        else ""
    )
    # print(
    #     f"action_json_output: {str(action_json_output)[:200]}{'...' if len(str(action_json_output)) > 200 else ''}"
    # )
    print(f"action_json_output: {str(action_json_output)}")
    chat_language_text_instruction = (
        f"Use {llm_action_class_instance.chat_language} to generate reply."
    )
    if llm_action_class_instance.chat_language == utility_models.ENGLISH:
        if (
            llm_action_class_instance.bot_setting_instance.english_type
            == utility_models.ENGLISH_US
        ):
            chat_language_text_instruction = "Use American English to generate reply."
        elif (
            llm_action_class_instance.bot_setting_instance.english_type
            == utility_models.ENGLISH_UK
        ):
            chat_language_text_instruction = "Use British English to generate reply."
        elif (
            llm_action_class_instance.bot_setting_instance.english_type
            == utility_models.SINGLISH
        ):
            chat_language_text_instruction = """Use Mild Singlish to generate reply. (Talk in a very casual chat-like manner as if on WhatsApp/Messenger between friends. Feel free to include Singaporean slang/lingo where appropriate. Keep it relaxed by using common abbreviations, emojis, etc. No need for proper punctuation or capitalization.)"""

    # Tips: If it's ValueError with Missing value, follow up with the customer accordingly.

    if status == llm_action_constant.ACTION_COMPLETED:
        action_json_result_str = f"You have performed the action: {action}, with the resulting status: {status}. Here's the output instruction of the action:\n{output}"
    else:
        action_json_result_str = f"You wish to perform this action: {action}, but you know that you will encounter this issue: \n{output}. Please ask for more information from the customer to proceed."

    #
    # , and keep the response in 1 or 2 sentences without using point form.
    # Generate a suitable reply to the user's message to continue the conversation in a friendly manner.
    #

    # instruction on word limit
    word_limit_str = f"The response should be around {int(llm_action_class_instance.bot_setting_instance.word_count*1.5)} words, unless presenting a table or list."
    if action_json_output.get("word_limit_str"):
        word_limit_str = action_json_output.get("word_limit_str")

    extra_note = ""
    if action_json_output.get("extra_note"):
        extra_note = action_json_output.get("extra_note")

    image_markdown_str = ""
    if action_json_output.get("image_markdown_str"):
        image_markdown_str = action_json_output.get("image_markdown_str")

    is_use_gpt_4 = (
        str(action_json_output.get("is_use_gpt_4", "false")).lower() == "true"
    )
    gpt_model_name = str(action_json_output.get("gpt_model_name", ""))

    system_message = utility_general_helper.clean_up_prompt(
        f"""
            Today is {llm_action_class_instance.today}.
            {llm_action_class_instance.intro}.

            Guideline to follow when generating reply:
            {llm_action_class_instance.bot_setting_instance.general_inquiry_guide}

            In the reply:
            1. {chat_language_text_instruction}
            2. Strictly no placeholder values.
            3. Information are humanized and readable.
            4. Your tone is set to {llm_action_class_instance.bot_setting_instance.tone}.
            5. {word_limit_str}

            {action_json_result_str}

            {f'Note: {extra_note}' if extra_note else ''}
        """
    )
    reply_user_message = (
        llm_action_class_instance.historical_messages
        + [
            {"role": "user", "content": llm_action_class_instance.incoming_message},
        ]
        + [
            {
                "role": "system",
                "content": utility_general_helper.clean_up_prompt(system_message),
            },
        ]
    )
    if gpt_model_name:
        (
            reply_user_response,
            _,
        ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
            reply_user_message, model_name=gpt_model_name
        )
    elif is_use_gpt_4:
        (
            reply_user_response,
            _,
        ) = bot_openai_helper.get_chat_completion_response_with_gpt_4(
            reply_user_message
        )
    else:
        (
            reply_user_response,
            _,
        ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
            reply_user_message
        )

    reply_user_response_str = reply_user_response.choices[0].message.content.strip()
    # Adjust tone if required
    if action_json_output.get("tone_adjust_guide"):
        tone_adjust_guide = action_json_output.get("tone_adjust_guide")
        adjusted_tone_reply_user_message = [
            {
                "role": "system",
                "content": utility_general_helper.clean_up_prompt(
                    f"""
                        {tone_adjust_guide}

                        Now, adjust the reply below to suits the tone and style while keeping the meaning of the reply, then return only the adjusted reply. No small talks, no chitchat, just the adjusted reply. 

                        \"\"\"{reply_user_response_str}\"\"\"
                        """
                ),
            }
        ]

        (
            adjusted_tone_reply_user_response,
            _,
        ) = bot_openai_helper.get_chat_completion_response_with_gpt_4(
            adjusted_tone_reply_user_message, model_name="gpt-4o-mini"
        )
        # print(f"Before Adjust reply: {reply_user_response_str}")
        reply_user_response_str = adjusted_tone_reply_user_response.choices[
            0
        ].message.content.strip()
        # print(f"After Adjust reply: {reply_user_response_str}")

    # print("reply_user_message: ", reply_user_message)
    return reply_user_response_str + image_markdown_str + sources_str


def raise_value_error(
    missing_value_list: List, optional_missing_value_list=[], extra_information=""
):
    missing_value_list_str = ", ".join(missing_value_list + optional_missing_value_list)
    error_msg = utility_general_helper.clean_up_prompt(
        f"""
        Missing values: {missing_value_list_str}
        {extra_information}
    """
    )

    raise ValueError(error_msg)


# appointment thingy


def check_appointment_datetime(
    appointment_location_name,
    appointment_date,
    appointment_time,
):
    check_datetime_details = {
        "is_date_valid": "",  # yes or no
        "is_time_valid": "",  # yes or no
        "is_datetime_valid": "",  # yes or no
    }

    today = utility_date_helper.get_today_str()
    appointment_chat_feature_location_instance = (
        llm_action_execute_helper.get_most_similar_location(appointment_location_name)
    )
    if not appointment_chat_feature_location_instance:
        print(
            f"{appointment_location_name} instance not found! Skipping datetime checking..."
        )
        return False
    location_opening_hour = (
        appointment_chat_feature_location_instance.describe_opening_hour()
    )

    check_datetime_message = [
        {
            "role": "system",
            "content": f"""Respond strictly in JSON formatted object in your answer.
If {appointment_date} is within {location_opening_hour}, set "is_date_valid": "yes", otherwise set "is_date_valid": "no".
If {appointment_time} is within {location_opening_hour}, set "is_time_valid": "yes", otherwise set "is_time_valid": "no".
If {appointment_date} {appointment_time} is in the future compared to {today}, set "is_datetime_valid": "yes". If it is not in the future, set "is_datetime_valid": "no".
Finally, return strictly in JSON format only by updating the JSON {{"is_date_valid": "", "is_time_valid": "", "is_datetime_valid": ""}} and nothing else (no free text, no small talk).""".replace(
                "\n", " "
            ),
        }
    ]

    check_datetime_response = client.chat.completions.create(
        model=chat_constant.GPT_4_MODEL_CHOICES[0],
        messages=check_datetime_message,
        temperature=0,
        stop="}",
    )
    # print(
    #     "check_datetime_response: ",
    #     check_datetime_response.choices[0].message.content,
    # )
    cost = bot_openai_helper.openai_api_calculate_cost(
        check_datetime_response.usage,
        model=chat_constant.GPT_4_MODEL_CHOICES[0],
    )
    check_datetime_json_response = (
        check_datetime_response.choices[0].message.content + "}"
    )  # add back the closing bracket used as stop-sequencem

    try:
        check_datetime_json_response = utility_general_helper.extract_json_from_str(
            check_datetime_json_response
        )
        check_datetime_details.update(json.loads(check_datetime_json_response))
    except Exception as e:
        print("can't load json: ", e)
        pass

    return check_datetime_details


def get_most_similar_location(appointment_location_name: str, threshold=70):
    # Get all location names
    all_locations = (
        chat_action_models.AppointmentChatFeatureLocation.objects.values_list(
            "name", flat=True
        )
    )
    # all_locations_address = (
    #     chat_action_models.AppointmentChatFeatureLocation.objects.values_list(
    #         "location", flat=True
    #     )
    # )

    # Convert QuerySet to list
    all_locations = list(all_locations)

    # Use fuzzywuzzy to find the most similar location
    best_match = process.extractOne(
        appointment_location_name, all_locations, score_cutoff=threshold
    )  # return (name, score) or None

    # if not best_match:
    #     # match with location address try
    #     best_match = process.extractOne(
    #         appointment_location_name, all_locations_address, score_cutoff=threshold
    #     )

    if best_match:
        appointment_chat_feature_location_instance = (
            chat_action_models.AppointmentChatFeatureLocation.objects.filter(
                name__icontains=best_match[0]
            ).first()
        )
    else:
        appointment_chat_feature_location_instance = None

    return appointment_chat_feature_location_instance
