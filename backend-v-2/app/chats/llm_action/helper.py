import app.chats.bot.openai_helper as bot_openai_helper
import app.chats.bot.helper as bot_helper

# import app.chats.bot.beefy_helper as bot_beefy_helper
import app.chats.bot.models as bot_models

import app.chats.chat.models as chat_models
import app.chats.chat.constant as chat_constant
import app.chats.chat_action.models as chat_action_models

import app.integrations.integration.models as integration_models

import app.chats.llm_action.models as llm_action_models
import app.chats.llm_action.constant as llm_action_constant
import app.chats.llm_action.predefined_actions as llm_predefined_actions
import app.chats.llm_action.execute_helper as llm_action_execute_helper

import app.marketing.marketing.models as marketing_models

import app.utility.general_helper as utility_general_helper
import app.utility.date_helper as utility_date_helper
import app.utility.models as utility_models

from typing import Dict, Callable, Union

import logging
import traceback
import json

logger = logging.getLogger(__name__)


client = bot_openai_helper.get_open_ai_client()
# Overall Idea:
# 1. Take in msg from user
# 2. Identify Action List, pick most relevant
# 3. Execute Action and return JSON Response
# 4. Process the JSON response and generate a reply msg
# 5. Reply to the user.


class LLMAction:
    def __init__(
        self,
        bot_setting_instance: bot_models.BotSetting,
        collection_name,
        chat_language,
        conversation_id,
        is_source,
        schema_name,
        tenant_name,
        incoming_message,
        historical_messages,
    ):
        """initialize and sets the self.name and self.metadata"""
        self.bot_setting_instance = bot_setting_instance
        self.collection_name = collection_name
        self.chat_language = chat_language
        self.conversation_id = conversation_id
        self.is_source = is_source  # is adding source to the msg or not
        self.schema_name = schema_name
        self.tenant_name = tenant_name
        self.incoming_message = incoming_message
        self.historical_messages = historical_messages
        self.custom_init()

    def custom_init(self):
        # Chatbot info
        # chat
        self.conversation_instance = chat_models.Conversation.objects.get(
            id=self.conversation_id
        )
        self.general_inquiry_guide = self.bot_setting_instance.general_inquiry_guide

        # check if verified; if verified, use internal collection guideline
        is_verified_conversation = (
            str(self.conversation_instance.metadata.get("is_verified", "false")).lower()
            == "true"
        )
        internal_bot_setting_instance = bot_models.BotSetting.objects.filter(
            bot_type=utility_models.INTERNAL_TEXT
        ).first()
        if is_verified_conversation and internal_bot_setting_instance:
            self.general_inquiry_guide = (
                internal_bot_setting_instance.general_inquiry_guide
            )

        self.intro = f"Your name is \"{self.bot_setting_instance.chatbot_name or 'Celestia'}\". You are a virtual assistant for {self.tenant_name}. \nYour task is to assist the customer. Avoid talking to yourself. Sometimes customer can mention other people like \"my college\" or \"my husband\", etc. Make sure you understand what the customer is requesting and do follow up actions accordingly."

        # Date
        self.today = utility_date_helper.get_today_str()
        # Features
        self.appointment_chat_feature_setting = (
            chat_action_models.AppointmentChatFeatureSetting.objects.first()
        )
        self.customer_support_chat_feature_setting = (
            chat_action_models.CustomerSupportChatFeatureSetting.objects.first()
        )
        self.callback_chat_feature_setting = (
            chat_action_models.CallbackChatFeatureSetting.objects.first()
        )
        self.missing_info_chat_feature_setting = (
            chat_action_models.MissingInfoChatFeatureSetting.objects.first()
        )
        self.is_shopify_connection = False
        shopify_connection = integration_models.ShopifyConnection.objects.first()
        if shopify_connection and shopify_connection.status == utility_models.ACTIVE:
            self.is_shopify_connection = True

        # WooCommerce
        self.is_woocommerce_connection = False
        woocommerce_connection = (
            integration_models.WooCommerceConnection.objects.first()
        )
        if (
            woocommerce_connection
            and woocommerce_connection.status == utility_models.ACTIVE
        ):
            self.is_woocommerce_connection = True

        self.is_appointment_feature_enabled = (
            self.appointment_chat_feature_setting.is_usable()
        )
        self.is_customer_support_feature_enabled = (
            self.customer_support_chat_feature_setting.is_usable()
        )
        self.is_callback_feature_enabled = (
            self.callback_chat_feature_setting.is_usable()
        )
        self.is_missing_info_feature_enabled = (
            self.missing_info_chat_feature_setting.is_usable()
        )
        # appointments
        self.appointment_chat_feature_setting_describe = (
            self.appointment_chat_feature_setting.describe()
        )

        # if only 1 location stated, automatically assigned the location name
        if chat_action_models.AppointmentChatFeatureLocation.objects.count() == 1:
            self.appointment_location_name = (
                chat_action_models.AppointmentChatFeatureLocation.objects.first().name
            )

        # actions
        self.available_action_dict = self.get_available_action_dict()

        # update client customer
        self.update_or_create_client_customer()

    def execute(self):
        """execute the action (self) and return the result"""
        chatbot_reply = (
            llm_action_execute_helper.generate_reply_from_action_json_output(self)
        )

        no_reply_on_inaccurate_answer_setting_instance = (
            chat_action_models.NoReplyOnInaccurateAnswerSetting.objects.first()
        )
        if (
            len(self.historical_messages) > 1
            and no_reply_on_inaccurate_answer_setting_instance
            and no_reply_on_inaccurate_answer_setting_instance.is_usable()
        ):  # if not the first message + if enabled, check if the generate answer make sense in overall context
            historical_messages_str = "\n".join(
                [f"{msg['role']}: {msg['content']}" for msg in self.historical_messages]
            )
            check_generated_answer_messages = [
                {
                    "role": "user",
                    "content": utility_general_helper.clean_up_prompt(
                        f"""
                    You are JSON expert from {self.tenant_name} that only reply in JSON, that contains \"is_reply_required\" (whether a reply is required to be made to the customer) and \"is_conversation_closed\" (whether the conversation ends based on the Historical Messages). Today is {self.today}. 
                    
                    Below is the Historical Messages between a customer (user) and customer service representative (assistant):
                                         
                    Historical Messages:
                    {historical_messages_str}

                    Now the reply that we planned to send the customer is: "{chatbot_reply}"

                    Your task now is to determine whether the reply above is required and reasonable to the entire conversation, especially to the latest reply by the customer. Do note:
                    1. If the conversation is in the middle of a process to perform an action, and the reply is to collect more information, then the is_reply_required is True, is_conversation_closed is False.
                    2. If the the reply do add values to the customer, then the is_reply_required is True, is_conversation_closed is False.
                    3. If the last message in the conversation are only expressing gratitude and the reply do not add any value to the customer, then the is_reply_required is False, is_conversation_closed is True.
                    
                    Then, return the JSON:
                    {{"is_reply_required": <"True" or "False">, "is_conversation_closed": <"True" or "False">}} and nothing else (no free text, no small talk).

                    """
                    ),
                },
            ]

            (
                chat_completion_response,
                _,
            ) = bot_openai_helper.get_chat_completion_response_with_gpt_4(
                check_generated_answer_messages, model_name="gpt-4o-mini"
            )

            try:
                chat_completion_response_json_str = (
                    utility_general_helper.extract_json_from_str(
                        chat_completion_response.choices[0].message.content.strip()
                    )
                )
                is_reply_required_json = json.loads(chat_completion_response_json_str)
                is_reply_required = is_reply_required_json.get("is_reply_required", "")
                is_conversation_closed = is_reply_required_json.get(
                    "is_conversation_closed", ""
                )
                if (
                    str(is_reply_required).lower().strip() == "false"
                    and str(is_conversation_closed).lower().strip() == "true"
                ):
                    logger.info(f"chatbot_reply that is not required: {chatbot_reply}")
                    chatbot_reply = ""
            except Exception as e:
                traceback.print_exc()
                logger.error(
                    f'chat_completion_response: "{chat_completion_response}" is not in json format with error: {e}. Skipping...'
                )

        return chatbot_reply

    def update_or_create_client_customer(self):
        client_customer_instance, _ = (
            marketing_models.ClientCustomer.objects.get_or_create(
                conversation=self.conversation_instance
            )
        )
        conversation_metadata = self.conversation_instance.metadata
        # Update fields only if they exist in metadata
        if conversation_metadata_name := conversation_metadata.get("name"):
            client_customer_instance.name = str(conversation_metadata_name).title()
        if conversation_metadata_email := conversation_metadata.get("email"):
            client_customer_instance.email = conversation_metadata_email
        if conversation_metadata.get("contact_number", ""):
            client_customer_instance.contact = conversation_metadata.get(
                "contact_number", ""
            )
        client_customer_instance.save()

    def get_action_and_update_metadata(self, available_action_describe):
        customer_information_str = f"""
        name, contact_number, email, appointment_date (use format {utility_date_helper.DATE_FORMAT}), appointment_time (use format {utility_date_helper.TIME_FORMAT}), appointment_location_name, appointment_reason, callback_reason
        """
        historical_messages_str = "\n".join(
            [f"{msg['role']}: {msg['content']}" for msg in self.historical_messages]
        )
        remaining_attempts = 3
        action_total_cost = 0
        while remaining_attempts > 0:
            remaining_attempts -= 1

            system_message_real = {
                "role": "system",
                "content": utility_general_helper.clean_up_prompt(
                    f"""
                    I'm having a conversation with a customer, and I need to know what action to perform next.

                    Here's the conversation I had with the customer:
                    {historical_messages_str}

                    Now, you are a JSON expert from {self.tenant_name} that only reply in JSON, that only contains \"action_name\" and \"metadata\". Today is {self.today}. Help me determine the action that I need to perform to assist the customer. Consider the context in the previous conversation, get only one and the most suitable action name, based on the ActionName and ActionDescription list here: 
                    {available_action_describe}. 

                    ----
                    Next, create or update the customer information (For example, {customer_information_str} and many more that is insightful for the analytic team to understand their customer. Keep the metadata short and consise, with text only) into the current meta information here in JSON Format: {self.conversation_instance.metadata}, based on the conversation happened. Strictly do not include factual information that is not found within the conversation. Leave them blank if you can't find any.

                    Return the information in JSON by updating the JSON:
                    ```
                    {{"action_name": "", metadata: {{}}}}
                    ``` and nothing else (no free text, no small talk)."""
                ),
            }

            action_messages = [system_message_real]
            # beefy_response = bot_beefy_helper.get_beefy_response(action_messages)
            # print("-" * 50)
            # print("beefy_response: ", beefy_response[:500])
            (
                chat_completion_response,
                _,
            ) = bot_openai_helper.get_chat_completion_response_with_gpt_4(
                action_messages
            )

            # print(
            #     "chat_completion_response: ",
            #     chat_completion_response.choices[0].message.content,
            # )
            # print("-" * 50)
            action_total_cost += bot_openai_helper.openai_api_calculate_cost(
                chat_completion_response.usage,
                model=chat_constant.GPT_4_MODEL_CHOICES[0],
            )
            action_response = (
                chat_completion_response.choices[0].message.content.strip().lower()
            )

            action_name = ""
            metadata = self.conversation_instance.metadata.copy()

            # print("action_response: ", action_response)
            try:
                action_response = utility_general_helper.extract_json_from_str(
                    action_response
                )
                response_data = json.loads(action_response)
                # Update only the metadata part of the action_information dictionary
                response_metadata = response_data.get("metadata", {})
                action_name = response_data.get("action_name", {})

                filtered_metadata = {
                    key: value
                    for key, value in response_metadata.items()
                    if value != ""
                    and (
                        key
                        not in llm_action_constant.CONVERSATION_METADATA_AVOID_KEY_LIST
                    )
                }
                metadata.update(filtered_metadata)
                # Now update the conversation_instance metadata with the merged information
                self.conversation_instance.metadata.update(metadata)
                self.conversation_instance.save()
            except Exception as e:
                print(
                    f'action_response: "{action_response}" is not in json format with error: {e}, retrying... attempt left: {remaining_attempts}'
                )
                continue

            # print(
            #     f"Updated Metadata: {json.dumps(metadata)}; Updated Action Name: {str(action_name)}"
            # )
            if any(
                action_choice in action_name
                for action_choice in self.available_action_dict.keys()
            ):
                return action_name, action_total_cost
            else:
                action_messages.append(
                    {
                        "role": "system",
                        "content": f'Your JSON response on "action" HAS TO BE either one in this list: {self.available_action_dict.keys()}. If you are unsure, please return {llm_action_constant.IRRELEVANT_CHATTER}',
                    }
                )

        if remaining_attempts == 0:
            return llm_action_constant.IRRELEVANT_CHATTER, action_total_cost

    def get_predefined_args(self):
        # Access instance variables
        variables = vars(self)

        # Filter out any special or unwanted attributes if necessary
        predefined_args = {
            key: value
            for key, value in variables.items()
            if isinstance(value, (str, int, float, list, bool))
        }

        return predefined_args

    def get_available_action_describe(
        self, is_with_list_action=False, excluded_action_list=[]
    ):
        available_action_dict = self.get_available_action_dict()

        available_action_describe = "\n"
        for index, (_, available_action_dict_) in enumerate(
            available_action_dict.items(), start=1
        ):
            if (
                available_action_dict_["name"] == llm_action_constant.LIST_ACTION
                and not is_with_list_action
            ):
                continue

            if available_action_dict_["name"] in excluded_action_list:
                continue

            available_action_describe += f"{index}. {available_action_dict_['name']}: {available_action_dict_['description']}\n"

        return available_action_describe

    def get_available_action_dict(
        self,
    ) -> Dict[str, Dict[str, Union[str, Callable]]]:
        """returns dictionary / table of available actions"""
        action_dict = {
            # General
            llm_action_constant.ANSWER_BUSINESS_RELATED_INQUIRY: {
                "name": llm_action_constant.ANSWER_BUSINESS_RELATED_INQUIRY,
                "description": llm_action_constant.ANSWER_BUSINESS_RELATED_INQUIRY_DESCRIPTION,
                "function": llm_predefined_actions.answer_business_related_inquiry,
            },
            llm_action_constant.IRRELEVANT_CHATTER: {
                "name": llm_action_constant.IRRELEVANT_CHATTER,
                "description": llm_action_constant.IRRELEVANT_CHATTER_DESCRIPTION,
                "function": llm_predefined_actions.irrelevant_chatter,
            },
            llm_action_constant.NEEDS_CLARIFICATION: {
                "name": llm_action_constant.NEEDS_CLARIFICATION,
                "description": llm_action_constant.NEEDS_CLARIFICATION_DESCRIPTION,
                "function": llm_predefined_actions.needs_clarification,
            },
            llm_action_constant.VOICE_MESSAGE_RECEIVED: {
                "name": llm_action_constant.VOICE_MESSAGE_RECEIVED,
                "description": llm_action_constant.VOICE_MESSAGE_RECEIVED_DESCRIPTION,
                "function": llm_predefined_actions.voice_message_received,
            },
            # # disable first as it clash with the "what services do you provide"
            # llm_action_constant.LIST_ACTION: {
            #     "name": llm_action_constant.LIST_ACTION,
            #     "description": llm_action_constant.LIST_ACTION_DESCRIPTION,
            #     "function": llm_predefined_actions.get_action_list,
            # },
            # # Ultimate Fallback
            # llm_action_constant.UNRECOGNIZED_REQUEST: {
            #     "name": llm_action_constant.UNRECOGNIZED_REQUEST,
            #     "description": llm_action_constant.UNRECOGNIZED_REQUEST_DESCRIPTION,
            #     "function": llm_predefined_actions.unrecognized_request,
            # },
        }

        # Shopify Product / Order
        shopify_product_or_order_actions = {
            llm_action_constant.SHOPIFY_PRODUCT_OR_ORDER_INQUIRY: {
                "name": llm_action_constant.SHOPIFY_PRODUCT_OR_ORDER_INQUIRY,
                "description": llm_action_constant.SHOPIFY_PRODUCT_OR_ORDER_INQUIRY_DESCRIPTION,
                "function": llm_predefined_actions.shopify_product_or_order_inquiry,
            },
        }
        if self.is_shopify_connection:
            action_dict = {**action_dict, **shopify_product_or_order_actions}

        # WooCommerce Product / Order
        # woocommerce_product_or_order_actions = {
        #     llm_action_constant.WOOMERCE_PRODUCT_INQUIRY: {
        #         "name": llm_action_constant.WOOMERCE_PRODUCT_INQUIRY,
        #         "description": llm_action_constant.WOOMERCE_PRODUCT_INQUIRY_DESCRIPTION,
        #         "function": llm_predefined_actions.woocommerce_product_inquiry,
        #     },
        # }
        # if self.is_woocommerce_connection:
        #     action_dict = {**action_dict, **woocommerce_product_or_order_actions}

        # Appointments
        appointment_actions = {
            llm_action_constant.GET_APPOINTMENT_LOCATION_DETAILS_REQUEST: {
                "name": llm_action_constant.GET_APPOINTMENT_LOCATION_DETAILS_REQUEST,
                "description": llm_action_constant.GET_APPOINTMENT_LOCATION_DETAILS_REQUEST_DESCRIPTION,
                "function": llm_predefined_actions.get_appointment_location_details_request,
            },
            llm_action_constant.CREATE_APPOINTMENT_REQUEST: {
                "name": llm_action_constant.CREATE_APPOINTMENT_REQUEST,
                "description": llm_action_constant.CREATE_APPOINTMENT_REQUEST_DESCRIPTION,
                "function": llm_predefined_actions.create_appointment_request,
            },
            llm_action_constant.RESCHEDULE_APPOINTMENT_REQUEST: {
                "name": llm_action_constant.RESCHEDULE_APPOINTMENT_REQUEST,
                "description": llm_action_constant.RESCHEDULE_APPOINTMENT_REQUEST_DESCRIPTION,
                "function": llm_predefined_actions.reschedule_appointment_request,
            },
        }
        if self.is_appointment_feature_enabled:
            # put some condition here on making appointment thingy
            action_dict = {**action_dict, **appointment_actions}

        # Customer Support
        customer_support_actions = {  # Customer Support
            llm_action_constant.CREATE_COMPLAINT_REQUEST: {
                "name": llm_action_constant.CREATE_COMPLAINT_REQUEST,
                "description": llm_action_constant.CREATE_COMPLAINT_REQUEST_DESCRIPTION,
                "function": llm_predefined_actions.create_complaint_request,
            },
        }
        if self.is_customer_support_feature_enabled:
            action_dict = {**action_dict, **customer_support_actions}

        # Callback
        callback_actions = {  # Callback
            llm_action_constant.CREATE_CALLBACK_REQUEST: {
                "name": llm_action_constant.CREATE_CALLBACK_REQUEST,
                "description": llm_action_constant.CREATE_CALLBACK_REQUEST_DESCRIPTION,
                "function": llm_predefined_actions.create_callback_request,
            },
        }
        if self.is_callback_feature_enabled:
            action_dict = {**action_dict, **callback_actions}

        custom_llm_action_instances = llm_action_models.CustomLLMAction.objects.all()
        function_environment = {}
        for custom_llm_action_instance in custom_llm_action_instances:
            try:
                exec(custom_llm_action_instance.function_str, function_environment)
                action_dict[custom_llm_action_instance.name] = {
                    "name": custom_llm_action_instance.name,
                    "description": custom_llm_action_instance.description,
                    "function": function_environment[custom_llm_action_instance.name],
                }
            except Exception as e:
                print(
                    f"Error in custom_llm_action_instance {custom_llm_action_instance.name}: {e}; Please review this action",
                )
            # pass

        # function_environment = {}
        # exec(
        #     selected_action_dict["function"], function_environment
        # )
        # print("function_environment: ", function_environment)

        return action_dict
