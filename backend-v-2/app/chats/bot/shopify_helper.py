import time

import app.chats.bot.chatbot_helper as bot_chatbot_helper
import app.chats.bot.intent_handler as bot_intent_handler
import app.chats.bot.intent_helper as bot_intent_helper
import app.chats.bot.template_helper as bot_template_helper
import app.chats.bot.openai_helper as bot_openai_helper

import app.chats.chat.constant as chat_constant

from app.integrations.integration.models import ShopifyConnection

import app.chats.llm_action.execute_helper as llm_action_execute_helper

from tenant_schemas.utils import schema_context

import app.utility.models as utility_models
import app.utility.general_helper as utility_general_helper

from collections import defaultdict
from fuzzywuzzy import fuzz, process

import traceback
import json
import requests


client = bot_openai_helper.get_open_ai_client()

SHOPIFY_PRODUCT_INQUIRY = "product_inquiry"
SHOPIFY_UPDATE_NOTE = "update_note"
SHOPIFY_CONFIRMATION_NEEDED = "confirmation_needed"
SHOPIFY_CHECKOUT = "checkout"
SHOPIFY_OTHER = "other"

SHOPIFY_INTENT_LIST = [
    SHOPIFY_PRODUCT_INQUIRY,
    SHOPIFY_UPDATE_NOTE,
    SHOPIFY_CONFIRMATION_NEEDED,
    SHOPIFY_CHECKOUT,
    SHOPIFY_OTHER,
]


class InvalidQueryException(Exception):
    pass


def get_upsell_items(product_type, product_price, access_token, graphql_url):
    # Endpoint for the Shopify GraphQL API

    # GraphQL query to get product data
    graphql_query = f"""{{
        products(first: 10, query: "{product_type}") {{
            edges {{
            node {{
                id
                title
                productType
                vendor
                priceRangeV2 {{
                minVariantPrice {{
                    amount
                    currencyCode
                }}
                maxVariantPrice {{
                    amount
                    currencyCode
                }}
                }}
            }}
            }}
        }}
    }}"""

    data = {"query": graphql_query}

    # Convert the data dictionary to a JSON string
    json_data = json.dumps(data)

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": access_token,
    }

    response = requests.post(graphql_url, data=json_data, headers=headers)
    upsell_items = []

    count = 0
    for similar_product in response.json()["data"]["products"]["edges"]:
        if (
            similar_product["node"]["priceRangeV2"]["maxVariantPrice"]["amount"]
            > product_price
        ):
            count += 1
            if count > 5:
                break
            upsell_items.append(similar_product["node"]["title"])

    return upsell_items


def get_frequently_bought_together_items(product_title, access_token, order_url):
    # Convert the data dictionary to a JSON string
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": access_token,
    }

    response = requests.get(order_url, headers=headers)

    # if response.status_code == 200:
    product_count = defaultdict(int)
    orders_containing_product = []

    orders = response.json().get("orders", [])

    for order in orders:
        for line_item in order.get("line_items", []):
            if line_item.get("title") == product_title:
                orders_containing_product.append(order)

    for order in orders_containing_product:
        for line_item in order.get("line_items", []):
            if line_item.get("title") != product_title:
                product_count[line_item.get("title")] += 1

    return [
        item[0]
        for item in sorted(product_count.items(), key=lambda x: x[1], reverse=True)
    ][:3]


def update_shopify_order_note(order_id, note):

    shopify_connection = ShopifyConnection.objects.first()
    graphql_url = shopify_connection.site_url + "/admin/api/2023-10/graphql.json"

    variables = {"input": {"id": order_id, "note": note}}
    query = f"""
    mutation orderUpdate($input: OrderInput!) {{
    orderUpdate(input: $input) {{
        order {{
        id
        note
        }}
        userErrors {{
        message
        field
        }}
    }}
    }}
    """

    headers = {
        "X-Shopify-Access-Token": shopify_connection.api_secret,
        "Content-Type": "application/json",
    }
    res = requests.post(
        graphql_url,
        json={"query": query, "variables": variables},
        headers=headers,
    )
    return res.json()


def get_shopify_order_id(order_number):
    # Endpoint for the Shopify GraphQL API
    shopify_connection = ShopifyConnection.objects.first()
    graphql_url = shopify_connection.site_url + "/admin/api/2023-10/graphql.json"

    # GraphQL query to get product data
    graphql_query = f"""{{
        orders(first: 1, query: "{order_number}") {{
            edges {{
            node {{
                id
                name
            }}
            }}
        }}
    }}"""

    data = {"query": graphql_query}

    # Convert the data dictionary to a JSON string
    json_data = json.dumps(data)

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_connection.api_secret,
    }

    response = requests.post(graphql_url, data=json_data, headers=headers)
    print(f"{response.json()=}")
    if len(response.json()["data"]["orders"]["edges"]) == 0:
        return -1
    order_id = response.json()["data"]["orders"]["edges"][0]["node"]["id"]
    if (
        order_number == response.json()["data"]["orders"]["edges"][0]["node"]["name"]
    ) or (
        "#" + order_number
        == response.json()["data"]["orders"]["edges"][0]["node"]["name"]
    ):
        return order_id

    return -1


def query_shopify_api(
    schema_name,
    incoming_message,
    historical_messages,
    shopify_query,
    shopify_intent_json,
):
    """
    return -1/0/1, response,
    -1: failed to connect to shopify, don't retry
    0: wrong query, retry
    1: success, don't retry
    """
    MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[0]
    with schema_context(schema_name):
        try:
            shopify_connection = ShopifyConnection.objects.first()

            graphql_url = (
                shopify_connection.site_url + "/admin/api/2023-10/graphql.json"
            )
            order_url = (
                shopify_connection.site_url
                + "/admin/api/2023-10/orders.json?status=any&limit=50"
            )
            headers = {
                "X-Shopify-Access-Token": shopify_connection.api_secret,
                "Content-Type": "application/graphql",
            }
            response = requests.post(graphql_url, headers=headers, data=shopify_query)
            if response.status_code != 200:
                return (
                    -1,
                    "This ChatBot is not connected to the store. Please contact the administrator.",
                )
            if "errors" in response.json():
                return (0, response.json()["errors"][0]["message"])
        except ShopifyConnection.DoesNotExist:
            return (
                -1,
                "This ChatBot is not connected to the store. Please contact the administrator.",
            )

        else:  # executed when there's no error
            # if intent['intent'] == "other" or intent['recommendation_opportunity'] == False:
            if shopify_intent_json["intent"] == bot_intent_helper.OTHER:
                chat_completion_messages = historical_messages + [
                    {
                        "role": "system",
                        "content": bot_template_helper.get_shopify_other_response_prompt_template(
                            response.json(),
                            schema_name,
                        ),
                    },
                    {
                        "role": "user",
                        "content": incoming_message,
                    },
                ]
                chat_completion_response = client.chat.completions.create(
                    model=MODEL_NAME, messages=chat_completion_messages, temperature=0
                )

                bot_openai_helper.openai_api_calculate_cost(
                    chat_completion_response.usage, model=MODEL_NAME
                )
                chatbot_answer = chat_completion_response.choices[0].message.content
                return (1, chatbot_answer)
            else:
                try:
                    product = response.json()["data"]["products"]["edges"][0]["node"]
                    current_product_price = product["priceRangeV2"]["maxVariantPrice"][
                        "amount"
                    ]
                    upsell_items = get_upsell_items(
                        product["productType"],
                        current_product_price,
                        shopify_connection.api_secret,
                        graphql_url,
                    )
                    frequently_bought_together_items = (
                        get_frequently_bought_together_items(
                            product["title"],
                            shopify_connection.api_secret,
                            order_url,
                        )
                    )
                    # frequently_bought_together_items = []

                    chat_completion_messages = historical_messages + [
                        {
                            "role": "system",
                            "content": bot_template_helper.get_shopify_product_response_prompt_template(
                                response.json(),
                                schema_name,
                                upsell_items,
                                frequently_bought_together_items,
                            ),
                        },
                        {
                            "role": "user",
                            "content": incoming_message,
                        },
                    ]
                    chat_completion_response = client.chat.completions.create(
                        model=MODEL_NAME,
                        messages=chat_completion_messages,
                        temperature=0,
                    )

                    bot_openai_helper.openai_api_calculate_cost(
                        chat_completion_response.usage, model=MODEL_NAME
                    )
                    chatbot_answer = chat_completion_response.choices[0].message.content
                    return (1, chatbot_answer)
                except Exception as e:
                    chat_completion_messages = historical_messages + [
                        {
                            "role": "system",
                            "content": bot_template_helper.get_shopify_other_response_prompt_template(
                                response.json(), schema_name
                            ),
                        },
                        {
                            "role": "user",
                            "content": incoming_message,
                        },
                    ]
                    chat_completion_response = client.chat.completions.create(
                        model=MODEL_NAME,
                        messages=chat_completion_messages,
                        temperature=0,
                    )

                    bot_openai_helper.openai_api_calculate_cost(
                        chat_completion_response.usage, model=MODEL_NAME
                    )
                    chatbot_answer = chat_completion_response.choices[0].message.content
                    return (1, chatbot_answer)


def get_checkout_url(
    schema_name, historical_messages, incoming_message, graphql_query, items
):
    MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[0]
    with schema_context(schema_name):
        try:
            shopify_connection = ShopifyConnection.objects.first()

            graphql_url = (
                shopify_connection.site_url + "/admin/api/2023-10/graphql.json"
            )
            headers = {
                "X-Shopify-Access-Token": shopify_connection.api_secret,
                "Content-Type": "application/graphql",
            }
            response = requests.post(graphql_url, headers=headers, data=graphql_query)
            if response.status_code != 200:
                print(
                    "======================\n======================\n======================"
                )
                print("response: ", response.content)
                return "This ChatBot is not connected to the store. Please contact the administrator."
        except ShopifyConnection.DoesNotExist:
            return "This ChatBot is not connected to the store. Please contact the administrator."
        else:
            products = response.json()["data"]["products"]["edges"]
            if len(products) == 0:
                return "I am sorry, I am not sure which product you are referring to. Please try again."
            # list first variant ids for each product
            return_url = shopify_connection.site_url + "/cart/"
            for product in products:
                variant_id = product["node"]["variants"]["edges"][0]["node"][
                    "id"
                ].split("/")[-1]
                quantity = items[product["node"]["title"].lower()]
                return_url += f"{variant_id}:{quantity},"
            return_url = return_url[:-1]
            chat_completion_messages = historical_messages + [
                {
                    "role": "system",
                    "content": f"""You are a helpful and expert customer service officer of {self.tenant_name}. 
                    You are to ask the customer if they want to checkout with the following items: {items}.
                    In your response, you are to include the checkout url: {return_url}.
                    """,
                },
                {
                    "role": "user",
                    "content": incoming_message,
                },
            ]
            chat_completion_response = client.chat.completions.create(
                model=MODEL_NAME, messages=chat_completion_messages, temperature=0
            )

            bot_openai_helper.openai_api_calculate_cost(
                chat_completion_response.usage, model=MODEL_NAME
            )
            chatbot_answer = chat_completion_response.choices[0].message.content
            return chatbot_answer


def get_response_for_shopify_query(
    schema_name,
    tenant_name,
    chat_language,
    historical_messages,
    incoming_message,
    shopify_intent_json,
):
    MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[0]
    if shopify_intent_json["intent"] == SHOPIFY_CONFIRMATION_NEEDED:
        return "I am sorry, I am not sure which product you are referring to. Please try again."
    elif shopify_intent_json["intent"] == SHOPIFY_UPDATE_NOTE:
        messages = (
            [
                {
                    "role": "system",
                    "content": f"""You are a helpful and expert customer service officer of {tenant_name}. You are to update the customer note for the order. You end up with  Please extract the order name and the order note from the customer's message. Return the answer in JSON format by updating {{"order_number": "","note": ""}} and nothing else (no free text, no small talk).""",
                },
            ]
            + historical_messages
            + [
                {
                    "role": "user",
                    "content": bot_template_helper.get_general_prompt_template(
                        chat_language,
                        incoming_message,
                        tenant_name,
                        extra_instruction="""Answer: return the answer in JSON format by updating {"order_number": "","note": ""} and nothing else (no free text, no small talk).""",
                    ),
                }
            ]
        )

        chat_completion_response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=messages,
            temperature=0,
        )
        data = json.loads(chat_completion_response.choices[0].message.content)
        if data["order_number"] == "" or data["note"] == "":
            messages = [
                {
                    "role": "system",
                    "content": f"""You are a helpful and expert customer service officer of {tenant_name}. You are to update the customer note for the order. You need order number and customer note to update the order note. So far, you have collected this:{data}. Please ask the customer for the missing information.""",
                },
                {
                    "role": "user",
                    "content": bot_template_helper.get_general_prompt_template(
                        chat_language,
                        incoming_message,
                        tenant_name,
                        extra_instruction="",
                    ),
                },
            ] + historical_messages
            chat_completion_response = client.chat.completions.create(
                model=MODEL_NAME,
                messages=messages,
                temperature=0,
            )
            return chat_completion_response.choices[0].message.content

        else:
            order_id = get_shopify_order_id(
                data["order_number"],
            )
            if order_id == -1:
                return f"I am sorry, I am unable to find the order with the order number: {data['order_number']}. Please check the order number and try again."
            update_shopify_order_note(
                order_id,
                data["note"],
            )
            messages = [
                {
                    "role": "system",
                    "content": f"""You are a helpful and expert customer service officer of {tenant_name}. You have updated the customer note for the order. Let's ask the customer if they need anything else.""",
                },
                {
                    "role": "user",
                    "content": bot_template_helper.get_general_prompt_template(
                        chat_language,
                        incoming_message,
                        tenant_name,
                        extra_instruction="",
                    ),
                },
            ] + historical_messages
            chat_completion_response = client.chat.completions.create(
                model=MODEL_NAME,
                messages=messages,
                temperature=0,
            )
            return chat_completion_response.choices[0].message.content
    elif shopify_intent_json["intent"] == SHOPIFY_CHECKOUT:
        items = shopify_intent_json["cart_items"]
        query = f"'{items[0]['item_name']}'"
        for item in items[1:]:
            query += f" OR '{item['item_name']}'"
        graphql_query = f"""{{products(first: 10, query: "{query}") {{edges {{node {{id title variants(first: 10) {{edges {{node {{id }}}}}}}}}}}}}}"""
        formatted_items = {}
        for item in items:
            formatted_items[item["item_name"].lower()] = item["quantity"]
        return get_checkout_url(
            schema_name, historical_messages, incoming_message, graphql_query, items
        )
    else:  # product_inquiry
        remaining_attempts = 3
        chat_completion_messages = historical_messages + [
            {
                "role": "system",
                "content": bot_template_helper.get_shopify_query_prompt_template(),
            },
            {
                "role": "user",
                "content": bot_template_helper.get_general_prompt_template(
                    chat_language,
                    incoming_message,
                    tenant_name,
                    (
                        "DO NOT INCLUDES ANY TEXT OR EXPLAINATION EXCEPT GRAPHQL QUERY IN THE RESPONSE.",
                        "DO NOT USE PRICE IN SORTKEYS IF THE QUERY IS ABOUT PRODUCTS",
                    ),
                ),
            },
        ]
        while remaining_attempts > 0:
            remaining_attempts -= 1
            try:
                try:
                    chat_completion_response = client.chat.completions.create(
                        model=MODEL_NAME,
                        messages=chat_completion_messages,
                        temperature=0,
                    )
                except:
                    MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[1]
                    chat_completion_response = client.chat.completions.create(
                        model=MODEL_NAME,
                        messages=chat_completion_messages,
                        temperature=0,
                    )
                bot_openai_helper.openai_api_calculate_cost(
                    chat_completion_response.usage, model=MODEL_NAME
                )
                shopify_query = chat_completion_response.choices[0].message.content
                formatted_shopify_query = shopify_query
                if not shopify_query.startswith("{"):
                    formatted_shopify_query = shopify_query.split("```")[1]
                    formatted_shopify_query = formatted_shopify_query.strip("graphql")
                    formatted_shopify_query = formatted_shopify_query.strip("\n")
                    formatted_shopify_query
                if not formatted_shopify_query.startswith("{"):
                    raise Exception("shopify query is not in valid format")
                if not formatted_shopify_query.endswith("}"):
                    raise Exception("shopify query is not in valid format")
                retry, shopify_query_answer = query_shopify_api(
                    schema_name,
                    incoming_message,
                    historical_messages,
                    formatted_shopify_query,
                    shopify_intent_json,
                )
                if retry == 0:
                    raise InvalidQueryException("shopify query is not valid")
                return shopify_query_answer
            except InvalidQueryException as e:
                chat_completion_messages.append(
                    {
                        "role": "system",
                        "content": f"The graph query failed with this error message: {shopify_query_answer}. Please try again.",
                    }
                )
            except Exception as e:
                chat_completion_messages.append(
                    {
                        "role": "system",
                        "content": f"Your response MUST NOT INCLUDE any free text except for the shopify query. Please try again.",
                    }
                )
        print(shopify_query)
        return "I am sorry, I am currently unable to help you with this question. Please try again later."


def get_shopify_intent_json(
    tenant_name, historical_messages, chat_language, incoming_message
):
    MODEL_NAME = chat_constant.GPT_4_MODEL_CHOICES[0]
    shopify_intent_json = {"intent": bot_intent_helper.OTHER}

    remaining_attempts = 3
    while remaining_attempts > 0:
        remaining_attempts -= 1
        chat_completion_messages = (
            [
                {
                    "role": "system",
                    "content": f"""
                    You are javascript object notation expert from {tenant_name}. 
                    You are now required to determine the intent of the customer's latest message within the context of the past conversation. 
                    Get only one of the following intents: {','.join(SHOPIFY_INTENT_LIST)}.
                    If the customer is asking to add to cart or checkout, base on the customer question and previous conversation, list out the items that customer want to checkout. Return the information in JSON by updating the JSON:{{"intent": "{SHOPIFY_CHECKOUT}","cart_items": [{{"item_name":"", "quantity":number}}]}} and nothing else (no free text, no small talk).
                    If the customer is asking to add_to_cart or checkout but you are unsure which product the customer is referring to or how many quantity for item, return {{"intent": "{SHOPIFY_CONFIRMATION_NEEDED}"}}.
                    Next, base on the customer question and previous conversation, Determine if there is an opportunity for upsell or cross-sell recommendtion, if there is, update field recommendation_opportunity=True.
                    Return the information in JSON by updating the JSON:{{"intent": "","recommendation_opportunity": }} and nothing else (no free text, no small talk).
                """,
                }
            ]
            + historical_messages
            + [
                {
                    "role": "user",
                    "content": bot_template_helper.get_general_prompt_template(
                        chat_language,
                        incoming_message,
                        tenant_name,
                        f"The response HAS TO BE in JSON format. IF you are unsure, please return {{'intent': '{bot_intent_helper.OTHER}'}}",
                    ),
                },
            ]
        )
        chat_completion_response = client.chat.completions.create(
            model=MODEL_NAME, messages=chat_completion_messages, temperature=0
        )
        bot_openai_helper.openai_api_calculate_cost(
            chat_completion_response.usage, model=MODEL_NAME
        )
        shopify_intent_response_message = chat_completion_response.choices[
            0
        ].message.content
        try:
            shopify_intent_json.update(json.loads(shopify_intent_response_message))
            remaining_attempts = 0
        except Exception as e:
            print(
                f"shopify_intent_response_message is not json, retrying... attempt left: {remaining_attempts} >> {shopify_intent_response_message}"
            )
            chat_completion_messages.append(
                {
                    "role": "system",
                    "content": f"Your response HAS TO BE in JSON format. IF you are unsure, please return {{'intent': 'other'}}",
                }
            )
            continue

        if not any(
            intent_choice in shopify_intent_json["intent"]
            for intent_choice in SHOPIFY_INTENT_LIST
        ):
            chat_completion_messages.append(
                {
                    "role": "system",
                    "content": f"Your response HAS TO BE either one in this list: {SHOPIFY_INTENT_LIST}. IF you are unsure, please return {SHOPIFY_INTENT_LIST[-1]}",
                }
            )
            continue

    return shopify_intent_json


# ^^^^^^^^^^^^ Above all LEGACY CODE ^^^^^^^^^^^^
# -------------For LLM Action-------------------
def get_shopify_update_note_output_feedback(
    tenant_name, historical_messages, incoming_message
):
    messages = (
        [
            {
                "role": "system",
                "content": f"""You are an expert customer service officer of {tenant_name}. You are to update the customer note for the order. Please extract the order name and the order note from the customer's message. Return the answer in JSON format by updating {{"order_number": "","note": ""}} and nothing else (no free text, no small talk).""",
            },
        ]
        + historical_messages
        + [
            {
                "role": "user",
                "content": incoming_message,
            }
        ]
    )

    (
        chat_completion_response,
        _,
    ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(messages)
    order_info = {
        "order_number": "",
        "note": "",
    }
    try:
        order_info.update(
            json.loads(chat_completion_response.choices[0].message.content)
        )
    except Exception as e:
        print(
            f"Error when updating order info: {e} >> {chat_completion_response.choices[0].message.content}"
        )
        pass
    missing_value_list = []
    order_number = order_info["order_number"]
    order_note = order_info["note"]
    if not order_number:
        missing_value_list.append("Order Number")
    if not order_note:
        missing_value_list.append("Order Note")

    if missing_value_list:
        missing_value_list_str = ", ".join(missing_value_list)
        return f"Missing Values: {missing_value_list_str}"
    else:
        order_id = get_shopify_order_id(
            order_number,
        )
        if order_id == -1:
            return f"Unable to find the order with the order number: {order_number}. Please check the order number."
        update_shopify_order_note(
            order_id,
            order_note,
        )
        return f"""Updated the customer note for the order: {order_number}."""


def get_shopify_checkout_url_output_feedback(graphql_query, item_quantity_dict):
    chatbot_no_connection_str = (
        "Chatbot connection to the store is down. Please contact the administrator."
    )
    cannot_find_product_str = "Unable to find the product."
    try:
        shopify_connection = ShopifyConnection.objects.first()
        graphql_url = shopify_connection.site_url + "/admin/api/2023-10/graphql.json"
        headers = {
            "X-Shopify-Access-Token": shopify_connection.api_secret,
            "Content-Type": "application/graphql",
        }
        response = requests.post(graphql_url, headers=headers, data=graphql_query)
        if response.status_code != 200:
            return chatbot_no_connection_str
    except ShopifyConnection.DoesNotExist:
        return chatbot_no_connection_str
    products = response.json()["data"]["products"]["edges"]
    if len(products) == 0:
        return cannot_find_product_str

    # list first variant ids for each product
    return_url = shopify_connection.site_url + "/cart/"
    item_title_list = list(item_quantity_dict.keys())
    for index, product in enumerate(products):
        product_title = product["node"]["title"]
        best_match = process.extractOne(product_title, item_title_list)
        if best_match:
            current_item_title = best_match[0]
        else:
            continue
        variant_id = product["node"]["variants"]["edges"][0]["node"]["id"].split("/")[
            -1
        ]
        quantity = item_quantity_dict[current_item_title]
        return_url += f"{variant_id}:{quantity},"
    return_url = return_url[:-1]
    output = (
        f"Generated the checkout URL: {return_url} for the items: {item_quantity_dict}."
    )

    return output


def get_query_shopify_api_output(
    shopify_query,
):
    try:
        shopify_connection = ShopifyConnection.objects.first()
        graphql_url = shopify_connection.site_url + "/admin/api/2023-10/graphql.json"
        order_url = (
            shopify_connection.site_url
            + "/admin/api/2023-10/orders.json?status=any&limit=50"
        )
        headers = {
            "X-Shopify-Access-Token": shopify_connection.api_secret,
            "Content-Type": "application/graphql",
        }
        response = requests.post(graphql_url, headers=headers, data=shopify_query)
        if response.status_code != 200:
            return (
                "This ChatBot is not connected to the store. Please contact the administrator.",
            )
        if "errors" in response.json():
            print(
                f'Error when running shopify query: {response.json()["errors"][0]["message"]}'
            )
            return "Unable to fetch the product information due to invalid query info. Please try again"
    except ShopifyConnection.DoesNotExist:
        return "This ChatBot is not connected to the store. Please contact the administrator."
    else:  # executed when there's no error
        response_result_str = response.json()
        output = f"Executed the query with result: {response_result_str}"
        try:
            product = response_result_str["data"]["products"]["edges"][0]["node"]
            current_product_price = product["priceRangeV2"]["maxVariantPrice"]["amount"]
            upsell_items = get_upsell_items(
                product["productType"],
                current_product_price,
                shopify_connection.api_secret,
                graphql_url,
            )
            frequently_bought_together_items = get_frequently_bought_together_items(
                product["title"],
                shopify_connection.api_secret,
                order_url,
            )
            output = utility_general_helper.clean_up_prompt(
                f"""
                Executed the query with result: {response_result_str}
                Recommended Items to the customer: {upsell_items};
                Frequently Bought Together Items: {frequently_bought_together_items}
            """
            )
        except Exception as e:
            print(f"Error when trying to get upsell and freq bought tgt item: {e}")
        return output


def get_shopify_product_information(historical_messages, incoming_message):
    shopify_product_information_str = ""
    MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[0]
    remaining_attempts = 3
    chat_completion_messages = historical_messages + [
        {
            "role": "system",
            "content": bot_template_helper.get_shopify_query_prompt_template()
            + "\nDO NOT INCLUDES ANY TEXT OR EXPLAINATION EXCEPT GRAPHQL QUERY IN THE RESPONSE.\nDO NOT USE PRICE IN SORTKEYS IF THE QUERY IS ABOUT PRODUCTS. Your response MUST NOT INCLUDE any free text except for the shopify query.",
        },
        {
            "role": "user",
            "content": incoming_message,
        },
    ]
    while remaining_attempts > 0:
        remaining_attempts -= 1
        try:
            chat_completion_response = client.chat.completions.create(
                model=MODEL_NAME,
                messages=chat_completion_messages,
                temperature=0,
            )
            shopify_query = chat_completion_response.choices[0].message.content
            formatted_shopify_query = shopify_query
            if not shopify_query.startswith("{"):
                formatted_shopify_query = shopify_query.split("```")[1]
                formatted_shopify_query = formatted_shopify_query.strip("graphql")
                formatted_shopify_query = formatted_shopify_query.strip("\n")
            if not formatted_shopify_query.startswith("{"):
                raise Exception("shopify query is not in valid format")
            if not formatted_shopify_query.endswith("}"):
                raise Exception("shopify query is not in valid format")
            shopify_product_information_str = get_query_shopify_api_output(
                formatted_shopify_query
            )
        except Exception as e:
            shopify_product_information_str = (
                "Shopify query failed. Please contact admin or try again later."
            )
    return shopify_product_information_str


def execute_shopify_intent(
    tenant_name, historical_messages, incoming_message, shopify_intent_json
):
    if shopify_intent_json["intent"] == SHOPIFY_UPDATE_NOTE:
        return get_shopify_update_note_output_feedback(
            tenant_name, historical_messages, incoming_message
        )
    elif shopify_intent_json["intent"] == SHOPIFY_CONFIRMATION_NEEDED:
        return "Cannot find the product detail."
    elif shopify_intent_json["intent"] == SHOPIFY_CHECKOUT:
        items = shopify_intent_json["cart_items"]

        query = f"'{items[0]['item_name']}'"
        for item in items[1:]:
            query += f" OR '{item['item_name']}'"

        graphql_query = f"""{{products(first: 10, query: "{query}") {{edges {{node {{id title variants(first: 10) {{edges {{node {{id }}}}}}}}}}}}}}"""

        item_quantity_dict = {}
        for item in items:
            item_quantity_dict[item["item_name"]] = item["quantity"]
        return get_shopify_checkout_url_output_feedback(
            graphql_query, item_quantity_dict
        )
    else:  # product inquiry
        return get_shopify_product_information(historical_messages, incoming_message)
