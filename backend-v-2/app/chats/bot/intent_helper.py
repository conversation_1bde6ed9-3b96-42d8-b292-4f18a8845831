import app.core.authapi.helper as authapi_helper
import app.chats.bot.openai_helper as bot_openai_helper
import app.chats.chat.constant as chat_constant

import app.utility.date_helper as utility_date_helper
import app.utility.general_helper as utility_general_helper

from django.conf import settings

import json
import os

from workers import task

client = bot_openai_helper.get_open_ai_client()

##LEGACY >> Replace via LLM

ORDER_INQUIRY = "order inquiry"
ORDER_CONFIRMATION = "order confirmation"
MAKE_APPOINTMENT = "make appointment"
APPOINTMENT_RELATED = "appointment related quiry"
SHOWROOM_RELATED = "showroom"
DIRECT_CONTACT_REQUEST_FROM_CUSTOMER = (
    "customer is requesting to be contacted personally."
)
CONTACT_INFORMATION_INQUIRY = "customer is asking about your contact information"
COMPLAINT = "complaint"
DELIVERY_SLOT_BOOKING = "delivery slot booking"
CUSTOMER_DELIVERY_INQUIRY = "customer purchased order delivery time inquiries"
GENERAL_DELIVERY_INQUIRY = "general delivery inquiries"
PRODUCT_INQUIRY = "product inquiry"
PAYMENT_RELATED = "payment related"
GENERAL_INQUIRY = "general inquiry"
IRRELEVANT_CHATTER = "irrelevant chatter"
OTHER = "other"

INTENT_LIST = [
    ORDER_INQUIRY,
    ORDER_CONFIRMATION,
    MAKE_APPOINTMENT,
    APPOINTMENT_RELATED,
    SHOWROOM_RELATED,
    DIRECT_CONTACT_REQUEST_FROM_CUSTOMER,
    CONTACT_INFORMATION_INQUIRY,
    COMPLAINT,
    # DELIVERY_SLOT_BOOKING,
    GENERAL_DELIVERY_INQUIRY,
    CUSTOMER_DELIVERY_INQUIRY,
    PRODUCT_INQUIRY,
    # PAYMENT_RELATED,
    GENERAL_INQUIRY,
    IRRELEVANT_CHATTER,  # This will always at the last
]


def get_intent(question, historical_messages, schema_name):
    # try:
    #     last_machine_chat = historical_messages[-1]["content"]

    #     print("last_machine_chat: ", last_machine_chat)
    #     if (
    #         "CLEARANCE SALE" in last_machine_chat
    #         or "I apologize for any inconvenience. Based on the details I have currently"
    #         in last_machine_chat
    #     ):
    #         return MAKE_APPOINTMENT
    # except:
    #     pass
    remaining_attempts = 3
    intent_total_cost = 0
    while remaining_attempts > 0:
        remaining_attempts -= 1

        intent_message = (
            [
                {
                    "role": "system",
                    "content": f"""You are javascript object notation expert from {schema_name}. 
       """,
                }
            ]
            + historical_messages
            + [
                {
                    "role": "user",
                    "content": utility_general_helper.clean_up_prompt(
                        f"""Question:{question}
                    Answer: You are now required to determine the latest intent of the customer within the context of the conversation. Consider the context of the previous messages to get the correct intent. Get only one of the following intents: {','.join(INTENT_LIST)} . Next, base on the conversation and the customer question, if the customer has the potential to be converted, update field is_potential_customer=True. Return the information in JSON by updating the JSON:{{"intent": "","is_potential_customer": }} and nothing else (no free text, no small talk). If the question is general query (e.g. are you halal? How much is the shipping fee?), please return {{"intent":"{GENERAL_INQUIRY}", "is_potential_customer": }} """
                    ),
                },
            ]
        )

        chat_completion_response = client.chat.completions.create(
            model=chat_constant.GPT_4_MODEL_CHOICES[0],
            messages=intent_message,
            temperature=0,
        )
        intent_total_cost += bot_openai_helper.openai_api_calculate_cost(
            chat_completion_response.usage, model=chat_constant.GPT_4_MODEL_CHOICES[0]
        )
        intent_information = (
            chat_completion_response.choices[0].message.content.strip().lower()
        )
        try:
            intent_information = json.loads(intent_information)
        except:
            remaining_attempts = 0
            print(
                f"intent information response is not json, retrying... attempt left: {remaining_attempts}"
            )
            continue

        print(f"intent_information: {str(intent_information)}")
        if any(
            intent_choice in intent_information["intent"]
            for intent_choice in INTENT_LIST
        ):
            return (
                intent_information["intent"],
                intent_information["is_potential_customer"],
                intent_total_cost,
            )
        else:
            intent_message.append(
                {
                    "role": "system",
                    "content": f"Your response HAS TO BE either one in this list: {INTENT_LIST}. IF you are unsure, please return {INTENT_LIST[-1]}",
                }
            )

    if remaining_attempts == 0:
        return INTENT_LIST[-1], False, intent_total_cost


def update_client_instance(client_customer_instance, attribute, value):
    current_value = getattr(client_customer_instance, attribute)

    if not value:
        value = current_value
    elif value != current_value:
        setattr(client_customer_instance, attribute, value)
        client_customer_instance.save()

    return value


@task()
def send_customer_support_email(
    customer_name,
    customer_contact,
    complaint_description,
    customer_email,
    invoice_number,
    conversation_id,
    tenant_name,
    schema_name,
    email_to,
):
    print(f"customer support email_to: {email_to}")
    if not email_to:
        print("No target email_to!")
        return

    # send email
    subject = f"Customer complaint on {tenant_name}"
    html_path = "customer_support_complaint_info.html"
    current_year_str = utility_date_helper.get_current_year_str()
    custom_kwargs = {
        "name": customer_name,
        "contact": customer_contact,
        "email": customer_email,
        "invoice_number": invoice_number,
        "complaint_description": complaint_description,
        "conversation_url": f"{os.environ.get('HTTPS_APP_URL_1')}/org/{schema_name.replace('_', '-')}/chat/{conversation_id}",
        "tenant_name": tenant_name,
        "current_year_str": current_year_str,
    }

    from_ = f"UtterUnicorn X {tenant_name} <{settings.DEFAULT_FROM_EMAIL}>"
    to_ = [email_to]
    print(f"Triggering sending complaint to the service team...")
    authapi_helper.send_user_email_with_html_template(
        html_path, subject, from_, to_, **custom_kwargs
    )
    print(f"Triggered sending complaint to the service team!")


@task()
def send_direct_contact_request_email(
    customer_name,
    customer_contact,
    customer_email,
    context,
    conversation_id,
    tenant_name,
    schema_name,
    email_to,
):
    print(f"direct contact email to: {email_to}")
    if not email_to:
        print("No target email_to!")
        return

    # send email
    subject = f"Customer direct contact request on {tenant_name}"
    html_path = "customer_direct_contact_request_info.html"
    current_year_str = utility_date_helper.get_current_year_str()
    custom_kwargs = {
        "name": customer_name,
        "contact": customer_contact,
        "email": customer_email,
        "context": context,
        "conversation_url": f"{os.environ.get('HTTPS_APP_URL_1')}/org/{schema_name.replace('_', '-')}/chat/{conversation_id}",
        "tenant_name": tenant_name,
        "current_year_str": current_year_str,
    }

    from_ = f"UtterUnicorn X {tenant_name} <{settings.DEFAULT_FROM_EMAIL}>"
    to_ = [email_to]
    print(f"Triggering sending complaint to the service team...")
    authapi_helper.send_user_email_with_html_template(
        html_path, subject, from_, to_, **custom_kwargs
    )
    print(f"Triggered sending complaint to the service team!")
