import app.core.authapi.helper as authapi_helper
import app.chats.bot.models as bot_models


import app.chats.llm_action.helper as llm_action_helper
from tenant_schemas.utils import schema_context

import dotenv

dotenv.load_dotenv()


# persist_directory, #replaced by collection name
# chatbot_extra_source_name="",
# is_check_hallucination=True,
#### Version 3
def get_chatbot_response(
    incoming_message,
    historical_messages,
    collection_name,
    bot_setting_instance: bot_models.BotSetting,
    schema_name,
    chat_language,
    conversation_id,
    is_source=True,
):
    with schema_context(schema_name):
        tenant_name = authapi_helper.get_tenant_name(schema_name)
        llm_action_instance = llm_action_helper.LLMAction(
            bot_setting_instance,
            collection_name,
            chat_language,
            conversation_id,
            is_source,
            schema_name,
            tenant_name,
            incoming_message,
            historical_messages,
        )
        chatbot_reply = llm_action_instance.execute()
    return chatbot_reply


"""
intent_answer, is_potential_customer, intent_total_cost = (
        bot_intent_helper.get_intent(question, historical_messages, schema_name)
    )
handle_question_intent_instance = bot_intent_handler.HandleQuestionIntentClass(
    collection_name,
    schema_name,
    question,
    bot_setting_instance,
    chat_language,
    historical_messages,
    intent_answer,
    is_potential_customer,
    conversation_id,
    is_live, #legacy
    delivery_csv_file_path,
    chatbot_extra_source_name,
    intent_total_cost,
    is_check_hallucination,
)
# check if shopify connection exist
is_shopify_connection = False
with schema_context(handle_question_intent_instance.schema_name):
    shopify_connection = ShopifyConnection.objects.first()
    if shopify_connection and shopify_connection.status == utility_models.ACTIVE:
        is_shopify_connection = True

# print(
#     "schema_name: ",
#     handle_question_intent_instance.schema_name,
#     "intent_answer: ",
#     intent_answer,
# )

if (
    intent_answer == bot_intent_helper.ORDER_INQUIRY
    or intent_answer == bot_intent_helper.PRODUCT_INQUIRY
    or intent_answer == bot_intent_helper.ORDER_CONFIRMATION
) and is_shopify_connection:
    print(
        f"Handling {bot_intent_helper.ORDER_INQUIRY} + { bot_intent_helper.PRODUCT_INQUIRY} with shopify..."
    )
    handle_question_intent_instance.handle_shopify_inquiry()
elif intent_answer == bot_intent_helper.CUSTOMER_DELIVERY_INQUIRY:
    print(f"Handling {bot_intent_helper.CUSTOMER_DELIVERY_INQUIRY}...")
    handle_question_intent_instance.handle_delivery_inquiry()  # order include delivery now
elif (
    (
        intent_answer == bot_intent_helper.MAKE_APPOINTMENT
        or intent_answer == bot_intent_helper.SHOWROOM_RELATED
        or intent_answer == bot_intent_helper.APPOINTMENT_RELATED
    )
    and handle_question_intent_instance.appointment_chat_feature_setting.is_usable()
):
    print(f"Handling {bot_intent_helper.MAKE_APPOINTMENT}...")
    handle_question_intent_instance.handle_make_appointment()  # if ask about sales, guide user to make appointment for more info
elif (
    intent_answer == bot_intent_helper.COMPLAINT
    and handle_question_intent_instance.customer_support_chat_feature_setting.is_usable()
):
    print(f"Handling {bot_intent_helper.COMPLAINT}...")
    handle_question_intent_instance.handle_complaint()  # if ask about sales, guide user to make appointment for more info

elif (
    intent_answer == bot_intent_helper.DIRECT_CONTACT_REQUEST_FROM_CUSTOMER
    and handle_question_intent_instance.callback_chat_feature_setting.is_usable()
):
    print(f"Handling {bot_intent_helper.DIRECT_CONTACT_REQUEST_FROM_CUSTOMER}...")
    handle_question_intent_instance.handle_direct_request_from_customer()

else:
    print(f"Handling {bot_intent_helper.GENERAL_INQUIRY}...")
    handle_question_intent_instance.handle_general_inquiry()

print(
    f"Total Cost for {intent_answer}: ${handle_question_intent_instance.cost:.4f}"
)

sources = "\n".join(handle_question_intent_instance.sources)
if is_source:
    chatbot_answer = (
        f"{handle_question_intent_instance.chatbot_answer} \nSOURCES: {sources}"
    )
else:
    chatbot_answer = f"{handle_question_intent_instance.chatbot_answer}"

chatbot_answer_str = handle_question_intent_instance.chatbot_answer.replace(
    "\n", ";"
)
sources_str = sources.replace("\n", ";")

print(f"Chatbot answer: {chatbot_answer_str}, Sources:{sources_str}")
"""
