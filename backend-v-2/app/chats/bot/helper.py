import re
from django.conf import settings
from tenant_schemas.utils import schema_context

import app.core.authapi.helper as authapi_helper
import app.chats.bot.models as bot_models
import app.documents.document.models as document_models
import app.utility.models as utility_models


def initialize_bot_setting(schema_name, verbose=False):
    with schema_context(schema_name):
        for bot_setting_type in utility_models.BOT_SETTING_TYPE:
            (
                _,
                is_bot_instance_created,
            ) = bot_models.BotSetting.objects.get_or_create(
                bot_type=bot_setting_type[0]
            )
            if verbose:
                print(
                    f"{schema_name} -> {bot_setting_type[0]} bot is created: {is_bot_instance_created}!"
                )


# Define a function that extracts the page number from a string
def get_page_number(s):
    match = re.search(r"#page=(\d+)", s)
    if match:
        return int(match.group(1))
    else:
        return 0  # or some other default value


def get_sources(file_name_page_list):
    """
    file_name_page = customer_policy_Te07OL1.pdf#page=1;;1
    turn file_name_page_list into list of :
    {
        "name": string
        "url": string,
        "pdf_pages": number[],
        "display_pages": number[],
    }
    """
    source_dict = {}
    schema_name = authapi_helper.get_schema_name()
    for file_name_page in file_name_page_list:
        parts = file_name_page.split("#page=")
        url_file_name = parts[0]
        # print("parts: ", parts)
        # parts[1] = pdf file page;;display page
        # if len(parts) > 1:
        # print("parts1: ", parts[1])

        page_num_raw = parts[1].replace(",", "") if len(parts) > 1 else None
        pdf_page = ""
        display_page = ""
        if page_num_raw:
            splitted_page_num_raw = page_num_raw.split(";;")
            pdf_page = splitted_page_num_raw[0]
            if len(splitted_page_num_raw) > 1:
                display_page = splitted_page_num_raw[1]

        # print("pdf_page: ", pdf_page)
        # print("display_page: ", display_page)
        # page_num = (
        #     [int(part) for part in parts[1].split(",")] if len(parts) > 1 else None
        # )
        # print("url_file_name:", url_file_name)
        if url_file_name not in source_dict:
            with schema_context(schema_name):
                document = document_models.Document.objects.filter(
                    file__icontains=url_file_name
                ).first()
                # print("document: ", document)
                if document:
                    if document.is_external:
                        is_source_muted = document.is_source_muted_external
                    else:
                        is_source_muted = document.is_source_muted_internal

                    if is_source_muted:
                        continue

                    source_dict[url_file_name] = {
                        "name": document.name,  # if needed, change to external_name
                        "url": (document.file.url if not is_source_muted else ""),
                        "pdf_pages": [],
                        "display_pages": [],
                    }
                else:
                    continue

        # print("source_dict: ", source_dict)

        # add in pages
        if pdf_page and pdf_page not in source_dict[url_file_name]["pdf_pages"]:
            source_dict[url_file_name]["pdf_pages"].append(pdf_page)
            if display_page:
                source_dict[url_file_name]["display_pages"].append(display_page)

    return list(source_dict.values())


def extract_file_name_and_page(file_urls):
    """
    Take in file_urls and capture the group of name with page number
    """
    raw_source_list = []
    for file_url in file_urls:
        name_page_pattern = "pattern that doesn't exist here"
        if ".pdf" in str(file_url):
            # to get pdf name + pages
            name_page_pattern = f"https://{settings.AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/\w+/(\w+\_+.+\.pdf#page=\w+(;;\w+)?)"
            # https://s3-utter-unicorn-dev.s3.amazonaws.com/demo_furnishing/customer_policy_cZ0sIAX.pdf#page=9;;9
        if str(file_url).endswith(".txt"):
            # to get txt name
            name_page_pattern = f"https://{settings.AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/\w+/(\w+\_+.+\.txt)"

        if pattern_found := re.search(name_page_pattern, file_url):
            raw_source_list.append(pattern_found.group(1))
        else:
            raw_source_list.append(file_url)

    return raw_source_list


def extract_chat_history_content(historical_messages):
    """
    extract only previous 3 user questions?

    """
    text = ""
    for historical_message in historical_messages:
        if historical_message.get("response") == "user":
            text += historical_message.get("content", "")

    return text
