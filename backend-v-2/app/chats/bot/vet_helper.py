def get_pet_diagnostic_info(
    customer_contact_number,
    pet_diagnostic_id,
):
    import app.chats.llm_action.execute_helper as llm_action_execute_helper
    import app.chats.llm_action.constant as llm_action_constant
    import app.integrations.integration.helper as integration_helper
    import app.utility.general_helper as utility_general_helper

    missing_value_list = []
    if not customer_contact_number:
        missing_value_list.append("Customer Contact Number")
    if not pet_diagnostic_id:
        missing_value_list.append("Pet Diagnostic Id")
    if missing_value_list:
        llm_action_execute_helper.raise_value_error(missing_value_list)

    query = f"""
        SELECT 
            vc."Name",
            vp."Name",
            vpd."Diagnosis",
            vpd."Medication",
            vpd."Dosage",
            vpd."Date"
        FROM 
            vet_pet_diagnostic vpd
        LEFT JOIN 
            vet_customer vc 
            ON vc."CustomerID" = vpd."CustomerID"
        LEFT JOIN 
            vet_pet vp
            ON vp."PetID" = vpd."PetID"
        WHERE 
            vc."Phone" = '{customer_contact_number}' AND
            vpd."DiagnosticID" = '{pet_diagnostic_id}';

    """
    db_data = integration_helper.execute_get_query(
        query,
        "db_name",
        "username",
        "password",
        "host",
        "port",
    )

    db_data_str = f"No Pet Information found, associated with {customer_contact_number} with pet diagostic id: {pet_diagnostic_id}"

    if db_data:
        db_data_str = utility_general_helper.clean_up_prompt(
            f"""
            Customer Name: {db_data[0]}
            Pet Name: {db_data[1]}
            Diagnosis Result: {db_data[2]}
            Medication: {db_data[3]}
            Dosage: {db_data[4]}
            Date of Diagnosis: {db_data[5]}
        """
        )

    return {"status": llm_action_constant.ACTION_COMPLETED, "output": db_data_str}
