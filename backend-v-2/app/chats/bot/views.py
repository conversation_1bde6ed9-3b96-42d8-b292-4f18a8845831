from rest_framework import generics, status
from rest_framework.permissions import AllowAny, IsAuthenticated
import app.chats.bot.models as bot_models
import app.chats.bot.serializers as bot_serializers
import app.core.authapi.permission as authapi_permission
from rest_framework.views import APIView
import app.utility.models as utility_models
from rest_framework.response import Response


# Create your views here.
class BotSettingView:
    class BotSettingBaseView(generics.GenericAPIView):
        queryset = bot_models.BotSetting.objects.all()
        serializer_class = bot_serializers.BotSettingSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(BotSettingBaseView, generics.ListAPIView):
        pass

    class Get(BotSettingBaseView, generics.RetrieveAPIView):
        pass

    class CustomGet(BotSettingBaseView, APIView):
        permission_classes = [AllowAny]

        def get(self, request, *args, **kwargs):
            bot_type = self.request.query_params.get(
                "bot_type", utility_models.EXTERNAL_TEXT
            )
            bot_setting_instance = bot_models.BotSetting.objects.filter(
                bot_type=bot_type
            ).first()
            if bot_setting_instance:
                return Response(
                    data=self.serializer_class(bot_setting_instance).data,
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    data="No bot setting found", status=status.HTTP_404_NOT_FOUND
                )

    # class Create(BotSettingBaseView, generics.CreateAPIView):
    #     serializer_class = bot_serializers.BotSettingSerializer.Post

    #     def post(self, request, *args, **kwargs):
    #         # request.data._mutable = True
    #         request.data["created_by"] = self.request.user.id
    #         # request.data._mutable = False

    #         res = super().post(request, *args, **kwargs)
    #         return res

    class Update(BotSettingBaseView, generics.UpdateAPIView):
        serializer_class = bot_serializers.BotSettingSerializer.Post

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                print(data)
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)

    # class Delete(BotSettingBaseView, generics.DestroyAPIView):
    #     def delete(self, request, *args, **kwargs):
    #         return super().delete(request, *args, **kwargs)
