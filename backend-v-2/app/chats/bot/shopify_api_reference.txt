Product Query

    after: String
    before: String
    first: Int
    last: Int

    query: String
    Supported filter parameters: 
        barcode
        bundles
        created_at
        delivery_profile_id
        error_feedback
        gift_card
        has_only_composites
        has_only_default_variant
        has_variant_with_components
        id
        inventory_total
        is_price_reduced
        out_of_stock_somewhere
        price
        product_configuration_owner
        product_publication_status
        product_type
        publishable_status
        published_status
        sku
        status
        tag
        tag_not
        title
        updated_at
        vendor
    reverse: Boolean = false
    savedSearchId: ID

    sortKey: ProductSortKeys = ID
    Supported ProductSortKeys:
        TITLE
        PRODUCT_TYPE
        VENDOR
        INVENTORY_TOTAL
        UPDATED_AT
        CREATED_AT
        PUBLISHED_AT
        ID

RELEVANCE
===========================

Product Nodes

    availablePublicationCount: Int!

    collections(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: CollectionSortKeys = ID
    query: String
    ): CollectionConnection!

    compareAtPriceRange: ProductCompareAtPriceRange
    contextualPricing(context: ContextualPricingContext!): ProductContextualPricing!
    createdAt: DateTime!
    defaultCursor: String!
    description(truncateAt: Int): String!
    descriptionHtml: HTML!
    featuredImage: Image
    featuredMedia: Media
    feedback: ResourceFeedback
    giftCardTemplateSuffix: String
    handle: String!
    hasOnlyDefaultVariant: Boolean!
    hasOutOfStockVariants: Boolean!
    hasVariantsThatRequiresComponents: Boolean!
    id: ID!

    images(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: ProductImageSortKeys = POSITION
    ): ImageConnection!

    inCollection(id: ID!): Boolean!
    isGiftCard: Boolean!
    legacyResourceId: UnsignedInt64!

    media(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: ProductMediaSortKeys = POSITION
    ): MediaConnection!

    mediaCount: Int!
    metafield(namespace: Stringkey: String!): Metafield

    metafieldDefinitions(
    namespace: String
    pinnedStatus: MetafieldDefinitionPinnedStatus = ANY
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: MetafieldDefinitionSortKeys = ID
    query: String
    ): MetafieldDefinitionConnection!

    metafields(
    namespace: String
    keys: [String!]
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): MetafieldConnection!

    onlineStorePreviewUrl: URL
    onlineStoreUrl: URL
    options(first: Int): [ProductOption!]!
    priceRangeV2: ProductPriceRangeV2!
    productCategory: ProductCategory
    productType: String!
    publicationCount(onlyPublished: Boolean = true): Int!
    publishedAt: DateTime
    publishedInContext(context: ContextualPublicationContext!): Boolean!
    publishedOnCurrentPublication: Boolean!
    publishedOnPublication(publicationId: ID!): Boolean!
    requiresSellingPlan: Boolean!
    resourcePublicationOnCurrentPublication: ResourcePublicationV2
    resourcePublications(

    onlyPublished: Boolean = true
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): ResourcePublicationConnection!

    resourcePublicationsV2(
    onlyPublished: Boolean = true
    catalogType: CatalogType
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): ResourcePublicationV2Connection!

    sellingPlanGroupCount: Int!

    sellingPlanGroups(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): SellingPlanGroupConnection!

    seo: SEO!
    status: ProductStatus!
    tags: [String!]!
    templateSuffix: String
    title: String!
    totalInventory: Int!
    totalVariants: Int!
    tracksInventory: Boolean!
    translations(locale: String!marketId: ID): [Translation!]!

    unpublishedPublications(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): PublicationConnection!

    updatedAt: DateTime!

    variants(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: ProductVariantSortKeys = POSITION
    ): ProductVariantConnection!

    vendor: String!

===========================
===========================

Order Query
    after: String
    before: String
    first: Int
    last: Int

    query: String
    Supported filter parameters:
        cart_token
        channel
        channel_id
        chargeback_status
        checkout_token
        confirmation_number
        created_at
        credit_card_last4
        customer_id
        delivery_method
        discount_code
        earliest_fulfill_by
        email
        financial_status
        fraud_protection_level
        fulfillment_location_id
        fulfillment_status
        gateway
        location_id
        name
        payment_id
        payment_provider_id
        po_number
        processed_at
        reference_location_id
        return_status
        risk_level
        sales_channel
        sku
        source_identifier
        source_name
        status
        tag
        tag_not
        test
        updated_at

    reverse: Boolean = false
    savedSearchId: ID
    sortKey: OrderSortKeys = PROCESSED_AT
    Supported OrderSortKeys:
        CREATED_AT
        CUSTOMER_NAME
        DESTINATION
        FINANCIAL_STATUS
        FULFILLMENT_STATUS
        ORDER_NUMBER
        PROCESSED_AT
        TOTAL_ITEMS_QUANTITY
        TOTAL_PRICE
        UPDATED_AT
        PO_NUMBER
        ID
        RELEVANCE


===========================

Order Nodes
    additionalFees: [AdditionalFee!]!

    agreements(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    query: String
    ): SalesAgreementConnection!

    alerts: [ResourceAlert!]!
    app: OrderApp
    billingAddress: MailingAddress
    billingAddressMatchesShippingAddress: Boolean!
    canMarkAsPaid: Boolean!
    canNotifyCustomer: Boolean!
    cancelReason: OrderCancelReason
    cancellation: OrderCancellation
    cancelledAt: DateTime
    capturable: Boolean!
    cartDiscountAmountSet: MoneyBag
    channelInformation: ChannelInformation
    clientIp: String
    closed: Boolean!
    closedAt: DateTime
    confirmationNumber: String
    confirmed: Boolean!
    createdAt: DateTime!
    currencyCode: CurrencyCode!
    currentCartDiscountAmountSet: MoneyBag!
    currentSubtotalLineItemsQuantity: Int!
    currentSubtotalPriceSet: MoneyBag!
    currentTaxLines: [TaxLine!]!
    currentTotalAdditionalFeesSet: MoneyBag
    currentTotalDiscountsSet: MoneyBag!
    currentTotalDutiesSet: MoneyBag
    currentTotalPriceSet: MoneyBag!
    currentTotalTaxSet: MoneyBag!
    currentTotalWeight: UnsignedInt64!
    customAttributes: [Attribute!]!
    customer: Customer
    customerAcceptsMarketing: Boolean!
    customerJourneySummary: CustomerJourneySummary
    customerLocale: String

    discountApplications(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): DiscountApplicationConnection!

    discountCode: String
    discountCodes: [String!]!
    displayAddress: MailingAddress
    displayFinancialStatus: OrderDisplayFinancialStatus
    displayFulfillmentStatus: OrderDisplayFulfillmentStatus!
    disputes: [OrderDisputeSummary!]!
    edited: Boolean!
    email: String
    estimatedTaxes: Boolean!

    events(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: EventSortKeys = ID
    query: String
    ): EventConnection!

    exchangeV2s(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    query: String
    ): ExchangeV2Connection!

    fulfillable: Boolean!

    fulfillmentOrders(
    displayable: Boolean = false
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    query: String
    ): FulfillmentOrderConnection!

    fulfillments(first: Int): [Fulfillment!]!
    fullyPaid: Boolean!
    hasTimelineComment: Boolean!
    id: ID!
    legacyResourceId: UnsignedInt64!

    lineItems(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): LineItemConnection!

    localizationExtensions(
    countryCodes: [CountryCode!]
    purposes: [LocalizationExtensionPurpose!]
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): LocalizationExtensionConnection!

    merchantEditable: Boolean!
    merchantEditableErrors: [String!]!
    merchantOfRecordApp: OrderApp
    metafield(namespace: Stringkey: String!): Metafield

    metafieldDefinitions(
    namespace: String
    pinnedStatus: MetafieldDefinitionPinnedStatus = ANY
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    sortKey: MetafieldDefinitionSortKeys = ID
    query: String
    ): MetafieldDefinitionConnection!

    metafields(
    namespace: String
    keys: [String!]
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): MetafieldConnection!

    name: String!
    netPaymentSet: MoneyBag!

    nonFulfillableLineItems(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): LineItemConnection!

    note: String
    originalTotalAdditionalFeesSet: MoneyBag
    originalTotalDutiesSet: MoneyBag
    originalTotalPriceSet: MoneyBag!
    paymentCollectionDetails: OrderPaymentCollectionDetails!
    paymentGatewayNames: [String!]!
    paymentTerms: PaymentTerms
    phone: String
    physicalLocation: Location
    poNumber: String
    presentmentCurrencyCode: CurrencyCode!
    processedAt: DateTime!
    publication: Publication
    purchasingEntity: PurchasingEntity
    refundDiscrepancySet: MoneyBag!
    refundable: Boolean!
    refunds(first: Int): [Refund!]!
    registeredSourceUrl: URL
    requiresShipping: Boolean!
    restockable: Boolean!
    returnStatus: OrderReturnStatus!

    returns(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    query: String
    ): ReturnConnection!

    riskLevel: OrderRiskLevel!
    risks(first: Int): [OrderRisk!]!
    shippingAddress: MailingAddress
    shippingLine: ShippingLine

    shippingLines(
    first: Int
    after: String
    last: Int
    before: String
    reverse: Boolean = false
    ): ShippingLineConnection!

    shopifyProtect: ShopifyProtectOrderSummary
    sourceIdentifier: String
    subtotalLineItemsQuantity: Int!
    subtotalPriceSet: MoneyBag

    suggestedRefund(
    shippingAmount: Money
    refundShipping: Boolean
    refundLineItems: [RefundLineItemInput!]
    refundDuties: [RefundDutyInput!]
    suggestFullRefund: Boolean = false
    ): SuggestedRefund

    tags: [String!]!
    taxExempt: Boolean!
    taxLines: [TaxLine!]!
    taxesIncluded: Boolean!
    test: Boolean!
    totalCapturableSet: MoneyBag!
    totalDiscountsSet: MoneyBag
    totalOutstandingSet: MoneyBag!
    totalPriceSet: MoneyBag!
    totalReceivedSet: MoneyBag!
    totalRefundedSet: MoneyBag!
    totalRefundedShippingSet: MoneyBag!
    totalShippingPriceSet: MoneyBag!
    totalTaxSet: MoneyBag
    totalTipReceivedSet: MoneyBag!
    totalWeight: UnsignedInt64

    transactions(
    first: Int
    capturable: Boolean
    manuallyResolvable: Boolean
    ): [OrderTransaction!]!

    unpaid: Boolean!
    updatedAt: DateTime!