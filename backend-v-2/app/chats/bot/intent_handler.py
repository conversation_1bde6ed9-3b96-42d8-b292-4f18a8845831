# from langchain.embeddings.openai import OpenAIEmbeddings

# import json
# import time
# from datetime import datetime, timedelta
# import os

# from tenant_schemas.utils import schema_context

# import authapi.helper as authapi_helper

# import bot.openai_helper as bot_openai_helper
# import bot.shopify_helper as bot_shopify_helper
# import bot.chatbot_helper as bot_chatbot_helper

# import bot.helper as bot_helper
# import bot.template_helper as bot_template_helper
# import bot.intent_helper as bot_intent_helper
# import bot.models as bot_models

# import chat.models as chat_models
# import chat.constant as chat_constant

# import chat_action.models as chat_action_models

# import chat_action_tracker.models as chat_action_tracker_models
# import chat_action_tracker.helper as chat_action_tracker_helper

# from integration.models import DatabaseConnection
# from integration import helper as db_helper

# import marketing.models as marketing_models
# import nova_related.helper as nova_related_helper

# import utility.date_helper as utility_date_helper
# import utility.models as utility_models
# import utility.general_helper as utility_general_helper
# import utility.chroma_db_helper as utility_chroma_db_helper

# from django.utils import timezone
# from django.conf import settings

# import math

# client = bot_openai_helper.get_open_ai_client()

"""
Flow:
1. Customer ask order details > Bot reply with the user order detail through contact_number > Which order you want to purchase (handle_order_inquiry)
2. Customer confirm the order > Bot ask user with the order invoice numbers that he/she wants to confirm by providing order invoices id > bot take in and checks > if exist, ask user whether they want to book delivery slot > else, ask user to reinput again (handle_order_confirmation)
3. If user ask for delivery slot, list out to them, and ask user to pick. if chosen, say will send out email. (handle_get_delivery_slot)
4. When user wanna check for delivery status -> get delivery inquiry (handle_delivery_inquiry)

"""

"""
label = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
host = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
port = models.IntegerField()
username = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
password = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
db_name = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
db_type = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
multi_select = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
status = models.CharField(
    max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
    choices=utility_models.CONNECTION_STATUS_CHOICES,
    default=utility_models.ACTIVE,
)

"""


# LEGACY, REPLACE WITH LLM ACTION
# class HandleQuestionIntentClass:

#     def __init__(
#         self,
#         collection_name,  # replace "persist_directory"
#         schema_name,
#         question,
#         bot_setting_instance: bot_models.BotSetting,
#         chat_language,
#         historical_messages,
#         intent_answer,
#         is_potential_customer,
#         conversation_id,
#         delivery_csv_file_path,
#         chatbot_extra_source_name,
#         intent_total_cost,
#         is_check_hallucination,
#     ):
#         with schema_context(schema_name):
#             timezone_now = timezone.now()

#             n_minutes_ago = timezone_now - timedelta(minutes=5)
#             appointment_instance = (
#                 chat_action_tracker_models.Appointment.objects.filter(
#                     conversation__id=conversation_id,
#                     created_at__gte=n_minutes_ago,
#                 ).first()
#             )
#             customer_support_instance = (
#                 chat_action_tracker_models.CustomerSupport.objects.filter(
#                     conversation__id=conversation_id,
#                     created_at__gte=n_minutes_ago,
#                 ).first()
#             )
#             callback_request_instance = (
#                 chat_action_tracker_models.CallListAction.objects.filter(
#                     conversation__id=conversation_id,
#                     created_at__gte=n_minutes_ago,
#                 ).first()
#             )
#             conversation_instance = chat_models.Conversation.objects.get(
#                 id=conversation_id
#             )
#             (
#                 client_customer_instance,
#                 _,
#             ) = marketing_models.ClientCustomer.objects.get_or_create(
#                 conversation=conversation_instance
#             )

#             today = (
#                 utility_date_helper.convert_to_yyyymmddThhmmZ(timezone_now)
#                 + f" ({utility_date_helper.get_day_of_week(timezone_now)})"
#                 + f" (We are in Timezone: {settings.TIME_ZONE})"
#             )
#             self.today = today
#             self.collection_name = collection_name
#             self.conversation_id = conversation_id
#             self.schema_name = schema_name
#             self.tenant_name = authapi_helper.get_tenant_name(schema_name)
#             self.question = question
#             self.bot_setting_instance = bot_setting_instance
#             self.chatbot_name_desc = (
#                 f"Your name is {self.bot_setting_instance.chatbot_name or 'Celestia'}."
#             )
#             self.intro = f"{self.chatbot_name_desc} You are an expert and friendly virtual AI customer service assistant of {self.tenant_name}"
#             self.chat_language = (
#                 bot_setting_instance.english_type
#                 if chat_language == utility_models.ENGLISH_US
#                 else chat_language
#             )
#             self.historical_messages = historical_messages
#             self.intent_answer = intent_answer
#             self.is_potential_customer = is_potential_customer
#             self.is_appointment_set = True if appointment_instance else False
#             self.is_customer_complaint_sent = (
#                 True if customer_support_instance else False
#             )
#             self.is_callback_request_made = True if callback_request_instance else False
#             self.client_customer_instance = client_customer_instance
#             self.conversation_instance = conversation_instance
#             self.chatbot_answer = ""
#             self.context = ""
#             self.sources = []
#             self.intent = ""
#             # self.is_novafurnishing = schema_name == "novafurnishing"
#             self.appointment_chat_feature_setting = (
#                 chat_action_models.AppointmentChatFeatureSetting.objects.first()
#             )
#             self.customer_support_chat_feature_setting = (
#                 chat_action_models.CustomerSupportChatFeatureSetting.objects.first()
#             )
#             self.callback_chat_feature_setting = (
#                 chat_action_models.CallbackChatFeatureSetting.objects.first()
#             )
#             self.missing_info_feature_setting = (
#                 chat_action_models.MissingInfoChatFeatureSetting.objects.first()
#             )
#             self.faq_feature_setting = (
#                 chat_action_models.FAQFeatureSetting.objects.first()
#             )
#             self.appointment_chat_feature_task_template = (
#                 f"""-Identify the type of user. If it's a potential customer, always steer conversation to invite potential buyers to come down to one of the {self.appointment_chat_feature_setting.location_type} today or the next few days, by asking them whether they are available to join any {self.appointment_chat_feature_setting.location_type} ({self.appointment_chat_feature_setting.describe()}). DO NOT FORCE THEM TO JOIN."""
#                 if self.appointment_chat_feature_setting.is_usable()
#                 else ""
#             )
#             self.customer_support_chat_feature_task_template = (
#                 f"""-If it is an existing customer who needs support, reassure customer that our team will reach them as soon as possible to assist them and ask for contact number, problem, email(optional), invoice number (optional) from there."""
#                 if self.customer_support_chat_feature_setting.is_usable()
#                 else ""
#             )
#             # self.callback_chat_feature_task_template = (
#             #     f"""If the customer asks about topics here: {self.callback_chat_feature_setting.topics}, that require a direct contact with the customer, provide our contact detail if available, and ask them to provide their contact number and email if they wish to get contacted by us."""
#             #     if self.callback_chat_feature_setting.is_usable()
#             #     and self.callback_chat_feature_setting.topics
#             #     else ""
#             # )
#             self.faq_feature_task_template = (
#                 f"""Here's the frequently asked question along with the answers for your reference:\n{self.faq_feature_setting.get_question_answer_pair_list()}"""
#                 if self.faq_feature_setting.is_question_answer_pair_exist()
#                 else ""
#             )
#             self.simplied_reply_prompt = "Reply using the first-person perspective. Minimize the use of thank you and apologies in responses to customers, focusing instead on directly addressing their inquiries or requests."

#             self.delivery_csv_file_path = delivery_csv_file_path
#             self.chatbot_extra_source_name = chatbot_extra_source_name
#             self.cost = intent_total_cost
#             self.is_check_hallucination = is_check_hallucination
#             # Get all DatabaseConnection objects
#             db_objs_list = list(DatabaseConnection.objects.all())

#             # Create and potentially save the new instance
#             own_db = DatabaseConnection(
#                 label="own-db",
#                 host=os.environ.get("POSTGRES_HOST"),
#                 port="5432",
#                 username=os.environ.get("POSTGRES_USER"),
#                 password=os.environ.get("POSTGRES_PASSWORD"),
#                 db_name=os.environ.get("POSTGRES_NAME", "postgres"),
#                 db_type="PostgreSQL",
#                 status="Active",
#             )

#             db_objs_list.append(own_db)

#             self.db_objs = db_objs_list

#     def get_or_create_client_customer(self):
#         """
#         it will try to extract name, contact number and email from the conversations, and update accordingly.
#         return contact
#         """
#         with schema_context(self.schema_name):
#             extract_client_customer_info_response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=[
#                     {"role": "user", "content": self.question},
#                     {
#                         "role": "system",
#                         "content": """Extract the latest name(if available from the customer, if not return ""), email (if available from the customer, if not return "") and contact number (if available from the customer, if not return "") from the conversation above and return the information in JSON by updating the JSON {"name": "", "email": "", "contact_number": ""} and nothing else (no free text, no small talk).""",
#                     },
#                 ],  # no need formatated chat history, as email and contact always comes from the user
#                 temperature=0,
#                 stop="}",
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 extract_client_customer_info_response.usage,
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#             )
#             json_response = (
#                 extract_client_customer_info_response.choices[0].message.content + "}"
#             )  # add back the closing bracket used as stop-sequence
#             # print(f"{json_response=}")
#             client_customer_details = {"name": "", "email": "", "contact_number": ""}
#             try:
#                 client_customer_details.update(json.loads(json_response))
#             except:
#                 pass
#             client_customer_details["contact_number"] = client_customer_details[
#                 "contact_number"
#             ].replace(" ", "")
#             is_save = False
#             if (
#                 client_customer_details["name"]
#                 and self.client_customer_instance.name
#                 != client_customer_details["name"]
#             ):
#                 self.client_customer_instance.name = client_customer_details["name"]
#                 is_save = True
#             if (
#                 client_customer_details["email"]
#                 and self.client_customer_instance.email
#                 != client_customer_details["email"]
#             ):
#                 self.client_customer_instance.email = client_customer_details["email"]
#                 is_save = True
#             if (
#                 client_customer_details["contact_number"]
#                 and self.client_customer_instance.contact
#                 != client_customer_details["contact_number"]
#             ):
#                 self.client_customer_instance.contact = client_customer_details[
#                     "contact_number"
#                 ]
#                 is_save = True
#             if is_save:
#                 self.client_customer_instance.save()
#             return self.client_customer_instance.contact

#     def handle_delivery_inquiry(self):
#         """
#         If enquiry is from Nova, then ask for contact number, then use the csv file instead.
#         """
#         self.get_or_create_client_customer()
#         if self.delivery_csv_file_path:  # if there's file to refer:
#             # try and capture contact:
#             print("delivery csv path that is referenced: ", self.delivery_csv_file_path)
#             get_customer_delivery_info_response = client.chat.completions.create(
#                 model=chat_constant.GPT_4_MODEL_CHOICES[0],
#                 messages=self.historical_messages
#                 + [
#                     {"role": "user", "content": self.question},
#                     {
#                         "role": "system",
#                         "content": """Extract the contact number (if available from the customer, if not return "") and the shipping address postal code (if available from the customer, if not return "") from the conversation above and return the information in JSON by updating the JSON {"contact_number": "", "shipping_address_postal_code":""} and nothing else (no free text, no small talk).""",
#                     },
#                 ],
#                 temperature=0,
#                 stop="}",
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 get_customer_delivery_info_response.usage,
#                 model=chat_constant.GPT_4_MODEL_CHOICES[0],
#             )

#             json_response = (
#                 get_customer_delivery_info_response.choices[0].message.content + "}"
#             )
#             delivery_identify_verify_details = {
#                 "contact_number": "",
#                 "shipping_address_postal_code": "",
#             }
#             try:
#                 delivery_identify_verify_details.update(json.loads(json_response))
#             except:
#                 pass
#             print(f"UPDATED: {delivery_identify_verify_details}")
#             if not delivery_identify_verify_details["contact_number"]:
#                 delivery_identify_verify_details["contact_number"] = (
#                     self.client_customer_instance.contact
#                 )

#             # IF ABLE TO CAPTURE ALL REQUIRED INFO
#             if (
#                 nova_related_helper.normalize_contact(
#                     delivery_identify_verify_details["contact_number"]
#                 )
#                 and delivery_identify_verify_details["shipping_address_postal_code"]
#             ):
#                 delivery_info_description = (
#                     nova_related_helper.get_nova_delivery_info_description(
#                         delivery_identify_verify_details["contact_number"],
#                         delivery_identify_verify_details[
#                             "shipping_address_postal_code"
#                         ],
#                         self.delivery_csv_file_path,
#                     )
#                 )
#                 print(f"{delivery_info_description=}")
#                 # IF ABLE TO FETCH DELIVERY INFORMATION
#                 if len(delivery_info_description) > 0:
#                     delivery_response_messages = [
#                         {
#                             "role": "system",
#                             "content": f"""The datetime now is {self.today}. {self.intro}. You have only 1 task, that is to inform the delivery information to the customer. For any other inquiries, please ask the customer to leave their name and contact number instead and we will reach out to them personally as soon as possible. Always reply in a casual and friendly way.""",
#                         },
#                     ] + [
#                         {
#                             "role": "user",
#                             "content": bot_template_helper.get_general_prompt_template(
#                                 self.chat_language,
#                                 self.question,
#                                 self.tenant_name,
#                                 extra_instruction=f"""You are asked by the customer about the delivery information on contact number {delivery_identify_verify_details['contact_number']} and shipping address postal code {delivery_identify_verify_details['shipping_address_postal_code']}, and these are the information that you found:{delivery_info_description}. Answer the customer's question in a way that is concise and easy to read, without losing any information.""",
#                             ),
#                         },
#                     ]
#                     (
#                         delivery_response,
#                         delivery_response_model_name,
#                     ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                         delivery_response_messages
#                     )
#                     self.chatbot_answer = delivery_response.choices[0].message.content
#                     self.sources = [f"{self.delivery_csv_file_path}"]
#                 # IF NOT ABLE TO FETCH DELIVERY INFORMATION
#                 else:
#                     # if no description found
#                     delivery_response_messages = [
#                         {
#                             "role": "system",
#                             "content": f"""The datetime now is {self.today}. {self.intro}.""",
#                         },
#                     ] + [
#                         {
#                             "role": "user",
#                             "content": bot_template_helper.get_general_prompt_template(
#                                 self.chat_language,
#                                 self.question,
#                                 self.tenant_name,
#                                 extra_instruction=f"""You realize that the combination for contact number {delivery_identify_verify_details['contact_number']} and shipping address postal code {delivery_identify_verify_details['shipping_address_postal_code']} records does not exist in the database. Ask the customer to double check the details in a casual and friendly way, and tell him you are happy to assist on further enquiry.""",
#                             ),
#                         },
#                     ]
#                     (
#                         delivery_response,
#                         delivery_response_model_name,
#                     ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                         delivery_response_messages
#                     )

#                     self.chatbot_answer = delivery_response.choices[0].message.content
#                     # self.chatbot_answer = "Upon checking our records, we found that the contact information you provided does not match any existing entries. Could you please double-check and provide us with the correct details? I am more than happy to assist you with any other inquiries you may have."
#                 self.cost += bot_openai_helper.openai_api_calculate_cost(
#                     delivery_response.usage,
#                     model=delivery_response_model_name,
#                 )
#                 return
#             # IF NOT ABLE TO CAPTURE ALL REQUIRED INFO
#             else:
#                 delivery_follow_up_messages = (
#                     [
#                         {
#                             "role": "system",
#                             "content": f"""
# The datetime now is {self.today}. {self.intro}. Your task is to collect information from the customer so that you could proceed to search for the order/delivery information). You need to collect the necessary information such as the contact_number and the shipping address postal code.\nHere are the details you have collected for now:\nContact number: {delivery_identify_verify_details['contact_number']}, Shipping Address Postal Code: {delivery_identify_verify_details['shipping_address_postal_code']}.
#                     """.strip(),
#                         },
#                     ]
#                     + self.historical_messages
#                     + [
#                         {
#                             "role": "user",
#                             "content": bot_template_helper.get_general_prompt_template(
#                                 self.chat_language,
#                                 self.question,
#                                 self.tenant_name,
#                                 extra_instruction="Ask follow up question to collect the missing information. If the customer wants to abort, say thank you politely and indicate we will continue assist them is there's more question.",
#                             ),
#                         },
#                     ]
#                 )
#                 (
#                     delivery_follow_up_res,
#                     delivery_follow_up_res_model_name,
#                 ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                     delivery_follow_up_messages
#                 )
#                 self.cost += bot_openai_helper.openai_api_calculate_cost(
#                     delivery_follow_up_res.usage,
#                     model=delivery_follow_up_res_model_name,
#                 )
#                 self.chatbot_answer = delivery_follow_up_res.choices[0].message.content
#                 # self.chatbot_answer = "Got it, I'm here to assist you. Could you please provide me with your contact number and shipping address postal code so I can help you check :)"
#             return
#         else:
#             print(
#                 "Throwing delivery inquiry to handle general inquiry cause no csv detected..."
#             )
#             self.handle_general_inquiry()
#             return

#     #     response = client.chat.completions.create(
#     #         model=chat_constant.GPT_35_MODEL_CHOICES[0],
#     #         messages=self.historical_messages
#     #         + [
#     #             {"role": "user", "content": self.question},
#     #             {
#     #                 "role": "system",
#     #                 "content": """Now, extract the latest invoice number (if available) and latest block number (if available) from the conversation above and return the information in JSON by updating the JSON {"invoice_number": "", "blk_number": ""} and nothing else (no free text, no small talk).""",
#     #             },
#     #         ],
#     #         temperature=0,
#     #         stop="}",
#     #     )
#     #     json_response = (
#     #         response.choices[0].message.content + "}"
#     #     )  # add back the closing bracket used as stop-sequence
#     #     print(f"{json_response=}")
#     #     auth_details = {"invoice_number": "", "blk_number": ""}
#     #     try:
#     #         auth_details.update(json.loads(json_response))
#     #     except:
#     #         pass
#     #     auth_details["invoice_number"] = auth_details["invoice_number"].upper()
#     #     auth_details["blk_number"] = auth_details["blk_number"].upper()
#     #     print(f"{auth_details=}")
#     #     if auth_details["invoice_number"] == auth_details["blk_number"] == "":
#     #         self.chatbot_answer = "Got it, I'm here to assist you. Could you please provide me with both your invoice number and block number so I can help you check :)"
#     #     elif auth_details["invoice_number"] == "":
#     #         self.chatbot_answer = "I'll just need the your invoice number now to get you your delivery details."
#     #     elif auth_details["blk_number"] == "":
#     #         self.chatbot_answer = (
#     #             "For verification, I'll need your block number as well."
#     #         )
#     #     else:
#     #         # set a default chatbot_answer first
#     #         self.chatbot_answer = "Sorry, I can't find your delivery details. Did you key in your invoice number and block number correctly?"
#     #         self.sources = ["Database Integration"]
#     #         db_context = "Database information\n"
#     #         for db_obj in self.db_objs:
#     #             db_context = db_helper.get_schema(
#     #                 db_obj.db_name,
#     #                 db_obj.username,
#     #                 db_obj.password,
#     #                 db_obj.host,
#     #                 db_obj.port,
#     #             )
#     #             response = client.chat.completions.create(
#     #                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#     #                 messages=[
#     #                     {
#     #                         "role": "system",
#     #                         "content": f"""You prepare SQL queries for extracting delivery details on behalf of users. Consider the following database information:\n{db_context}""",
#     #                     },
#     #                     {
#     #                         "role": "user",
#     #                         "content": f'my invoice_number is {auth_details["invoice_number"]} blk_number is {auth_details["blk_number"]}',
#     #                     },
#     #                     {
#     #                         "role": "system",
#     #                         "content": """Now respond only in syntatically correct SQL statements and nothing else (no free text, no small talk).
#     # Start your response with SELECT and end your response with ;
#     # Use ilike instead of exact matching where possible.""",
#     #                     },
#     #                 ],
#     #                 temperature=0,
#     #                 stop=";",
#     #             )
#     #             query = (
#     #                 response.choices[0].message.content + ";"
#     #             )  # add back the semicolon used as stop-sequence
#     #             print(query)

#     #             db_data = db_helper.execute_query(
#     #                 db_obj.db_name,
#     #                 db_obj.username,
#     #                 db_obj.password,
#     #                 db_obj.host,
#     #                 db_obj.port,
#     #                 query,
#     #             )

#     #             if db_data is None:
#     #                 print(f"No resolution on {db_obj.label}, trying next database...")
#     #                 continue

#     #             response = client.chat.completions.create(
#     #                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#     #                 messages=[
#     #                     {
#     #                         "role": "system",
#     #                         "content": f"""You are a helpful but cautious customer service agent, that will give just enough information to satisfy customers, but not more (e.g. driver/company details for privacy/security reasons), though for time-related, please give always both date and time. The datetime now is {self.today}. These are some of the information that you have about the customer's delivery:{db_data}""",
#     #                     },
#     #                 ]
#     #                 + self.historical_messages
#     #                 + [
#     #                     {
#     #                         "role": "user",
#     #                         "content": bot_template_helper.get_general_prompt_template(
#     #                             self.chat_language, self.question, self.tenant_name
#     #                         ),
#     #                     },
#     #                 ],
#     #                 temperature=0,
#     #             )
#     #             self.chatbot_answer = response.choices[0].message.content
#     #             self.sources = [f"Database Integration ({db_obj.label})"]
#     #             break

#     def handle_make_appointment(self):
#         self.get_or_create_client_customer()
#         if self.is_appointment_set:
#             print("appointment is set! throwing to general inquiry....")
#             self.handle_general_inquiry(is_from_skip_hallu_intent=True)
#             return
#         extract_appointment_info_response = client.chat.completions.create(
#             model=chat_constant.GPT_4_MODEL_CHOICES[0],
#             messages=self.historical_messages
#             + [
#                 {"role": "user", "content": self.question},
#                 {
#                     "role": "system",
#                     "content": f"""Respond strictly in JSON formatted object in your answer. The datetime now is {self.today}, and here's are the details for the appointment location for your reference: {self.appointment_chat_feature_setting.describe()}. Extract the latest name(if available from the customer, otherwise set as "name":"") contact number (if available from the customer, otherwise set as "contact_number": ""), email (if available from the customer, otherwise set as "email":""), appointment location (if available that is answered by customer. Always capture the address that matches the appointment location reference. Otherwise set as "appointment_address":""), appointment date (the date cannot be in the past of the datetime now. Derive it correctly from the customer's reply in the date format: "{utility_date_helper.DATE_FORMAT}" otherwise set as "appointment_date":""), appointment time (derive it correctly from the customer's reply, in the time format: "{utility_date_helper.TIME_FORMAT}" otherwise set as "appointment_time": "").\nFinally, return all the above information in JSON format by updating the JSON {{"name": "", "contact_number": "", "email": "", "appointment_address": "", "appointment_date": "", "appointment_time": ""}} and nothing else (no free text, no small talk).""",
#                 },
#             ],
#             temperature=0,
#             stop="}",
#         )
#         self.cost += bot_openai_helper.openai_api_calculate_cost(
#             extract_appointment_info_response.usage,
#             model=chat_constant.GPT_4_MODEL_CHOICES[0],
#         )

#         json_response = (
#             extract_appointment_info_response.choices[0].message.content + "}"
#         )  # add back the closing bracket used as stop-sequencem
#         print(f"INITIAL: {json_response=}")
#         appointment_details = {
#             "name": "",
#             "contact_number": "",
#             "email": "",
#             "appointment_address": "",
#             "appointment_date": "",
#             "appointment_time": "",
#         }
#         try:
#             appointment_details.update(json.loads(json_response))
#         except Exception as e:
#             print("can't load json: ", e)
#             pass

#         appointment_details["name"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance,
#             "name",
#             appointment_details["name"],
#         )
#         appointment_details["contact_number"] = (
#             bot_intent_helper.update_client_instance(
#                 self.client_customer_instance,
#                 "contact",
#                 appointment_details["contact_number"],
#             )
#         )
#         appointment_details["email"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance, "email", appointment_details["email"].lower()
#         )

#         if not (
#             appointment_details["appointment_address"]
#             in self.appointment_chat_feature_setting.get_location_address_list()
#         ):
#             appointment_details["appointment_address"] = ""

#         appointment_details["email"] = appointment_details["email"].lower()

#         check_datetime_details = {
#             "is_date_valid": "",
#             "is_time_valid": "",
#             "is_datetime_valid": "",
#             "date": "",
#             "time": "",
#         }
#         # Check whether date and time is valid if there's address
#         if (
#             appointment_details["appointment_address"] != ""
#             and appointment_details["appointment_date"] != ""
#             and appointment_details["appointment_time"] != ""
#         ):
#             # Try to use another gpt to check for is_valid_date and is_valid_time
#             # location_address_desc = self.appointment_chat_feature_setting.describe()
#             location_opening_hour = ""
#             location_opening_hour = self.appointment_chat_feature_setting.get_location_address_opening_hour_through_address_str(
#                 appointment_details["appointment_address"]
#             )
#             # print("location_opening_hour: ", location_opening_hour)
#             if location_opening_hour:
#                 check_datetime_message = [
#                     {
#                         "role": "system",
#                         "content": f"""Respond strictly in JSON formatted object in your answer.
# If {appointment_details["appointment_date"]} is within {location_opening_hour}, set "is_date_valid": "yes", otherwise set "is_date_valid": "no".
# If {appointment_details["appointment_time"]} is within {location_opening_hour}, set "is_time_valid": "yes", otherwise set "is_time_valid": "no".
# If {appointment_details["appointment_date"]} {appointment_details["appointment_time"]} is in the future compared to {self.today}, set "is_datetime_valid": "yes". If it is not in the future, set "is_datetime_valid": "no".
# Finally, return strictly in JSON format only by updating the JSON {{"is_date_valid": "", "is_time_valid": "", "is_datetime_valid": ""}} and nothing else (no free text, no small talk).""".replace(
#                             "\n", " "
#                         ),
#                     }
#                 ]
#                 check_datetime_response = client.chat.completions.create(
#                     model=chat_constant.GPT_4_MODEL_CHOICES[0],
#                     messages=check_datetime_message,
#                     temperature=0,
#                     stop="}",
#                 )
#                 # print(
#                 #     "check_datetime_response: ",
#                 #     check_datetime_response.choices[0].message.content,
#                 # )
#                 self.cost += bot_openai_helper.openai_api_calculate_cost(
#                     check_datetime_response.usage,
#                     model=chat_constant.GPT_4_MODEL_CHOICES[0],
#                 )
#                 check_datetime_json_response = (
#                     check_datetime_response.choices[0].message.content + "}"
#                 )  # add back the closing bracket used as stop-sequencem

#                 try:
#                     check_datetime_details.update(
#                         json.loads(check_datetime_json_response)
#                     )
#                 except Exception as e:
#                     print("can't load json: ", e)
#                     pass
#                 print("check_datetime_details: ", check_datetime_details)
#                 if (
#                     check_datetime_details["is_date_valid"] == "no"
#                     or check_datetime_details["is_datetime_valid"] == "no"
#                 ):
#                     check_datetime_details["date"] = appointment_details[
#                         "appointment_date"
#                     ]
#                     appointment_details["appointment_date"] = ""
#                 if (
#                     check_datetime_details["is_time_valid"] == "no"
#                     or check_datetime_details["is_datetime_valid"] == "no"
#                 ):
#                     check_datetime_details["time"] = appointment_details[
#                         "appointment_time"
#                     ]
#                     appointment_details["appointment_time"] = ""
#             else:
#                 print(
#                     f'{appointment_details["appointment_address"]} instance not found! Skipping datetime checking...'
#                 )

#         print(f"MOST UPDATED: {appointment_details=}")

#         is_missing_name = appointment_details["name"] == ""
#         is_missing_contact_number = appointment_details["contact_number"] == ""
#         is_missing_email = appointment_details["email"] == ""  # make email optional
#         is_missing_appointment_address = (
#             appointment_details["appointment_address"] == ""
#         )
#         is_missing_appointment_date = appointment_details["appointment_date"] == ""
#         is_missing_appointment_time = appointment_details["appointment_time"] == ""

#         is_wrong_date = check_datetime_details["is_date_valid"] == "no"
#         is_wrong_time = check_datetime_details["is_time_valid"] == "no"

#         if (
#             is_missing_name
#             or is_missing_contact_number
#             # or is_missing_email
#             or is_missing_appointment_address
#             or is_missing_appointment_date
#             or is_missing_appointment_time
#         ):
#             # follow up with user to get information
#             # missing_information_str = f"{'Customer Name,' if is_missing_name else ''}{'Customer Email,' if is_missing_email else ''}{'Appointment Location,' if is_missing_appointment_address else ''}{'Appointment Date,' if is_missing_appointment_date else ''}{'Appointment Time,' if is_missing_appointment_time else ''}"
#             appointment_date_str = (
#                 ""
#                 if is_missing_appointment_date
#                 else f"{appointment_details['appointment_date']} (Valid and within the opening hours)"
#             )
#             appointment_time_str = (
#                 ""
#                 if is_missing_appointment_time
#                 else f"{appointment_details['appointment_time']} (Valid and within the opening hours)"
#             )
#             missing_information = []

#             if is_missing_name:
#                 missing_information.append("Name")
#             if is_missing_contact_number:
#                 missing_information.append("Contact Number")
#             if is_missing_email:
#                 missing_information.append("Email")
#             if is_missing_appointment_address:
#                 missing_information.append("Appointment Location")
#             if is_missing_appointment_date:
#                 if is_wrong_date:
#                     missing_information.append(
#                         f"Appointment Date (Inform the customer {check_datetime_details['date']} is not within opening dates)"
#                     )
#                 else:
#                     missing_information.append("Appointment Date")
#             if is_missing_appointment_time:
#                 if is_wrong_time:
#                     missing_information.append(
#                         f"Appointment Time (Inform the customer {check_datetime_details['time']} is not within opening hours)"
#                     )
#                 else:
#                     missing_information.append(
#                         "Appointment Time (Show the opening hours)"
#                     )

#             follow_up_question_count = len(missing_information)
#             missing_information_str = ", ".join(missing_information)
#             missing_information_final_message = f"""These are the customer information that you have not collected yet, please ask the follow up questions accordingly: {missing_information_str}. Ask {min(follow_up_question_count, 3)} follow up questions to collect the missing information in one reply."""

#             location_address_count = (
#                 self.appointment_chat_feature_setting.get_location_address_list_count()
#             )
#             is_multiple_location = location_address_count > 1

#             location_str = f"location{'s' if is_multiple_location else ''}"
#             address_str = f"address{'es' if is_multiple_location else ''}"

#             appointment_extra_instruction = utility_general_helper.clean_up_prompt(
#                 f"""
#                 Here's some rules you should follow:
#                 -You have {location_address_count} {self.appointment_chat_feature_setting.location_type}.
#                 -You have not schedule the appointment yet.
#                 -Do not recommend timing that is not within the opening hours of the {location_str}.
#                 -If the customer wants to abort, say thank you politely and indicate we will continue assist them is there's more question.
#                 -Tell the customer about the opening hours of the {location_str} if required.
#                 -Keep the answer STRICTLY under 100 words by simplifying the information you have.
#                 -Do not tell the customer what instruction you are getting.
#                 -{missing_information_final_message}
#                 -{self.simplied_reply_prompt}
#             """
#             )

#             appointment_follow_up_messages = (
#                 [
#                     {
#                         "role": "system",
#                         "content": utility_general_helper.clean_up_prompt(
#                             f"""
# The datetime now is {self.today}. {self.intro}. Your task is to collect information from the customer so that you could proceed to make a booking for the customer to attend to {self.appointment_chat_feature_setting.location_type} ({self.appointment_chat_feature_setting.describe()}). You need to collect the necessary information such as the name, contact_number, email, appointment location(Tell the customer about the {location_str} if they asked for them. The answer can STRICTLY only be from the {address_str} in the {self.appointment_chat_feature_setting.location_type} {'list' if is_multiple_location else ''} that I have showed you) and appointment datetime. Here are the details you have collected for now:\nName: {appointment_details['name']}, Contact number: {appointment_details['contact_number']}, Email: {appointment_details['email']}, Appointment Address: {appointment_details['appointment_address']}, Appointment Date: {appointment_date_str}, Appointment Time: {appointment_time_str}.
#                     """
#                         ),
#                     },
#                 ]
#                 + self.historical_messages
#                 + [
#                     {
#                         "role": "user",
#                         "content": bot_template_helper.get_general_prompt_template(
#                             self.chat_language,
#                             self.question,
#                             self.tenant_name,
#                             extra_instruction=appointment_extra_instruction,
#                         ),
#                     },
#                 ]
#             )
#             (
#                 appointment_follow_up_res,
#                 appointment_follow_up_res_model_name,
#             ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                 appointment_follow_up_messages
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 appointment_follow_up_res.usage,
#                 model=appointment_follow_up_res_model_name,
#             )
#             self.chatbot_answer = appointment_follow_up_res.choices[0].message.content
#         else:
#             # Process the request.
#             print(
#                 f"All neccessary appointment details are collected: \n {appointment_details} \n "
#             )

#             appointment_chat_feature_location_instance = (
#                 chat_action_models.AppointmentChatFeatureLocation.objects.filter(
#                     location__icontains=appointment_details["appointment_address"]
#                 ).first()
#             )

#             person_in_charge_str = (
#                 f"\nPerson in Charge: {appointment_chat_feature_location_instance.person_in_charge}"
#                 if appointment_chat_feature_location_instance.person_in_charge
#                 else ""
#             )

#             appointment_confirm_messages = self.historical_messages + [
#                 {
#                     "role": "system",
#                     "content": f"""The datetime now is {self.today}. {self.intro}. You have collected information from the customer and booked for the customer to attend to a {self.appointment_chat_feature_setting.location_type}. Here are the customer details: {appointment_details}.""",
#                 },
#                 {
#                     "role": "user",
#                     "content": bot_template_helper.get_general_prompt_template(
#                         self.chat_language,
#                         self.question,
#                         self.tenant_name,
#                         extra_instruction=f"""Tell the customer about this in a friendly and casual way (not email format) by including the following information: \n{self.appointment_chat_feature_setting.location_type} Location: <appointment_address>\nAppointment Datetime: <appointment_date & appointment_time>{person_in_charge_str}, as well as that we have notify our team. Here's some rules you should follow: -{self.simplied_reply_prompt}""",
#                     ),
#                 },
#             ]

#             (
#                 appointment_confirm_res,
#                 appointment_confirm_res_model_name,
#             ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                 appointment_confirm_messages
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 appointment_confirm_res.usage,
#                 model=appointment_confirm_res_model_name,
#             )

#             # Create appointment object:
#             (
#                 created_appointment_instance,
#                 is_created_appointment_instance,
#             ) = chat_action_tracker_models.Appointment.objects.get_or_create(
#                 appointment_chat_feature_location=appointment_chat_feature_location_instance,
#                 name=appointment_details["name"],
#                 contact=appointment_details["contact_number"],
#                 email=appointment_details["email"],
#                 location=appointment_details["appointment_address"],
#                 datetime=datetime.strptime(
#                     appointment_details["appointment_date"]
#                     + "T"
#                     + appointment_details["appointment_time"],
#                     utility_date_helper.DATE_TIME_FORMAT,
#                 ),
#                 conversation=self.conversation_instance,
#                 defaults={
#                     "message": self.conversation_instance.messages.first(),
#                 },
#             )

#             if (
#                 is_created_appointment_instance
#                 and appointment_chat_feature_location_instance.identifier != ""
#             ):
#                 appointment_conversation_summarized_info = chat_action_tracker_helper.get_appointment_conversation_summarized_info(
#                     self
#                 )

#                 appointment_email_str = (
#                     f" <{created_appointment_instance.email}>"
#                     if created_appointment_instance.email
#                     else ""
#                 )

#                 info_parts = [
#                     part
#                     for part in [
#                         appointment_conversation_summarized_info.get("products", ""),
#                         appointment_conversation_summarized_info.get("vouchers", ""),
#                     ]
#                     if part
#                 ]
#                 appointment_conversation_summarized_info_str = ", ".join(info_parts)
#                 appointment_conversation_summarized_info_str = (
#                     f"\n{appointment_conversation_summarized_info_str}"
#                     if appointment_conversation_summarized_info_str
#                     else ""
#                 )

#                 whatsapp_org_information = (
#                     f"{self.tenant_name} \n"
#                     if "labrador"
#                     in appointment_chat_feature_location_instance.identifier.lower()
#                     else ""
#                 )  # if whatsapp notification contain

#                 appointment_whatsapp_message = f"""{whatsapp_org_information}{appointment_chat_feature_location_instance.name}\n{created_appointment_instance.name}\n{created_appointment_instance.contact}{appointment_email_str}\n{utility_date_helper.convert_to_human_friendly_format(created_appointment_instance.datetime)}{appointment_conversation_summarized_info_str}"""

#                 chat_action_tracker_models.MessageToSend.objects.create(
#                     appointment=created_appointment_instance,
#                     contact=appointment_chat_feature_location_instance.identifier,
#                     message=appointment_whatsapp_message,
#                     to_be_sent_at=created_appointment_instance.datetime
#                     - timedelta(hours=6),
#                 )
#                 https_app_url_1 = os.environ.get("HTTPS_APP_URL_1")
#                 base_url = f"{https_app_url_1}/org/{self.schema_name.replace('_', '-')}/appointment/{created_appointment_instance.code}"

#                 customer_info_str = f"""{created_appointment_instance.name} {created_appointment_instance.contact}{appointment_email_str}"""
#                 sale_made_url_str = f"Sale Made: {base_url}/yes"
#                 absent_url_str = f"No Show/Postponed: {base_url}/absent"
#                 no_sale_url_str = f"No Sale: {base_url}/no"

#                 follow_up_message = f"""{whatsapp_org_information}Pls update {created_appointment_instance.datetime.strftime("%I:%M %p")} {customer_info_str}\n\n{sale_made_url_str}\n\n{absent_url_str}\n\n{no_sale_url_str}"""
#                 # print("follow_up_message: ", follow_up_message)
#                 chat_action_tracker_models.MessageToSend.objects.create(
#                     appointment=created_appointment_instance,
#                     contact=appointment_chat_feature_location_instance.identifier,
#                     message=follow_up_message,
#                     to_be_sent_at=created_appointment_instance.datetime
#                     + timedelta(hours=2),
#                 )

#             self.chatbot_answer = appointment_confirm_res.choices[0].message.content
#             self.sources = ["Customize"]

#     def handle_complaint(self):
#         if self.is_customer_complaint_sent:
#             print("Complaint is sent out! throwing to general inquiry....")
#             self.handle_general_inquiry()
#             return

#         extract_complaint_info_response = client.chat.completions.create(
#             model=chat_constant.GPT_35_MODEL_CHOICES[0],
#             messages=self.historical_messages
#             + [
#                 {"role": "user", "content": self.question},
#                 {
#                     "role": "system",
#                     "content": f"""The datetime now is {self.today}, extract the latest name(if available from the customer, if not return ""), contact number (if available from the customer, if not return ""), email (if available from the customer, if not return ""), invoice number (if available from the customer, if not return ""), problem description (if available from the customer, if not return ""), from the conversation above and return the information in JSON by updating the JSON {{"name": "", "contact_number": "", "complaint_description": "", "email": "", "invoice_number"}} and nothing else (no free text, no small talk).""",
#                 },
#             ],
#             temperature=0,
#             stop="}",
#         )

#         self.cost += bot_openai_helper.openai_api_calculate_cost(
#             extract_complaint_info_response.usage,
#             model=chat_constant.GPT_35_MODEL_CHOICES[0],
#         )

#         json_response = (
#             extract_complaint_info_response.choices[0].message.content + "}"
#         )  # add back the closing bracket used as stop-sequence
#         print(f"{json_response=}")
#         complaint_details = {
#             "name": "",
#             "contact_number": "",
#             "complaint_description": "",
#             "email": "",
#             "invoice_number": "",
#         }
#         try:
#             complaint_details.update(json.loads(json_response))
#         except:
#             pass

#         complaint_details["name"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance,
#             "name",
#             complaint_details["name"],
#         )
#         complaint_details["contact_number"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance,
#             "contact",
#             complaint_details["contact_number"],
#         )
#         complaint_details["email"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance, "email", complaint_details["email"].lower()
#         )
#         print(f"{complaint_details=}")

#         if (
#             complaint_details["name"] == ""
#             or complaint_details["contact_number"] == ""
#             or complaint_details["email"] == ""
#             or complaint_details["complaint_description"] == ""
#         ):
#             # follow up with user to get information
#             complaint_follow_up_messages = self.historical_messages + [
#                 {
#                     "role": "system",
#                     "content": utility_general_helper.clean_up_prompt(
#                         f"""{self.intro}. Here's the knowledge for you to know:
#                             {self.bot_setting_instance.general_inquiry_guide}.
#                             Your task now is to collect information from the customer complaint so that you could proceed to make ticket submission to the customer support team about the complaint. You need to collect the necessary information such as the name, contact number,email and complaint_description. The invoice_number is optional.\nHere are the details you have now: Name: {complaint_details['name']}, Contact number: {complaint_details['contact_number']}, Complaint Description: {complaint_details['complaint_description']}, Email: {complaint_details['email']}, Invoice Number: {complaint_details['invoice_number']}
#                         """
#                     ),
#                 },
#                 {
#                     "role": "user",
#                     "content": bot_template_helper.get_general_prompt_template(
#                         self.chat_language,
#                         self.question,
#                         self.tenant_name,
#                         extra_instruction=f"""Ask follow up question to collect the missing information. If the customer wants to abort, say thank you politely and indicate we will continue assist them is there's more question. Here's some rules you should follow: -{self.simplied_reply_prompt}""",
#                     ),
#                 },
#             ]

#             (
#                 complaint_follow_up_res,
#                 complaint_follow_up_res_model_name,
#             ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                 complaint_follow_up_messages
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 complaint_follow_up_res.usage,
#                 model=complaint_follow_up_res_model_name,
#             )
#             self.chatbot_answer = complaint_follow_up_res.choices[0].message.content

#         else:
#             print(
#                 f"All neccessary complaint details are collected: \n {complaint_details} \n."
#             )
#             customer_support_instance = (
#                 chat_action_tracker_models.CustomerSupport.objects.create(
#                     name=complaint_details["name"],
#                     contact=complaint_details["contact_number"],
#                     complaint_description=complaint_details["complaint_description"],
#                     email=complaint_details["email"],
#                     invoice_number=complaint_details["invoice_number"],
#                     conversation=self.conversation_instance,
#                     message=self.conversation_instance.messages.first(),
#                 )
#             )

#             # TODO-yc: Trigger sending the compaint to the service team (possibly email)!
#             bot_intent_helper.send_customer_support_email(
#                 customer_support_instance.name,
#                 customer_support_instance.contact,  # customer contact
#                 customer_support_instance.complaint_description,
#                 customer_support_instance.email,  # customer email
#                 customer_support_instance.invoice_number,
#                 customer_support_instance.conversation.id,
#                 self.tenant_name,
#                 self.schema_name,
#                 self.customer_support_chat_feature_setting.email_to,
#             )

#             confirm_complaint_messages = self.historical_messages + [
#                 {
#                     "role": "system",
#                     "content": f"""The datetime now is {self.today}. {self.intro}. You have collected the complaint information from the customer and sent to the customer support team. Here are the customer details: {complaint_details}.""",
#                 },
#                 {
#                     "role": "user",
#                     "content": bot_template_helper.get_general_prompt_template(
#                         self.chat_language,
#                         self.question,
#                         self.tenant_name,
#                         extra_instruction=f"""Inform the customer in a casual way (not email format) that we have have notify our customer support team, and re-assure customer that the team will reach them as soon as possible to assist them.""",
#                     ),
#                 },
#             ]

#             (
#                 confirm_complaint_res,
#                 confirm_complaint_res_model_name,
#             ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                 confirm_complaint_messages
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 confirm_complaint_res.usage,
#                 model=confirm_complaint_res_model_name,
#             )
#             self.chatbot_answer = confirm_complaint_res.choices[0].message.content
#             # Process the request.

#     def handle_shopify_inquiry(self):
#         shopify_intent_json = bot_shopify_helper.get_shopify_intent_json(
#             self.tenant_name,
#             self.historical_messages,
#             self.chat_language,
#             self.question,
#         )
#         print("shopify_intent_json: ", shopify_intent_json)
#         if shopify_intent_json["intent"] == bot_intent_helper.OTHER:
#             self.chatbot_answer = "I am sorry, I am currently unable to help you with this question. Please try again later."
#         else:
#             self.chatbot_answer = bot_shopify_helper.get_response_for_shopify_query(
#                 self.schema_name,
#                 self.tenant_name,
#                 self.chat_language,
#                 self.historical_messages,
#                 self.question,
#                 shopify_intent_json,
#             )
#         self.sources = ["Shopify Integration"]

#     # In future to combine with missing information
#     def handle_direct_request_from_customer(self):
#         if self.is_callback_request_made:
#             print("Callback request is made! throwing to general inquiry....")
#             self.handle_general_inquiry()
#             return

#         extract_direct_request_from_customer_info_response = client.chat.completions.create(
#             model=chat_constant.GPT_35_MODEL_CHOICES[0],
#             messages=self.historical_messages
#             + [
#                 {"role": "user", "content": self.question},
#                 {
#                     "role": "system",
#                     "content": f"""The datetime now is {self.today}, extract the latest name(if available from the customer, if not return ""), contact number (if available from the customer, if not return ""), email (if available from the customer, if not return ""), from the conversation above and return the information in JSON by updating the JSON {{"name": "", "contact_number": "", "email": ""}} and nothing else (no free text, no small talk).""",
#                 },
#             ],
#             temperature=0,
#             stop="}",
#         )
#         self.cost += bot_openai_helper.openai_api_calculate_cost(
#             extract_direct_request_from_customer_info_response.usage,
#             model=chat_constant.GPT_35_MODEL_CHOICES[0],
#         )
#         json_response = (
#             extract_direct_request_from_customer_info_response.choices[
#                 0
#             ].message.content
#             + "}"
#         )  # add back the closing bracket used as stop-sequence
#         contact_detail = {
#             "name": "",
#             "contact_number": "",
#             "email": "",
#         }
#         try:
#             contact_detail.update(json.loads(json_response))
#         except:
#             pass
#         contact_detail["name"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance,
#             "name",
#             contact_detail["name"],
#         )
#         contact_detail["contact_number"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance,
#             "contact",
#             contact_detail["contact_number"],
#         )
#         contact_detail["email"] = bot_intent_helper.update_client_instance(
#             self.client_customer_instance, "email", contact_detail["email"].lower()
#         )

#         # print("contact_detail: ", contact_detail)
#         if contact_detail["name"] == "" or (
#             contact_detail["contact_number"] == "" and contact_detail["email"] == ""
#         ):
#             direct_request_from_customer_follow_up_messages = (
#                 [
#                     {
#                         "role": "system",
#                         "content": utility_general_helper.clean_up_prompt(
#                             f"""
#                         {self.intro}.
#                         Here's the rules for you to refer:
#                         {self.bot_setting_instance.general_inquiry_guide}.

#                         Your task is to collect information from the customer so you can proceed to inform the team to contact them back as soon as possible (do not promise a specific datetime, even requested by the customer).
#                         You need to collect the information such as the name, contact number and email.
#                         Here are the details you have now: Name: {contact_detail['name']}, Contact number: {contact_detail['contact_number']}, Email: {contact_detail['email']}.
#                         """
#                         ),
#                     }
#                 ]
#                 + self.historical_messages
#                 + [
#                     {
#                         "role": "user",
#                         "content": bot_template_helper.get_general_prompt_template(
#                             self.chat_language,
#                             self.question,
#                             self.tenant_name,
#                             extra_instruction=f"""Ask follow up question to collect the missing information. When the customer ask about our contact, we prefer them to provide their contact information instead to contact them back. If the customer wants to abort, say thank you politely and indicate we will continue assist them is there's more question.""",
#                         ),
#                     }
#                 ]
#             )
#             (
#                 direct_request_from_customer_follow_up_response,
#                 direct_request_from_customer_follow_up_model_name,
#             ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                 direct_request_from_customer_follow_up_messages
#             )
#             self.cost += bot_openai_helper.openai_api_calculate_cost(
#                 direct_request_from_customer_follow_up_response.usage,
#                 model=direct_request_from_customer_follow_up_model_name,
#             )
#             self.chatbot_answer = (
#                 direct_request_from_customer_follow_up_response.choices[
#                     0
#                 ].message.content
#             )
#         else:
#             with schema_context(self.schema_name):
#                 get_context_messages = (
#                     [
#                         {
#                             "role": "system",
#                             "content": f"""{self.intro}.""",
#                         }
#                     ]
#                     + self.historical_messages
#                     + [
#                         {
#                             "role": "user",
#                             "content": bot_template_helper.get_general_prompt_template(
#                                 self.chat_language,
#                                 self.question,
#                                 self.tenant_name,
#                                 extra_instruction="""
#                                 Customer has provided their contact information for us to contact them. Extract the valid reason why the customer want us to contact them from the chat history. Return in JSON format by updating the JSON {{"reason": ""}} and nothing else (no free text, no small talk)..
#                             """,
#                             ),
#                         }
#                     ]
#                 )

#                 get_context_response = client.chat.completions.create(
#                     model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                     messages=get_context_messages,
#                     temperature=0,
#                     stop="}",
#                 )
#                 self.cost += bot_openai_helper.openai_api_calculate_cost(
#                     get_context_response.usage,
#                     model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 )
#                 json_response = (
#                     get_context_response.choices[0].message.content + "}"
#                 )  # add back the closing bracket used as stop-sequence

#                 context = {
#                     "reason": "",
#                 }
#                 try:
#                     context.update(json.loads(json_response))
#                 except:
#                     pass
#                 call_list_action_instance = (
#                     chat_action_tracker_models.CallListAction.objects.create(
#                         conversation=self.conversation_instance,
#                         message=self.conversation_instance.messages.first(),
#                         name=contact_detail["name"],
#                         contact=contact_detail["contact_number"],
#                         email=contact_detail["email"],
#                         context=context["reason"],
#                     )
#                 )

#                 # TODO-yc: Trigger sending the callback request to the service team (possibly email)!
#                 bot_intent_helper.send_direct_contact_request_email(
#                     call_list_action_instance.name,
#                     call_list_action_instance.contact,  # customer contact
#                     call_list_action_instance.email,  # customer email
#                     call_list_action_instance.context,
#                     call_list_action_instance.conversation.id,
#                     self.tenant_name,
#                     self.schema_name,
#                     self.callback_chat_feature_setting.email_to,
#                 )

#                 direct_request_from_customer_confirmed_messages = (
#                     [
#                         {
#                             "role": "system",
#                             "content": f"{self.intro}. Inform the customer that you have collected the contact number and email and will inform the customer support to reach out to them. Thank them for their time and ask them whether they still need anything to be assisted with. No extra information is needed.",
#                         }
#                     ]
#                     + self.historical_messages
#                     + [
#                         {
#                             "role": "user",
#                             "content": bot_template_helper.get_general_prompt_template(
#                                 self.chat_language, self.question, self.tenant_name
#                             ),
#                         }
#                     ]
#                 )

#                 (
#                     direct_request_from_customer_confirmed_response,
#                     direct_request_from_customer_confirmed_model_name,
#                 ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#                     direct_request_from_customer_confirmed_messages
#                 )
#                 self.cost += bot_openai_helper.openai_api_calculate_cost(
#                     direct_request_from_customer_confirmed_response.usage,
#                     model=direct_request_from_customer_confirmed_model_name,
#                 )
#                 self.chatbot_answer = (
#                     direct_request_from_customer_confirmed_response.choices[
#                         0
#                     ].message.content
#                 )

#     def handle_general_inquiry(self, is_from_skip_hallu_intent=False):
#         embeddings = OpenAIEmbeddings()
#         chroma_db_collection = utility_chroma_db_helper.load_chroma_db_collection(
#             embeddings, self.collection_name
#         )
#         retriever = chroma_db_collection.as_retriever(
#             search_type="similarity_score_threshold",
#             search_kwargs={
#                 "k": 4,  # now it includes Docs + FAQ also
#                 "score_threshold": 0.6,
#             },  # return consine similarity, the higher the more similar
#         )
#         print(f"Getting answers from {self.collection_name} ...")
#         # print(
#         #     f"{bot_helper.extract_chat_history_content(self.historical_messages)=}"
#         # )
#         retriever_content = retriever.get_relevant_documents(
#             bot_helper.extract_chat_history_content(self.historical_messages[-4:])
#             + self.question
#         )
#         # print("retriever_content: ", retriever_content)
#         if retriever_content:
#             self.context = "\n".join([i.page_content for i in retriever_content])
#             self.sources = list(
#                 set([i.metadata.get("source", "") for i in retriever_content])
#             )

#         # print("retriever_content: ", retriever_content)
#         # print("self.sources: ", self.sources)

#         # {self.faq_feature_task_template}
#         # {self.bot_setting_instance.general_inquiry_guide}

#         system_message_content = utility_general_helper.clean_up_prompt(
#             f"""The datetime now is {self.today}.
#             {self.intro}{f" at {self.chatbot_extra_source_name}" if self.chatbot_extra_source_name else ""}, with the personality of {self.bot_setting_instance.tone}.
#             Here's some knowledge and rules you have to know:
#             -Do not chitchat with the customer that is unrelated to {self.tenant_name}.
#             -You must uphold and champion the brand's reputation, refraining from public criticisms while utilizing our internal channels for feedback and concerns
#             {self.bot_setting_instance.general_inquiry_guide}
#             -------------
#             You have these tasks:
#             -Answer customer question with the context available. If there's insufficient context in the question or chat history, ask follow-up questions to get more clarity.
#             -If you cannot find a reference to answer the question base on the context, tell the customer that you need to check back with the team, DO NOT TRY TO MAKE UP AN ANSWER.
#             -Retain the full URL links if any.
#             {self.appointment_chat_feature_task_template}
#             {self.customer_support_chat_feature_task_template}
#             ----------------
#             Context: {self.context}
#             """
#         )

#         extra_instruction = f"Keep the answer less than {math.ceil(self.bot_setting_instance.word_count * 1.5)} words."
#         if (
#             self.is_potential_customer
#             and self.is_appointment_set == False
#             and self.appointment_chat_feature_setting.is_usable()
#         ):
#             extra_instruction = (
#                 f"{self.appointment_chat_feature_setting.instruction}"
#                 if self.appointment_chat_feature_setting.instruction
#                 else f"If they are potential customer, passionately ask them for their availability for an appointment by sending a formal invite!"
#             ) + extra_instruction

#         extra_instruction = f"({extra_instruction})"
#         # print(f"{extra_instruction=}")
#         # If you cannot find any revelant answer to the inquiry, tell the customer that you have to check for the inquiry, do not promise anything to the customer and tell them we will get back to it ASAP.
#         general_inquiry_messages = (
#             [
#                 {
#                     "role": "system",
#                     "content": system_message_content,
#                 }
#             ]
#             + self.historical_messages
#             + [
#                 {
#                     "role": "user",
#                     "content": bot_template_helper.get_general_prompt_template(
#                         self.chat_language,
#                         self.question,
#                         self.tenant_name,
#                         extra_instruction,
#                     ),
#                 }
#             ]
#         )
#         # print(json.dumps(general_inquiry_messages, indent=2))
#         (
#             general_inquiry_response,
#             general_inquiry_model_name,
#         ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#             general_inquiry_messages
#         )
#         self.cost += bot_openai_helper.openai_api_calculate_cost(
#             general_inquiry_response.usage, model=general_inquiry_model_name
#         )

#         self.chatbot_answer = general_inquiry_response.choices[0].message.content

#         ## Missing Info / Hallucination
#         # FB Attachment skip check_hallu & is from other intent skip check hallu
#         if self.missing_info_feature_setting.is_usable():
#             retriever_content_base_on_answer = ""
#             retriever_content_base_on_answer_raw = retriever.get_relevant_documents(
#                 general_inquiry_response.choices[0].message.content
#             )
#             if retriever_content_base_on_answer_raw:
#                 retriever_content_base_on_answer = "\n".join(
#                     [i.page_content for i in retriever_content_base_on_answer_raw]
#                 )

#                 chat_action_tracker_helper.check_if_is_missing_info(
#                     self.conversation_id,
#                     self.historical_messages,
#                     self.question,
#                     self.intro,
#                     self.tenant_name,
#                     self.bot_setting_instance.general_inquiry_guide,
#                     self.context,
#                     retriever_content_base_on_answer=retriever_content_base_on_answer,
#                 )

#         # if (
#         #     self.is_check_hallucination
#         #     and not is_from_skip_hallu_intent
#         #     and self.missing_info_feature_setting.is_usable()
#         # ):

#         #     is_hallucination = (
#         #         bot_chatbot_helper.handle_hallucination_and_record_missing_info(
#         #             self,
#         #             general_inquiry_response.choices[0].message.content,
#         #             retriever_content_base_on_answer,
#         #         )
#         #     )
#         #     if is_hallucination:  # not sure how to use this better yet
#         #         pass


# BACKLOG


# def handle_order_inquiry(self, historical_messages, question):
#     """
#     need to take in the contact number (with or without '-'?)
#     """
#     response = client.chat.completions.create(
#         model=chat_constant.GPT_35_MODEL_CHOICES[0],
#         messages=historical_messages
#         + [
#             {"role": "user", "content": question},
#             {
#                 "role": "system",
#                 "content": """Now, extract the latest contact number (if available) from the conversation above and return the information in JSON by updating the JSON {"contact_number": ""} and nothing else (no free text, no small talk).""",
#             },
#         ],
#         temperature=0,
#         stop="}",
#     )
#     json_response = (
#         response.choices[0].message.content + "}"
#     )  # add back the closing bracket used as stop-sequence
#     print(f"{json_response=}")
#     auth_details = {"contact_number": "", "id": -1}
#     try:
#         auth_details.update(json.loads(json_response))
#     except:
#         pass
#     # auth_details["invoice_number"] = auth_details["invoice_number"].upper()
#     print(f"{auth_details=}")

#     if auth_details["contact_number"] == "":
#         self.chatbot_answer = "Got it, I'm here to assist you. Could you please provide me with your contact number so I can help you check :)"
#     else:
#         # set a default chatbot_answer first
#         self.chatbot_answer = "Sorry, I can't find your order details. Did you key in your contact number correctly?"
#         self.sources = ["Database Integration"]
#         db_context = "Database information\n"

#         customer_instance = None
#         order_instances = None
#         try:
#             customer_instance = customer_models.Customer.objects.get(
#                 customer_id=auth_details["contact_number"]
#             )
#             order_instances = order_models.Order.objects.filter(
#                 customer=customer_instance
#             )
#             auth_details["id"] = customer_instance.id
#         except:
#             pass

#         for db_obj in self.db_objs:
#             db_context = db_helper.get_schema(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#             )
#             print(f"{db_context=}")
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=[
#                     {
#                         "role": "system",
#                         "content": f"""You prepare SQL queries for extracting order details on behalf of users. Always prioritize order table to get data. Consider the following database information:\n{db_context}""",
#                     },
#                     {
#                         "role": "user",
#                         "content": f'my customer_id OR my idcust is {auth_details["contact_number"]} OR id is {auth_details["id"]}',
#                     },
#                     {
#                         "role": "system",
#                         "content": """Now respond only in syntatically correct SQL statements and nothing else (no free text, no small talk).
# Start your response with SELECT and end your response with ;
# Use ilike instead of exact matching where possible.""",
#                     },
#                 ],
#                 temperature=0,
#                 stop=";",
#             )
#             query = (
#                 response.choices[0].message.content + ";"
#             )  # add back the semicolon used as stop-sequence
#             print(f"{query=}")

#             db_data = db_helper.execute_query(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#                 query,
#             )

#             if order_instances:
#                 db_data = ""
#                 for index, order_instance in enumerate(order_instances):
#                     db_data += f"Order {index+1}:\n" + order_instance.describe()
#             elif db_data is None:
#                 print(f"No resolution on {db_obj.label}, trying next database...")
#                 continue
#             print(f"{db_data=}")
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=historical_messages
#                 + [
#                     {
#                         "role": "system",
#                         "content": f"""You are a helpful but cautious customer service agent, that will give just enough information to satisfy customers. If you have product information that suits the customer, you will try to upsell or cross-sell the product to them politely. Finally, you will ask the customer which orders they want to confirm.

# These are some of the information that you have about the customer's order:
# {db_data}""",
#                     },
#                 ]
#                 + [
#                     {"role": "user", "content": question},
#                 ],
#                 temperature=0,
#             )
#             self.chatbot_answer = response.choices[0].message.content
#             self.sources = [f"Database Integration ({db_obj.label})"]
#             break

# def handle_order_confirmation(self, historical_messages, question):
#     """
#     need to take in the order invoice number list
#     """
#     response = client.chat.completions.create(
#         model=chat_constant.GPT_35_MODEL_CHOICES[0],
#         messages=historical_messages
#         + [
#             {"role": "user", "content": question},
#             {
#                 "role": "system",
#                 "content": """Now, extract the list of confirmed orders' invoice number (if available) from the conversation above and return the information in JSON by updating the JSON {"confirmed_order_list": []} and nothing else (no free text, no small talk).""",
#             },
#         ],
#         temperature=0,
#         stop="}",
#     )
#     json_response = (
#         response.choices[0].message.content + "}"
#     )  # add back the closing bracket used as stop-sequence
#     print(f"{json_response=}")
#     confirmed_order_list = []
#     try:
#         confirmed_order_list = json.loads(json_response)
#     except:
#         pass
#     # auth_details["invoice_number"] = auth_details["invoice_number"].upper()
#     print(f"{confirmed_order_list=}")

#     if len(confirmed_order_list) == 0:
#         self.chatbot_answer = "I'm here to assist you. Could you please provide me the orders' invoice numbers that you would love to confirm so I can proceed?"
#     else:
#         # set a default chatbot_answer first
#         self.chatbot_answer = "Sorry, there's a mismatch with some of the orders' invoice number. Could you please double check whether all the orders' invoice number are keyed in correctly?"
#         self.sources = ["Database Integration"]
#         db_context = "Database information\n"
#         for db_obj in self.db_objs:
#             print(f"reading {db_obj.label}...")
#             db_context = db_helper.get_schema(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#             )
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=[
#                     {
#                         "role": "system",
#                         "content": f"""You prepare SQL queries for extracting orders details on behalf of users. Consider the following database information:\n{db_context}""",
#                     },
#                     {
#                         "role": "user",
#                         "content": f"the order invoice number list are: {confirmed_order_list}",
#                     },
#                     {
#                         "role": "system",
#                         "content": """Now respond only in syntatically correct SQL statements and nothing else (no free text, no small talk).
# Start your response with SELECT and end your response with ;
# Use ilike instead of exact matching where possible.""",
#                     },
#                 ],
#                 temperature=0,
#                 stop=";",
#             )
#             query = (
#                 response.choices[0].message.content + ";"
#             )  # add back the semicolon used as stop-sequence
#             print(query)

#             db_data = db_helper.execute_query(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#                 query,
#             )

#             if db_data is None:
#                 print(f"No resolution on {db_obj.label}, trying next database...")
#                 continue

#             # found something
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=historical_messages
#                 + [
#                     {
#                         "role": "system",
#                         "content": f"""You are a helpful but cautious customer service agent, that will give just enough information to satisfy customers.

# These are the orders that the customer would like to confirm:
# {db_data}""",
#                     },
#                 ]
#                 + [
#                     {
#                         "role": "user",
#                         "content": "Summarize the orders details and tell the customer that these are the confirmed orders.",
#                     },
#                 ],
#                 temperature=0,
#             )
#             self.chatbot_answer = (
#                 response.choices[0].message.content
#                 + "Do you want to book for a delivery slots for the orders?"
#             )
#             self.sources = [f"Database Integration ({db_obj.label})"]
#             break

# def handle_get_delivery_slot(self, historical_messages, question):
#     response = client.chat.completions.create(
#         model=chat_constant.GPT_35_MODEL_CHOICES[0],
#         messages=historical_messages
#         + [
#             {"role": "user", "content": question},
#             {
#                 "role": "system",
#                 "content": """Now, extract the delivery slot that is chosen by the user (if available) from the conversation above and return the information in JSON by updating the JSON {"preferred_date": "", "preferred_time": ""} and nothing else (no free text, no small talk).""",
#             },
#         ],
#         temperature=0,
#         stop="}",
#     )
#     json_response = (
#         response.choices[0].message.content + "}"
#     )  # add back the closing bracket used as stop-sequence
#     print(f"{json_response=}")
#     chosen_delivery_slot_detail = {"preferred_date": "", "preferred_time": ""}
#     try:
#         chosen_delivery_slot_detail.update(json.loads(json_response))
#     except:
#         pass
#     print(f"{chosen_delivery_slot_detail=}")
#     if (
#         chosen_delivery_slot_detail["preferred_date"]
#         == chosen_delivery_slot_detail["preferred_time"]
#         == ""
#     ):
#         # set a default chatbot_answer first
#         self.chatbot_answer = "Thank you for your patience. We're currently optimizing our delivery slot allocation. Kindly get in touch with us, and we'll ensure a seamless experience for you."
#         self.sources = ["Database Integration"]
#         db_context = "Database information\n"
#         for db_obj in self.db_objs:
#             db_context = db_helper.get_schema(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#             )
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=[
#                     {
#                         "role": "system",
#                         "content": f"""You prepare SQL queries for extracting delivery slots details on behalf of users. Consider the following database information:\n{db_context}""",
#                     },
#                     {
#                         "role": "user",
#                         "content": f"Tell me the earliest five delivery slots that are available now.",
#                     },
#                     {
#                         "role": "system",
#                         "content": """Now respond only in syntatically correct SQL statements and nothing else (no free text, no small talk).
#     Start your response with SELECT and end your response with ;
#     Use ilike instead of exact matching where possible.""",
#                     },
#                 ],
#                 temperature=0,
#                 stop=";",
#             )
#             query = (
#                 response.choices[0].message.content + ";"
#             )  # add back the semicolon used as stop-sequence
#             print(query)

#             db_data = db_helper.execute_query(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#                 query,
#             )

#             if db_data is None:
#                 print(f"No resolution on {db_obj.label}, trying next database...")
#                 continue

#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=historical_messages
#                 + [
#                     {
#                         "role": "system",
#                         "content": f"""You are a helpful but cautious customer service agent, that will give just enough information to ask the customers which time slots they are prefer to have to delivery their orders, but not more (e.g. driver/company details for privacy/security reasons), though for time-related, please give always both date and time.

#     These are some of the information that you have about the free time slots:
#     {db_data}""",
#                     },
#                 ]
#                 + [
#                     {
#                         "role": "user",
#                         "content": "I want to get my orders delivered. What time slot do you have?",
#                     },
#                 ],
#                 temperature=0,
#             )
#             self.chatbot_answer = response.choices[0].message.content
#             self.sources = [f"Database Integration ({db_obj.label})"]
#             break
#     else:
#         self.chatbot_answer = f"""Got it! We will scheduled it on {chosen_delivery_slot_detail["preferred_date"]} at {chosen_delivery_slot_detail["preferred_time"]}, and you shall receive an email confirmation shortly. Have a nice day ahead!"""
#         # have the date and time
#         pass


# def handle_product_inquiry(self, schema_name, historical_messages, question):
#     response = client.chat.completions.create(
#         model=chat_constant.GPT_35_MODEL_CHOICES[0],
#         messages=historical_messages
#         + [
#             {"role": "user", "content": question},
#             {
#                 "role": "system",
#                 "content": """Now, extract the product name (if available) and product description (if available) from the conversation above and return the information in JSON by updating the JSON {"product_name": "", "product_description": ""} and nothing else (no free text, no small talk).""",
#             },
#         ],
#         temperature=0,
#         stop="}",
#     )
#     json_response = (
#         response.choices[0].message.content + "}"
#     )  # add back the closing bracket used as stop-sequence
#     print(f"{json_response=}")
#     product_details = {"product_name": "", "product_description": ""}
#     try:
#         product_details.update(json.loads(json_response))
#     except:
#         pass
#     product_details["product_name"] = product_details["product_name"].lower()
#     product_details["product_description"] = product_details[
#         "product_description"
#     ].lower()
#     print(f"{product_details=}")
#     if (
#         product_details["product_name"]
#         == product_details["product_description"]
#         == ""
#     ):
#         self.chatbot_answer = "Got it, I'm here to assist you. Could you please provide me with both your invoice number and block number so I can help you check :)"
#     else:
#         # set a default chatbot_answer first
#         self.chatbot_answer = "Sorry, we don't have this product details. Did you key in the details correctly?"
#         self.sources = ["Database Integration"]
#         db_context = "Database information\n"
#         for db_obj in self.db_objs:
#             db_context = db_helper.get_schema(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#             )
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=[
#                     {
#                         "role": "system",
#                         "content": f"""You prepare SQL queries for extracting delivery details on behalf of users. Consider the following database information:\n{db_context}""",
#                     },
#                     {
#                         "role": "user",
#                         "content": f'my product name includes {product_details["product_name"]} OR description related to {product_details["product_description"]}',
#                     },
#                     {
#                         "role": "system",
#                         "content": """Now respond only in syntatically correct SQL statements and nothing else (no free text, no small talk).
# Start your response with SELECT and end your response with ;
# Use ilike instead of exact matching where possible.""",
#                     },
#                 ],
#                 temperature=0,
#                 stop=";",
#             )
#             query = (
#                 response.choices[0].message.content + ";"
#             )  # add back the semicolon used as stop-sequence
#             print(query)

#             db_data = db_helper.execute_query(
#                 db_obj.db_name,
#                 db_obj.username,
#                 db_obj.password,
#                 db_obj.host,
#                 db_obj.port,
#                 query,
#             )

#             if db_data is None:
#                 print(f"No resolution on {db_obj.label}, trying next database...")
#                 continue
#             print(f"{db_data=}")
#             response = client.chat.completions.create(
#                 model=chat_constant.GPT_35_MODEL_CHOICES[0],
#                 messages=historical_messages
#                 + [
#                     {
#                         "role": "system",
#                         "content": f"""You are a helpful but cautious customer service agent, that will give just enough information to satisfy customers, but not more (e.g. driver/company details for privacy/security reasons), though for time-related, please give always both date and time.

# These are some of the information that you have about the customer's delivery:
# {db_data}""",
#                     },
#                 ]
#                 + [
#                     {"role": "user", "content": question},
#                 ],
#                 temperature=0,
#             )
#             self.chatbot_answer = response.choices[0].message.content
#             self.sources = [f"Database Integration ({db_obj.label})"]
#             break
