# Generated by Django 4.2.5 on 2023-09-08 13:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BotSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tone', models.CharField(default='clear, concise, neutral manner, maintaining consistency, politeness, objectivity without the use of slang and humor and personal pronouns', max_length=8912)),
                ('word_count', models.IntegerField(default=30)),
                ('word_per_chat_bubble', models.IntegerField(default=20)),
                ('sentence_count', models.Integer<PERSON>ield(default=3)),
                ('bot_type', models.CharField(choices=[('internal', 'Internal'), ('external', 'External'), ('internal_external', 'Internal and External')], max_length=50)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['bot_type'],
            },
        ),
    ]
