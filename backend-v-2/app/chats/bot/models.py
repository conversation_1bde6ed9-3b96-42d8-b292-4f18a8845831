from django.db import models
from django.contrib.auth import get_user_model
from app.utility import models as utility_models

# Create your models here.

User = get_user_model()


class BotSetting(utility_models.BaseModel):
    """
    You should summarize it in a tone of {chatbot_tone}
    Summarize the following answer into {output_sentence_count} sentences, around {output_word_count} words for each answer.
    """

    tone = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        default="clear, concise, neutral manner, maintaining consistency, politeness, friendly",
    )
    word_count = models.IntegerField(default=30)
    word_per_chat_bubble = models.IntegerField(default=20)
    sentence_count = models.IntegerField(default=3)  # no use
    general_inquiry_guide = models.Char<PERSON>ield(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX,
        blank=True,
        default="-Our objective is to answer customer queries related to our organization.",
    )
    guideline_char_limit = models.IntegerField(default=2000)
    bot_type = models.Char<PERSON>ield(
        max_length=50,
        choices=utility_models.BOT_SETTING_TYPE,
    )
    is_show_branding = models.BooleanField(default=True)

    english_type = models.Char<PERSON>ield(
        max_length=50,
        choices=utility_models.ENGLISH_LANGUAGE_CHOICE,
        default=utility_models.ENGLISH_US,
    )

    color = models.CharField(max_length=15, blank=True)
    image = models.FileField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, null=True, blank=True
    )
    chatbot_name = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    title = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    questions = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # questions.split(";;")

    class Meta:
        ordering = ["bot_type"]
