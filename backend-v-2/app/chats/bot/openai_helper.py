import app.chats.chat.constant as chat_constant

from openai import OpenAI
from openai.types.completion_usage import CompletionUsage


client = OpenAI()


def get_open_ai_client():
    return client


# calculate price:
def openai_api_calculate_cost(
    usage: CompletionUsage, model=chat_constant.GPT_35_MODEL_CHOICES[0], verbose=False
):
    pricing = {
        chat_constant.GPT_35_MODEL_CHOICES[0]: {
            "prompt": 0.0005,
            "completion": 0.0015,
        },
        chat_constant.GPT_35_MODEL_CHOICES[1]: {
            "prompt": 0.0005,
            "completion": 0.0015,
        },
        chat_constant.GPT_4_MODEL_CHOICES[0]: {
            "prompt": 0.03,
            "completion": 0.06,
        },
        chat_constant.GPT_4_MODEL_CHOICES[1]: {
            "prompt": 0.01,
            "completion": 0.03,
        },
        "text-embedding-ada-002-v2": {
            "prompt": 0.0001,
            "completion": 0.0001,
        },
    }

    try:
        model_pricing = pricing[model]
    except KeyError:
        print("Invalid model specified, skipping cost calculation...")
        return 0

    prompt_cost = usage.prompt_tokens * model_pricing["prompt"] / 1000
    completion_cost = usage.completion_tokens * model_pricing["completion"] / 1000

    total_cost = round(prompt_cost + completion_cost, 4)
    if verbose:
        print(
            f"\n{model} >> Tokens used:  {usage.prompt_tokens:,} prompt + {usage.completion_tokens:,} completion = {usage.total_tokens:,} tokens; ${total_cost:.4f}"
        )
    return total_cost


def get_chat_completion_response(model, messages, temperature=0):
    return client.chat.completions.create(
        model=model, messages=messages, temperature=temperature
    )


def get_chat_completion_response_with_gpt_35(messages, temperature=0, model_name=chat_constant.GPT_35_MODEL_CHOICES[0]):
    try:
        chat_completion_response = client.chat.completions.create(
            model=model_name, messages=messages, temperature=temperature
        )
    except:
        model_name = chat_constant.GPT_35_MODEL_CHOICES[1]
        chat_completion_response = client.chat.completions.create(
            model=model_name, messages=messages, temperature=temperature
        )
    return chat_completion_response, model_name


def get_chat_completion_response_with_gpt_4(
    messages, temperature=0, model_name=chat_constant.GPT_4_MODEL_CHOICES[0]
):
    try:
        chat_completion_response = client.chat.completions.create(
            model=model_name, messages=messages, temperature=temperature
        )
    except:
        model_name = chat_constant.GPT_4_MODEL_CHOICES[1]
        chat_completion_response = client.chat.completions.create(
            model=model_name, messages=messages, temperature=temperature
        )
    return chat_completion_response, model_name


# import tiktoken


# def num_tokens_from_messages(messages):
#     """Return the number of tokens used by a list of messages."""
#     encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
#     tokens_per_message = 4  # every message follows <|start|>{role/name}\n{content}<|end|>\n
#     tokens_per_name = -1  # if there's a name, the role is omitted

#     num_tokens = 0
#     for message in messages:
#         num_tokens += tokens_per_message
#         for key, value in message.items():
#             num_tokens += len(encoding.encode(value))
#             if key == "name":
#                 num_tokens += tokens_per_name
#     num_tokens += 3  # every reply is primed with <|start|>assistant<|message|>
#     return num_tokens
