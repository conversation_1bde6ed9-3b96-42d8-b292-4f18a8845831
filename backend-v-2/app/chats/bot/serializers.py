from rest_framework import serializers
import app.chats.bot.models as bot_models
import app.chats.chat_action.models as chat_action_models


class BotSettingSerializer:
    class Get(serializers.ModelSerializer):
        created_at = serializers.DateTimeField()
        questions = serializers.SerializerMethodField()

        def get_questions(self, bot_setting_instance):
            return bot_setting_instance.questions.split(";;")

        email_popup_setting = serializers.SerializerMethodField()

        def get_email_popup_setting(self, bot_setting_instance):
            email_popup_feature_setting_instance = (
                chat_action_models.EmailPopUpFeatureSetting.objects.first()
            )

            email_popup_setting = {
                "is_show_email_popup": False,
                "is_email_popup_compulsory": False,
                "title": "",
                "email_field_label": "",
                "button_label": "",
                "is_display_signup_newsletter_field": True,
                "newsletter_field_label": "",
                "skip_button_label": "",
            }

            if email_popup_feature_setting_instance:
                email_popup_setting = {
                    "is_show_email_popup": email_popup_feature_setting_instance.is_usable(),
                    "is_email_popup_compulsory": email_popup_feature_setting_instance.is_compulsory,
                    "title": email_popup_feature_setting_instance.title,
                    "email_field_label": email_popup_feature_setting_instance.email_field_label,
                    "button_label": email_popup_feature_setting_instance.button_label,
                    "is_display_signup_newsletter_field": email_popup_feature_setting_instance.is_display_signup_newsletter_field,
                    "newsletter_field_label": email_popup_feature_setting_instance.newsletter_field_label,
                    "skip_button_label": email_popup_feature_setting_instance.skip_button_label,
                }

            return email_popup_setting

        class Meta:
            model = bot_models.BotSetting
            fields = [
                "id",
                "tone",
                "word_count",
                "sentence_count",
                "word_per_chat_bubble",
                "general_inquiry_guide",
                "guideline_char_limit",
                "created_by",
                "created_at",
                "bot_type",
                "is_show_branding",
                "english_type",
                "color",
                "image",
                "chatbot_name",
                "title",
                "questions",
                "email_popup_setting",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = bot_models.BotSetting
            fields = [
                "tone",
                "word_count",
                "sentence_count",
                "word_per_chat_bubble",
                "general_inquiry_guide",
                "guideline_char_limit",
                "created_by",
                "bot_type",
                "is_show_branding",
                "english_type",
                "color",
                "image",
                "chatbot_name",
                "title",
                "questions",
            ]
