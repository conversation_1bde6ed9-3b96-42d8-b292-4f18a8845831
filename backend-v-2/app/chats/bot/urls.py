from django.urls import path
import app.chats.bot.views as views

urlpatterns = [
    path("setting/list", views.BotSettingView.List.as_view()),
    path("setting/custom-get", views.BotSettingView.CustomGet.as_view()),
    # path("setting/create", views.BotSettingView.Create.as_view()),
    path("setting/<int:pk>", views.BotSettingView.Get.as_view()),
    path("setting/<int:pk>/update", views.BotSettingView.Update.as_view()),
    # path("setting/<int:pk>/delete", views.BotSettingView.Delete.as_view()),
]
