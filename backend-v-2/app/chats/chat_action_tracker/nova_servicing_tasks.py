import app.chats.chat.models as chat_models
import app.chats.chat_action_tracker.models as chat_action_tracker_models

from tenant_schemas.utils import schema_context

import app.client_specific_files.nova_related.task_helper as nova_related_task_helper

import app.utility.models as utility_models
import app.utility.gcalendar_helper as utility_gcalendar_helper
import app.utility.location_helper as utility_location_helper

from celery import shared_task
from datetime import datetime, timedelta, time, date
from django.utils import timezone
from typing import List
import googleapiclient.errors

import traceback
import logging
import os
import json

logger = logging.getLogger(__name__)

MIN_BUFFER_DAYS = 2
REMINDER_COUNT_TO_WORD_MAPPING = {
    1: "First reminder",
    2: "Second reminder",
    3: "Last reminder",
    4: "Cancel reminder",
}
GCALENDAR_EVENT_PENDING = "[Pending]"
GCALENDAR_EVENT_CONFIRMED = "[Confirmed]"
GCALENDAR_EVENT_APPROVED = "[Approved]"

unassigned_calendar_id = "<EMAIL>"
t1_calendar_id = "<EMAIL>"
t2_calendar_id = "<EMAIL>"


def is_production():
    environment = os.environ.get("ENVIRONMENT", "Local")
    # environment = os.environ.get("ENVIRONMENT", "Production")
    return environment.lower() == "production"


def get_calendar_event_list(
    start_after: str, end_before: str, query: str = ""
) -> List[utility_gcalendar_helper.Event]:

    nova_gcalendar_service_account_key_json = os.environ.get(
        "NOVA_SERVICING_GCALENDAR_SERVICE_ACCOUNT_KEY_JSON"
    )
    if not nova_gcalendar_service_account_key_json:
        logger.error("No nova gcalendar service account key json found.")
        return

    nova_gcalendar_service_account_key_json = json.loads(
        nova_gcalendar_service_account_key_json
    )

    credentials = utility_gcalendar_helper.get_credentials(
        nova_gcalendar_service_account_key_json
    )
    service = utility_gcalendar_helper.get_service(credentials)
    unassigned_calendar = utility_gcalendar_helper.Calendar(
        unassigned_calendar_id, "Service Schedule", service
    )
    t1_calendar = utility_gcalendar_helper.Calendar(
        t1_calendar_id, "T1 Service Schedule", service
    )
    t2_calendar = utility_gcalendar_helper.Calendar(
        t2_calendar_id, "T2 Service Schedule", service
    )

    events = []
    for calendar in [unassigned_calendar, t1_calendar, t2_calendar]:
        events.extend(
            calendar.events.list(
                start_after=start_after,
                end_before=end_before,
                query=query,
            )
        )

    return events


@shared_task(queue="light")  # daily 9am, 3pm
def create_calendar_event_confirmation_reminder():
    """
    Create confirmation messages for the user.
    reminder_stage: The current stage of reminders sent to the user
    """
    if not is_production():
        logger.info(
            "Not in Production environment, skipping create_calendar_event_confirmation_reminder..."
        )
        return

    today = timezone.localtime(timezone.now())
    tmr = today + timedelta(days=1)
    two_days_later = today + timedelta(days=2)

    # get all events from T+1 days to T+2 days (2 days)
    time_min = timezone.make_aware(
        datetime.combine(tmr.date(), time.min), timezone.get_current_timezone()
    ).isoformat()
    time_max = timezone.make_aware(
        datetime.combine(two_days_later.date(), time.max),
        timezone.get_current_timezone(),
    ).isoformat()

    events = get_calendar_event_list(
        start_after=time_min, end_before=time_max, query=GCALENDAR_EVENT_APPROVED
    )

    # 1 day before > 9am reminder (stage 3), 3pm reminder (stage 4, cancel)
    today_date = today.date()
    today_time = today.time()

    to_be_sent_at_today_9am = timezone.localtime(timezone.now()).replace(
        hour=9, minute=0, second=0, microsecond=0
    )  # today 9am
    to_be_sent_at_today_3pm = timezone.localtime(timezone.now()).replace(
        hour=15, minute=0, second=0, microsecond=0
    )  # today 9am

    with schema_context("nova_servicing"):
        third_party_id_mapping_to_conversation_id = {}
        for conversation_instance in chat_models.Conversation.objects.all():
            if conversation_instance.third_party_id:
                third_party_id_mapping_to_conversation_id[
                    conversation_instance.third_party_id
                ] = conversation_instance.id  # contact number

        def get_third_party_id(contact_number):
            for third_party_id in third_party_id_mapping_to_conversation_id.keys():
                if contact_number in third_party_id:
                    # print(f"{contact_number} found in {third_party_id}!")
                    return third_party_id
            return contact_number

        for event in events:
            # f"[{so_number}|{contact_number}|WA:{third_party_id}] Service@{postal_code} [Pending]"
            so_number = str(event.summary).split("|")[0].split("[")[1].strip()
            contact_number = str(event.summary).split("|")[1].strip()
            third_party_contact = (
                str(event.summary).split("WA:")[1].split("]")[0].strip()
            )
            if not third_party_contact:
                third_party_contact = contact_number
            third_party_contact = get_third_party_id(third_party_contact)

            conversation_instance = chat_models.Conversation.objects.filter(
                third_party_id=third_party_contact
            ).first()
            custom_instance = None
            if conversation_instance:
                custom_instance = (
                    chat_action_tracker_models.CustomChatActionTracker.objects.filter(
                        conversation=conversation_instance,
                        status=utility_models.APPROVED,
                    ).first()
                )

            is_confirmed = False
            if custom_instance:
                custom_instance_af = json.loads(custom_instance.additional_fields)
                is_confirmed = (
                    str(custom_instance_af.get("confirmed", "no")).lower() == "yes"
                )

            if custom_instance and is_confirmed:
                # if confirmed, skip (no need to send reminder)
                continue

            start_time_obj = event.get_datetime_obj_from_gcalendar_event_time(
                event.start_time
            )
            start_date = start_time_obj.date()

            # check how much time diff from today
            day_diff = (start_date - today_date).days

            # time_range = (
            #     "2:00pm - 6:00pm" if start_time_obj.hour >= 14 else "10:00am - 2:00pm"
            # )
            # time_range_label = "afternoon" if start_time_obj.hour >= 14 else "morning"
            reminder_message = f"""Sales Order Number: {so_number}\nContact Number: {contact_number}\n\nOur service team will be available on {start_time_obj.date()}. Please confirm by replying "Confirm", if the date works for you. If not, you can easily reschedule through this chat.\n\nThank you!"""

            cancel_message = f"Sales Order Number: {so_number}\nContact Number: {contact_number}\n\nHello! We have not received any confirmation from you for the servicing scheduled on {start_time_obj.date()}, and we'll have to cancel the booking for now. Please let us know whenever you're ready and we can place a booking for you. Thank you!"
            reminder_stage = 1

            # 2 days before > 9am reminder (stage 1), 3pm reminder (stage 2)
            if day_diff == 2:
                # create 2 reminder
                if today_time.hour == 9:
                    chat_action_tracker_models.MessageToSend.objects.create(
                        contact=third_party_contact,
                        message=reminder_message,
                        target_source=utility_models.WHATSAPP_QR,
                        to_be_sent_at=to_be_sent_at_today_9am,
                    )
                    reminder_stage = 1
                    logger.info(
                        f"Reminder Stage {reminder_stage} created for {contact_number}, will be send to {third_party_contact}"
                    )
                else:
                    logger.info(f"day diff is 2, but not 9am.")

                if today_time.hour == 15:
                    chat_action_tracker_models.MessageToSend.objects.create(
                        contact=third_party_contact,
                        message=reminder_message,
                        target_source=utility_models.WHATSAPP_QR,
                        to_be_sent_at=to_be_sent_at_today_3pm,
                    )
                    reminder_stage = 2
                    logger.info(
                        f"Reminder Stage {reminder_stage} created for {contact_number}, will be send to {third_party_contact}"
                    )
                else:
                    logger.info(f"day diff is 2, but not 3pm.")

            else:
                logger.info(
                    f"Day diff is not 2. No reminder created for {contact_number}"
                )

            if day_diff == 1:
                # create 2 reminder
                if today_time.hour == 9:
                    chat_action_tracker_models.MessageToSend.objects.create(
                        contact=third_party_contact,
                        message=reminder_message,
                        target_source=utility_models.WHATSAPP_QR,
                        to_be_sent_at=to_be_sent_at_today_9am,
                    )
                    reminder_stage = 3
                    logger.info(
                        f"Reminder Stage {reminder_stage} created for {contact_number}, will be send to {third_party_contact}"
                    )
                else:
                    logger.info(f"day diff is 1, but not 9am.")

                if today_time.hour == 15:
                    # cancel message
                    chat_action_tracker_models.MessageToSend.objects.create(
                        contact=third_party_contact,
                        message=cancel_message,
                        target_source=utility_models.WHATSAPP_QR,
                        to_be_sent_at=to_be_sent_at_today_3pm,
                    )
                    # trigger reject
                    if custom_instance:
                        try:
                            function_environment = {}
                            exec(
                                custom_instance.custom_chat_action_setting.on_reject,
                                function_environment,
                            )
                            function_environment["on_reject"](custom_instance.id)
                            logger.info(
                                "on_reject executed correctly upon sending rejection msg"
                            )
                        except Exception as e:
                            logger.error("Error when try to execute on_reject", e)

                    reminder_stage = 4
                    logger.info(
                        f"Reminder & Cancel Stage {reminder_stage} created for {contact_number}, will be send to {third_party_contact}"
                    )
                else:
                    logger.info(f"day diff is 1, but not 3pm.")
            else:
                logger.info(
                    f"Day diff is not 1. No reminder created for {contact_number}"
                )

            if custom_instance:
                custom_instance_af["reminded"] = REMINDER_COUNT_TO_WORD_MAPPING[
                    reminder_stage
                ]
                custom_instance.additional_fields = json.dumps(custom_instance_af)
                custom_instance.save()

    logger.info("create_calendar_event_confirmation_reminder done.")


@shared_task(queue="light")  # daily 6am
def create_calendar_event_confirmed_reminder():
    """
    Create reminder messages for the servicing on that day to the customer via WhatsApp.
    """
    if not is_production():
        logger.info(
            "Not in Production environment, skipping create_calendar_event_confirmed_reminder..."
        )
        return
    today = timezone.localtime(timezone.now())
    time_min = timezone.make_aware(
        datetime.combine(today.date(), time.min), timezone.get_current_timezone()
    ).isoformat()
    time_max = timezone.make_aware(
        datetime.combine(today.date(), time.max), timezone.get_current_timezone()
    ).isoformat()

    events = get_calendar_event_list(
        start_after=time_min, end_before=time_max, query=GCALENDAR_EVENT_CONFIRMED
    )

    with schema_context("nova_servicing"):
        third_party_id_mapping_to_conversation_id = {}
        for conversation_instance in chat_models.Conversation.objects.all():
            if conversation_instance.third_party_id:
                third_party_id_mapping_to_conversation_id[
                    conversation_instance.third_party_id
                ] = conversation_instance.id  # contact number

        def get_third_party_id(contact_number):
            for third_party_id in third_party_id_mapping_to_conversation_id.keys():
                if contact_number in third_party_id:
                    return third_party_id
            return contact_number

        for event in events:
            # happening events
            # f"[{so_number}|{contact_number}|WA:{third_party_id}] Service@{postal_code} [Pending]"
            so_number = str(event.summary).split("|")[0].split("[")[1].strip()
            contact_number = str(event.summary).split("|")[1].strip()
            third_party_contact = (
                str(event.summary).split("WA:")[1].split("]")[0].strip()
            )
            if not third_party_contact:
                third_party_contact = contact_number
            third_party_contact = get_third_party_id(third_party_contact)

            start_time_obj = event.get_datetime_obj_from_gcalendar_event_time(
                event.start_time
            )

            # start_time_string = start_time_obj.strftime("%-I %p (%d/%m/%Y)")
            # time_range = (
            #     "2:00pm - 6:00pm" if start_time_obj.hour >= 14 else "10:00am - 2:00pm"
            # )
            if contact_number in third_party_contact:  # xxxxxxxx and 65xxxxxxxx
                # Means that it's the same number used to purchase
                arriving_reminder_message = f"Hello! Just a heads-up! Our service team will be visiting you today."
            else:
                # Means it's someone else that help to create the service request
                arriving_reminder_message = f"SO Number: {so_number}\nContact Number: {contact_number}\nHello! Just a heads-up! Our service team will be visiting today."

            to_be_sent_at = start_time_obj - timedelta(hours=1)
            chat_action_tracker_models.MessageToSend.objects.create(
                contact=third_party_contact,
                message=arriving_reminder_message,
                target_source=utility_models.WHATSAPP_QR,
                to_be_sent_at=to_be_sent_at,
            )

            logger.info(
                f"Created confirmed reminder for {contact_number} to be sent at {to_be_sent_at}"
            )
    logger.info("create_calendar_event_confirmed_reminder done.")


def get_sorted_calendar_events_with_coor(
    calendar: utility_gcalendar_helper.Calendar,  # utility_gcalendar_helper.Calendar
    start_point,
    time_min,
    time_max,
):
    # Morning Session
    # Create the timeMin and timeMax for the whole day in RFC3339 format
    event_raw_result = (
        calendar.service.events()
        .list(
            calendarId=calendar.id,
            timeMin=time_min,
            timeMax=time_max,
            singleEvents=True,
            orderBy="startTime",
        )
        .execute()
    )
    event_list = event_raw_result.get("items", [])
    if not event_list:
        print(f"No events found in calendar {calendar.id}.")
        return

    # Get coordinates for each event CONTINUE HERE
    event_coords = []
    for event in event_list:
        postal_code = (
            str(event["summary"]).split("@")[1].split("[")[0].strip()
        )  # Assuming postal code is stored in location
        lat, lon = utility_location_helper.get_coordinates(postal_code)
        if lat is not None and lon is not None:
            event_coords.append((event, (lat, lon)))

    if not event_coords:
        print("No valid coordinates found for any events in morning.")
        return []

    # Sort events based on distance from the start point
    event_coords.sort(
        key=lambda x: utility_location_helper.calculate_distance(start_point, x[1])
    )

    return event_coords


def update_calendar_events(
    event_coords,
    current_time,
    slot_duration,
    gap_duration,
    calendar: utility_gcalendar_helper.Calendar,
):
    if not event_coords:
        logger.info("No events found in event_coords.")
        return
    for event, _ in event_coords:
        new_start_time = current_time
        new_end_time = new_start_time + timedelta(minutes=slot_duration)

        event["start"]["dateTime"] = new_start_time.isoformat()
        event["end"]["dateTime"] = new_end_time.isoformat()

        try:
            updated_event = (
                calendar.service.events()
                .update(
                    calendarId=calendar.id,
                    eventId=event["id"],
                    body=event,
                )
                .execute()
            )
            print(
                f"Event updated: {updated_event['summary']} to {new_start_time.isoformat()} - {new_end_time.isoformat()}"
            )

            current_time = new_end_time + timedelta(minutes=gap_duration)

        except Exception as e:
            logger.error(f"Error when updating event: {e}")


def optimize_calendar_events(
    calendar: utility_gcalendar_helper.Calendar,  # utility_gcalendar_helper.Calendar
    specific_date: date,
    slot_duration=40,
    gap_duration=20,
    start_from="West",  # Either "West" or "East"
):
    # Define starting points for West and East
    if start_from == "West":
        start_point = (1.3521, 103.6825)
    else:
        start_point = (1.3521, 103.9440)
    try:
        # Sort events based on distance from the start point
        morning_time_min = timezone.make_aware(
            datetime.combine(specific_date, time.min), timezone.get_current_timezone()
        ).isoformat()
        morning_time_max = timezone.make_aware(
            datetime.combine(specific_date, time(14, 0)),
            timezone.get_current_timezone(),
        ).isoformat()
        morning_event_coords = get_sorted_calendar_events_with_coor(
            calendar,
            start_point,
            morning_time_min,
            morning_time_max,  # utility_gcalendar_helper.Calendar
        )

        ##Afternoon
        afternoon_time_min = timezone.make_aware(
            datetime.combine(specific_date, time(14, 0)),
            timezone.get_current_timezone(),
        ).isoformat()
        afternoon_time_max = timezone.make_aware(
            datetime.combine(specific_date, time.max),
            timezone.get_current_timezone(),
        ).isoformat()

        afternoon_event_coords = get_sorted_calendar_events_with_coor(
            calendar,
            start_point,
            afternoon_time_min,
            afternoon_time_max,  # utility_gcalendar_helper.Calendar
        )

        morning_start_time = timezone.make_aware(
            datetime.combine(specific_date, time(10, 0))
        )
        afternoon_start_time = timezone.make_aware(
            datetime.combine(specific_date, time(14, 0))
        )

        logger.info("Updating Morning Events...")
        update_calendar_events(
            morning_event_coords,
            morning_start_time,
            slot_duration,
            gap_duration,
            calendar,
        )
        logger.info("Updated Morning Events!")

        logger.info("Updating Afternoon Events...")
        update_calendar_events(
            afternoon_event_coords,
            afternoon_start_time,
            slot_duration,
            gap_duration,
            calendar,
        )
        logger.info("Updated Afternoon Events!")

        logger.info(f"Optimization finished for calendar {calendar.id}!")
    except:
        traceback.print_exc()


@shared_task(queue="light")  # daily 6am
def optimize_calendar():
    # if not is_production():
    #     logger.info("Not in Production environment, skipping optimize_calendar...")
    #     return
    today = timezone.localtime(timezone.now())
    # get events from T+2 days
    specific_date = (today + timedelta(days=2)).date()

    nova_gcalendar_service_account_key_json = os.environ.get(
        "NOVA_SERVICING_GCALENDAR_SERVICE_ACCOUNT_KEY_JSON"
    )
    if not nova_gcalendar_service_account_key_json:
        logger.error("No nova gcalendar service account key json found")
        return

    nova_gcalendar_service_account_key_json = json.loads(
        nova_gcalendar_service_account_key_json
    )
    credentials = utility_gcalendar_helper.get_credentials(
        nova_gcalendar_service_account_key_json
    )
    service = utility_gcalendar_helper.get_service(credentials)
    unassigned_calendar = utility_gcalendar_helper.Calendar(
        unassigned_calendar_id, "Service Schedule", service
    )
    t1_calendar = utility_gcalendar_helper.Calendar(
        t1_calendar_id, "T1 Service Schedule", service
    )
    t2_calendar = utility_gcalendar_helper.Calendar(
        t2_calendar_id, "T2 Service Schedule", service
    )
    try:
        optimize_calendar_events(unassigned_calendar, specific_date)
        optimize_calendar_events(t1_calendar, specific_date)
        optimize_calendar_events(t2_calendar, specific_date)
        logger.info("optimize_calendar_events done.")
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error in optimize_calendar_events: {e}")


import time as time_


# Nova Tasks Globe3
@shared_task()
def run_update_globe3_task():
    if not is_production():
        logger.info("Not in Production environment, skipping run_update_globe3_task...")
        return
    try:

        start_time = time_.time()
        print(f"Start run_update_globe3_task...")
        from_date_str, to_date_str = nova_related_task_helper.get_from_and_to_date_str()
        data, columns = nova_related_task_helper.get_globe3_report_columns_and_data(
            from_date_str, to_date_str
        )
        # TODO-yc: Update the copy of the sheet to avoid downtime.
        nova_related_task_helper.update_gsheet(data, columns)
        nova_related_task_helper.update_database(data, columns, from_date_str)
        logger.info(
            f"run_update_globe3_task done within {time_.time() - start_time :2f} seconds."
        )
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error in run_update_globe3_task: {e}")
        raise e
