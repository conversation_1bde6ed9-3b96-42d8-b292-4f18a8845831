import app.chats.bot.intent_handler as bot_intent_handler
import app.chats.bot.template_helper as bot_template_helper

import app.chats.bot.openai_helper as bot_openai_helper

import app.chats.chat.models as chat_models
import app.chats.chat_action_tracker.models as chat_action_tracker_models

import app.utility.date_helper as utility_date_helper
import app.utility.general_helper as utility_general_helper

import json


def check_if_is_missing_info(
    conversation_id,
    historical_messages,
    incoming_message,
    intro,
    tenant_name,
    general_inquiry_guide,
    context,
    retriever_content_base_on_answer="",
):
    today = utility_date_helper.get_today_str()

    # try targeting the question instead of question answer
    check_is_missing_info_message = (
        historical_messages
        + [
            {
                "role": "user",
                "content": utility_general_helper.clean_up_prompt(
                    f"""
                    Last Customer Message: {incoming_message}; 
                    """
                ),
            }
        ]
        + [
            {
                "role": "system",
                "content": utility_general_helper.clean_up_prompt(
                    f"""
                The datetime now is {today}. {intro}. You have an conversation with your customer previously, and now you are a query-knowledge base compatibility assesser working for {tenant_name}.
                You are to study the conversation so far, then determine whether based on the Knowledge Base to you, are you able to provide informative answer to reply the Last Customer Message from the customer.
                Your task is to check whether there's information deficiency, using the following rules: 

                - If Last Customer Message is widely recognized or constitutes common knowledge, such as well-known fact (like "The sky is blue" or "Water boils at 100 degrees Celsius"), or frequently asked question (like "What's the time?" or "How's the weather?"), then set the JSON values for "is_missing_information" to false, even if this common information does not explicitly appear in the General Guide or Information Context.
                
                == Knowledge Base (CHECK THIS CAREFULLY) ==
                {general_inquiry_guide}
                {retriever_content_base_on_answer}
                {context}
                """
                ),
            }
        ]
        + [
            {
                "role": "user",
                "content": utility_general_helper.clean_up_prompt(
                    f"""Return the answer in JSON format by updating:
                {{"is_missing_information": <true/false>,  "is_missing_information_reason":"<why do you think the Knowledge Bank are not sufficient>"}} and nothing else (no free text, no small talk).
                """
                ),
            }
        ]
    )

    # print(json.dumps(check_is_missing_info_message, indent=2))
    (
        check_is_missing_info_message_response,
        _,
    ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
        check_is_missing_info_message
    )
    # print(f"{check_is_missing_info_message_response.choices[0].message.content=}")

    # self.cost += bot_openai_helper.openai_api_calculate_cost(
    #     check_is_missing_info_message_response.usage,
    #     model=check_is_missing_info_message_model_name,
    # )

    check_is_missing_info_message_result = {
        "is_missing_information": False,
        "is_missing_information_reason": "",
    }
    check_is_missing_info_message_str = check_is_missing_info_message_response.choices[
        0
    ].message.content

    check_is_missing_info_json = utility_general_helper.extract_json_from_str(
        check_is_missing_info_message_str
    )

    try:
        check_is_missing_info_message_result.update(
            json.loads(check_is_missing_info_json)
        )
    except Exception as e:
        print(
            "can't load json of check_is_missing_info_message_response: ",
            check_is_missing_info_message_str,
            "with error: ",
            e,
        )

    is_missing_information = check_is_missing_info_message_result.get(
        "is_missing_information", False
    )
    is_missing_information_reason = check_is_missing_info_message_result.get(
        "is_missing_information_reason", False
    )
    if is_missing_information:
        print(
            f'Missing information detected on question "{incoming_message}". Recorded!'
        )
        # print(f"Original answer: {chatbot_answer}")
        conversation_instance = chat_models.Conversation.objects.get(id=conversation_id)
        if is_missing_information and is_missing_information_reason:
            chat_action_tracker_models.MissingInfoAction.objects.create(
                conversation=conversation_instance,
                message=conversation_instance.messages.first(),
                question=incoming_message,
                reason=f"{is_missing_information_reason}",
            )

    return is_missing_information


# # LEGACY
# def get_appointment_conversation_summarized_info(
#     self: "bot_intent_handler.HandleQuestionIntentClass",
# ):
#     appointment_conversation_summarized_info_message = self.historical_messages + [
#         {
#             "role": "system",
#             "content": f"""{self.intro}. From the previous conversation, I need to know: 1. What service(s) or product(s) is the customer interested in? 2. Did we promise any special offers or vouchers during this interaction? 3. Any other important notes or preferences mentioned by the customer? Keep the answer concise and update the JSON, {{"products":"", "vouchers":"", "other":""}} and nothing else (no free text, no small talk). Each answer should not exceed 5 words.""",
#         },
#         {
#             "role": "user",
#             "content": bot_template_helper.get_general_prompt_template(
#                 self.chat_language,
#                 self.question,
#                 self.tenant_name,
#                 extra_instruction=f"""Do not ask more question. Do not include customer info, appointment date or time. Extract what is available from the conversation above and return the information in a JSON format. Each answer should not exceed 5 words. If "other" is about visiting showroom, leave blank""",
#             ),
#         },
#     ]

#     (
#         appointment_conversation_summarized_info_response,
#         appointment_conversation_summarized_info_model_name,
#     ) = bot_openai_helper.get_chat_completion_response_with_gpt_35(
#         appointment_conversation_summarized_info_message
#     )
#     self.cost += bot_openai_helper.openai_api_calculate_cost(
#         appointment_conversation_summarized_info_response.usage,
#         model=appointment_conversation_summarized_info_model_name,
#     )

#     appointment_conversation_summarized_info = {
#         "products": "",
#         "vouchers": "",
#         "other": "",
#     }
#     try:
#         appointment_conversation_summarized_info.update(
#             json.loads(
#                 appointment_conversation_summarized_info_response.choices[
#                     0
#                 ].message.content
#             )
#         )
#     except Exception as e:
#         print(
#             "error loading appointment_conversation_summarized_info_response to json:",
#             appointment_conversation_summarized_info_response.choices[
#                 0
#             ].message.content,
#         )

#     return appointment_conversation_summarized_info
