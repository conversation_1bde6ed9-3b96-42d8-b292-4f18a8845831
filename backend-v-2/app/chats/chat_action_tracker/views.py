import app.core.authapi.permission as authapi_permission
import app.core.authapi.helper as authapi_helper

import app.chats.chat.models as chat_models
import app.chats.chat_action.models as chat_action_models
import app.chats.chat_action_tracker.models as chat_action_tracker_models
import app.chats.chat_action_tracker.serializers as chat_action_tracker_serializers
import app.chats.chat_action_tracker.query_helper as chat_action_tracker_query_helper

import app.core.tenants.models as tenants_models

import app.utility.common_class as utility_common_class
import app.utility.date_helper as utility_date_helper
import app.utility.models as utility_models
import app.utility.general_helper as utility_general_helper


from django.http import HttpResponse
from django.utils import timezone
from django.contrib.auth import get_user_model

from datetime import datetime, timedelta

from rest_framework.response import Response
from rest_framework import generics, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.views import APIView

import csv

import requests
import os

import re
import time
import pytz

from backend.settings import DEFAULT_FROM_EMAIL
from app.core.authapi.helper import (
    send_user_email_with_html_template,
    encrypt_data,
    decrypt_data,
)


class ChatActionTrackerView:
    class ChatActionTrackerBaseView(generics.GenericAPIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class GetCount(ChatActionTrackerBaseView, APIView):
        def get(self, request, *args, **kwargs):
            appointment_count = chat_action_tracker_models.Appointment.objects.filter(
                status=utility_models.PENDING
            ).count()

            customer_support_count = (
                chat_action_tracker_models.CustomerSupport.objects.filter(
                    status=utility_models.PENDING
                ).count()
            )

            call_list_count = chat_action_tracker_models.CallListAction.objects.filter(
                status=utility_models.PENDING
            ).count()

            missing_info_count = (
                chat_action_tracker_models.MissingInfoAction.objects.filter(
                    status=utility_models.PENDING
                ).count()
            )
            custom_chat_action_dict = {}
            custom_chat_action_setting_instances = (
                chat_action_models.CustomChatActionSetting.objects.all()
            )
            total = 0
            for (
                custom_chat_action_setting_instance
            ) in custom_chat_action_setting_instances:
                custom_chat_action_key = (
                    utility_general_helper.convert_to_snake_case(
                        custom_chat_action_setting_instance.name
                    )
                    + "_count"
                )
                custom_chat_action_count = (
                    chat_action_tracker_models.CustomChatActionTracker.objects.filter(
                        custom_chat_action_setting=custom_chat_action_setting_instance,
                        status=utility_models.PENDING,
                    ).count()
                )
                custom_chat_action_dict[custom_chat_action_key] = (
                    custom_chat_action_count
                )
                total += custom_chat_action_count

            total += (
                appointment_count
                + customer_support_count
                + call_list_count
                + missing_info_count
            )

            return Response(
                {
                    "total": total,
                    "appointment_count": appointment_count,
                    "customer_support_count": customer_support_count,
                    "callback_request_count": call_list_count,
                    "missing_information_count": missing_info_count,
                    **custom_chat_action_dict,
                }
            )


class AppointmentView:
    class AppointmentBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.Appointment.objects.all()
        serializer_class = chat_action_tracker_serializers.AppointmentSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(AppointmentBaseView, generics.ListAPIView):
        pagination_class = utility_common_class.CustomPageNumberPagination
        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "email",
            "contact",
            "location",
            "datetime",
            "status",
            "created_at",
            "updated_at",
        ]
        ordering_fields = [
            "email",
            "contact",
            "location",
            "datetime",
            "status",
            "created_at",
            "updated_at",
        ]
        ordering = ["-created_at"]

        def get_queryset(self):
            queryset = super().get_queryset()

            # dates
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")

            if start_datetime and end_datetime:
                start_date = (
                    utility_date_helper.convert_datetime_to_desired_timezone_datetime(
                        timezone.make_aware(
                            datetime.strptime(
                                start_datetime, utility_date_helper.FE_DATE_TIME_FORMAT
                            ),
                            timezone=timezone.utc,
                        )
                    ).date()
                )

                end_date = (
                    utility_date_helper.convert_datetime_to_desired_timezone_datetime(
                        timezone.make_aware(
                            datetime.strptime(
                                end_datetime, utility_date_helper.FE_DATE_TIME_FORMAT
                            ),
                            timezone=timezone.utc,
                        )
                        + timedelta(minutes=1)
                    ).date()
                )
                queryset = queryset.filter(datetime__range=[start_date, end_date])

            # status
            status_list = [
                status
                for status in self.request.query_params.get("status", "").split(",")
                if status
            ]
            if status_list:
                queryset = queryset.filter(status__in=status_list)

            # location
            location_list = [
                location
                for location in self.request.query_params.get("locations", "").split(
                    ","
                )
                if location
            ]
            if location_list:
                queryset = queryset.filter(
                    appointment_chat_feature_location__id__in=location_list
                )

            return queryset.filter(datetime__isnull=False)

    class Update(AppointmentBaseView, generics.UpdateAPIView):
        # When update, need to update messages to send too if they are pending
        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)
            latest_status = request.data["status"]
            # if appt status updated, then no need to send the "message to send" anymore. No U-turn for now.
            if latest_status != utility_models.PENDING:
                appointment_instance = self.get_object()
                message_to_send_instances = (
                    appointment_instance.messages_to_send.filter(
                        status=utility_models.PENDING
                    )
                )
                for message_to_send_instance in message_to_send_instances:
                    message_to_send_instance.status = utility_models.APPT_UPDATED
                    message_to_send_instance.save()

            return super().patch(request, *args, **kwargs)

    class Delete(AppointmentBaseView, generics.DestroyAPIView):
        pass

    class GetAppointmentCsvFile(List, APIView):
        def get(self, request, *args, **kwargs):
            appointment_instances = super().get_queryset()

            # Export to csv
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                f'attachment; filename="appointment_list_{timezone.now().strftime("%d.%m.%Y")}.csv"'
            )

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Code",
                    "Name",
                    "Email",
                    "Contact",
                    "Location",
                    "Appt Date",
                    "Conversation",
                    "Status",
                    "Remarks",
                    "Created At",
                    "Last Updated",
                ]
            )

            for appointment_instance in appointment_instances:
                writer.writerow(
                    [
                        appointment_instance.code,
                        appointment_instance.name,
                        appointment_instance.email,
                        appointment_instance.contact,
                        appointment_instance.location,
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            appointment_instance.datetime
                        ),
                        appointment_instance.conversation.id,
                        appointment_instance.status,
                        (
                            appointment_instance.remarks
                            if appointment_instance.remarks
                            else "-"
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            appointment_instance.created_at
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            appointment_instance.updated_at
                        ),
                    ]
                )

            return response

    class GetByCode(AppointmentBaseView, generics.RetrieveAPIView):
        permission_classes = [AllowAny]
        lookup_field = "code"

    class UpdateByCode(AppointmentBaseView, generics.UpdateAPIView):
        permission_classes = [AllowAny]
        lookup_field = "code"


class CustomerSupportView:
    class CustomerSupportBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.CustomerSupport.objects.all()
        serializer_class = chat_action_tracker_serializers.CustomerSupportSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(CustomerSupportBaseView, generics.ListAPIView):
        pagination_class = utility_common_class.CustomPageNumberPagination
        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "email",
            "invoice_number",
            "complaint_description",
            "status",
            "created_at",
            "updated_at",
        ]
        ordering_fields = [
            "email",
            "invoice_number",
            "complaint_description",
            "status",
            "created_at",
            "updated_at",
        ]
        ordering = ["-created_at"]

    # WIP
    # class ListAll(List):
    #     User = get_user_model()
    #     queryset = chat_action_tracker_models.CustomerSupport.objects.all()

    class Update(CustomerSupportBaseView, generics.UpdateAPIView):
        pass

    class Delete(CustomerSupportBaseView, generics.DestroyAPIView):
        pass

    class GetCustomerSupportCsvFile(List, APIView):
        def get(self, request, *args, **kwargs):
            """
            Return:
            CSV: Full name, Email, Role
            """
            customer_support_instances = super().get_queryset()

            # Export to csv
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                f'attachment; filename="customer_support_list_{timezone.now().strftime("%d.%m.%Y")}.csv"'
            )

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Name",
                    "Email",
                    "Contact",
                    "Invoice Number",
                    "Complaint Desc",
                    "Conversation",
                    "Status",
                    "Remarks",
                    "Created At",
                    "Last Updated",
                ]
            )

            for customer_support_instance in customer_support_instances:
                writer.writerow(
                    [
                        customer_support_instance.name,
                        customer_support_instance.email,
                        customer_support_instance.contact,
                        customer_support_instance.invoice_number,
                        customer_support_instance.complaint_description,
                        customer_support_instance.conversation.id,
                        customer_support_instance.status,
                        (
                            customer_support_instance.remarks
                            if customer_support_instance.remarks
                            else "-"
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            customer_support_instance.created_at
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            customer_support_instance.updated_at
                        ),
                    ]
                )

            return response


class CallListActionView:
    class CallListActionBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.CallListAction.objects.all().order_by(
            "-created_at"
        )
        serializer_class = chat_action_tracker_serializers.CallListActionSerializer
        permission_classes = [IsAuthenticated]

    class List(CallListActionBaseView, generics.ListAPIView):
        pagination_class = utility_common_class.CustomPageNumberPagination

        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = ["email", "created_at", "status"]
        ordering_fields = ["email", "status", "contact"]
        ordering = ["-created_at"]

    class Create(CallListActionBaseView, generics.CreateAPIView):
        pass

    class Get(CallListActionBaseView, generics.RetrieveAPIView):
        pass

    class Update(CallListActionBaseView, generics.UpdateAPIView):
        pass

    class Delete(CallListActionBaseView, generics.DestroyAPIView):
        pass

    class GetCsv(List, APIView):
        def get(self, request, *args, **kwargs):
            queryset = self.filter_queryset(self.get_queryset())

            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                f'attachment; filename="callback_list_{timezone.now().strftime("%d.%m.%Y")}.csv"'
            )

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Name",
                    "Email",
                    "Contact",
                    "Reason",
                    "Conversation",
                    "Status",
                    "Remarks",
                    "Created At",
                    "Last Updated",
                ]
            )

            callback_action_data = self.get_serializer(queryset, many=True).data

            for callback_action_instance_data in callback_action_data:
                writer.writerow(
                    [
                        callback_action_instance_data["name"],
                        callback_action_instance_data["email"],
                        callback_action_instance_data["contact"],
                        callback_action_instance_data["context"],
                        callback_action_instance_data["conversation"],
                        callback_action_instance_data["status"],
                        (
                            callback_action_instance_data["remarks"]
                            if callback_action_instance_data["remarks"]
                            else "-"
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            utility_date_helper.convert_created_at_str_to_datetime_object(
                                callback_action_instance_data["created_at"]
                            )
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            utility_date_helper.convert_created_at_str_to_datetime_object(
                                callback_action_instance_data["updated_at"]
                            )
                        ),
                    ]
                )

            return response


class MissingInfoActionView:
    class MissingInfoActionBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.MissingInfoAction.objects.all().order_by(
            "-created_at"
        )
        serializer_class = chat_action_tracker_serializers.MissingInfoActionSerializer
        permission_classes = [IsAuthenticated]

    class List(MissingInfoActionBaseView, generics.ListAPIView):
        pass

    class Create(MissingInfoActionBaseView, generics.CreateAPIView):
        pass

    class Get(MissingInfoActionBaseView, generics.RetrieveAPIView):
        pass

    class Update(MissingInfoActionBaseView, generics.UpdateAPIView):
        pass

    class Delete(MissingInfoActionBaseView, generics.DestroyAPIView):
        pass

    class GetMissingInfoCsvFile(List, APIView):
        def get(self, request, *args, **kwargs):
            """
            Return:
            CSV: Question, Reason, Status, Remarks, Conversation, Created At
            """
            missing_info_instances = super().get_queryset()

            # Export to csv
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                f'attachment; filename="missing_info_list_{timezone.now().strftime("%d.%m.%Y")}.csv"'
            )

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Question",
                    "Reason",
                    "Conversation",
                    "Status",
                    "Remarks",
                    "Created At",
                    "Updated At",
                ]
            )

            for missing_info_instance in missing_info_instances:
                writer.writerow(
                    [
                        missing_info_instance.question,
                        missing_info_instance.reason,
                        missing_info_instance.conversation.id,
                        missing_info_instance.status,
                        (
                            missing_info_instance.remarks
                            if missing_info_instance.remarks
                            else "-"
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            missing_info_instance.created_at
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            missing_info_instance.updated_at
                        ),
                    ]
                )

            return response


class MessageToSendView:

    class MessageToSendBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.MessageToSend.objects.all()
        serializer_class = chat_action_tracker_serializers.MessageToSendSerializer
        # permission_classes = [
        #     IsAuthenticated,
        #     authapi_permission.IsAdminInSameTenant,
        # ]

    # TODO: change whatsapp such that it call the ListForThirdPartyApp instead of List
    # This should only for this application usage
    class List(MessageToSendBaseView, generics.ListAPIView):
        pagination_class = utility_common_class.CustomPageNumberPagination
        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "contact",
            "message",
            "status",
            "message_type",
            "to_be_sent_at",
            "created_at",
            "updated_at",
        ]
        ordering_fields = [
            "contact",
            "message",
            "status",
            "to_be_sent_at",
            "created_at",
            "updated_at",
        ]
        ordering = ["-to_be_sent_at"]

        def get_queryset(self):
            queryset = super().get_queryset()
            status_list = [
                status
                for status in self.request.query_params.get("status", "").split(",")
                if status
            ]
            if status_list:
                queryset = queryset.filter(status__in=status_list)

            return queryset

    class ListScheduledMessages(MessageToSendBaseView, generics.ListAPIView):
        def get_queryset(self):
            queryset = super().get_queryset()
            return queryset.filter(message_type=utility_models.SCHEDULED_TEXT)

    class Get(MessageToSendBaseView, generics.RetrieveAPIView):
        pass

    class Create(MessageToSendBaseView, generics.CreateAPIView):
        def post(self, request, *args, **kwargs):
            files = request.FILES.getlist("attachments")
            files_to_create = []
            if files:
                for file in files:
                    files_to_create.append(
                        chat_action_tracker_models.MessageToSendAttachment(
                            file=file,
                            attachment_type=utility_general_helper.categorize_file(
                                file.name
                            ),
                        )
                    )

            res = super().post(request, *args, **kwargs)
            if files:
                message_to_send_instance = (
                    chat_action_tracker_models.MessageToSend.objects.get(
                        id=res.data["id"]
                    )
                )
                for file_to_create in files_to_create:
                    file_to_create.message_to_send = message_to_send_instance
                    file_to_create.save()
            return res

    class Update(MessageToSendBaseView, generics.UpdateAPIView):
        pass

    class Delete(MessageToSendBaseView, generics.DestroyAPIView):
        pass

    class ListForWhatsAppQR(MessageToSendBaseView, generics.ListAPIView):
        permission_classes = [AllowAny]

        def get(self, request, *args, **kwargs):
            self.queryset = (
                self.queryset.filter(
                    status=utility_models.PENDING,
                    target_source=utility_models.WHATSAPP_QR,
                    to_be_sent_at__lte=timezone.now(),
                )
                # .exclude(message="")
                .order_by("-created_at")
            )

            schema_name = authapi_helper.get_schema_name()
            tenant_instance = tenants_models.Tenant.objects.get(schema_name=schema_name)
            if tenant_instance.test_contact:
                # modify the "contact" field to the test contact number
                for message_to_send_instance in self.queryset:
                    message_to_send_instance.contact = tenant_instance.test_contact

            return super().get(request, *args, **kwargs)

    class UpdateForWhatsAppQR(MessageToSendBaseView, generics.UpdateAPIView):
        permission_classes = [AllowAny]

        def patch(self, request, *args, **kwargs):
            try:
                instance: chat_action_tracker_models.MessageToSend = self.get_object()

                # Save original data for later use
                original_status = instance.status

                # Update the instance with request data first
                serializer = self.get_serializer(
                    instance, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                self.perform_update(serializer)

                # only three possible message_type here: PAGE_ADMIN_TEXT // MACHINE_TEXT // SCHEDULED_TEXT
                if (
                    (
                        instance.message_type == utility_models.MACHINE_TEXT
                        or instance.message_type == utility_models.SCHEDULED_TEXT
                    )
                    and request.data.get("status") == utility_models.COMPLETED
                    and original_status != utility_models.COMPLETED
                ):
                    # it's a reminder, create a msg under the conversation
                    conversations = []

                    # Check if uu_message exists
                    message_instance = getattr(instance, "uu_message", None)
                    if message_instance and hasattr(message_instance, "conversation"):
                        conversations = [message_instance.conversation]

                    if not conversations:
                        # If no conversation found and contact exists
                        if instance.contact:
                            contacts = instance.contact.split("|")
                            # if conversation doesn't exist in msg to send, try getting via source + third_party-id
                            for contact in contacts:
                                contact = contact.strip()
                                if contact:  # Only process non-empty contacts
                                    conversation, _ = (
                                        chat_models.Conversation.objects.get_or_create(
                                            source=utility_models.WHATSAPP_QR,
                                            third_party_id=contact,
                                        )
                                    )
                                    conversations.append(conversation)

                    # Create messages for each conversation
                    for conversation in conversations:
                        message = chat_models.Message.objects.create(
                            conversation=conversation,
                            message=instance.message,
                            message_type=instance.message_type,
                            third_party_id=instance.id,
                        )

                        # Link the message back to the MessageToSend instance if no uu_message exists
                        if (
                            not hasattr(instance, "uu_message")
                            or not instance.uu_message
                        ):
                            instance.uu_message = message
                            instance.save(update_fields=["uu_message"])

                        # Process attachments if any
                        message_to_send_attachment_instances = (
                            instance.attachments.all()
                        )
                        if message_to_send_attachment_instances:
                            for (
                                message_to_send_attachment_instance
                            ) in message_to_send_attachment_instances:
                                chat_models.Attachment.objects.create(
                                    message=message,
                                    file=message_to_send_attachment_instance.file,
                                    attachment_type=message_to_send_attachment_instance.attachment_type,
                                )

                # PAGE ADMIN TEXT already created when it first sent.
                if (
                    instance.message_type == utility_models.PAGE_ADMIN_TEXT
                    and request.data.get("third_party_id")
                ):
                    message_instance = getattr(instance, "uu_message", None)
                    if message_instance:
                        message_instance.third_party_id = request.data.get(
                            "third_party_id"
                        )
                        message_instance.save(update_fields=["third_party_id"])

                return Response(serializer.data)

            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    class DeleteAttachment(APIView):
        def post(self, request):
            attachment_ids = request.data.get("attachment_ids")
            print(attachment_ids)
            attachment_instance = (
                chat_action_tracker_models.MessageToSendAttachment.objects.filter(
                    id__in=attachment_ids
                )
            )
            attachment_instance.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)


# Create your views here.
class CustomChatActionTrackerView:
    class CustomChatActionTrackerBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.CustomChatActionTracker.objects.all()
        serializer_class = (
            chat_action_tracker_serializers.CustomChatActionTrackerSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(CustomChatActionTrackerBaseView, generics.ListAPIView):
        pagination_class = utility_common_class.CustomPageNumberPagination
        ordering = ["-created_at"]

        def get_queryset(self):
            queryset = super().get_queryset()
            # status
            status_list = [
                status
                for status in self.request.query_params.get("status", "").split(",")
                if status
            ]
            if status_list:
                queryset = queryset.filter(status__in=status_list)

            # custom action name
            custom_action_name = self.request.query_params.get("custom_action_name", "")
            if custom_action_name:
                queryset = queryset.filter(
                    custom_chat_action_setting__name=custom_action_name
                )

            return queryset

        def get(self, request, *args, **kwargs):
            queryset = self.get_queryset()
            serializer = chat_action_tracker_serializers.CustomChatActionTrackerSerializer.CustomGet(
                queryset, many=True
            )
            field_items_list = [
                item["field_items"] for item in serializer.data
            ]  # list of key value pairs
            filtered_field_items_list = (
                chat_action_tracker_query_helper.filter_field_items_list(
                    self, field_items_list, request
                )
            )
            return Response(
                data=filtered_field_items_list,
                status=status.HTTP_200_OK,
            )

    class Update(CustomChatActionTrackerBaseView, generics.UpdateAPIView):
        def patch(self, request, *args, **kwargs):
            response = super().patch(request, *args, **kwargs)
            custom_chat_action_tracker_id = self.kwargs["pk"]
            custom_chat_action_tracker_instance = (
                chat_action_tracker_models.CustomChatActionTracker.objects.get(
                    id=custom_chat_action_tracker_id
                )
            )
            custom_chat_action_setting = (
                custom_chat_action_tracker_instance.custom_chat_action_setting
            )
            # approve // reject
            function_environment = {}
            if custom_chat_action_tracker_instance.status == utility_models.APPROVED:
                exec(custom_chat_action_setting.on_approve, function_environment)
                function_environment["on_approve"](custom_chat_action_tracker_id)

            if custom_chat_action_tracker_instance.status == utility_models.REJECTED:
                exec(custom_chat_action_setting.on_reject, function_environment)
                function_environment["on_reject"](custom_chat_action_tracker_id)

            return response

    class Delete(CustomChatActionTrackerBaseView, generics.DestroyAPIView):
        pass

    class GetCustomChatActionTrackerCsvFile(List, APIView):
        def get(self, request, *args, **kwargs):

            custom_action_tracker_instance_list = super().get_queryset()

            # Export to csv
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                f'attachment; filename="appointment_list_{timezone.now().strftime("%d.%m.%Y")}.csv"'
            )

            writer = csv.writer(response)

            value_keys = []  # this should be populated from additional json dict
            # Write headers to CSV
            writer.writerow(value_keys)

            for custom_action_tracker_instance in custom_action_tracker_instance_list:

                list_value = custom_action_tracker_instance.json_data.get("list", [])

                writer.writerow(
                    [
                        *list_value,
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            custom_action_tracker_instance.created_at
                        ),
                        utility_date_helper.convert_to_yyyymmddThhmmZ(
                            custom_action_tracker_instance.updated_at
                        ),
                    ]
                )

            return response


class CalendarView:
    class CalendarBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.Calendar.objects.all()
        serializer_class = chat_action_tracker_serializers.CalendarSerializer
        permission_classes = [IsAuthenticated]

    class List(CalendarBaseView, generics.ListAPIView):
        def get_queryset(self):
            queryset = super().get_queryset()

            # dates
            start_datetime = self.request.query_params.get("start_datetime", "")
            end_datetime = self.request.query_params.get("end_datetime", "")

            if start_datetime and end_datetime:
                start_date = (
                    utility_date_helper.convert_datetime_to_desired_timezone_datetime(
                        timezone.make_aware(
                            datetime.strptime(
                                start_datetime, utility_date_helper.FE_DATE_TIME_FORMAT
                            ),
                            timezone=timezone.utc,
                        )
                    )
                )

                end_date = (
                    utility_date_helper.convert_datetime_to_desired_timezone_datetime(
                        timezone.make_aware(
                            datetime.strptime(
                                end_datetime, utility_date_helper.FE_DATE_TIME_FORMAT
                            ),
                            timezone=timezone.utc,
                        )
                        + timedelta(minutes=1)
                    )
                )
                queryset = queryset.filter(
                    start_date__lte=end_date, end_date__gte=start_date
                )

            # location
            location_list = [
                location.strip()
                for location in self.request.query_params.get("location", "").split(",")
                if location.strip()
            ]

            if location_list:
                queryset = queryset.filter(location__in=location_list)

            # doctor
            doctor_list = [
                doctor.strip()
                for doctor in self.request.query_params.get("ids", "").split(",")
                if doctor.strip()
            ]

            if doctor_list:
                queryset = queryset.filter(doctor_id__in=doctor_list)

            return queryset.filter(start_date__isnull=False, end_date__isnull=False)

    class Create(CalendarBaseView, generics.CreateAPIView):
        def fetch_data_from_api(self, endpoint, max_retries=3):
            bearer_token = os.getenv("BEARER_TOKEN")
            if not bearer_token:
                raise ValueError("Bearer token not found in environment variables.")
            headers = {"Authorization": f"Bearer {bearer_token}"}

            for attempt in range(max_retries):
                try:
                    response = requests.get(endpoint, headers=headers)
                    if response.status_code == 200:
                        json_response = response.json()
                        return {
                            "data": json_response.get("data", []),
                            "pager": json_response.get("pager", {}),
                        }
                    elif response.status_code == 401:
                        raise ValueError(
                            "Access denied. Check if the bearer token is correct."
                        )
                    else:
                        raise ValueError(
                            f"Failed to access the endpoint with status code: {response.status_code}."
                        )
                except Exception as e:
                    if attempt < max_retries - 1:
                        time.sleep(30)
                    else:
                        raise ValueError(
                            f"Failed to access the endpoint after {max_retries} attempts: {str(e)}"
                        )

        def post(self, request, *args, **kwargs):
            # Convert UTC datetime to Singapore time
            sg_tz = pytz.timezone("Asia/Singapore")
            start_datetime = (
                datetime.strptime(
                    self.request.data.get("start_date"), "%Y-%m-%dT%H:%M:%S.%fZ"
                )
                .replace(tzinfo=pytz.UTC)
                .astimezone(sg_tz)
            )
            end_datetime = (
                datetime.strptime(
                    self.request.data.get("end_date"), "%Y-%m-%dT%H:%M:%S.%fZ"
                )
                .replace(tzinfo=pytz.UTC)
                .astimezone(sg_tz)
            )

            # Extract date and time separately
            start_date = start_datetime.date()
            start_time = start_datetime.time()
            end_date = end_datetime.date()
            end_time = end_datetime.time()

            doctor_id = self.request.data.get("doctor_id")
            location = self.request.data.get("location")

            url = f"https://api.hb.sgimed.com/openapi/appointment?start_date={start_date}&end_date={end_date}"

            # Fetch appointment data
            filtered_appointments = []

            response = self.fetch_data_from_api(url)
            pager = response.get("pager", {})
            pages = pager.get("pages", {})

            for page in range(1, pages + 1):
                url_with_page = f"{url}&page={page}"

                response = self.fetch_data_from_api(url_with_page)
                appointment_data = response.get("data", [])

                for apt in appointment_data:
                    if apt.get("patient") is None:
                        continue  # Skip appointments with null patient
                    apt_start_time = datetime.strptime(
                        apt["start_time"], "%H:%M:%S"
                    ).time()
                    apt_end_time = datetime.strptime(apt["end_time"], "%H:%M:%S").time()

                    if (
                        apt_start_time >= start_time
                        and apt_end_time <= end_time
                        and apt["doctor"]["id"] == doctor_id
                        and apt["branch_id"] == location
                        and apt["appointment_type"]["name"] == "Consult"
                    ):
                        filtered_appointments.append(apt)

            # Get patient emails from filtered appointments and send emails
            for apt in filtered_appointments:
                patient_details = apt.get("patient")
                if patient_details is not None and "email" in patient_details:
                    email = "<EMAIL>"
                    mobile = patient_details["mobile"]

                    # Prepare email details
                    from_ = f"UtterUnicorn X Amelio <{DEFAULT_FROM_EMAIL}>"
                    html_path = "amelio/important_appointment_template.html"
                    subject = "Appointment Rescheduling Notice"
                    to_ = [email]  # Send to individual email

                    appointment_date = datetime.strptime(
                        apt["start_date"][:-6], "%Y-%m-%dT%H:%M:%S"
                    )
                    formatted_date = appointment_date.strftime("%A, %d %B %Y")
                    formatted_time = datetime.strptime(
                        apt["start_time"], "%H:%M:%S"
                    ).strftime("%I:%M %p")

                    if location == "CCK":
                        phone_number = "+**********"
                    elif location == "KCS":
                        phone_number = "+**********"
                    elif location == "OR":
                        phone_number = "+**********"
                    elif location == "SBW":
                        phone_number = "+**********"
                    else:
                        phone_number = "Not available"

                    custom_kwargs = {
                        "name": patient_details.get("name", "Patient"),
                        "date": formatted_date,
                        "time": formatted_time,
                        "phone_number": phone_number,
                    }

                    # Send email
                    send_user_email_with_html_template(
                        html_path, subject, from_, to_, **custom_kwargs
                    )

                    chat_action_tracker_models.MessageToSend.objects.create(
                        contact=**********,
                        message=f"Dear {patient_details.get('name', 'Patient')},\n\nWe regret to inform you that your appointment needs to be rescheduled due to the doctor unavailability. We apologize for any inconvenience.\n\Current Appointment:\nDate: {formatted_date}\nTime: {formatted_time}\n\nPlease contact us at {phone_number} to reschedule your appointment at your earliest convenience. Our team will assist in finding a new time that works for you.\n\nThank you for your understanding. We look forward to seeing you soon!",
                        to_be_sent_at=timezone.now(),
                        metadata={
                            "is_send_reminder": True,
                            "is_resend_reminder": False,
                            "is_send_to_staff": False,
                            "appointment_id": apt["id"],
                            "appointment_date": apt["start_date"],
                            "appointment_time": apt["start_time"],
                        },
                    )
                else:
                    print(f"Skipping appointment due to missing patient details: {apt}")

            print(f"Emails sent for {len(filtered_appointments)} appointments")

            return super().post(request, *args, **kwargs)

    class Get(CalendarBaseView, generics.RetrieveAPIView):
        pass

    class Update(CalendarBaseView, generics.UpdateAPIView):
        pass

    class Delete(CalendarBaseView, generics.DestroyAPIView):
        pass

    class DoctorList(CalendarBaseView):
        @staticmethod
        def fetch_data_from_api(endpoint):
            bearer_token = os.getenv("BEARER_TOKEN")
            if not bearer_token:
                raise ValueError("Bearer token not found in environment variables.")
            headers = {"Authorization": f"Bearer {bearer_token}"}
            response = requests.get(endpoint, headers=headers)
            if response.status_code == 200:
                data = response.json().get("data", [])
                unique_doctors = {}
                for doctor in data:
                    key = (doctor["branch_id"], doctor["name"])
                    if key not in unique_doctors:
                        unique_doctors[key] = {
                            "id": doctor["id"],
                            "branch_id": doctor["branch_id"],
                            "name": doctor["name"],
                        }
                sorted_doctors = sorted(
                    unique_doctors.values(), key=lambda x: (x["branch_id"], x["name"])
                )
                return sorted_doctors
            elif response.status_code == 401:
                raise ValueError("Access denied. Check if the bearer token is correct.")
            else:
                raise ValueError(
                    f"Failed to access the endpoint with status code: {response.status_code}."
                )

        def get(self, request, *args, **kwargs):
            try:
                doctor_data = self.fetch_data_from_api(
                    "https://api.hb.sgimed.com/openapi/doctor"
                )
                return Response(doctor_data, status=status.HTTP_200_OK)
            except ValueError as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BlacklistView:
    class BlacklistBaseView(generics.GenericAPIView):
        queryset = chat_action_tracker_models.Blacklist.objects.all()
        serializer_class = chat_action_tracker_serializers.BlacklistSerializer
        permission_classes = [IsAuthenticated]

    class List(BlacklistBaseView, generics.ListAPIView):
        def decrypt_and_mask_item(self, item):
            decrypted_item = item.copy()
            decrypted_nric = decrypt_data(item["nric"])
            decrypted_name = decrypt_data(item["name"])

            # Mask NRIC to show only last 3 characters
            decrypted_item["nric"] = (
                "*" * (len(decrypted_nric) - 3) + decrypted_nric[-3:]
            )

            # Mask name to show only first character of each word
            decrypted_item["name"] = " ".join(
                word[0] + "*" * (len(word) - 1) for word in decrypted_name.split()
            )

            return decrypted_item

        def list(self, request, *args, **kwargs):
            queryset = self.filter_queryset(self.get_queryset().filter(status=1))
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                masked_data = [
                    self.decrypt_and_mask_item(item) for item in serializer.data
                ]
                return self.get_paginated_response(masked_data)

            serializer = self.get_serializer(queryset, many=True)
            masked_data = [self.decrypt_and_mask_item(item) for item in serializer.data]
            return Response(masked_data)

        def get_paginated_response(self, data):
            assert self.paginator is not None
            return Response(
                {
                    "count": self.paginator.page.paginator.count,
                    "next": self.paginator.get_next_link(),
                    "previous": self.paginator.get_previous_link(),
                    "results": data,
                }
            )

    class Get(BlacklistBaseView, generics.RetrieveAPIView):
        pass

    class Create(BlacklistBaseView, generics.CreateAPIView):
        @staticmethod
        def get_user_id(request):
            return request.user.id if request.user.is_authenticated else None

        def post(self, request, *args, **kwargs):
            try:
                nric = request.data.get("nric")
                name = request.data.get("name")
                reason = request.data.get("reason")
                user_id = self.get_user_id(request)
                status_value = 1

                # Validate NRIC format
                if not re.match(r"^[STFG]\d{7}[A-Z]$", nric):
                    return Response(
                        {"error": "Invalid NRIC format"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # Check if NRIC already exists in the database
                existing_blacklists = chat_action_tracker_models.Blacklist.objects.all()
                for blacklist in existing_blacklists:
                    if decrypt_data(blacklist.nric) == nric:
                        return Response(
                            {"error": "NRIC already exists"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                # Create a new Blacklist instance directly
                blacklist_instance = chat_action_tracker_models.Blacklist(
                    nric=encrypt_data(nric),
                    name=encrypt_data(name),
                    reason=reason,
                    status=status_value,
                    updated_by_id=user_id,
                    count=3,
                )

                # Save the instance
                blacklist_instance.save()

                # Serialize the instance for the response
                serializer = self.get_serializer(blacklist_instance)
                headers = self.get_success_headers(serializer.data)
                return Response(
                    serializer.data, status=status.HTTP_201_CREATED, headers=headers
                )
            except Exception as e:
                import traceback

                traceback.print_exc()
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    class Update(BlacklistBaseView, generics.UpdateAPIView):
        @staticmethod
        def get_user_id(request):
            return request.user if request.user.is_authenticated else None

        def patch(self, request, *args, **kwargs):
            blacklist_id = self.kwargs["pk"]
            blacklist_instance = chat_action_tracker_models.Blacklist.objects.get(
                id=blacklist_id
            )

            status = request.data.get("status")
            if status is not None:
                blacklist_instance.status = status

            user = self.get_user_id(request)
            if user:
                blacklist_instance.updated_by = user

            blacklist_instance.count = 0

            blacklist_instance.save()

            serializer = self.get_serializer(
                blacklist_instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            return Response(serializer.data)
