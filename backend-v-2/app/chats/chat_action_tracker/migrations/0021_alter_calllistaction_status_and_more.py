# Generated by Django 4.2.5 on 2024-05-21 09:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0024_attachment_google_drive_url'),
        ('chat_action', '0011_alter_customchatactionsetting_options_and_more'),
        ('chat_action_tracker', '0020_delete_customersupportattachment'),
    ]

    operations = [
        migrations.AlterField(
            model_name='calllistaction',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.AlterField(
            model_name='customersupport',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.AlterField(
            model_name='missinginfoaction',
            name='status',
            field=models.CharField(choices=[('Approved', 'Approved'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.CreateModel(
            name='CustomChatActionTracker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('Approved', 'Approved'), ('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912)),
                ('remarks', models.CharField(blank=True, max_length=8912)),
                ('additional_fields', models.TextField(blank=True)),
                ('conversation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custom_action_trackers', to='chat.conversation')),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('custom_chat_action_setting', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custom_action_trackers', to='chat_action.customchatactionsetting')),
                ('message', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custom_action_trackers', to='chat.message')),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
