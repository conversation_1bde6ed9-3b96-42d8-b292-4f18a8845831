# Generated by Django 4.2.5 on 2024-06-05 06:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0024_attachment_google_drive_url'),
        ('chat_action_tracker', '0022_alter_customchatactiontracker_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='messagetosend',
            name='message_type',
            field=models.CharField(choices=[('machine', 'Machine'), ('page-admin', 'Page Admin')], default='machine', max_length=10),
        ),
        migrations.AddField(
            model_name='messagetosend',
            name='uu_message',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='message_to_send', to='chat.message'),
        ),
        migrations.AlterField(
            model_name='messagetosend',
            name='status',
            field=models.Char<PERSON>ield(choices=[('Pending', 'Pending'), ('Failed', 'Failed'), ('Completed', 'Completed'), ('Appointment Updated', 'Appointment Updated')], default='Pending', max_length=8912),
        ),
    ]
