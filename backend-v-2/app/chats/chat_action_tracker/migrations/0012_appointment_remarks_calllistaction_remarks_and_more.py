# Generated by Django 4.2.5 on 2023-12-08 02:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat_action_tracker', '0011_customersupport_status_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='remarks',
            field=models.Char<PERSON>ield(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='calllistaction',
            name='remarks',
            field=models.Char<PERSON>ield(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='customersupport',
            name='remarks',
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='missinginfoaction',
            name='remarks',
            field=models.CharField(blank=True, max_length=8912),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='status',
            field=models.CharField(choices=[('Pending', 'Pending'), ('Sales Made', 'Sales Made'), ('Sales Not Made', 'Sales Not Made'), ('Absent', 'Absent')], default='Pending', max_length=31),
        ),
    ]
