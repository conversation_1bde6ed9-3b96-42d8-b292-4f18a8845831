# Generated by Django 4.2.5 on 2023-12-07 07:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0015_conversation_is_auto_reply'),
        ('chat_action_tracker', '0010_missinginfoaction_reason'),
    ]

    operations = [
        migrations.AddField(
            model_name='customersupport',
            name='status',
            field=models.CharField(choices=[('Resolved', 'Resolved'), ('Pending', 'Pending'), ('Rejected', 'Rejected')], default='Pending', max_length=8912),
        ),
        migrations.AlterField(
            model_name='calllistaction',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_list_actions', to='chat.conversation'),
        ),
        migrations.AlterField(
            model_name='calllistaction',
            name='message',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='call_list_action', to='chat.message'),
        ),
        migrations.AlterField(
            model_name='missinginfoaction',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='missing_info_actions', to='chat.conversation'),
        ),
        migrations.AlterField(
            model_name='missinginfoaction',
            name='message',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='missing_info_action', to='chat.message'),
        ),
    ]
