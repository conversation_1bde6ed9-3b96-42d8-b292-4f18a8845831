from django.urls import path
import app.chats.chat_action_tracker.views as views

urlpatterns = [
    path("get-count", views.ChatActionTrackerView.GetCount.as_view()),
    path("appointment/list", views.AppointmentView.List.as_view()),
    path("appointment/<int:pk>/update", views.AppointmentView.Update.as_view()),
    path("appointment/<int:pk>/delete", views.AppointmentView.Delete.as_view()),
    path(
        "appointment/get-csv-file",
        views.AppointmentView.GetAppointmentCsvFile.as_view(),
    ),
    path("appointment-by-code/<str:code>", views.AppointmentView.GetByCode.as_view()),
    path(
        "appointment-by-code/<str:code>/update",
        views.AppointmentView.UpdateByCode.as_view(),
    ),
    path("customer-support/list", views.CustomerSupportView.List.as_view()),
    path(
        "customer-support/<int:pk>/update", views.CustomerSupportView.Update.as_view()
    ),
    path(
        "customer-support/<int:pk>/delete", views.CustomerSupportView.Delete.as_view()
    ),
    path(
        "customer-support/get-csv-file",
        views.CustomerSupportView.GetCustomerSupportCsvFile.as_view(),
    ),
    path("callback/list", views.CallListActionView.List.as_view()),
    path("callback/create", views.CallListActionView.Create.as_view()),
    path("callback/<int:pk>", views.CallListActionView.Get.as_view()),
    path("callback/<int:pk>/update", views.CallListActionView.Update.as_view()),
    path("callback/<int:pk>/delete", views.CallListActionView.Delete.as_view()),
    path("callback/get-csv-file", views.CallListActionView.GetCsv.as_view()),
    path("missing-info/list", views.MissingInfoActionView.List.as_view()),
    path("missing-info/create", views.MissingInfoActionView.Create.as_view()),
    path("missing-info/<int:pk>", views.MissingInfoActionView.Get.as_view()),
    path("missing-info/<int:pk>/update", views.MissingInfoActionView.Update.as_view()),
    path("missing-info/<int:pk>/delete", views.MissingInfoActionView.Delete.as_view()),
    path(
        "missing-info/get-csv-file",
        views.MissingInfoActionView.GetMissingInfoCsvFile.as_view(),
    ),
    path(
        "message-to-send/whatsapp-qr/list",
        views.MessageToSendView.ListForWhatsAppQR.as_view(),
    ),  # whatsapp qr call this
    path(
        "message-to-send/list", views.MessageToSendView.List.as_view()
    ),  # django app call this (display table) TODO-yc: to merge this with list, when FE passing other params
    path(
        "message-to-send/scheduled/list",
        views.MessageToSendView.ListScheduledMessages.as_view(),
    ),
    path("message-to-send/create", views.MessageToSendView.Create.as_view()),
    path("message-to-send/<int:pk>", views.MessageToSendView.Get.as_view()),
    path(
        "message-to-send/<int:pk>/whatsapp-qr/update",
        views.MessageToSendView.UpdateForWhatsAppQR.as_view(),
    ),  # whatsapp qr call this
    path(
        "message-to-send/<int:pk>/update",
        views.MessageToSendView.Update.as_view(),
    ),  # django app call this (edit table) TODO-yc: to merge this with list, when FE passing other params
    path("message-to-send/<int:pk>/delete", views.MessageToSendView.Delete.as_view()),
    path(
        "message-to-send/attachment/delete",
        views.MessageToSendView.DeleteAttachment.as_view(),
    ),
    path(
        "custom-chat-action-tracker/list",
        views.CustomChatActionTrackerView.List.as_view(),
    ),
    path(
        "custom-chat-action-tracker/<int:pk>/update",
        views.CustomChatActionTrackerView.Update.as_view(),
    ),
    path(
        "custom-chat-action-tracker/<int:pk>/delete",
        views.CustomChatActionTrackerView.Delete.as_view(),
    ),
    path(
        "custom-chat-action-tracker/get-csv-file",
        views.CustomChatActionTrackerView.GetCustomChatActionTrackerCsvFile.as_view(),
    ),
    path("calendar/list", views.CalendarView.List.as_view()),
    path("calendar/create", views.CalendarView.Create.as_view()),
    path("calendar/<int:pk>", views.CalendarView.Get.as_view()),
    path("calendar/<int:pk>/update", views.CalendarView.Update.as_view()),
    path("calendar/<int:pk>/delete", views.CalendarView.Delete.as_view()),
    path("calendar/doctor/list", views.CalendarView.DoctorList.as_view()),
    path("blacklist/list", views.BlacklistView.List.as_view()),
    path("blacklist/<int:pk>", views.BlacklistView.Get.as_view()),
    path("blacklist/<int:pk>/update", views.BlacklistView.Update.as_view()),
    path("blacklist/create", views.BlacklistView.Create.as_view()),
]
