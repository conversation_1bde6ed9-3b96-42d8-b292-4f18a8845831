from rest_framework import serializers
import app.chats.chat_action_tracker.models as chat_action_tracker_models
import app.core.authapi.serializers as authapi_serializers


class AppointmentSerializer:
    class Get(serializers.ModelSerializer):

        location_label = serializers.SerializerMethodField()

        def get_location_label(self, appointment_instance):
            if hasattr(appointment_instance, "appointment_chat_feature_location"):
                return appointment_instance.appointment_chat_feature_location.name
            return ""

        location = serializers.SerializerMethodField()  # address

        def get_location(self, appointment_instance):
            if hasattr(appointment_instance, "appointment_chat_feature_location"):
                return appointment_instance.appointment_chat_feature_location.location
            return ""

        class Meta:
            model = chat_action_tracker_models.Appointment
            fields = [
                "id",
                "name",
                "email",
                "contact",
                "location",
                "location_label",
                "datetime",
                "status",
                "remarks",
                "conversation",
                "message",
                "code",
                "created_at",
                "updated_at",
                "updated_by",
            ]


class CustomerSupportSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_tracker_models.CustomerSupport
            fields = [
                "id",
                "name",
                "contact",
                "complaint_description",
                "email",
                "invoice_number",
                "status",
                "remarks",
                "conversation",
                "message",
                "created_at",
                "updated_at",
                "updated_by",
            ]


class CallListActionSerializer(serializers.ModelSerializer):
    class Meta:
        model = chat_action_tracker_models.CallListAction
        fields = "__all__"


class MissingInfoActionSerializer(serializers.ModelSerializer):
    class Meta:
        model = chat_action_tracker_models.MissingInfoAction
        fields = "__all__"


class MessageToSendAttachmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = chat_action_tracker_models.MessageToSendAttachment
        fields = "__all__"


class MessageToSendSerializer(serializers.ModelSerializer):
    attachments = MessageToSendAttachmentSerializer(many=True, read_only=True)

    class Meta:
        model = chat_action_tracker_models.MessageToSend
        fields = "__all__"


class CustomChatActionTrackerSerializer:

    class CustomGet(serializers.ModelSerializer):

        field_items = serializers.SerializerMethodField()

        def get_field_items(self, custom_chat_action_tracker_instance):
            return custom_chat_action_tracker_instance.get_field_items()

        class Meta:
            model = chat_action_tracker_models.CustomChatActionTracker
            fields = ["field_items"]

    class Get(serializers.ModelSerializer):

        class Meta:
            model = chat_action_tracker_models.CustomChatActionTracker
            fields = [
                "id",
                "message",
                "conversation",
                "status",
                "remarks",
                "additional_fields",
                "custom_chat_action_setting",
                "created_at",
                "created_by",
                "updated_at",
                "updated_by",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = chat_action_tracker_models.CustomChatActionTracker
            fields = [
                "id",
                "message",
                "conversation",
                "status",
                "remarks",
                "additional_data",
                "custom_chat_action_setting",
                "created_by",
                "updated_by",
            ]
            

class CalendarSerializer(serializers.ModelSerializer):
    class Meta:
        model = chat_action_tracker_models.Calendar
        fields = "__all__"
        
class BlacklistSerializer(serializers.ModelSerializer):
    updated_by = authapi_serializers.ShortCustomUserSerializer()
    class Meta:
        model = chat_action_tracker_models.Blacklist
        fields = ["id", "nric", "name", "reason", "status", "created_at", "updated_at", "updated_by"]

