def filter_field_items_list(self, field_items_list, request):
    search_param = request.query_params.get("search", "")
    ordering_params = request.query_params.get("ordering", self.ordering)
    # Apply search filters
    if search_param:
        field_items_list = [
            item
            for item in field_items_list
            if search_param in str(item.values()).lower()
        ]

    # Apply ordering filters
    if ordering_params:
        ordering_fields = (
            ordering_params
            if isinstance(ordering_params, list)
            else ordering_params.split(",")
        )
        for field in reversed(ordering_fields):
            reverse = field.startswith("-")
            field_name = field.lstrip("-")
            field_items_list.sort(key=lambda x: x.get(field_name, ""), reverse=reverse)

    return field_items_list
