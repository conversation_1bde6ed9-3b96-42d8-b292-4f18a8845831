from django.db import models

import app.utility.models as utility_models


class Customer(utility_models.BaseModel):  # only for nova
    so_date = models.DateField()
    so_number = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    full_so_number = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    party_name = models.TextField()
    ship_to_address = models.TextField()
    attention = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    contact_number = models.Char<PERSON>ield(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    full_contact_number = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    stock_code = models.CharField(max_length=utility_models.DEFAULT_CHAR_FIELD_MAX)
    stock_description = models.TextField()
    stock_remarks = models.TextField()

    def __str__(self):
        return self.so_number
