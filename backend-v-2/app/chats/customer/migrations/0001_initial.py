# Generated by Django 4.2.5 on 2025-01-07 04:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('so_date', models.DateField()),
                ('so_number', models.Char<PERSON>ield(max_length=8912)),
                ('full_so_number', models.CharField(max_length=8912)),
                ('party_name', models.TextField()),
                ('ship_to_address', models.TextField()),
                ('attention', models.Char<PERSON><PERSON>(max_length=8912)),
                ('contact_number', models.Char<PERSON>ield(max_length=8912)),
                ('full_contact_number', models.CharField(max_length=8912)),
                ('stock_code', models.CharField(max_length=8912)),
                ('stock_description', models.TextField()),
                ('stock_remarks', models.TextField()),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
