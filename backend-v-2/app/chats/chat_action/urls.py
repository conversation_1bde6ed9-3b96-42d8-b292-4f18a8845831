from django.urls import path
import app.chats.chat_action.views as views

urlpatterns = [
    path(
        "appointment-chat-feature-setting/list",
        views.AppointmentChatFeatureSettingView.List.as_view(),
    ),
    path(
        "appointment-chat-feature-setting/<int:pk>/update",
        views.AppointmentChatFeatureSettingView.Update.as_view(),
    ),
    path(
        "appointment-chat-feature-location/list",
        views.AppointmentChatFeatureLocationView.List.as_view(),
    ),
    path(
        "appointment-chat-feature-location/<int:pk>/update",
        views.AppointmentChatFeatureLocationView.Update.as_view(),
    ),
    path(
        "appointment-chat-feature-location/create",
        views.AppointmentChatFeatureLocationView.Create.as_view(),
    ),
    path(
        "appointment-chat-feature-location/<int:pk>/delete",
        views.AppointmentChatFeatureLocationView.Delete.as_view(),
    ),
    path(
        "customer-support-chat-feature-setting/list",
        views.CustomerSupportChatFeatureSettingView.List.as_view(),
    ),
    path(
        "customer-support-chat-feature-setting/<int:pk>/update",
        views.CustomerSupportChatFeatureSettingView.Update.as_view(),
    ),
    path(
        "callback-chat-feature-setting/list",
        views.CallbackChatFeatureSettingView.List.as_view(),
    ),
    path(
        "callback-chat-feature-setting/<int:pk>/update",
        views.CallbackChatFeatureSettingView.Update.as_view(),
    ),
    path(
        "missing-info-chat-feature-setting/list",
        views.MissingInfoChatFeatureSettingView.List.as_view(),
    ),
    path(
        "missing-info-chat-feature-setting/<int:pk>/update",
        views.MissingInfoChatFeatureSettingView.Update.as_view(),
    ),
    path(
        "email-pop-up-feature-setting/list",
        views.EmailPopUpFeatureSettingView.List.as_view(),
    ),
    path(
        "email-pop-up-feature-setting/<int:pk>/update",
        views.EmailPopUpFeatureSettingView.Update.as_view(),
    ),
    path(
        "no-reply-on-inaccurate-answer-setting/list",
        views.NoReplyOnInaccurateAnswerSettingView.List.as_view(),
    ),
    path(
        "no-reply-on-inaccurate-answer-setting/<int:pk>/update",
        views.NoReplyOnInaccurateAnswerSettingView.Update.as_view(),
    ),
    path(
        "faq-feature-setting/list",
        views.FAQFeatureSettingView.List.as_view(),
    ),
    path(
        "faq-feature-setting/<int:pk>/update",
        views.FAQFeatureSettingView.Update.as_view(),
    ),
    path(
        "question-answer-pair/list",
        views.QuestionAnswerPairView.List.as_view(),
    ),
    path(
        "question-answer-pair/<int:pk>/update",
        views.QuestionAnswerPairView.Update.as_view(),
    ),
    path(
        "question-answer-pair/create",
        views.QuestionAnswerPairView.Create.as_view(),
    ),
    path(
        "question-answer-pair/<int:pk>/delete",
        views.QuestionAnswerPairView.Delete.as_view(),
    ),
    path(
        "custom-chat-action-setting/list",
        views.CustomChatActionSettingView.List.as_view(),
    ),
    path(
        "custom-chat-action-setting/<str:name>/get",
        views.CustomChatActionSettingView.Get.as_view(),
    ),
    path(
        "custom-chat-action-setting/<int:pk>/update",
        views.CustomChatActionSettingView.Update.as_view(),
    ),
    path(
        "custom-chat-action-setting/<int:pk>/create",
        views.CustomChatActionSettingView.Create.as_view(),
    ),
]
