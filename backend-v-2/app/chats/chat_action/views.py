import app.core.authapi.permission as authapi_permission
import app.core.authapi.helper as authapi_helper

import app.chats.chat_action.models as chat_action_models
import app.chats.chat_action.serializers as chat_action_serializers
import app.chats.chat.constant as chat_constant

import app.integrations.integration.models as integration_models

from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

import app.utility.chroma_db_helper as utility_chroma_db_helper
import app.utility.models as utility_models
import app.utility.response_helper as utility_response_helper

from rest_framework.response import Response


# Create your views here.
class AppointmentChatFeatureSettingView:
    class AppointmentChatFeatureSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.AppointmentChatFeatureSetting.objects.all()
        serializer_class = (
            chat_action_serializers.AppointmentChatFeatureSettingSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    # class List(AppointmentChatFeatureSettingBaseView, generics.ListAPIView):
    class List(AppointmentChatFeatureSettingBaseView, APIView):
        def get(self, request, *args, **kwargs):
            appointment_chat_feature_setting = (
                chat_action_models.AppointmentChatFeatureSetting.objects.first()
            )
            if not appointment_chat_feature_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)
            return Response(
                self.serializer_class(appointment_chat_feature_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(AppointmentChatFeatureSettingBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.AppointmentChatFeatureSettingSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


class AppointmentChatFeatureLocationView:
    class AppointmentChatFeatureLocationBaseView(generics.GenericAPIView):
        queryset = chat_action_models.AppointmentChatFeatureLocation.objects.all()
        serializer_class = (
            chat_action_serializers.AppointmentChatFeatureLocationSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(AppointmentChatFeatureLocationBaseView, generics.ListAPIView):
        pass

    class Update(AppointmentChatFeatureLocationBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.AppointmentChatFeatureLocationSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            res = super().patch(request, *args, **kwargs)

            try:
                appointment_chat_feature_location_instance = (
                    chat_action_models.AppointmentChatFeatureLocation.objects.get(
                        id=self.kwargs["pk"]
                    )
                )

                if (
                    "platform" in request.data
                    and request.data["platform"] != "WhatsApp"
                ):
                    # delete all existing obj in whatsapp
                    integration_models.WhatsAppQRWhiteList.objects.filter(
                        appointment_chat_feature_location=appointment_chat_feature_location_instance,
                    ).delete()

                if (
                    "platform" in request.data
                    and request.data["platform"] == "WhatsApp"
                    and "identifier" in request.data
                ):
                    # Update
                    integration_models.WhatsAppQRWhiteList.objects.update_or_create(
                        appointment_chat_feature_location=appointment_chat_feature_location_instance,
                        defaults={
                            "name": request.data["identifier"],
                            "created_by": self.request.user,
                            "updated_by": self.request.user,
                        },
                    )
            except Exception as e:
                print("Error when updating whatsapp whitelist in whatsapp: ", e)

            return res

    class Create(AppointmentChatFeatureLocationBaseView, generics.CreateAPIView):
        serializer_class = (
            chat_action_serializers.AppointmentChatFeatureLocationSerializer.Post
        )

        def post(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["created_by"] = self.request.user.id
            set_mutable(request.data, False)

            res = super().post(request, *args, **kwargs)

            try:
                appointment_chat_feature_location_id = res.data["id"]
                appointment_chat_feature_location_instance = (
                    chat_action_models.AppointmentChatFeatureLocation.objects.get(
                        id=appointment_chat_feature_location_id
                    )
                )

                if (
                    "platform" in request.data
                    and request.data["platform"] == "WhatsApp"
                    and "identifier" in request.data
                ):
                    integration_models.WhatsAppQRWhiteList.objects.create(
                        name=request.data["identifier"],
                        appointment_chat_feature_location=appointment_chat_feature_location_instance,
                        created_by=self.request.user,
                        updated_by=self.request.user,
                    )
            except Exception as e:
                print("Error when whitelisting in whatsapp: ", e)

            return res

    class Delete(AppointmentChatFeatureLocationBaseView, generics.DestroyAPIView):
        def delete(self, request, *args, **kwargs):
            try:
                appointment_chat_feature_location_instance = (
                    chat_action_models.AppointmentChatFeatureLocation.objects.get(
                        id=self.kwargs["pk"]
                    )
                )
                # Delete existing
                integration_models.WhatsAppQRWhiteList.objects.filter(
                    appointment_chat_feature_location=appointment_chat_feature_location_instance
                ).delete()
            except Exception as e:
                print("Error when deleting whitelisting in whatsapp: ", e)

            return super().delete(request, *args, **kwargs)


class CustomerSupportChatFeatureSettingView:
    class CustomerSupportBaseView(generics.GenericAPIView):
        queryset = chat_action_models.CustomerSupportChatFeatureSetting.objects.all()
        serializer_class = (
            chat_action_serializers.CustomerSupportChatFeatureSettingSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    # class List(CustomerSupportBaseView, generics.ListAPIView):
    class List(CustomerSupportBaseView, APIView):
        def get(self, request, *args, **kwargs):
            customer_support_chat_feature_setting = (
                chat_action_models.CustomerSupportChatFeatureSetting.objects.first()
            )
            if not customer_support_chat_feature_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)

            return Response(
                self.serializer_class(customer_support_chat_feature_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(CustomerSupportBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.CustomerSupportChatFeatureSettingSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


class CallbackChatFeatureSettingView:
    class CallbackChatFeatureSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.CallbackChatFeatureSetting.objects.all()
        serializer_class = (
            chat_action_serializers.CallbackChatFeatureSettingSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(CallbackChatFeatureSettingBaseView, APIView):
        def get(self, request, *args, **kwargs):
            callback_chat_feature_setting = (
                chat_action_models.CallbackChatFeatureSetting.objects.first()
            )
            if not callback_chat_feature_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)

            return Response(
                self.serializer_class(callback_chat_feature_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(CallbackChatFeatureSettingBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.CallbackChatFeatureSettingSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


## Missing Information


class MissingInfoChatFeatureSettingView:
    class MissingInfoChatFeatureSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.MissingInfoChatFeatureSetting.objects.all()
        serializer_class = (
            chat_action_serializers.MissingInfoChatFeatureSettingSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(MissingInfoChatFeatureSettingBaseView, APIView):
        def get(self, request, *args, **kwargs):
            missing_info_chat_feature_setting = (
                chat_action_models.MissingInfoChatFeatureSetting.objects.first()
            )
            if not missing_info_chat_feature_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)

            return Response(
                self.serializer_class(missing_info_chat_feature_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(MissingInfoChatFeatureSettingBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.MissingInfoChatFeatureSettingSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


## Email Pop Up Feature Setting


class EmailPopUpFeatureSettingView:
    class EmailPopUpFeatureSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.EmailPopUpFeatureSetting.objects.all()
        serializer_class = (
            chat_action_serializers.EmailPopUpFeatureSettingSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(EmailPopUpFeatureSettingBaseView, APIView):
        def get(self, request, *args, **kwargs):
            email_pop_up_feature_setting = (
                chat_action_models.EmailPopUpFeatureSetting.objects.first()
            )
            if not email_pop_up_feature_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)

            return Response(
                self.serializer_class(email_pop_up_feature_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(EmailPopUpFeatureSettingBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.EmailPopUpFeatureSettingSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


## No Reply On Inaccurate Answer


class NoReplyOnInaccurateAnswerSettingView:
    class NoReplyOnInaccurateAnswerSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.NoReplyOnInaccurateAnswerSetting.objects.all()
        serializer_class = (
            chat_action_serializers.NoReplyOnInaccurateAnswerSettingSerializer.Get
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(NoReplyOnInaccurateAnswerSettingBaseView, APIView):
        def get(self, request, *args, **kwargs):
            no_reply_on_inaccurate_answer_setting = (
                chat_action_models.NoReplyOnInaccurateAnswerSetting.objects.first()
            )
            if not no_reply_on_inaccurate_answer_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)

            return Response(
                self.serializer_class(no_reply_on_inaccurate_answer_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(NoReplyOnInaccurateAnswerSettingBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.NoReplyOnInaccurateAnswerSettingSerializer.Patch
        )

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


## FAQ
class QuestionAnswerPairView:
    class QuestionAnswerPairBaseView(generics.GenericAPIView):
        queryset = chat_action_models.QuestionAnswerPair.objects.all()
        serializer_class = chat_action_serializers.QuestionAnswerPairSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(QuestionAnswerPairBaseView, generics.ListAPIView):

        def get_queryset(self, *args, **kwargs):
            queryset = self.queryset

            # Filter via chroma_db_type
            if chroma_db_type := self.request.query_params.get("chroma_db_type"):
                if chroma_db_type in [
                    utility_models.EXTERNAL_TEXT,
                    utility_models.INTERNAL_TEXT,
                ]:
                    queryset = queryset.filter(chroma_db_type=chroma_db_type)
                else:
                    queryset = queryset.filter(chroma_db_type="does not exist")
            return queryset

    class Update(QuestionAnswerPairBaseView, generics.UpdateAPIView):
        serializer_class = chat_action_serializers.QuestionAnswerPairSerializer.Patch

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            # UPDATE THE CHROMA WITH FAQ
            chroma_db_type = request.data.get(
                "chroma_db_type", utility_models.EXTERNAL_TEXT
            )
            if not (
                chroma_db_type
                in [utility_models.EXTERNAL_TEXT, utility_models.INTERNAL_TEXT]
            ):
                return utility_response_helper.get_return_400_response(
                    "Failed to update QnA due to invalid chroma db type."
                )

            schema_name = authapi_helper.get_schema_name()

            id_ = self.kwargs.get("pk", "")
            question = request.data.get("question", "")
            answer = request.data.get("answer", "")

            if chroma_db_type == utility_models.EXTERNAL_TEXT:
                collection_name = chat_constant.get_collection_name(
                    chat_constant.external_collection, schema_name
                )
            else:
                collection_name = chat_constant.get_collection_name(
                    chat_constant.internal_collection, schema_name
                )

            if id_ and question and answer:
                # print(f"updating Q:{question} and A:{answer} in {collection_name}...")
                utility_chroma_db_helper.create_update_chroma_through_single_text(
                    collection_name,
                    id_,
                    question,
                    answer,
                )

            return super().patch(request, *args, **kwargs)

    class Create(QuestionAnswerPairBaseView, generics.CreateAPIView):
        serializer_class = chat_action_serializers.QuestionAnswerPairSerializer.Post

        def post(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["created_by"] = self.request.user.id
            set_mutable(request.data, False)

            # UPDATE THE CHROMA WITH FAQ
            chroma_db_type = request.data.get(
                "chroma_db_type", utility_models.EXTERNAL_TEXT
            )
            if not (
                chroma_db_type
                in [utility_models.EXTERNAL_TEXT, utility_models.INTERNAL_TEXT]
            ):
                return utility_response_helper.get_return_400_response(
                    "Failed to create QnA due to invalid chroma db type."
                )

            schema_name = authapi_helper.get_schema_name()

            res = super().post(request, *args, **kwargs)
            id_ = res.data["id"]

            question = request.data.get("question", "")
            answer = request.data.get("answer", "")

            if chroma_db_type == utility_models.EXTERNAL_TEXT:
                collection_name = chat_constant.get_collection_name(
                    chat_constant.external_collection, schema_name
                )
            else:
                collection_name = chat_constant.get_collection_name(
                    chat_constant.internal_collection, schema_name
                )

            if id_ and question and answer:
                # print(f"creating Q:{question} and A:{answer} in {collection_name}...")
                utility_chroma_db_helper.create_update_chroma_through_single_text(
                    collection_name,
                    id_,
                    question,
                    answer,
                )

            return res

    class Delete(QuestionAnswerPairBaseView, generics.DestroyAPIView):
        def delete(self, request, *args, **kwargs):
            # UPDATE THE CHROMA WITH FAQ
            schema_name = authapi_helper.get_schema_name()

            external_collection_name = chat_constant.get_collection_name(
                chat_constant.external_collection, schema_name
            )

            id_ = self.kwargs.get("pk", "")
            if id_:
                ids = [utility_chroma_db_helper.get_chroma_text_id(id_)]
                utility_chroma_db_helper.remove_chroma_content_through_text(
                    external_collection_name, ids
                )

            return super().delete(request, *args, **kwargs)


class FAQFeatureSettingView:
    class FAQFeatureSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.FAQFeatureSetting.objects.all()
        serializer_class = chat_action_serializers.FAQFeatureSettingSerializer.Get
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(FAQFeatureSettingBaseView, APIView):
        def get(self, request, *args, **kwargs):
            callback_chat_feature_setting = (
                chat_action_models.FAQFeatureSetting.objects.first()
            )
            if not callback_chat_feature_setting:
                return Response(status=status.HTTP_404_NOT_FOUND)

            return Response(
                self.serializer_class(callback_chat_feature_setting).data,
                status=status.HTTP_200_OK,
            )

    class Update(FAQFeatureSettingBaseView, generics.UpdateAPIView):
        serializer_class = chat_action_serializers.FAQFeatureSettingSerializer.Patch

        def patch(self, request, *args, **kwargs):
            def set_mutable(data, mutable=True):
                try:
                    data._mutable = mutable
                except AttributeError:
                    pass

            set_mutable(request.data, True)
            request.data["updated_by"] = self.request.user.id
            set_mutable(request.data, False)

            return super().patch(request, *args, **kwargs)


class CustomChatActionSettingView:

    class CustomChatActionSettingBaseView(generics.GenericAPIView):
        queryset = chat_action_models.CustomChatActionSetting.objects.all()
        serializer_class = (
            chat_action_serializers.CustomChatActionSettingSettingSerializer.List
        )
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

    class List(CustomChatActionSettingBaseView, generics.ListAPIView):
        def get(self, request, *args, **kwargs):
            return super().get(request, *args, **kwargs)

    class Get(CustomChatActionSettingBaseView, generics.RetrieveAPIView):
        serializer_class = (
            chat_action_serializers.CustomChatActionSettingSettingSerializer.Get
        )
        lookup_field = "name"

        def get(self, request, *args, **kwargs):
            return super().get(request, *args, **kwargs)

    class Update(CustomChatActionSettingBaseView, generics.UpdateAPIView):
        serializer_class = (
            chat_action_serializers.CustomChatActionSettingSettingSerializer.Post
        )

        def patch(self, request, *args, **kwargs):
            request.data["updated_by"] = self.request.user.id
            return super().patch(request, *args, **kwargs)

    class Create(CustomChatActionSettingBaseView, generics.CreateAPIView):
        serializer_class = (
            chat_action_serializers.CustomChatActionSettingSettingSerializer.Post
        )

        def post(self, request, *args, **kwargs):
            request.data["created_by"] = self.request.user.id
            return super().post(request, *args, **kwargs)
