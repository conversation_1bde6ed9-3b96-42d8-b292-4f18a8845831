from rest_framework import serializers
import app.chats.chat_action.models as chat_action_models

import app.utility.general_helper as utility_general_helper


class AppointmentChatFeatureLocationSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.AppointmentChatFeatureLocation
            fields = [
                "id",
                "name",
                "appointment_chat_feature_setting",
                "location",
                "contact",
                "opening_day_hour",
                "platform",
                "identifier",
                "person_in_charge",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.AppointmentChatFeatureLocation
            fields = [
                "id",
                "name",
                "appointment_chat_feature_setting",
                "location",
                "contact",
                "opening_day_hour",
                "platform",
                "identifier",
                "person_in_charge",
                "updated_by",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.AppointmentChatFeatureLocation
            fields = [
                "id",
                "name",
                "appointment_chat_feature_setting",
                "location",
                "contact",
                "opening_day_hour",
                "platform",
                "identifier",
                "person_in_charge",
                "created_by",
            ]


class AppointmentChatFeatureSettingSerializer:
    class Get(serializers.ModelSerializer):
        locations = serializers.SerializerMethodField()

        def get_locations(self, appointment_chat_feature_setting_instance):
            return AppointmentChatFeatureLocationSerializer.Get(
                appointment_chat_feature_setting_instance.locations, many=True
            ).data

        class Meta:
            model = chat_action_models.AppointmentChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "instruction",
                "location_type",
                "locations",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.AppointmentChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "instruction",
                "location_type",
                "updated_by",
            ]


class CustomerSupportChatFeatureSettingSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.CustomerSupportChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "is_invoice_number",
                "is_invoice_number_mandatory",
                "email_to",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.CustomerSupportChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "is_invoice_number",
                "is_invoice_number_mandatory",
                "email_to",
                "updated_by",
            ]


class CallbackChatFeatureSettingSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.CallbackChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "topics",
                "email_to",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.CallbackChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "email_to",
                "topics",
                "updated_by",
            ]


# Missing Info


class MissingInfoChatFeatureSettingSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.MissingInfoChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.MissingInfoChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "updated_by",
            ]


# Email Pop up
class EmailPopUpFeatureSettingSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.EmailPopUpFeatureSetting
            fields = [
                "id",
                "title",
                "email_field_label",
                "button_label",
                "is_display_signup_newsletter_field",
                "newsletter_field_label",
                "skip_button_label",
                "is_activated",
                "is_compulsory",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.EmailPopUpFeatureSetting
            fields = [
                "id",
                "title",
                "email_field_label",
                "button_label",
                "is_display_signup_newsletter_field",
                "newsletter_field_label",
                "skip_button_label",
                "is_activated",
                "is_compulsory",
                "updated_by",
            ]


# NoReplyOnInaccurateAnswerSetting


class NoReplyOnInaccurateAnswerSettingSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.NoReplyOnInaccurateAnswerSetting
            fields = [
                "id",
                "is_activated",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.NoReplyOnInaccurateAnswerSetting
            fields = [
                "id",
                "is_activated",
                "updated_by",
            ]


class QuestionAnswerPairSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.QuestionAnswerPair
            fields = [
                "id",
                "faq_feature_setting",
                "chroma_db_type",
                "question",
                "answer",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.QuestionAnswerPair
            fields = [
                "id",
                "faq_feature_setting",
                "chroma_db_type",
                "question",
                "answer",
                "updated_by",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.QuestionAnswerPair
            fields = [
                "id",
                "faq_feature_setting",
                "chroma_db_type",
                "question",
                "answer",
                "created_by",
            ]


class FAQFeatureSettingSerializer:
    class Get(serializers.ModelSerializer):
        question_answer_pair_list = serializers.SerializerMethodField()

        def get_question_answer_pair_list(self, faq_feature_setting_instance):
            return QuestionAnswerPairSerializer.Get(
                faq_feature_setting_instance.question_answer_pairs, many=True
            ).data

        class Meta:
            model = chat_action_models.FAQFeatureSetting
            fields = [
                "id",
                "is_activated",
                "question_answer_pair_list",
                "updated_at",
                "updated_by",
            ]

    class Patch(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.FAQFeatureSetting
            fields = [
                "id",
                "is_activated",
                "updated_by",
            ]


# custom
class CustomChatActionSettingSettingSerializer:
    class List(serializers.ModelSerializer):

        snake_cased_name = serializers.SerializerMethodField()

        def get_snake_cased_name(self, custom_chat_action_setting_instance):
            return utility_general_helper.convert_to_snake_case(
                custom_chat_action_setting_instance.name
            )

        class Meta:
            model = chat_action_models.CustomChatActionSetting
            fields = [
                "id",
                "name",
                "snake_cased_name",
                "description",
                "is_activated",
                "icon_svg",
                "created_at",
                "created_by",
                "updated_at",
                "updated_by",
            ]

    class Get(serializers.ModelSerializer):

        additional_field_label_name_list = serializers.SerializerMethodField()

        def get_additional_field_label_name_list(
            self,
            custom_chat_action_setting_instance: chat_action_models.CustomChatActionSetting,
        ):
            additional_field_label_name_list = (
                custom_chat_action_setting_instance.get_additional_field_label_name_list()
            )
            return additional_field_label_name_list

        class Meta:
            model = chat_action_models.CustomChatActionSetting
            fields = [
                "id",
                "name",
                "description",
                "is_activated",
                "icon_svg",
                "additional_field_label_name_list",
                "created_at",
                "created_by",
                "updated_at",
                "updated_by",
            ]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = chat_action_models.AppointmentChatFeatureSetting
            fields = [
                "id",
                "is_activated",
                "icon_svg",
                "created_by",
                "updated_by",
            ]
