from tenant_schemas.utils import schema_context
import app.chats.chat_action.models as chat_action_models


def initialize_chat_action(schema_name, is_activated=False, verbose=False):
    with schema_context(schema_name):
        appointment_chat_feature_setting = (
            chat_action_models.AppointmentChatFeatureSetting.objects.first()
        )

        is_appointment_chat_feature_setting_created = False
        if not appointment_chat_feature_setting:
            is_appointment_chat_feature_setting_created = True
            chat_action_models.AppointmentChatFeatureSetting.objects.create(
                is_activated=is_activated
            )

        customer_support_chat_feature_setting = (
            chat_action_models.CustomerSupportChatFeatureSetting.objects.first()
        )

        is_customer_support_chat_feature_setting_created = False
        if not customer_support_chat_feature_setting:
            is_customer_support_chat_feature_setting_created = True
            chat_action_models.CustomerSupportChatFeatureSetting.objects.create(
                is_activated=is_activated
            )

        callback_chat_feature_setting = (
            chat_action_models.CallbackChatFeatureSetting.objects.first()
        )
        is_callback_chat_feature_setting_created = False
        if not callback_chat_feature_setting:
            is_callback_chat_feature_setting_created = True
            chat_action_models.CallbackChatFeatureSetting.objects.create(
                is_activated=is_activated
            )

        missing_info_chat_feature_setting = (
            chat_action_models.MissingInfoChatFeatureSetting.objects.first()
        )
        is_missing_info_chat_feature_setting_created = False
        if not missing_info_chat_feature_setting:
            is_missing_info_chat_feature_setting_created = True
            chat_action_models.MissingInfoChatFeatureSetting.objects.create(
                is_activated=is_activated
            )

        email_pop_up_feature_setting = (
            chat_action_models.EmailPopUpFeatureSetting.objects.first()
        )
        is_email_pop_up_feature_setting_created = False
        if not email_pop_up_feature_setting:
            is_email_pop_up_feature_setting_created = True
            chat_action_models.EmailPopUpFeatureSetting.objects.create(
                is_activated=is_activated
            )

        no_reply_on_inaccurate_answer_setting = (
            chat_action_models.NoReplyOnInaccurateAnswerSetting.objects.first()
        )
        is_no_reply_on_inaccurate_answer_setting_created = False
        if not no_reply_on_inaccurate_answer_setting:
            is_no_reply_on_inaccurate_answer_setting_created = True
            chat_action_models.NoReplyOnInaccurateAnswerSetting.objects.create(
                is_activated=is_activated
            )

        faq_feature_setting = chat_action_models.FAQFeatureSetting.objects.first()
        is_faq_feature_setting_created = False
        if not faq_feature_setting:
            is_faq_feature_setting_created = True
            chat_action_models.FAQFeatureSetting.objects.create(
                is_activated=is_activated
            )

        if verbose:
            print(
                f"{schema_name} -> Appointment Chat Feature Setting is created: {is_appointment_chat_feature_setting_created}; Customer Support Chat Feature Setting is created: {is_customer_support_chat_feature_setting_created}; Callback Chat Feature Setting is created: {is_callback_chat_feature_setting_created}; Missing Info Chat Feature Setting is created: {is_missing_info_chat_feature_setting_created}; Email Pop Up Feature Setting is created: {is_email_pop_up_feature_setting_created}; No Reply On Inaccurate Answer Setting is created: {is_no_reply_on_inaccurate_answer_setting_created}; FAQ Feature Setting is created: {is_faq_feature_setting_created}"
            )
