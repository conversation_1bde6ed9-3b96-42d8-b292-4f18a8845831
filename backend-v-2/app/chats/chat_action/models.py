from app.utility import models as utility_models

from django.db import models
from django.db.models import QuerySet


# Create your models here.
class AppointmentChatFeatureSetting(utility_models.BaseModel):
    """
    is_activated: whether this feature is activated
    instruction: what to put in the extra instruction, e.g.: "(If they are potential customer, passionately ask them for their availability to the showrooms by sending a formal invite!)"
    location_type: Showroom, Office, Shop, etc?
    addresses: what are the locations + details
    """

    is_activated = models.BooleanField(default=False)
    location_type = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # showroom, office etc...
    instruction = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # If they are potential customer, passionately ask them for their availability to the showrooms by sending a formal invite!

    def is_usable(self):
        location_count = (
            self.locations.count()
        )  # Assuming 'locations' is the related name for AppointmentChatFeatureLocation
        return self.is_activated and location_count > 0 and self.location_type

    def describe(self):
        #  Adding locations and their details if any, with numbering
        description_parts = []
        locations = (
            self.locations.all()
        )  # Assuming 'locations' is the related name for AppointmentChatFeatureLocation
        if locations:
            description_parts.append("Locations:")
            for index, location in enumerate(locations, start=1):
                location_description = location.describe()
                description_parts.append(f"{index}. {location_description}")

        return "\n".join(description_parts)

    def get_location_address_list(self):
        #  Adding locations and their details if any, with numbering
        location_address_list = []
        locations: QuerySet[AppointmentChatFeatureLocation] = (
            self.locations.all()
        )  # Assuming 'locations' is the related name for AppointmentChatFeatureLocation
        if locations:
            for index, location in enumerate(locations, start=1):
                location_address_list.append(location.location)

        return location_address_list

    def get_location_address_list_count(self):
        #  Adding locations and their details if any, with numbering
        locations: QuerySet[AppointmentChatFeatureLocation] = self.locations.all()
        return locations.count()

    def get_location_address_opening_hour_through_address_str(
        self, location_address_str
    ):
        #  Adding locations and their details if any, with numbering
        location = self.locations.filter(
            location__icontains=location_address_str
        ).first()  # Assuming 'locations' is the related name for AppointmentChatFeatureLocation
        if location:
            return location.describe_opening_hour()
        return ""

    def get_location_address_opening_hour_through_name(self, location_name):
        #  Adding locations and their details if any, with numbering
        location = self.locations.filter(
            name__icontains=location_name
        ).first()  # Assuming 'locations' is the related name for AppointmentChatFeatureLocation
        if location:
            return location.describe_opening_hour()
        return ""

    class Meta:
        ordering = ["-created_at"]


class AppointmentChatFeatureLocation(utility_models.NamedBaseModel):
    appointment_chat_feature_setting = models.ForeignKey(
        AppointmentChatFeatureSetting,
        related_name="locations",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    location = models.TextField(blank=True)  # address
    contact = models.CharField(max_length=17, blank=True)
    opening_day_hour = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # Monday - Friday, 11:30am - 9:00pm

    # where to update via chat? e.g channel: WhatsAppQR, platform: Test Group
    platform = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    identifier = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # The group/chat name
    person_in_charge = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )

    def describe(self):
        descriptions = []
        descriptions.append(f"Location: {self.name}")
        descriptions.append(f"Address: {self.location}")
        descriptions.append(f"Contact: {self.contact}")
        descriptions.append(
            f"Opening Hour (GMT+8): {self.opening_day_hour}"
        )  # TODO-yc: dynamic in future
        return ". ".join(descriptions) + "."

    def describe_opening_hour(self):
        return f"{self.opening_day_hour} (GMT+8)"  # TODO-yc: dynamic in future

    class Meta:
        ordering = ["-created_at"]


class CustomerSupportChatFeatureSetting(utility_models.BaseModel):
    """
    by default it will collect contact,email,description
    is_activated: whether this feature is activated
    is_invoice_number: Ask whether there's invoice number
    is_invoice_number_mandatory: Is invoice number mandatory to collect
    """

    is_activated = models.BooleanField(default=False)
    is_invoice_number = models.BooleanField(default=False)
    is_invoice_number_mandatory = models.BooleanField(default=False)
    email_to = models.EmailField(max_length=255, blank=True)

    def is_usable(self):
        return self.is_activated

    class Meta:
        ordering = ["-created_at"]


class CallbackChatFeatureSetting(utility_models.BaseModel):
    """
    is_activated: whether this feature is activated
    topics: List of topics that show what should trigger thec callback feature

    THOUGHTS:
    1. Thinking to combine this with "Missing Information"
    2. Flow:
        a) User enquiry come in
        b) Bot answer
        c) Beefy evaluate whether this is good enough
        d) Beefy says it's not good, create an instance to jot this question down.
        e) When reply to the customer, add some addition instruction like "Ask the customer whether they want to get contacted for further assistances"
        f) If yes, trigger callback request thingy, which is this thing.

    """

    is_activated = models.BooleanField(default=False)
    topics = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    email_to = models.EmailField(max_length=255, blank=True)

    def is_usable(self):
        return self.is_activated

    def describe(self):
        return self.topics

    class Meta:
        ordering = ["-created_at"]


##Missing Info


class MissingInfoChatFeatureSetting(utility_models.BaseModel):
    """
    is_activated: whether this feature is activated
    """

    is_activated = models.BooleanField(default=False)

    def is_usable(self):
        return self.is_activated

    class Meta:
        ordering = ["-created_at"]


## Email Pop up
class EmailPopUpFeatureSetting(utility_models.BaseModel):
    """
    is_activated: whether this feature is activated
    """

    title = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    email_field_label = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    button_label = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    is_display_signup_newsletter_field = models.BooleanField(default=True)
    newsletter_field_label = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    skip_button_label = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )

    is_activated = models.BooleanField(default=False)
    is_compulsory = models.BooleanField(default=False)

    def is_usable(self):
        return self.is_activated

    class Meta:
        ordering = ["-created_at"]


## No reply when conversation end
class NoReplyOnInaccurateAnswerSetting(utility_models.BaseModel):
    """
    is_activated: whether this feature is activated
    """

    is_activated = models.BooleanField(default=False)

    def is_usable(self):
        return self.is_activated

    class Meta:
        ordering = ["-created_at"]


##FAQ
class FAQFeatureSetting(utility_models.BaseModel):
    is_activated = models.BooleanField(default=False)  # legacy, no use now

    def is_question_answer_pair_exist(self):
        question_answer_pair_count = self.question_answer_pairs.count()
        return question_answer_pair_count > 0

    def get_question_answer_pair_list(self):
        #  Adding locations and their details if any, with numbering
        question_answer_pair_list = []
        faq_question_answer_pairs: QuerySet[QuestionAnswerPair] = (
            self.question_answer_pairs.all()
        )  # Assuming 'faq_question_answer_pairs' is the related name for AppointmentChatFeatureLocation
        if faq_question_answer_pairs:
            for index, question_answer_pair in enumerate(
                faq_question_answer_pairs, start=1
            ):
                question_answer_pair_list.append(
                    f"{index}. {question_answer_pair.describe()}"
                )

        return "\n".join(question_answer_pair_list)


class QuestionAnswerPair(utility_models.BaseModel):
    faq_feature_setting = models.ForeignKey(
        FAQFeatureSetting,
        related_name="question_answer_pairs",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    chroma_db_type = models.CharField(
        max_length=50,
        choices=utility_models.CHROMA_DB_TYPE,
        default=utility_models.EXTERNAL_TEXT,
    )
    question = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )
    answer = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )

    def describe(self):
        description = f"Question: {self.question};Answer: {self.answer}"
        return description

    class Meta:
        ordering = ["question"]


# Customize
class CustomChatActionSetting(utility_models.UniqueNamedBaseModel):  # serve as category
    # unique name so the tab don't duplicate
    description = models.CharField(
        max_length=utility_models.DEFAULT_CHAR_FIELD_MAX, blank=True
    )  # description for the name
    is_activated = models.BooleanField(default=True)  # to disable if required
    icon_svg = models.TextField(blank=True)
    on_approve = models.TextField(blank=True)  # _function_str
    on_reject = models.TextField(blank=True)  # _function_str

    def get_additional_field_labels(self):
        if self.custom_action_trackers.exists():
            additional_field_labels = (
                self.custom_action_trackers.first().get_field_labels()
            )
            return additional_field_labels
        return []

    def get_additional_field_names(self):
        if self.custom_action_trackers.exists():
            additional_field_names = (
                self.custom_action_trackers.first().get_field_names()
            )
            return additional_field_names
        return []

    def get_additional_field_label_name_list(self):
        labels = self.get_additional_field_labels()
        names = self.get_additional_field_names()

        if labels and names:
            return [
                {"name": name, "label": label} for name, label in zip(names, labels)
            ]

        return []

    class Meta:
        ordering = ["id"]
