from app.integrations.facebook_integration.models import Credential
from app.chats.chat.models import Conversation
from app.chats.chat_action_tracker.models import Appointment
from app.chats.chat_action.models import AppointmentChatFeatureLocation
import app.utility.models as utility_models
from tenant_schemas.utils import schema_context, get_tenant_model
from django.contrib.auth import get_user_model
from django.db import connections, router


def get_chat_count(schema_name, start_date, end_date):
    with schema_context(schema_name):
        fb_pages = Credential.objects.filter(tenant__schema_name=schema_name)
        for fb_page in fb_pages:
            chat_count = Conversation.objects.filter(
                source="Facebook",
                sub_source=fb_page.PAGE_ID,
                created_at__gte=start_date,
                created_at__lte=end_date,
            ).count()
            print(f"{fb_page.PAGE_NAME} Chat Count: {chat_count}")


def get_appointment_count(schema_name, start_date, end_date):
    with schema_context(schema_name):
        locations = AppointmentChatFeatureLocation.objects.all()
        total_appointment_count = 0
        for location in locations:
            appointment_instances = Appointment.objects.filter(
                datetime__gte=start_date,
                datetime__lte=end_date,
                appointment_chat_feature_location=location,
            )
            total_appointment_count += appointment_instances.count()
            print(f"\n{location.name}")
            print(
                f"{utility_models.PENDING} Appointments: {appointment_instances.filter(status=utility_models.PENDING).count()}"
            )
            print(
                f"{utility_models.SALES_NOT_MADE} Appointments: {appointment_instances.filter(status=utility_models.SALES_NOT_MADE).count()}"
            )
            print(
                f"{utility_models.ABSENT} Appointments: {appointment_instances.filter(status=utility_models.ABSENT).count()}"
            )
            sales_made_instances = appointment_instances.filter(
                status=utility_models.SALES_MADE
            )
            print(
                f"{utility_models.SALES_MADE} Appointments: {sales_made_instances.count()}"
            )
            for index, sales_made_instance in enumerate(sales_made_instances, start=1):
                print(f"Remarks {index}: {sales_made_instance.remarks}")

        print(f"\nTotal Appointments made in {schema_name}: {total_appointment_count}")


def print_appointment_stats(schema_name_list, start_date, end_date):
    all_tenants = get_tenant_model().objects.filter(schema_name__in=schema_name_list)
    User = get_user_model()
    using = router.db_for_read(User)
    connection = connections[using]

    for tenant in all_tenants:
        # Set the current tenant.
        connection.set_tenant(tenant)
        saved_schema = (
            tenant.schema_name
        )  # Get schema name directly from the tenant object.
        if saved_schema == "public":
            continue
        else:
            print(
                f"============= {saved_schema} ({start_date} - {end_date}) ==============="
            )
            get_chat_count(saved_schema, start_date, end_date)
            print(f"----------------------------------------------")
            get_appointment_count(saved_schema, start_date, end_date)
            print(f"============================================\n")

    # Don't forget to close the connection at the end.
    connection.close()
