# Generated by Django 4.2.5 on 2024-05-17 02:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat_action', '0009_appointmentchatfeaturelocation_person_in_charge'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomChatActionSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=8912, unique=True)),
                ('description', models.CharField(blank=True, max_length=8912)),
                ('is_activated', models.BooleanField(default=True)),
                ('icon_svg', models.TextField(blank=True)),
                ('additional_field_names', models.J<PERSON>NField(blank=True, default=list, null=True)),
                ('created_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
