# Generated by Django 4.2.5 on 2024-08-19 09:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat_action', '0014_noreplyoninaccurateanswersetting'),
    ]

    operations = [
        migrations.AddField(
            model_name='emailpopupfeaturesetting',
            name='button_label',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='emailpopupfeaturesetting',
            name='email_field_label',
            field=models.Char<PERSON>ield(blank=True, max_length=8912),
        ),
        migrations.AddField(
            model_name='emailpopupfeaturesetting',
            name='is_display_signup_newsletter_field',
            field=models.<PERSON><PERSON><PERSON><PERSON>ield(default=True),
        ),
        migrations.AddField(
            model_name='emailpopupfeaturesetting',
            name='newsletter_field_label',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=8912),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='emailpopupfeaturesetting',
            name='skip_button_label',
            field=models.Char<PERSON>ield(blank=True, max_length=8912),
        ),
        migrations.Add<PERSON>ield(
            model_name='emailpopupfeaturesetting',
            name='title',
            field=models.CharField(blank=True, max_length=8912),
        ),
    ]
