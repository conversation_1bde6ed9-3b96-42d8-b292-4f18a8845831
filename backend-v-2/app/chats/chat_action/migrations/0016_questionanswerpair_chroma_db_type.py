# Generated by Django 4.2.5 on 2024-08-22 08:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat_action', '0015_emailpopupfeaturesetting_button_label_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='questionanswerpair',
            name='chroma_db_type',
            field=models.CharField(choices=[('internal', 'internal'), ('external', 'external')], default='external', max_length=50),
        ),
    ]
