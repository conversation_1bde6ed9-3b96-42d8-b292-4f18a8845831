import requests
from bs4 import BeautifulSoup
from app.core.onboarding.scrape import <PERSON><PERSON><PERSON>
import os
from sumy.parsers.plaintext import PlaintextParser
from sumy.nlp.tokenizers import Tokenizer
from sumy.summarizers.lsa import LsaSummarizer
from sentence_transformers import SentenceTransformer, util
import re
import time

WEB_CONTENT_DIRECTORY = "./web_content"
MAX_CHARS = (
    18000 * 16
)  # gpt4 turbo have 128k context (initially 8k) > let's use multiply of 16 of the original 18k chars


def get_web_content_prefix_directory(schema_name):
    return os.path.join(WEB_CONTENT_DIRECTORY, f"{schema_name}_content")


# Web scrape
def fetch_and_parse(url):
    response = requests.get(url)
    if response.status_code == 200:
        return BeautifulSoup(response.text, "html.parser")
    else:
        print(f"Failed to fetch the webpage. HTTP Error Code: {response.status_code}")
        return None


def extract_content(soup):
    # Remove script and style tags
    [s.extract() for s in soup(["script", "style"])]
    # Optionally: Target specific tags or classes. Uncomment below if needed.
    # soup = soup.find_all('p')  # Extracts only paragraph content
    content = soup.get_text()
    # Remove extra spaces and line breaks
    content = " ".join(content.split())
    return content


# ============ summarizing content
def is_only_english_chars(sentence):
    non_ascii_count = 0

    for char in sentence:
        if not char.isascii():  # Check if the character is non-ASCII
            non_ascii_count += 1
            if non_ascii_count > 10:  # If more than 10 non-ASCII characters are found
                return False

    return True  # Returns True if non-ASCII characters are 10 or less


def is_significant(sentence):
    non_informative_phrases = ["thank you", "please note", "hello", "hi", "regards"]
    stopwords = ["a", "an", "the", "in", "of", "to", "is", "and", "on", "for", "with"]

    # Check against non-informative phrases
    for phrase in non_informative_phrases:
        if phrase in sentence.lower():
            return False

    # Check for sentences with mostly stopwords
    words = sentence.split()
    if len(words) < 2:
        return False
    stopword_count = sum(1 for word in words if word.lower() in stopwords)
    if stopword_count / len(words) > 1.5:
        return False

    return True


def remove_duplicates(sentences, threshold=0.9):
    model = SentenceTransformer("paraphrase-MiniLM-L6-v2")

    # Convert sentences to embeddings
    embeddings = model.encode(sentences, convert_to_tensor=True)

    deduplicated_sentences = []
    deduplicated_embeddings = []

    for idx, sentence in enumerate(sentences):
        is_duplicate = False

        for dedup_emb in deduplicated_embeddings:
            # Compute cosine similarity
            cosine_scores = util.pytorch_cos_sim(embeddings[idx], dedup_emb)

            if cosine_scores.item() > threshold:
                is_duplicate = True
                break

        if not is_duplicate:
            deduplicated_sentences.append(sentence)
            deduplicated_embeddings.append(embeddings[idx])

    return deduplicated_sentences


def summarize_to_characters(text):
    if len(text) <= MAX_CHARS:
        return text

    parser = PlaintextParser.from_string(text, Tokenizer("english"))
    summarizer = LsaSummarizer()

    # Adjust the number of sentences dynamically based on text length
    initial_sentences = min(100, len(text) // 100)

    all_sentences = [
        str(sentence) for sentence in summarizer(parser.document, initial_sentences)
    ]
    summary = []
    current_length = 0

    for sentence in all_sentences:
        if current_length + len(sentence) + 1 <= MAX_CHARS:  # +1 for space
            summary.append(sentence)
            current_length += len(sentence) + 1
        else:
            if current_length == 0 and len(sentence) > MAX_CHARS:
                # Break long sentences at a natural point
                return sentence[:MAX_CHARS].rsplit(" ", 1)[0]
            break  # Ensure the summary ends with a complete sentence

    return " ".join(summary)[:MAX_CHARS]


def get_cleaned_text(text):
    # Helper function to judge if a sentence is significant

    # 1. Identify and replace links with placeholders
    link_pattern = (
        r"https?://\S+|www\.\S+"  # Regular expression pattern for detecting links
    )
    links = re.findall(link_pattern, text)

    for idx, link in enumerate(links):
        placeholder = f"{{LINK{idx}}}"
        text = text.replace(link, placeholder)

    # 2. Split the text into sentences
    sentences = re.split(r"\. |\n|\? |\! ", text)
    sentences = [sentence.strip() for sentence in sentences if sentence.strip()]

    # 3. Replace the placeholders with the original links
    for idx, link in enumerate(links):
        placeholder = f"{{LINK{idx}}}"
        sentences = [sentence.replace(placeholder, link) for sentence in sentences]
    print("Links found: ", len(links))
    # 4. Filter out non-significant sentences
    sentences = [
        sentence
        for sentence in sentences
        if is_significant(sentence) and is_only_english_chars(sentence)
    ]
    sentences = remove_duplicates(sentences)
    clean_text = summarize_to_characters(".".join(sentences))
    # print("Clean text length: ", len(clean_text))
    # print("Cleaned text: ", clean_text)
    return clean_text + f"Website Links: {links}"


def limit_content_to_pages(content, words_per_page=250, pages=30):
    words = content.split()
    limit = words_per_page * pages

    # Limit the number of words
    limited_words = words[:limit]

    return " ".join(limited_words)


def get_website_content(url, schema_name, iterations=3, url_limit=10):
    start_time = time.time()
    raw_content_directory = get_web_content_prefix_directory(schema_name)
    raw_content_directory_file = os.path.join(raw_content_directory, "raw.txt")
    summarize_content_directory_file = os.path.join(
        raw_content_directory, "summarized.txt"
    )

    # if True:
    #     with open(summarize_content_directory_file, "r") as f:
    #         content_read = f.read()
    #         return content_read

    os.makedirs(raw_content_directory, exist_ok=True)

    crawler = Crawler(url, url_limit=url_limit)
    crawler.pull_content(iterations=iterations)  # 5 levels of urls
    content = crawler.get_content_in_string()
    with open(raw_content_directory_file, "w") as f:
        f.write(content)

    content = get_cleaned_text(content)
    with open(summarize_content_directory_file, "w") as f:
        f.write(content)
    crawler.quit()

    # #read content
    # with open(summarize_content_directory_file, "r") as f:
    #     content = f.read()

    print(
        f"Time taken to scrape web: {time.time() - start_time:.2f} seconds"
    )  # eatirvins: Time taken to scrape web: 47.54 seconds; after multithread (3 workers) -> 33.46; eatirvins in server: 84.01, multithread stuck in server.
    return content
    # try:
    # except Exception as e:
    #     print(f'An error occured while pulling content from {url}: \n{e}')
    # crawler.quit()
