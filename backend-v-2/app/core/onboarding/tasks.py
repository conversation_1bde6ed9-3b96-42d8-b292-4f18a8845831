from celery import shared_task
import app.documents.document.models as document_models
import app.core.onboarding.helper as onboarding_helper
import app.core.onboarding.web_content_helper as web_content_helper
import app.core.onboarding.customer_policy_generator as onboarding_customer_policy_generator
import app.core.onboarding.customer_policy_helper as onboarding_customer_policy_helper
import app.documents.document.helper as document_helper
import app.utility.models as utility_models
from tenant_schemas.utils import schema_context
from django.core.files import File
import app.chats.chat.constant as chat_constant
import app.utility.chroma_db_helper as utility_chroma_db_helper

import app.redteam.tasks as redteam_tasks
import app.redteam.models as redteam_models
import os
import re
import time


def generate_policy(
    website_url,
    customer_name,
    about_me,
    target_industries_audiences,
    contact,
    testimonials,
    milestones,
    schema_name,
    document_id,
    web_content_document_id,
    iterations,
    url_limit,
):
    """
    27304 characters = ~6838 tokens; 25952 characters = ~ 6609; 23650 characters ~= 6144  (GPT-4 Input limit = 8192 * 4)
    Completion Roughly need ~600 tokens (GPT 4 output token limit = 4096)
    1 token ~= 3.8 characters

    Total GPT-4 chars = 8192 * 4 * 8.5/10 (margin 15%) -> ~ 28000 characters
    v1 policy (chars) = 9493 (instructions), hence context can only be < 18000 (safe)
    """
    start_time = time.time()

    with schema_context(schema_name):
        print("start generating policy...")
        document_instance = document_models.Document.objects.get(id=document_id)
        web_content_document_instance = document_models.Document.objects.filter(
            id=web_content_document_id
        ).first()
        try:
            if web_content_document_instance:
                print(
                    f"Getting website content with {url_limit} urls in {iterations} iterations..."
                )
                web_content = web_content_helper.get_website_content(
                    website_url,
                    schema_name,
                    iterations=iterations,
                    url_limit=url_limit,
                )
                # assert 1 == 2
                print("Website content is pulled ")
                print("=====================================")
            else:
                print("No web_content_document_instance")
                web_content = ""
            re.sub(" +", " ", web_content)

            customer_info = onboarding_helper.get_customer_info(
                website_url,
                about_me,
                target_industries_audiences,
                contact,
                testimonials,
                milestones,
            )

            print("Generating customer policy...")
            customer_policy = (
                onboarding_customer_policy_generator.generate_customer_policy(
                    web_content,
                    customer_name,
                    customer_info,
                )
            )

            web_content_status = utility_models.FAILED
            if web_content and web_content_document_instance:
                print("Website context found. Saving the context to document...")
                # save web content to s3
                web_content_txt_file_io = onboarding_helper.txt_to_txt_file_io(
                    web_content
                )
                web_content_document_instance.file.save(
                    f"{onboarding_customer_policy_helper.DOCUMENT_WEB_CONTENT_INSTANCE_NAME}.txt",
                    File(web_content_txt_file_io),
                )
                web_content_status = utility_models.SUCCEED

            if web_content_document_instance:
                web_content_document_instance.status = web_content_status
                if web_content_status == utility_models.FAILED:
                    web_content_document_instance.status_description = (
                        "No context found in the provided URL"
                    )
                web_content_document_instance.save()

            # save customer policy to s3
            customer_policy_txt_file_io = onboarding_helper.txt_to_txt_file_io(
                customer_policy
            )
            document_instance.file.save(
                f"{onboarding_customer_policy_helper.DOCUMENT_CUSTOMER_POLICY_INSTANCE_NAME}.txt",
                File(customer_policy_txt_file_io),
            )

            # if path exist
            external_collection_name = chat_constant.get_collection_name(
                chat_constant.external_collection,
                schema_name,
            )
            internal_collection_name = chat_constant.get_collection_name(
                chat_constant.internal_collection,
                schema_name,
            )
            client = utility_chroma_db_helper.get_chromadb_client()
            all_collection_names = utility_chroma_db_helper.list_collection_names(
                client
            )
            if (
                external_collection_name in all_collection_names
                and internal_collection_name in all_collection_names
            ):
                print("Chroma DB directory exist, adding doc to chroma db...")
                # add doc only
                utility_chroma_db_helper.add_doc_to_all_chroma_db(
                    document_instance.file.url,
                    document_instance.name,
                    chat_constant.get_collection_name(
                        chat_constant.external_collection,
                        schema_name,
                    ),
                    chat_constant.get_collection_name(
                        chat_constant.internal_collection,
                        schema_name,
                    ),
                )
                if web_content:
                    utility_chroma_db_helper.add_doc_to_all_chroma_db(
                        web_content_document_instance.file.url,
                        web_content_document_instance.name,
                        chat_constant.get_collection_name(
                            chat_constant.external_collection,
                            schema_name,
                        ),
                        chat_constant.get_collection_name(
                            chat_constant.internal_collection,
                            schema_name,
                        ),
                    )
                print("Docs are added to chroma db!")
            else:
                print("Chroma DB directory doesn't exist. Calling update doc search...")
                # update chroma_db_collection
                document_helper.call_update_doc_search(schema_name)
                print("Finish calling update doc search!")

            print(
                f"Before: {document_instance.status=}, {document_instance.file.name=}"
            )
            document_instance.status = utility_models.SUCCEED
            document_instance.save()
            print(f"After: {document_instance.status=}, {document_instance.file.name=}")

        except Exception as e:
            print("error when generating policy: ", e)
            import traceback

            print("Traceback start =======")
            traceback.print_exc()
            print("Traceback end =======")
            document_instance.status = utility_models.FAILED
            document_instance.status_description = (
                "Error occurs during the policy generation."
            )
            document_instance.save()

            if web_content_document_instance:
                web_content_document_instance.status = utility_models.FAILED
                web_content_document_instance.status_description = (
                    "Error occurs during the policy generation."
                )
                web_content_document_instance.save()
            print("updated docs to failed status.")
            return

        print(
            f"Time taken to generate policy: {time.time() - start_time:.2f} seconds"
        )  # eatirvins: Time taken to generate policy: 182.93 seconds # Server: 201.68 seconds

        # the return statement above will prevents continue for evaluation if error
        # If no existing evaluation_instance after policy generation, then generate report
        # if redteam_models.Evaluation.objects.count() == 0:
        #     evaluation_instance = redteam_models.Evaluation.objects.create(
        #         status=utility_models.PENDING,
        #     )
        #     redteam_tasks.generate_evaluation_report_task.apply_async(
        #         args=[evaluation_instance.id, schema_name],
        #     )


@shared_task()
def generate_policy_task(
    website_url,
    customer_name,
    about_me,
    target_industries_audiences,
    contact,
    testimonials,
    milestones,
    schema_name,
    document_id,
    web_content_document_id,
    iterations=5,
    url_limit=10,
):
    generate_policy(
        website_url,
        customer_name,
        about_me,
        target_industries_audiences,
        contact,
        testimonials,
        milestones,
        schema_name,
        document_id,
        web_content_document_id,
        iterations,
        url_limit,
    )
