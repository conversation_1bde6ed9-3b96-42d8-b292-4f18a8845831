import time
import app.chats.chat.constant as chat_constant
import app.chats.bot.openai_helper as bot_openai_helper


def generate_customer_policy(
    context,
    customer_name,
    customer_info,
):
    customer_policy_v1_messages = [
        {
            "role": "system",
            "content": get_system_prompt_template(
                context, customer_name, customer_info
            ),
        },
        {"role": "user", "content": get_user_policy_prompt_template(customer_name)},
    ]
    # print(
    #     "customer_policy_v1_messages content char count: ",
    #     len(customer_policy_v1_messages[0]["content"]),
    # )
    start_time = time.time()
    try:
        MODEL_NAME = chat_constant.GPT_4_MODEL_CHOICES[1]
        chat_completion_response = bot_openai_helper.get_chat_completion_response(
            MODEL_NAME,
            customer_policy_v1_messages,
        )
    except Exception as e:
        print("error with gpt-4: ", e)
        MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[1]
        chat_completion_response = bot_openai_helper.get_chat_completion_response(
            MODEL_NAME,
            customer_policy_v1_messages,
        )

    print(
        f"Time taken for Generatin Customer Policy V1: {time.time() - start_time:.2f} seconds. With response: {chat_completion_response}"
    )  # eatirivins: Time taken for Generatin Customer Policy V1: 59.32 seconds; Server: 65.89

    bot_openai_helper.openai_api_calculate_cost(
        chat_completion_response.usage, model=MODEL_NAME, verbose=True
    )
    customer_policy_v1 = chat_completion_response.choices[0].message.content
    # print(f"V1: {customer_policy_v1=}")

    # V2 clean up with chatgpt4
    # summarized_context = onboarding_web_content_helper.get_cleaned_text(
    #     context, 23000
    # )  # to get summarize to feed in gpt4
    summarized_context = context
    customer_policy_v2_messages = [
        {
            "role": "system",
            "content": get_system_prompt_policy_enhance_template(
                summarized_context, customer_name, customer_info, customer_policy_v1
            ),
        },
        {
            "role": "user",
            "content": get_user_policy_prompt_template(customer_name, True),
        },
    ]
    # print(
    #     "customer_policy_v2_messages content char count: ",
    #     len(customer_policy_v2_messages[0]["content"]),
    # )
    start_time = time.time()
    try:
        MODEL_NAME = chat_constant.GPT_4_MODEL_CHOICES[1]
        chat_completion_response_v2 = bot_openai_helper.get_chat_completion_response(
            MODEL_NAME,
            customer_policy_v2_messages,
        )

    except Exception as e:
        MODEL_NAME = chat_constant.GPT_35_MODEL_CHOICES[1]
        print("error with gpt-4: ", e)
        chat_completion_response_v2 = bot_openai_helper.get_chat_completion_response(
            MODEL_NAME,
            customer_policy_v2_messages,
        )

    print(
        f"Time taken for Generatin Customer Policy V2: {time.time() - start_time:.2f} seconds. With response: {chat_completion_response_v2}"
    )  # eatirvins: Time taken for Generatin Customer Policy V2: 70.96 seconds; server: 46.58
    bot_openai_helper.openai_api_calculate_cost(
        chat_completion_response_v2.usage, model=MODEL_NAME, verbose=True
    )

    customer_policy_v2 = chat_completion_response_v2.choices[0].message.content
    # print("customer_policy_v2: ", customer_policy_v2)
    return customer_policy_v2


def get_system_prompt_template(context, customer_name, customer_info):
    system_prompt_template = f"""
You are a Curation & Customer Policy Development Expert with adeptness in distilling large volumes of company-related text into concise, impactful content. Renowned for synthesizing detailed company profiles and crafting analogous policy narratives that resonate with brand ethos. Demonstrates an intrinsic understanding of contextual nuances and aligns them seamlessly with business objectives. Skilled in cross-functional collaboration, ensuring streamlined communication and content consistency across all touchpoints.

This is Kumo (Mattresses Related Business) customer policy:
----
## Hey there! Welcome aboard!
- This is your guide to all things Kumo.
- Our website is https://kumo.sg/
- Here you'll find everything you need to know about our mattresses and services.
- Sit back, relax, and let's dive right in!

## All about our Mattresses
- Kumo sells the most value for money mattress in Singapore.
- Backed by over 40 years of mattress and furniture retail experience in Asia Pacific, our founders are Jamie and Ken.
- We are committed to making the best mattress available at the most comfortable price points.
- We've got four sizes: single, super single, queen, and king.
- Each mattress is uniquely crafted with top-notch materials.
- Our mattresses are designed in Germany, and made in Malaysia.
- We're proud of our quality.
- We value comfort and durability.
- Our mattresses are designed for universal comfort.
- We currently do not offer custom sizes.
- You can select your mattress here: https://kumo.sg/product/the-mattress/
- You can buy your mattress here: https://kumo.sg/product/the-mattress/

## The Stuff We Use
- A range of materials goes into our mattresses, starting with pocket-coils, to memory foam, high-density foam and even a cooling fabric to keep you cool at night!
- Our research helped us arrive at the best quality for Singapore's preferred price point.
- Each material is chosen for the best balance of comfort and support.
- Our mattresses are designed to keep you cool in the Singapore weather.
- We have progressive firmness across the mattress to perfectly handle weight distribution.
- Our materials are durable and long-lasting.
- We constantly innovate to provide the best sleeping experience.
- All mattresses are hypoallergenic.
- We believe that a good day starts with a good night's sleep!

## Show me the Money!
- We offer quality that doesn't break the bank.
- Our payment gateway is powered by Stripe, which accepts most major credit cards, and we'll be exploring QR payments soon.
- We strive to make the payment process easy.
- We believe in transparency, no hidden costs.
- We often run promotions and discounts, currently we have the 8/8 Sale!
- We're all about giving you the best value.
- We're here to help if you encounter any payment issues.
- Our payment process is secure and encrypted.
- We respect your privacy and protect your information.

## Delivery and Setup
- We deliver right to your doorstep, free of charge.
- Each mattress comes wrapped in durable plastic for transport.
- We do not offer mattress in a box as it can affect spring durability if rolled up for too long. Also, our springs are too dense to be bent :)
- We strive for quick, reliable delivery.
- We'll keep you informed every step of the way.
- Your comfort is just a click away!

## Try and Return
- You get a 120-night trial period.
- Not happy? We'll arrange for pickup and a full refund.
- We believe in love at first sleep, but if it's not, that's okay.
- We make returns hassle-free.
- Just reach out, and we'll guide you through the process.
- We value your feedback and work on improvements.
- We're committed to your satisfaction.
- No restocking fees or hidden costs.
- Your happiness is our priority.
- We believe that buying a mattress should be risk-free.

## Warranty
- We offer a 10-year warranty.
- Found a manufacturing defect? We'll replace your mattress.
- We stand by our quality and durability.
- We're here to support you long after your purchase.
- We handle warranty claims quickly and efficiently.
- We value your trust and work hard to maintain it.
- Your peace of mind is important to us.
- Our warranty covers a comprehensive range of defects.
- We believe in our products, and we back them up.
- Rest easy knowing you're covered!

## Old Mattress Removal
- We currently don't offer mattress removal.
- But we're always looking for ways to enhance our services.
- Stay tuned for updates and new services.
- We value your feedback and suggestions.
- We're constantly evolving to meet your needs.
- We appreciate your understanding and patience.
- Your comfort and convenience are our top priorities.
- In the meantime, we're here to guide you through other aspects of your purchase.
- We're committed to making your experience seamless.
- Your journey to better sleep is our mission.

## Need Help?
- Got questions? Just drop us a message!
- Our team is always here to help.
- We're friendly, approachable, and ready to assist.
- We believe in prompt, efficient customer service.
- Reach out anytime, we're just a message away.
- We're dedicated to making your experience awesome.
- No question is too big or small.
- Your concerns are our concerns.
- We're here to ensure you get the best sleep ever!


Understand the nature of {customer_name} in the Context below.

----------------
Context: 
{context} 
{customer_info}

Then, generate a company policy created for {customer_name} in the same structure and style as the Kumo policy, with meticulous extraction and adaptation and only RELEVANT INFORMATION. 

Here's a step-by-step guide on how you might approach it:

Introduction:
Extract basic information about {customer_name} from the provided PDF.
Document the official website and any introductory details.

Product Details:
Identify the main products or services offered by {customer_name}.
Note unique selling points, product varieties, and direct links to specific product pages if available.

Materials/Technology/Process:
Document the unique materials, technologies, or processes that differentiate their offerings, similar to Kumo's use of specific mattress materials.

Pricing and Payment:
Extract all pricing details and payment methods.
Include information on promotions and guarantees related to payment transparency and security.

Delivery and Setup:
Describe the delivery process for products, or access methods for services or digital goods.

Trial and Return (if applicable or equivalent):
Outline any trial or return policies, noting the process, fees, and guarantees.

Warranty (if applicable or equivalent):
If relevant, detail the warranty period, coverage, and claim process. Omit this section for organizations that do not provide tangible products.

Additional Services (if applicable or equivalent):
Identify any additional services like Kumo's "Old Mattress Removal" and document their details.

Customer Service:
Extract information on customer support channels, satisfaction guarantees, and unique service aspects.

Note: Omit sections not relevant to the specific nature of {customer_name}'s business to keep the policy pertinent and concise.
    """
    return system_prompt_template.strip()


def get_system_prompt_policy_enhance_template(
    context, customer_name, customer_info, customer_policy_v1
):
    system_prompt_template = f"""
System: You are a Curation & Customer Policy Development Expert with adeptness in distilling large volumes of company-related text into concise, impactful content. Renowned for synthesizing detailed company profiles and crafting analogous policy narratives that resonate with brand ethos. Demonstrates an intrinsic understanding of contextual nuances and aligns them seamlessly with business objectives. Skilled in cross-functional collaboration, ensuring streamlined communication and content consistency across all touchpoints.

This is {customer_name} customer policy now:
{customer_policy_v1}


Use the following context below, enhance the above customer policy.
Context: {context}
{customer_info}

Your goal is to:
1. Correct all the wrong information in the current customer policy, for example the website url and etc by referencing the Context as the source of truth.
2. Add in additional information that are not stated in the customer policy.
3. Capture as many useful information from the context and add it to the customer policy appropriately. 
4. Do not make up information that doesn't exist.
5. Finalize and return a new improved customer policy with all the important context stated correctly. 

    """
    return system_prompt_template.strip()


def get_user_policy_prompt_template(customer_name, is_new=False):
    prompt_template = f"""
{"New " if is_new else ""}{customer_name} customer policy:
    """
    return prompt_template
