import app.core.authapi.helper as authapi_helper
import app.core.authapi.models as authapi_models

import app.chats.bot.helper as bot_helper

import app.chats.chat.constant as chat_constant
import app.chats.chat_action.helper as chat_action_helper

import app.documents.document.helper as document_helper
import app.documents.document.models as document_models
import app.documents.document.serializers as document_serializers

import app.core.onboarding.tasks as onboarding_tasks
import app.core.onboarding.helper as onboarding_helper
import app.core.onboarding.customer_policy_helper as onboarding_customer_policy_helper

import app.core.tenants.models as tenants_models

import app.utility.chroma_db_helper as utility_chroma_db_helper
import app.utility.models as utility_models

import os
import traceback

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny

from tenant_schemas.utils import schema_context

from django.db import transaction
from django.contrib.auth import get_user_model


User = get_user_model()


class OnboardingView:
    class OnboardingBaseView:
        permission_classes = [AllowAny]

    class HandleOnboardingInformation(OnboardingBaseView, APIView):
        def post(self, request, *args, **kwargs):
            client_name = request.data.get("client_name", "")
            schema_name_slug = request.data.get("schema_name", "")
            stripe_subscription_plan_price_id = request.data.get(
                "stripe_subscription_plan_price_id", ""
            )
            plan_name = request.data.get("plan_name", "")
            is_free_plan = plan_name == "Free"

            schema_name = schema_name_slug.replace(
                "-", "_"
            )  # request.data.get("schema_name", "")
            domain_url = f"{schema_name_slug}.{os.environ.get('HTTPS_URL_1')}"
            email = request.data.get("email", "").lower()

            response = onboarding_helper.check_request_data_validity(
                schema_name, stripe_subscription_plan_price_id, email
            )

            # if failed check, return error response
            if not response.status_code == status.HTTP_200_OK:
                return response

            try:
                with transaction.atomic():
                    new_tenant_instance = tenants_models.Tenant.objects.create(
                        domain_url=domain_url,  # don't add your port or www here! on a local server you'll want to use localhost here
                        schema_name=schema_name,
                        name=client_name,
                        stripe_email=email,
                    )

                    # user creation
                    password = request.data.get("password", "")
                    contact = request.data.get("contact", "")
                    full_name = request.data.get("full_name", "")

                    tenant_role = authapi_models.ADMIN
                    authapi_helper.create_user(
                        email,
                        password,
                        full_name,
                        contact,
                        tenant_role,
                        new_tenant_instance,
                    )

                    onboarding_helper.create_stripe_related_stuff(
                        email,
                        schema_name,
                        new_tenant_instance,
                        stripe_subscription_plan_price_id,
                        is_free_plan,
                    )

                    # Bot setting creation
                    bot_helper.initialize_bot_setting(schema_name, verbose=True)

                    # Chat Action creation
                    chat_action_helper.initialize_chat_action(schema_name, verbose=True)

                    return Response(
                        data=schema_name_slug, status=status.HTTP_201_CREATED
                    )
            except Exception as e:
                print("Onboarding error: ", e)
                print("Traceback start =======")
                traceback.print_exc()
                print("Traceback end =======")
                return Response(
                    data={"error": str(e)}, status=status.HTTP_400_BAD_REQUEST
                )

    class HandleStartPageOnboardingInformation(OnboardingBaseView, APIView):
        def post(self, request, *args, **kwargs):
            client_name = request.data.get("client_name", "")
            schema_name_slug = request.data.get("schema_name", "")
            stripe_subscription_plan_price_id = request.data.get(
                "stripe_subscription_plan_price_id", ""
            )
            plan_name = request.data.get("plan_name", "")
            is_free_plan = plan_name == "Free"
            user_instance = self.request.user
            email = user_instance.email
            schema_name = schema_name_slug.replace(
                "-", "_"
            )  # request.data.get("schema_name", "")
            domain_url = f"{schema_name_slug}.{os.environ.get('HTTPS_URL_1')}"

            response = onboarding_helper.check_request_data_validity(
                schema_name, stripe_subscription_plan_price_id
            )  # no need to check email on existing page

            # if failed check, return error response
            if not response.status_code == status.HTTP_200_OK:
                return response
            try:
                with transaction.atomic():
                    new_tenant_instance = tenants_models.Tenant.objects.create(
                        domain_url=domain_url,  # don't add your port or www here! on a local server you'll want to use localhost here
                        schema_name=schema_name,
                        name=client_name,
                        stripe_email=email,
                    )

                    # Add user tenant
                    tenant_role = authapi_models.ADMIN
                    authapi_helper.add_user_to_user_tenant_bridge(
                        user_instance, new_tenant_instance, tenant_role
                    )

                    onboarding_helper.create_stripe_related_stuff(
                        email,
                        schema_name,
                        new_tenant_instance,
                        stripe_subscription_plan_price_id,
                        is_free_plan,
                    )
                    # Bot setting creation
                    bot_helper.initialize_bot_setting(schema_name, verbose=True)

                    # Chat Action creation
                    chat_action_helper.initialize_chat_action(schema_name, verbose=True)

                    return Response(
                        data=schema_name_slug, status=status.HTTP_201_CREATED
                    )
            except Exception as e:
                print("Onboarding error: ", e)
                print("Traceback start =======")
                traceback.print_exc()
                print("Traceback end =======")
                return Response(
                    data={"error": str(e)}, status=status.HTTP_400_BAD_REQUEST
                )

    class GeneratePolicy(APIView):
        def post(self, request, *args, **kwargs):
            schema_name = authapi_helper.get_schema_name()
            website_url = request.data.get("website_url", "")  # web scrape
            customer_name = request.data.get("customer_name", "")
            about_me = request.data.get("about_me", "")
            target_industries_audiences = request.data.get(
                "target_industries_audiences", ""
            )
            contact = request.data.get("contact", "")
            testimonials = request.data.get("testimonials", "")
            milestones = request.data.get("milestones", "")
            scrape_type = request.data.get("scrape_type", utility_models.SCRAPE_LIGHT)
            if not scrape_type:
                scrape_type = utility_models.SCRAPE_LIGHT

            url_limit = utility_models.WEB_SCRAPE_URL_COUNT_LIGHT
            iterations = utility_models.WEB_SCRAPE_ITERATION_COUNT_LIGHT
            if scrape_type == utility_models.SCRAPE_COMPREHENSIVE:
                url_limit = utility_models.WEB_SCRAPE_URL_COUNT_COMPREHENSIVE
                iterations = utility_models.WEB_SCRAPE_ITERATION_COUNT_COMPREHENSIVE
            elif scrape_type == utility_models.SCRAPE_STANDARD:
                url_limit = utility_models.WEB_SCRAPE_URL_COUNT_STANDARD
                iterations = utility_models.WEB_SCRAPE_ITERATION_COUNT_STANDARD

            # create a temporary document instance
            with schema_context(schema_name):
                policy_generator_information_instance = (
                    document_models.PolicyGeneratorInformation.objects.create(
                        website_url=website_url,
                        scrape_type=scrape_type,
                        customer_name=customer_name,
                        about_me=about_me,
                        target_industries_audiences=target_industries_audiences,
                        contact=contact,
                        testimonials=testimonials,
                        milestones=milestones,
                    )
                )

                (
                    document_instance,
                    is_cp_created,
                ) = document_models.Document.objects.get_or_create(
                    name=onboarding_customer_policy_helper.DOCUMENT_CUSTOMER_POLICY_INSTANCE_NAME,
                    is_auto_generated=True,
                )
                if is_cp_created:
                    document_instance.is_internal = True
                    document_instance.is_external = True
                else:
                    print("removing initial policy from doc...")
                    if document_instance.file:
                        file_url = document_instance.file.url
                        utility_chroma_db_helper.remove_doc_from_all_chroma_db(
                            file_url,
                            chat_constant.get_collection_name(
                                chat_constant.external_collection,
                                schema_name,
                            ),
                            chat_constant.get_collection_name(
                                chat_constant.internal_collection,
                                schema_name,
                            ),
                        )
                        file_key = document_helper.get_file_key_from_url(file_url)
                        if file_key:
                            document_helper.delete_file_from_s3(file_key)

                    document_instance.file = None
                    document_instance.status_description = ""
                    print("Finished removing initial policy from doc!")
                document_instance.policy_generator_information = (
                    policy_generator_information_instance
                )
                document_instance.status = utility_models.PENDING
                document_instance.save()

                web_content_document_instance_id = -1
                if website_url:
                    web_content_document_instance_id = (
                        onboarding_helper.reset_web_content_instance(schema_name)
                    )

            onboarding_tasks.generate_policy_task.apply_async(
                args=[
                    website_url,
                    customer_name,
                    about_me,
                    target_industries_audiences,
                    contact,
                    testimonials,
                    milestones,
                    schema_name,
                    document_instance.id,
                    web_content_document_instance_id,
                ],
                kwargs={"iterations": iterations, "url_limit": url_limit},
            )
            return Response(
                data=document_serializers.DocumentSerializer.Get(
                    document_instance
                ).data,
                status=status.HTTP_201_CREATED,
            )

    class RetryGeneratePolicy(APIView):
        def post(self, request, *args, **kwargs):
            schema_name = authapi_helper.get_schema_name()
            document_id = request.data.get("document")
            # create a temporary document instance
            with schema_context(schema_name):
                document_instance = document_models.Document.objects.get(id=document_id)
                print("removing initial policy from doc...")
                if document_instance.file:
                    file_url = document_instance.file.url
                    utility_chroma_db_helper.remove_doc_from_all_chroma_db(
                        file_url,
                        chat_constant.get_collection_name(
                            chat_constant.external_collection,
                            schema_name,
                        ),
                        chat_constant.get_collection_name(
                            chat_constant.internal_collection,
                            schema_name,
                        ),
                    )
                    file_key = document_helper.get_file_key_from_url(file_url)
                    if file_key:
                        document_helper.delete_file_from_s3(file_key)
                document_instance.file = None
                document_instance.status_description = ""
                print("Finished removing initial policy from doc!")
                document_instance.status = utility_models.PENDING
                document_instance.save()

                web_content_document_instance_id = -1
                if document_instance.policy_generator_information.website_url:
                    web_content_document_instance_id = (
                        onboarding_helper.reset_web_content_instance(schema_name)
                    )

                scrape_type = document_instance.policy_generator_information.scrape_type

                url_limit = utility_models.WEB_SCRAPE_URL_COUNT_LIGHT
                iterations = utility_models.WEB_SCRAPE_ITERATION_COUNT_LIGHT
                if scrape_type == utility_models.SCRAPE_COMPREHENSIVE:
                    url_limit = utility_models.WEB_SCRAPE_URL_COUNT_COMPREHENSIVE
                    iterations = utility_models.WEB_SCRAPE_ITERATION_COUNT_COMPREHENSIVE
                elif scrape_type == utility_models.SCRAPE_STANDARD:
                    url_limit = utility_models.WEB_SCRAPE_URL_COUNT_STANDARD
                    iterations = utility_models.WEB_SCRAPE_ITERATION_COUNT_STANDARD

            onboarding_tasks.generate_policy_task.apply_async(
                args=[
                    document_instance.policy_generator_information.website_url,
                    document_instance.policy_generator_information.customer_name,
                    document_instance.policy_generator_information.about_me,
                    document_instance.policy_generator_information.target_industries_audiences,
                    document_instance.policy_generator_information.contact,
                    document_instance.policy_generator_information.testimonials,
                    document_instance.policy_generator_information.milestones,
                    schema_name,
                    document_instance.id,
                    web_content_document_instance_id,
                ],
                kwargs={"iterations": iterations, "url_limit": url_limit},
            )
            return Response(
                data=document_serializers.DocumentSerializer.Get(
                    document_instance
                ).data,
                status=status.HTTP_201_CREATED,
            )

    class GeneratePolicyWithMoreInfo(APIView):
        def post(self, request, *args, **kwargs):
            schema_names = request.data.get("schema_names", "")
            if schema_names == "":
                schema_names = [authapi_helper.get_schema_name()]

            if schema_names == "public" or schema_names == "all":
                tenant_instances = tenants_models.Tenant.objects.all()
            else:
                tenant_instances = tenants_models.Tenant.objects.filter(
                    schema_name__in=schema_names.split(",")
                )

            schema_name_list = tenant_instances.exclude(
                schema_name="public"
            ).values_list("schema_name", flat=True)

            print(f"Re-enhancing customer policy for {schema_names}...")
            for schema_name in schema_name_list:
                with schema_context(schema_name):
                    document_instance = document_models.Document.objects.filter(
                        name=onboarding_customer_policy_helper.DOCUMENT_CUSTOMER_POLICY_INSTANCE_NAME,
                        is_auto_generated=True,
                    ).first()

                    web_content_document_instance = document_models.Document.objects.filter(
                        name=onboarding_customer_policy_helper.DOCUMENT_WEB_CONTENT_INSTANCE_NAME,
                        is_auto_generated=True,
                    ).first()

                    if document_instance and web_content_document_instance:
                        # remove customer policy and web content from chroma
                        for auto_generated_doc_instance in [
                            document_instance,
                            web_content_document_instance,
                        ]:
                            if auto_generated_doc_instance.file:
                                file_url = auto_generated_doc_instance.file.url
                                utility_chroma_db_helper.remove_doc_from_all_chroma_db(
                                    file_url,
                                    chat_constant.get_collection_name(
                                        chat_constant.external_collection,
                                        schema_name,
                                    ),
                                    chat_constant.get_collection_name(
                                        chat_constant.internal_collection,
                                        schema_name,
                                    ),
                                )
                                file_key = document_helper.get_file_key_from_url(
                                    file_url
                                )
                                if file_key:
                                    document_helper.delete_file_from_s3(file_key)

                            auto_generated_doc_instance.file = None
                            auto_generated_doc_instance.status_description = ""
                            print("Finished removing initial policy from doc!")
                            auto_generated_doc_instance.status = utility_models.PENDING
                            auto_generated_doc_instance.save()

                        onboarding_tasks.generate_policy_task.apply_async(
                            args=[
                                document_instance.policy_generator_information.website_url,
                                document_instance.policy_generator_information.customer_name,
                                document_instance.policy_generator_information.about_me,
                                document_instance.policy_generator_information.target_industries_audiences,
                                document_instance.policy_generator_information.contact,
                                document_instance.policy_generator_information.testimonials,
                                document_instance.policy_generator_information.milestones,
                                schema_name,
                                document_instance.id,
                                web_content_document_instance.id,
                            ],
                            kwargs={
                                "iterations": utility_models.WEB_SCRAPE_URL_COUNT_COMPREHENSIVE,
                                "url_limit": utility_models.WEB_SCRAPE_ITERATION_COUNT_COMPREHENSIVE,
                            },
                        )
                        print(
                            f"Initiated task to enhance customer_policy for {schema_name} with {utility_models.WEB_SCRAPE_URL_COUNT_COMPREHENSIVE} iterations and {utility_models.WEB_SCRAPE_ITERATION_COUNT_COMPREHENSIVE} url limit..."
                        )
                    else:
                        print(
                            f"No existing customer_policy or web_content_instance found for {schema_name}"
                        )

            return Response(
                data={"message": "OK"},
                status=status.HTTP_200_OK,
            )
