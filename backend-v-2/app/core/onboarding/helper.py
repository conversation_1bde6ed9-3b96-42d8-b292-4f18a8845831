import app.core.authapi.models as authapi_models

import app.chats.chat.constant as chat_constant
import app.core.onboarding.customer_policy_helper as onboarding_customer_policy_helper

import app.documents.document.models as document_models
import app.documents.document.helper as document_helper

from app.core.payment.custom_stripe import StripeCustomer

import app.core.tenants.models as tenants_models

import app.utility.models as utility_models
import app.utility.chroma_db_helper as utility_chroma_db_helper

import re
import os
import requests

from fpdf import FPDF
from io import BytesIO
from bs4 import BeautifulSoup
from rest_framework.response import Response
from rest_framework import status


def clean_text_for_pdf(text, replacement="?"):
    cleaned_text = "".join(char if ord(char) < 128 else replacement for char in text)
    return cleaned_text


def text_to_pdf(text, is_customer_policy=False):
    text = clean_text_for_pdf(text)
    pdf = FPDF()
    pdf.add_page()
    if is_customer_policy:
        start_page = True
        lines = text.split("\n")
        for line in lines:
            if line.startswith("##"):
                if not start_page:
                    pdf.add_page()
                line = line.replace("##", "")
                pdf.set_font("Arial", size=16)
                pdf.multi_cell(0, 10, line)
                start_page = False
            else:
                pdf.set_font("Arial", size=12)
                pdf.cell(10)
                pdf.multi_cell(0, 10, line)
            pdf.ln(5)
    else:
        pdf.set_font("Arial", size=12)
        pdf.multi_cell(0, 10, txt=text)
    buffer = BytesIO()
    pdf.output(buffer, "F")
    buffer.seek(0)

    return buffer


def txt_to_txt_file_io(text):
    encoded_text = text.encode("utf-8")
    txt_file_io = BytesIO(encoded_text)
    return txt_file_io


# Web scrape
def fetch_and_parse(url):
    response = requests.get(url)
    if response.status_code == 200:
        return BeautifulSoup(response.text, "html.parser")
    else:
        print(f"Failed to fetch the webpage. HTTP Error Code: {response.status_code}")
        return None


def extract_content(soup):
    # Remove script and style tags
    [s.extract() for s in soup(["script", "style"])]
    # Optionally: Target specific tags or classes. Uncomment below if needed.
    # soup = soup.find_all('p')  # Extracts only paragraph content
    content = soup.get_text()
    # Remove extra spaces and line breaks
    content = " ".join(content.split())
    return content


def limit_content_to_pages(content, words_per_page=250, pages=30):
    words = content.split()
    limit = words_per_page * pages

    # Limit the number of words
    limited_words = words[:limit]

    return " ".join(limited_words)


# Customer Info process
def get_customer_info(
    website_url,
    about_me,
    target_industries_audiences,
    contact,
    testimonials,
    milestones,
):
    formatted_website_url = f"Website: {website_url}" if len(website_url) > 0 else ""
    formatted_about_me = f"About the business: {about_me}" if len(about_me) > 0 else ""
    formatted_industries_audiences = (
        f"Target Industries/Audiences: {target_industries_audiences}"
        if len(target_industries_audiences) > 0
        else ""
    )
    formatted_contact = (
        f"Preferred Contact Channel: {contact}" if len(contact) > 0 else ""
    )
    formatted_testimonials = (
        f"Testimonials: {testimonials}" if len(testimonials) > 0 else ""
    )
    formatted_milestones = (
        f"Milestones Achieved: {milestones}" if len(milestones) > 0 else ""
    )

    customer_info = f"""
{formatted_website_url}
{formatted_about_me}
{formatted_industries_audiences}
{formatted_contact}
{formatted_testimonials}
{formatted_milestones}
    """.strip()

    #     customer_info = """
    # Industries: ["Healthcare", "Biotechnology", "Pharmaceuticals"]
    # Preferred Contact Channel: You can reach out to our team via <NAME_EMAIL> or call us at +1-************. Our operating hours are Monday to Friday, 9 AM - 6 PM EST.
    # Testimonials: ["The team at 'OurCompany' has been instrumental in our product development. Their expertise and dedication are unmatched. - CEO, HealthTech Corp.", "I've collaborated with several companies in the past, but the efficiency and professionalism of 'OurCompany' stand out. - Director, BioSolutions.", "We relied on 'OurCompany' for our latest project, and they delivered beyond our expectations. Truly remarkable! - Project Manager, PharmaWorld."]
    # Milestones Achieved: ["Annual HealthTech Symposium - March 15th 2022","Company's Anniversary - June 20th 2022","Pharmaceuticals Product Launch - September 5th 2022"]
    # Target Audience Profiles: ["Medical professionals", "Research scientists", "Pharmaceutical companies", "Healthcare startups"]
    # """
    return customer_info


def reset_web_content_instance(schema_name):
    document_web_content_instance = document_models.Document.objects.filter(
        name=onboarding_customer_policy_helper.DOCUMENT_WEB_CONTENT_INSTANCE_NAME,
        is_auto_generated=True,
    ).first()
    if document_web_content_instance:
        print("removing initial policy web content from doc...")
        if document_web_content_instance.file:
            file_url = document_web_content_instance.file.url
            utility_chroma_db_helper.remove_doc_from_all_chroma_db(
                file_url,
                chat_constant.get_collection_name(
                    chat_constant.external_collection,
                    schema_name,
                ),
                chat_constant.get_collection_name(
                    chat_constant.internal_collection,
                    schema_name,
                ),
            )
            file_key = document_helper.get_file_key_from_url(file_url)
            if file_key:
                document_helper.delete_file_from_s3(file_key)
        document_web_content_instance.delete()
        print("Finished removing initial policy web content from doc!")

    (
        web_content_document_instance,
        _,
    ) = document_models.Document.objects.get_or_create(
        name=onboarding_customer_policy_helper.DOCUMENT_WEB_CONTENT_INSTANCE_NAME,
        is_auto_generated=True,
        defaults={
            "is_internal": True,
            "is_external": True,
            "status": utility_models.PENDING,
        },
    )
    return web_content_document_instance.id


def check_request_data_validity(
    schema_name, stripe_subscription_plan_price_id, email=""
):
    backend_domain = os.environ.get("HTTPS_URL_1")
    if not backend_domain:
        error = "Server error. Please try again later"
        print("HTTPS_URL_1 not set correctly", error)
        return Response(error, status=status.HTTP_404_NOT_FOUND)

    if not schema_name:
        return Response(
            {"error": "The schema name for the tenant is not located."},
            status=status.HTTP_400_BAD_REQUEST,
        )

    if schema_name in ["backend", "public"]:
        return Response(
            {
                "error": "The unique URL for the organization is already taken. Please provide an alternative organization name."
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    # if schema_name exist
    if tenants_models.Tenant.objects.filter(schema_name=schema_name):
        return Response(
            {
                "error": "The unique URL for the organization is already taken. Please provide an alternative organization name."
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    # if schema_name invalid
    pattern = re.compile(r"^(\d+|\d[a-zA-Z]+)$")
    if bool(pattern.match(schema_name)):
        return Response(
            {
                "error": "Organizational names must not commence with a numeral and cannot be composed solely of numerals."
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    # stripe
    if not stripe_subscription_plan_price_id:
        return Response(
            {"error": "No subscription plan found."},
            status=status.HTTP_400_BAD_REQUEST,
        )

    # if email existed
    if email:
        if authapi_models.User.objects.filter(email=email).count() > 0:
            return Response(
                {
                    "error": "The email address you provided is already registered with us. Please log in to proceed with adding a new organization."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    return Response("Validation pass", status=status.HTTP_200_OK)


def create_stripe_related_stuff(
    email,
    schema_name,
    new_tenant_instance,
    stripe_subscription_plan_price_id,
    is_free_plan,
    trial_days=14,
):
    print(f"Creating stripe customer and subscription for {schema_name}...")
    # create_stripe_customer_and_return_customer_id
    stripe_customer = StripeCustomer(email=email, schema_name=schema_name)

    # add in stripe customer id
    new_tenant_instance.stripe_customer_id = stripe_customer.customer_id
    new_tenant_instance.save()

    # add plan
    stripe_customer.add_trial_subscription(
        stripe_subscription_plan_price_id,
        is_free_plan=is_free_plan,
        trial_days=trial_days,
    )
    print(f"Finished creating stripe customer and subscription for {schema_name}!")
