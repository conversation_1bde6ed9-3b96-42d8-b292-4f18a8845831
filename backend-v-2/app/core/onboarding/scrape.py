from bs4 import BeautifulSoup

from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromiumService
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    StaleElementReferenceException,
    NoSuchElementException,
)

from tqdm import tqdm

from urllib.parse import urlparse

from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.core.utils import ChromeType

import xml.etree.ElementTree as ET

import requests
import time


class Crawler:
    """
    1. Retrieve main url
    2. Loop through n iterations:
        a) Loop through urls:
            i) self.driver.get(url)
            ii) scroll down to load all content
            iii) search for extra links
            iv) add searched url
            v) retrieve content
    """

    # @staticmethod
    # def clean_text(text):
    #     text = re.sub(" +", " ", text)
    #     text = re.sub("\s\s+", " ", text)
    #     text = re.sub("\n+", " ", text)
    #     return text.strip()

    @staticmethod
    def remove_tags(html):
        soup = BeautifulSoup(html, "html.parser")
        for data in soup(["style", "script"]):
            data.decompose()
        return " ".join(soup.stripped_strings)

    def __init__(self, main_url, url_limit):
        parsed_url = urlparse(main_url)
        self.identifier = parsed_url.netloc
        self.MAX_URLS_TO_PROCESS = url_limit
        self.main_url = main_url
        self.urls = self.sort_urls_by_genericness(
            list(
                set(
                    [
                        main_url,
                        *self.parse_sitemap(
                            self.fetch_sitemap(main_url + "/sitemap.xml")
                        ),
                    ]
                )
            )
        )
        self.content = {}
        self.initiate_driver()
        # Additional param for searching for more urls
        self.searched = []
        self.downloadable_extensions = [
            ".jpg",
            ".pdf",
            ".docx",
            ".png",
            ".zip",
            ".mp3",
            ".mp4",
            ".exe",
            ".csv",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".txt",
            ".rar",
            ".gz",
            ".tar",
            ".7z",
            ".dmg",
            ".iso",
            ".flv",
            ".avi",
            ".mov",
            ".wmv",
        ]

    def get_prod_chrome_option(self):
        # ----- Prod section
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--use-fake-ui-for-media-stream")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-extensions")
        # chrome_options.add_argument("--incognito")
        # profile
        chrome_options.add_argument(f"--user-data-dir=chrome-scrape")

        # Only for deployment
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--remote-debugging-port=9222")
        chrome_options.add_argument("--window-size=1440,934")
        chrome_options.add_argument("start-maximized")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--log-level=3")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-webrtc")
        chrome_options.add_argument(
            "--header=Accept:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
        )
        chrome_options.add_argument("--header=Accept-Encoding:gzip, deflate, br")
        chrome_options.add_argument("--header=Accept-Language:en-US,en;q=0.9")

        # Set user agent to be User-Agent:Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
        chrome_options.add_argument(
            "--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        )

        chrome_options.add_experimental_option(
            "prefs",
            {
                "profile.default_content_setting_values.media_stream_mic": 1,
                "profile.default_content_setting_values.media_stream_camera": 1,
                "profile.default_content_setting_values.geolocation": 0,
                "profile.default_content_setting_values.notifications": 2,
            },
        )

        chrome_options.add_argument("--disable-notifications")
        return chrome_options

    def get_local_chrome_option(self):
        # --- Local section
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("excludeSwitches", ["enable-logging"])
        chrome_options.add_argument("--no-sandbox")  # linux only
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--headless")
        ##Local backlog
        # options.add_argument("--profile-directory=/home/<USER>/.config/google-chrome/default")
        # options.add_argument("--profile-directory=Default")
        # options.add_argument("--user-data-dir=/home/<USER>")
        # options.add_argument("--disable-extensions")
        # options.add_argument("--no-sandbox")  # linux only
        # options.add_argument("--disable-dev-shm-usage")
        # options.add_argument("--headless")  # "--headless=new" >> will result error
        # options.add_argument("--user-data-dir=/home/<USER>")
        # options.add_experimental_option("excludeSwitches", ["enable-logging"])

        # self.driver = webdriver.Chrome(
        #     ChromeDriverManager(
        #         chrome_type=ChromeType.CHROMIUM, path="/home/<USER>"
        #     ).install(),
        #     chrome_options=options,
        # )
        # self.driver = webdriver.Chrome(
        #     "/home/<USER>/chromedriver-linux64/chromedriver",
        #     chrome_options=options,
        # )
        return chrome_options

    def initiate_driver(self):
        # --- Local Section
        # chrome_options = self.get_local_chrome_option()
        # self.driver = webdriver.Chrome(
        #     "/home/<USER>/.wdm/drivers/chromedriver/linux64/114.0.5735.90/chromedriver",
        #     options=chrome_options,
        # )

        # --- Prod Section:
        chrome_options = self.get_prod_chrome_option()
        self.driver = webdriver.Chrome(
            service=ChromiumService(
                ChromeDriverManager(chrome_type=ChromeType.CHROMIUM).install()
            ),
            options=chrome_options,
        )

    # def create_new_driver_instance(self):
    #     # # --- Local section
    #     # chrome_options = self.get_local_chrome_option()
    #     # driver = webdriver.Chrome(
    #     #     "/home/<USER>/.wdm/drivers/chromedriver/linux64/117.0.5938.88/chromedriver",
    #     #     options=chrome_options,
    #     # )

    #     # --- Prod Section:
    #     chrome_options = self.get_prod_chrome_option()
    #     driver = webdriver.Chrome(
    #         service=ChromiumService(
    #             ChromeDriverManager(chrome_type=ChromeType.CHROMIUM).install()
    #         ),
    #         options=chrome_options,
    #     )

    #     return driver

    # Conclusion: Passing driver as variables cause the function stuck!

    def sort_urls_by_genericness(self, urls):
        def url_depth(url):
            path = urlparse(url).path
            return path.count("/")

        return sorted(urls, key=url_depth)

    def pull_content(self, iterations):
        """Pulls content from URLs for a given number of iterations."""
        for iteration in range(iterations):
            searched_list_length = len(self.searched.copy())
            if searched_list_length >= self.MAX_URLS_TO_PROCESS:
                print(
                    f"{searched_list_length} urls are searched! Skip the {iteration+1} iteration..."
                )
                break

            new_urls = self.sort_urls_by_genericness(self.urls.copy())
            if len(new_urls) > 0:
                urls_to_process = new_urls[
                    0 : self.MAX_URLS_TO_PROCESS - searched_list_length
                ]  # this will return self.MAX_URLS_TO_PROCESS number of urls
                print(
                    f"{len(urls_to_process)} url(s) to be processed in {iteration+1} iteration..."
                )
                self.process_urls(
                    urls_to_process, iteration
                )  # need to pass self.urls here cause don't want it to be affected when new urls coming in
            else:
                print("No URL Found anymore!")
                print(
                    f"self.searched: {self.searched} ({len(self.searched)})",
                )
        self.driver.quit()

    # def process_urls(self, urls, iteration):
    #     """Processes each URL to extract content and find new URLs using multi-threading."""
    #     with ThreadPoolExecutor(max_workers=3) as executor:
    #         # Process each URL and collect results
    #         results = list(
    #             tqdm(
    #                 executor.map(self.process_single_url, urls),
    #                 total=len(urls),
    #                 desc=f"Iteration {iteration+1}: Processing URLs...",
    #             )
    #         )

    #     # Post-processing after all URLs have been processed
    #     for url, result in zip(urls, results):
    #         if result != url:
    #             print(f"Error processing URL: {url}, result: {result}")

    #         # Update searched list and remove processed URL
    #         self.urls.remove(url)
    #         self.searched.append(url)

    #     print(f"self.searched: {self.searched} ({len(self.searched)})")

    def process_urls(self, urls, iteration):
        """Processes each URL to extract content and find new URLs."""
        for url in tqdm(
            urls, total=len(urls), desc=f"Iteration {iteration+1}: Processing URLs..."
        ):
            print("URL now: ", url)
            self.process_single_url(url)
            # tracking
            self.urls.remove(url)
            self.searched.append(url)
        print(
            f"self.searched: {self.searched} ({len(self.searched)})",
        )

    # def process_single_url(self, processing_url):
    #     """Loads a single URL and extracts content and new URLs from it."""
    #     try:
    #         driver = self.create_new_driver_instance()
    #         driver.get(processing_url)
    #         time.sleep(1)  # Consider replacing with more dynamic wait
    #         self.extract_new_urls_from_page(driver) #cannot pass in driver here, very time consuming. if want implement need to find other way
    #         self.extract_content_from_page(driver, processing_url)
    #         driver.quit()
    #         return processing_url
    #     except Exception as e:
    #         print(f"Error when fetching {processing_url}: {e}")
    #         return False

    def process_single_url(self, processing_url):
        """Loads a single URL and extracts content and new URLs from it."""
        try:
            # driver = self.create_new_driver_instance()
            self.driver.get(processing_url)
            time.sleep(1)  # Consider replacing with more dynamic wait
            self.extract_new_urls_from_page()
            self.extract_content_from_page(processing_url)
        except Exception as e:
            print(f"Error when fetching {processing_url}: {e}")
            return False

    def extract_new_urls_from_page(self):
        """Extracts new URLs from the current page, excluding downloadable links."""
        visited_links = set()
        new_links_found = True

        while (
            new_links_found
            and len(self.searched) + len(self.urls) < self.MAX_URLS_TO_PROCESS
        ):
            new_links_found = False
            links = self.driver.find_elements(by=By.XPATH, value="//a[@href]")

            for link in links:
                if len(self.searched) + len(self.urls) >= self.MAX_URLS_TO_PROCESS:
                    break  # Stop if the limit of total URLs is reached
                try:
                    href = link.get_attribute("href")
                    if href not in visited_links and self.is_url_eligible(href):
                        self.urls.append(href)
                        visited_links.add(href)
                        new_links_found = True
                except (StaleElementReferenceException, NoSuchElementException):
                    # If element is stale or not found, continue to the next element
                    continue

    def is_url_eligible(self, url):
        """Checks if a URL is eligible for processing."""
        is_not_main_url = url != self.main_url
        contains_identifier = self.identifier in url
        is_not_downloadable = not any(
            url.endswith(ext) for ext in self.downloadable_extensions
        )
        is_not_mailto = not url.startswith("mailto:")  # Excluding mailto links
        # Not duplicated
        is_not_duplicated = url not in self.urls and url not in self.searched
        has_room_for_more = (
            len(self.urls) < 20
        )  # assuming 1 web shouldn't have more than 20 generic urls
        # Additional checks for filtering irrelevant pages
        is_relevant = self.is_relevant_page(url)

        return (
            is_not_main_url
            and contains_identifier
            and is_not_downloadable
            and is_not_duplicated
            and has_room_for_more
            and is_relevant
            and is_not_mailto
        )

    def is_relevant_page(self, url):
        """Determines if a page is likely to contain relevant information."""
        irrelevant_patterns = [
            "login",
            "register",
            "subscribe",
            "logout",
            "cart",
            "account",
        ]
        if any(pattern in url for pattern in irrelevant_patterns):
            return False

        # Additional logic can be added here for more advanced filtering
        return True

    def extract_content_from_page(self, processing_url):
        """Extracts and stores content from the current page."""
        page_content = self.retrieve_page_content()
        cleaned_content = Crawler.remove_tags(page_content)
        self.content[processing_url] = (
            f"URL: {processing_url}; Content: {cleaned_content}"
        )

    def scroll_down_page(self, sleep, scroll_max=10):
        scroll_count = 0
        try:
            last_height = self.driver.execute_script(
                "return document.body.scrollHeight"
            )
            while scroll_count < scroll_max:
                self.driver.execute_script(
                    "window.scrollTo(0, document.body.scrollHeight);"
                )
                time.sleep(sleep)
                new_height = self.driver.execute_script(
                    "return document.body.scrollHeight"
                )
                if new_height == last_height:
                    break
                last_height = new_height
                scroll_count += 1
        except Exception as e:
            print(e)

    def retrieve_page_content(self):
        # try to scroll down
        self.scroll_down_page(2, scroll_max=10)
        # Get content: page_source = whole page content
        return self.driver.page_source

    # def retrieve_urls(self):
    #     if self.urls:
    #         return self.urls + [self.main_url]
    #     return [self.main_url]

    def get_content_in_string(self):
        content_values = list(set([line.strip() for line in self.content.values()]))
        return " ".join(content_values)

    def quit(self):
        return self.driver.quit()

    # Sitemap thingy
    def fetch_sitemap(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()  # Raises an HTTPError for bad responses
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"Failed to fetch sitemap from {url}: {e}")
            return None

    def parse_sitemap(self, sitemap_text):
        """Parses sitemap and returns the list of URLs"""
        if sitemap_text is None:
            return []

        sitemap = ET.fromstring(sitemap_text)
        urls = []

        for sitemap_tag in sitemap:
            tag = sitemap_tag.tag.split("}")[-1]  # Handles namespace
            if tag == "sitemap":
                loc = sitemap_tag.find("{*}loc").text
                child_sitemap = self.fetch_sitemap(loc)
                urls.extend(
                    self.parse_sitemap(child_sitemap)
                )  # Recursive call for sitemap index
            elif tag == "url":
                loc = sitemap_tag.find("{*}loc").text
                urls.append(loc)

        return urls
