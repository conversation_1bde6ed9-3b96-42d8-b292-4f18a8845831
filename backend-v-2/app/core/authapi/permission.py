from rest_framework.permissions import BasePermission
import app.core.authapi.models as authapi_models
import app.core.authapi.helper as authapi_helper


class IsAdminInSameTenant(BasePermission):
    message = "You must be the a Super Admin OR admin under the same tenant to perform this action."

    def has_permission(self, request, view):
        schema_name = authapi_helper.get_schema_name()
        if request.user.is_authenticated:
            if request.user.role == authapi_models.ADMIN:
                return True
            user_tenant_list = request.user.user_tenant_bridges.values_list(
                "tenant__schema_name", "role"
            )
            tenant_dict = {schema_name: role for schema_name, role in user_tenant_list}

            if tenant_dict.get(schema_name, "None") == authapi_models.ADMIN:
                return True

        return False

    def has_object_permission(self, request, view, obj):
        schema_name = authapi_helper.get_schema_name()
        if request.user.is_authenticated:
            if request.user.role == authapi_models.ADMIN:
                return True
            user_tenant_list = request.user.user_tenant_bridges.values_list(
                "tenant__schema_name", "role"
            )
            tenant_dict = {schema_name: role for schema_name, role in user_tenant_list}

            if tenant_dict.get(schema_name, "None") == authapi_models.ADMIN:
                return True

        return False
