from django.db import connections, router
from django.contrib.auth import get_user_model, authenticate
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail

import app.core.user_tenant_bridge.models as user_tenant_bridge_models
import app.core.tenants.models as tenants_models
import app.utility.date_helper as utility_date_helper

import string
import random
import os
import re

from typing import List
from cryptography.fernet import Fernet

User = get_user_model()


def get_schema_name():
    User = get_user_model()
    using = router.db_for_read(User)
    connection = connections[using]
    saved_schema = connection.schema_name
    return saved_schema


def get_tenant_name(schema_name):
    tenant_instance = tenants_models.Tenant.objects.get(schema_name=schema_name)
    return tenant_instance.name


def add_user_to_user_tenant_bridge(user_instance, tenant_instance, tenant_role):
    user_tenant_bridge_models.UserTenantBridge.objects.get_or_create(
        user=user_instance, tenant=tenant_instance, role=tenant_role
    )


def create_or_invite_user(email, password, tenant_role, schema_name):
    is_user_created = False
    tenant_instance = tenants_models.Tenant.objects.get(schema_name=schema_name)
    # if exist already -> invite them to the org only.
    user_instance = User.objects.filter(email=email).first()
    if not user_instance:
        is_user_created = True
        print("No account exist in Utter Unicorn. Creating one now...")
        user_instance = create_user(
            email, password, "", "", tenant_role, tenant_instance
        )  # if through invite, no need input full name and contact first, let the user login and input themselves.
        print(f"{email} account created!")
    else:
        add_user_to_user_tenant_bridge(user_instance, tenant_instance, tenant_role)
    return user_instance, is_user_created


def create_user(
    email,
    password,
    full_name,
    contact,
    tenant_role,
    tenant_instance,
):
    user_instance, created = User.objects.get_or_create(email=email)
    if created:
        user_instance.set_password(password)
        user_instance.contact = contact
        user_instance.full_name = full_name
        user_instance.save()
    else:
        user_instance = authenticate(email=email, password=password)
        if not user_instance:
            raise Exception("User already exists but password is incorrect.")

    add_user_to_user_tenant_bridge(user_instance, tenant_instance, tenant_role)
    return user_instance


def is_valid_folder_name(name):
    # Check for invalid characters
    invalid_chars = re.compile(r'[<>:"/\\|?*]')
    if invalid_chars.search(name):
        return False

    return True


def folder_exists(directory, folder_name):
    return os.path.exists(os.path.join(directory, folder_name))


def check_folder(directory, folder_name):
    if not is_valid_folder_name(folder_name):
        return "Invalid folder name."
    elif folder_exists(directory, folder_name):
        return "The folder exists in the directory."
    else:
        return "The folder name is valid and does not exist in the directory."


# EMAIL
def generate_random_password(length=14):
    characters = string.ascii_letters + string.digits + string.punctuation
    password = "".join(random.choice(characters) for i in range(length))
    return password


def send_user_email_with_html_template(
    html_path: str, subject: str, from_: str, to_: List[str], **kwargs
):
    # send email
    html_message = render_to_string(
        html_path,
        {**kwargs},
    )
    plain_message = strip_tags(html_message)
    res = send_mail(
        subject,
        plain_message,
        from_,
        to_,
        html_message=html_message,
    )
    print(f"sent email titled '{subject}' to {to_},  with res: ", res)


def send_user_invite_email(
    user_instance, password, login_url, tenant_name, is_user_created
):
    # send email
    subject = f"Login Credentials for Your Account in UtterUnicorn X {tenant_name}"
    email = user_instance.email
    current_year_str = utility_date_helper.get_current_year_str()
    if is_user_created:
        html_path = "first_time_user_invite.html"
        custom_kwargs = {
            "email": email,
            "password": password,
            "preferred_name": email.split("@")[0],
            "login_url": login_url,
            "tenant_name": tenant_name,
            "current_year_str": current_year_str,
        }
    else:
        html_path = "existing_user_invite.html"
        custom_kwargs = {
            "email": email,
            "preferred_name": email.split("@")[0],
            "login_url": login_url,
            "tenant_name": tenant_name,
            "current_year_str": current_year_str,
        }

    from_ = f"UtterUnicorn <{settings.DEFAULT_FROM_EMAIL}>"
    to_ = [email]
    send_user_email_with_html_template(html_path, subject, from_, to_, **custom_kwargs)

# Function to get the encryption key from environment variables
def get_fernet():
    encryption_key = os.getenv("FERNET_KEY")

    if not encryption_key:
        raise ValueError("Encryption key not found in environment variables.")
    return Fernet(encryption_key)

# Function to encrypt data
def encrypt_data(data):
    fernet = get_fernet()
    return fernet.encrypt(data.encode()).decode()

# Function to decrypt data
def decrypt_data(data):
    fernet = get_fernet()
    return fernet.decrypt(data.encode()).decode()

