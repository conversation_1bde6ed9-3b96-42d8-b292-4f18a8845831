from rest_framework import generics, viewsets, permissions, pagination

# from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.views import APIView

# from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from app.core.authapi.serializers import ChangePasswordSerializer, CustomTokenObtainPairSerializer

# import analytic.models as analytic_models
from app.analytic.views import get_user_id_ip_address_country


# from djoser.serializers import UserCreateSerializer
# from django.core.mail import send_mail
# from django.template.loader import render_to_string
# from django.utils.html import strip_tags
# from django.conf import settings

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from django.http import HttpResponse

import app.core.authapi.serializers as authapi_serializers
import app.core.authapi.permission as authapi_permission
import app.core.authapi.helper as authapi_helper
import app.core.authapi.models as authapi_models


import csv

import app.utility.models as utility_models
import app.utility.common_class as utility_common_class
import app.utility.date_helper as utility_date_helper

from tenant_schemas.utils import schema_context
import app.core.tenants.models as tenants_models

import os

import app.core.user_tenant_bridge.models as user_tenant_bridge_models

User = get_user_model()


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        schema_name = authapi_helper.get_schema_name()
        if "org" in request.data:
            if request.data["org"] != "":
                schema_name = request.data["org"]
        with schema_context(schema_name):
            res = super().post(request, *args, **kwargs)

            user_id = res.data["id"]  # from custom serializer

            if user_id:
                user_instance = User.objects.get(id=user_id)
                today = timezone.now()
                if not (user_instance.start_date <= today <= user_instance.end_date):
                    return Response(
                        status=status.HTTP_400_BAD_REQUEST,
                        data={"body": "The account is not within activated date!"},
                    )
                _, ip_address, country = get_user_id_ip_address_country(request)
                user_instance.last_login = timezone.now()
                # TODO: check whether login correct (esp staff acc cannot login admin website)

                user_instance.last_login_ip = ip_address
                user_instance.country = country
                user_instance.save()

        return res


class ChangePasswordView(generics.UpdateAPIView):
    queryset = User.objects.all()
    serializer_class = ChangePasswordSerializer
    permission_classes = [
        IsAuthenticated,
    ]


class UserView:
    class UserBaseView(generics.GenericAPIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]
        serializer_class = authapi_serializers.CustomUserCreateSerializer
        queryset = User.objects.all()

    class ListAll(UserBaseView, generics.ListAPIView):
        serializer_class = authapi_serializers.CustomUserSerializer
        permission_classes = [IsAuthenticated]
        pagination_class = utility_common_class.CustomPageNumberPagination
        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "email",
            "full_name",
            "contact",
        ]
        ordering_fields = [
            "email",
            "full_name",
            "contact",
            "last_login",
        ]
        ordering = ["full_name"]

        def get_queryset(self):
            queryset = super().get_queryset()
            user_instance = self.request.user

            if user_instance.role == authapi_models.ADMIN:
                return queryset

            tenant_schema_name_list = user_instance.user_tenant_bridges.order_by(
                "tenant__schema_name"
            ).values_list("tenant__schema_name", flat=True)

            queryset = queryset.filter(
                user_tenant_bridges__tenant__schema_name__in=tenant_schema_name_list
            ).distinct()

            return queryset

        def list(self, request):
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    class List(UserBaseView, generics.ListAPIView):
        serializer_class = authapi_serializers.CustomUserSerializer
        pagination_class = (
            utility_common_class.CustomPageNumberPagination
        )  # Add this line
        filter_backends = [SearchFilter, OrderingFilter]
        search_fields = [
            "email",
            "full_name",
            "contact",
        ]
        ordering_fields = [
            "email",
            "full_name",
            "contact",
            "last_login",
        ]
        ordering = ["full_name"]

        def get_queryset(self):
            queryset = super().get_queryset()
            schema_name = authapi_helper.get_schema_name()
            if schema_name:
                queryset = queryset.filter(
                    user_tenant_bridges__tenant__schema_name__iexact=schema_name
                )
            # queryset = queryset.filter(created_by=self.request.user)
            return queryset

        def list(self, request):
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    class Create(UserBaseView, APIView):
        # invite user to the tenant
        def post(self, request, *args, **kwargs):
            schema_name = authapi_helper.get_schema_name()
            if not schema_name:
                return Response("No schema name!", status=status.HTTP_400_BAD_REQUEST)

            if not os.environ.get("HTTPS_APP_URL_1"):
                return Response(
                    "No frontend domain found!", status=status.HTTP_400_BAD_REQUEST
                )

            tenant_name = tenants_models.Tenant.objects.get(
                schema_name=schema_name
            ).name

            # email_password_tracker = {}
            for data in request.data:
                random_password = authapi_helper.generate_random_password()
                # email_password_tracker[data["email"]] = random_password

                email = data.get("email", "").lower()
                password = random_password
                tenant_role = data.get("tenant_role", authapi_models.ADMIN)

                with transaction.atomic():
                    (
                        user_instance,
                        is_user_created,
                    ) = authapi_helper.create_or_invite_user(
                        email, password, tenant_role, schema_name
                    )

                    login_url = f"{os.environ.get('HTTPS_APP_URL_1')}/org/{schema_name.replace('_', '-')}/login"

                    # send email
                    if (
                        settings.IS_EMAIL_ENABLED == "True"
                        and settings.DEFAULT_FROM_EMAIL
                    ):
                        print("Triggered email send upon acc creation.")
                        authapi_helper.send_user_invite_email(
                            user_instance,
                            password,
                            login_url,
                            tenant_name,
                            is_user_created,
                        )
                    else:
                        pass
                    # print("No email is triggered.")

            return Response("User created!", status=status.HTTP_201_CREATED)

    class Update(UserBaseView, generics.UpdateAPIView):
        serializer_class = authapi_serializers.CustomUserSerializer

    class Delete(UserBaseView, APIView):
        """
        This is remove user from the tenant only.
        """

        def delete(self, request, *args, **kwargs):
            schema_name = authapi_helper.get_schema_name()
            tenant_instance = tenants_models.Tenant.objects.filter(
                schema_name=schema_name
            ).first()
            user_instance = User.objects.get(id=self.kwargs["pk"])
            # delete user bridges
            user_bridge_to_delete = (
                user_tenant_bridge_models.UserTenantBridge.objects.filter(
                    user=user_instance, tenant=tenant_instance
                ).first()
            )
            print("user_bridge_to_delete: ", user_bridge_to_delete)
            if user_bridge_to_delete:
                user_bridge_to_delete.delete()

            # No need to delete user, let it empty only.
            # user_to_delete = User.objects.exclude(role=authapi_models.ADMIN).filter(
            #     id=self.kwargs["pk"], user_tenant_bridges__isnull=True
            # )
            # print("user_to_delete: ", user_to_delete)
            # if user_to_delete:
            #     user_to_delete.delete()

            return Response(status=status.HTTP_204_NO_CONTENT)

    class ExportFullList(APIView):
        permission_classes = [
            IsAuthenticated,
            authapi_permission.IsAdminInSameTenant,
        ]

        def get(self, request, *args, **kwargs):
            """
            Return:
            CSV: Full name, Email, Role
            """
            schema_name = authapi_helper.get_schema_name()
            user_instances = User.objects.filter(
                user_tenant_bridges__tenant__schema_name=schema_name
            )

            # Export to csv
            response = HttpResponse(content_type="text/csv")
            response[
                "Content-Disposition"
            ] = f'attachment; filename="full_user_list_{timezone.now().strftime("%d.%m.%Y")}.csv"'

            writer = csv.writer(response)

            # Write headers to CSV
            writer.writerow(
                [
                    "Full Name",
                    "Email",
                    "Role",
                    "Last Login",
                ]
            )

            for user in user_instances:
                role = (
                    user.user_tenant_bridges.filter(tenant__schema_name=schema_name)
                    .first()
                    .role
                )
                writer.writerow(
                    [
                        user.full_name,
                        user.email,
                        role,
                        utility_date_helper.convert_to_yyyymmddThhmmZ(user.last_login),
                    ]
                )

            return response


##backlog

# class CustomUserBulkCreate(UserBaseView, APIView):
#     serializer_class = authapi_serializers.CustomUserBulkCreateSerializer

#     def create_user(self, user_list):
#         for user in user_list:
#             user["password"] = utility_models.DEFAULT_PASSWORD

#         serializer = self.get_serializer(data=user_list, many=True)
#         if serializer.is_valid(raise_exception=True):
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     def update_user(self, user_list):
#         for user in user_list:
#             serializer = self.get_serializer(
#                 User.objects.get(email=user["email"]), data=user, partial=True
#             )
#             if serializer.is_valid(raise_exception=True):
#                 serializer.save()

#         return Response({"body": "Users updated"}, status=status.HTTP_200_OK)

#     def delete_user(self, user_list):
#         res = User.objects.filter(email__in=[user["email"] for user in user_list])
#         res.delete()
#         return Response(status=status.HTTP_204_NO_CONTENT)

#     def check_for_updates(self, existing_user, new_user):
#         is_to_update = False
#         updates = {}

#         if existing_user.full_name != new_user["full_name"]:
#             updates["existing_full_name"] = existing_user.full_name
#             is_to_update = True

#         if existing_user.role != new_user["role"]:
#             updates["existing_role"] = existing_user.role
#             is_to_update = True

#         if not utility_date_helper.have_same_date(
#             utility_date_helper.convert_string_to_datetime_object(
#                 new_user["start_date"]
#             ),
#             existing_user.start_date,
#         ):
#             updates["existing_start_date"] = existing_user.start_date
#             is_to_update = True

#         if not authapi_helper.have_same_date(
#             utility_date_helper.convert_string_to_datetime_object(
#                 new_user["end_date"]
#             ),
#             existing_user.end_date,
#         ):
#             updates["existing_end_date"] = existing_user.end_date
#             is_to_update = True

#         new_user.update(updates)
#         return is_to_update

#     def post(self, request, *args, **kwargs):
#         """
#         get user list from frontend (read from csv)
#         {
#             email: string;
#             full_name: string;
#             start_date:string; (datetime format)
#             end_date:string; (datetime format)
#             role:string;
#         }[]

#         return:
#         {
#             users_to_create: [],
#             users_to_update: [{
#                 full_name:Yi Chong,
#                 existing_full_name: None,
#                 ...
#             }],
#             users_to_delete:[],
#             users_unchanged:[]
#         }
#         """
#         dry_run = self.request.query_params.get("dry_run", True)
#         new_user_list = request.data

#         users_to_create = []
#         users_to_update = []
#         users_to_delete = []
#         users_unchanged = []

#         existing_users = User.objects.all()
#         existing_user_dict = {
#             user.email: user for user in existing_users
#         }  # email as dictionary key for existing

#         new_email_set = set(
#             [user["email"] for user in new_user_list]
#         )  # new user email set

#         if len(new_email_set) != len(new_user_list):
#             return Response(
#                 "Duplicated email found in the file",
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         for new_user in new_user_list:
#             if new_user["email"] in existing_user_dict:
#                 existing_user = existing_user_dict[new_user["email"]]
#                 if self.check_for_updates(existing_user, new_user):
#                     users_to_update.append(new_user)
#                 else:
#                     users_unchanged.append(new_user)
#             else:
#                 users_to_create.append(new_user)

#         existing_email_set = set(existing_user_dict.keys())
#         delete_email_set = existing_email_set - new_email_set
#         users_to_delete = self.serializer_class(
#             [existing_user_dict[email] for email in delete_email_set], many=True
#         ).data

#         if dry_run == "false" or dry_run == False:
#             try:
#                 with transaction.atomic():
#                     # call add, update, delete
#                     create_res = self.create_user(users_to_create)
#                     # print("create_res: ", create_res.status_code)
#                     update_res = self.update_user(users_to_update)
#                     # print("update_res: ", update_res.status_code)
#                     delete_res = self.delete_user(users_to_delete)
#                     # print("delete_res: ", delete_res.status_code)

#                     if not (
#                         create_res.status_code == 201
#                         and update_res.status_code == 200
#                         and delete_res.status_code == 204
#                     ):
#                         return Response(
#                             data="something wrong with create/update/delete",
#                             status=status.HTTP_400_BAD_REQUEST,
#                         )

#             except Exception as e:
#                 print("Errors: ", e)
#                 return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

#             return Response(
#                 data={"body": "Mass create/update/delete user succeed."},
#                 status=status.HTTP_200_OK,
#             )

#         return Response(
#             data={
#                 "users_to_create": users_to_create,
#                 "users_to_update": users_to_update,
#                 "users_to_delete": users_to_delete,
#                 "users_unchanged": users_unchanged,
#             },
#             status=status.HTTP_200_OK,
#         )
