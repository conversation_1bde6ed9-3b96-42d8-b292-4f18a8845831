import stripe
from app.core.payment.models import SubscriptionPlan
from app.utility.models import SUBSCRIPTION_PLANS

from django.core.management.base import BaseCommand

from backend.settings import STRIPE_SECRET_KEY
import stripe


class Command(BaseCommand):
    help = "Get all prices from stripe and store them in DB"

    def handle(self, *args, **options):
        stripe_obj = stripe
        stripe_obj.api_key = STRIPE_SECRET_KEY
        for plan in SUBSCRIPTION_PLANS:
            name = plan.pop("name")
            product = stripe_obj.Product.search(
                query='name:"{}" AND active:"true"'.format(name)
            ).data
            if len(product) == 0:
                product = stripe_obj.Product.create(name=name)
            else:
                product = product[0]
            price = stripe_obj.Price.search(
                query='product:"{}" AND active:"true"'.format(product["id"])
            ).data
            if len(price) == 0:
                price = stripe_obj.Price.create(product=product["id"], **plan)
            else:
                price = price[0]
            plan = SubscriptionPlan.objects.get_or_create(
                stripe_price_id=price["id"],
                subscription_fee=price["unit_amount"] / 100,
                name=name,
                defaults={
                    "name": name,
                    "subscription_fee": price["unit_amount"] / 100,
                },
            )
