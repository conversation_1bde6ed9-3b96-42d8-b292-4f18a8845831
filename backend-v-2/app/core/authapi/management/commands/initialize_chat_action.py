from django.core.management.base import BaseCommand

import app.chats.chat_action.helper as chat_action_helper

from tenant_schemas.utils import get_tenant_model


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Get all tenants.
        all_tenants = get_tenant_model().objects.all()
        for tenant in all_tenants:
            if tenant.schema_name == "public":
                continue

            chat_action_helper.initialize_chat_action(tenant.schema_name)
