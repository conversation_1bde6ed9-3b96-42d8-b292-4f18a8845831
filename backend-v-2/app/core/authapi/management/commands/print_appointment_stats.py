from django.core.management.base import BaseCommand
import app.chats.chat_action.appointment_helper as chat_action_appointment_helper
from datetime import datetime


class Command(BaseCommand):
    help = "Print appointment stats for given schemas within a date range"

    # schema_name_list = [
    #     "novafurnishing",
    #     "sofa_colony",
    #     "rozel_furnishing",
    #     "kawah_furnishing",
    #     "xclusive_home",
    #     "musterring",
    # ]

    def add_arguments(self, parser):
        # Adding an argument for schema names, which accepts multiple values
        parser.add_argument(
            "--schema_name_list", type=str, help="Comma-separated list of schema names"
        )

        # Adding arguments for start and end dates
        parser.add_argument(
            "--start_date", type=self.parse_date, help="Start date (YYYY-MM-DD)"
        )
        parser.add_argument(
            "--end_date", type=self.parse_date, help="End date (YYYY-MM-DD)"
        )

    def handle(self, *args, **options):
        # Extracting the arguments
        schema_name_list = options["schema_name_list"].split(",")
        start_date = options["start_date"]
        end_date = options["end_date"]

        # Call the function with the provided arguments
        chat_action_appointment_helper.print_appointment_stats(
            schema_name_list, start_date, end_date
        )

    def parse_date(self, date_str):
        # Helper method to parse date strings into datetime objects
        try:
            return datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError(
                f"Unable to parse date: {date_str}. Ensure the date is in YYYY-MM-DD format."
            )
