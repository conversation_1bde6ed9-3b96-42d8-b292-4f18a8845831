from django.core.management.base import BaseCommand

from tenant_schemas.utils import get_tenant_model
from django.db import connections, router
from django.contrib.auth import get_user_model

import app.documents.document.helper as document_helper


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Get all tenants.
        all_tenants = get_tenant_model().objects.all()
        User = get_user_model()
        using = router.db_for_read(User)
        connection = connections[using]

        for tenant in all_tenants:
            # Set the current tenant.
            connection.set_tenant(tenant)

            saved_schema = (
                tenant.schema_name
            )  # Get schema name directly from the tenant object.
            if saved_schema == "public":
                continue

            document_helper.call_update_doc_search(saved_schema)

        # Don't forget to close the connection at the end.
        connection.close()
