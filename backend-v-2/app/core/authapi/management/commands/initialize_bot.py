from django.core.management.base import BaseCommand
from tenant_schemas.utils import get_tenant_model


import app.chats.bot.helper as bot_helper


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Get all tenants.
        all_tenants = get_tenant_model().objects.all()
        for tenant in all_tenants:
            if tenant.schema_name == "public":
                continue

            bot_helper.initialize_bot_setting(tenant.schema_name)
