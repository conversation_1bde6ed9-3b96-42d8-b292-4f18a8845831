from django.core.management.base import BaseCommand

# from topic.wc_helper import update_conversation_topics
# from topic.constant import (
#     persist_directory_word_cloud,
# )


class Command(BaseCommand):
    help = "Update Conversation instance related topics"

    def add_arguments(self, parser):
        parser.add_argument(
            "--initialize",
            action="store_true",
            help="whether to re-initialize the word cloud chroma db.",
        )

    def handle(self, *args, **options):
        is_initialize = options.get("initialize")
        # update_conversation_topics(persist_directory_word_cloud, is_initialize)
