from django.urls import include, path
from app.core.authapi.views import (
    ChangePasswordView,
    CustomTokenObtainPairView,
    UserView,
)

urlpatterns = [
    path("", include("djoser.urls")),
    path("", include("djoser.urls.jwt")),
    path("auth/", include("djoser.urls.authtoken")),
    path("jwt/login/", CustomTokenObtainPairView.as_view()),
    path("jwt/password/change/<int:pk>/", ChangePasswordView.as_view()),
    path("users/list", UserView.List.as_view()),
    path("users/list-all", UserView.ListAll.as_view()),
    path("users/export-full-list", UserView.ExportFullList.as_view()),
    path("users/create", UserView.Create.as_view()),
    # path("users/custom-bulk-create", UserView.CustomUserBulkCreate.as_view()),
    path("user/<int:pk>/update", UserView.Update.as_view()),
    path("user/<int:pk>/delete", UserView.Delete.as_view()),
]
