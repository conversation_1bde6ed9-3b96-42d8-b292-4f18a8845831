import json
import logging
import os
import re
import time
from datetime import datetime, timedelta

import gspread
import pandas as pd
import requests  # pip install requests
from bs4 import BeautifulSoup  # pip install beautifulsoup4
from google.oauth2.service_account import Credentials
from pytz import timezone
from tenant_schemas.utils import schema_context

from app.chats.customer.models import Customer

logger = logging.getLogger(__name__)

NOVA_GLOBE3_LOGIN_URL = (
    "https://g3alb.globe3cloud.com/masterlogin/cloud_login_submit_v5.cfm"
)
COMPANY_ID = "NOVA"
USER_ID = "KEN AL"
USER_PASSWORD = os.environ.get("NOVA_SERVICING_GLOBE_3_USER_PASSWORD")
NOVA_SERVICING_GSHEET_SERVICE_ACCOUNT_KEY_JSON = os.environ.get(
    "NOVA_SERVICING_GSHEET_SERVICE_ACCOUNT_KEY_JSON"
)
NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL = os.environ.get(
    "NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL"
)
try:
    NOVA_SERVICING_GLOBE3_REFRESH_DAY_RANGE = int(
        os.environ.get("NOVA_SERVICING_GLOBE3_REFRESH_DAY_RANGE", "180")
    )
except:
    NOVA_SERVICING_GLOBE3_REFRESH_DAY_RANGE = 180


def get_from_and_to_date_str():
    # 6 months from today to today, dynamically using time.now() with asia/singapore timezone
    sg = timezone("Asia/Singapore")
    today = datetime.now(sg)
    from_date = today - timedelta(days=NOVA_SERVICING_GLOBE3_REFRESH_DAY_RANGE)
    to_date = today

    from_date_str = from_date.strftime("%Y-%m-%d")
    to_date_str = to_date.strftime("%Y-%m-%d")
    return from_date_str, to_date_str


def extract_url(text):
    pattern = r'url=\("([^"]+)"\)'
    match = re.search(pattern, text)
    if match:
        url = match.group(1)
        return url
    raise Exception("No matching URL found.")


def get_globe3_report_columns_and_data(from_date_str: str, to_date_str: str):
    if not USER_PASSWORD:
        logger.error("globe 3 password is not set in environment variables.")
        return

    form_data = {
        "companyloginid": COMPANY_ID,
        "userloginid": USER_ID,
        "formpassword": USER_PASSWORD,
        "pgdb_datasource": "pos5h_novaf9215807live",
        "customerfolder": "v50stringg3new",
        "cookiemfn": "novafurni27489mfn",
        "account_id": "novafurni9215807live",
    }
    # other values can be obtained from the login form in "https://login.globe3cloud.com/?gid=novafurni9215807live"
    # but we will save 1 request by hardcording directly.
    # We may have to update these hardcoded values in future if the values change.

    s = requests.Session()

    # set additional cookie that is pre-set in web application
    s.cookies.set("COOKMULTIPURPOSE", "ORnnn", domain="g3alb.globe3cloud.com")
    response = s.post(NOVA_GLOBE3_LOGIN_URL, data=form_data)
    assert response.status_code == 200, response.status_code

    redirect_url = extract_url(response.text)
    s.get(redirect_url)  # sets remaining site cookies for g3alb.globe3cloud.com

    tnxo_cookspecialnum = s.cookies.get("TNXO_COOKSPECIALNUM")
    cfsqlfilename = s.cookies.get("COOKSQLFILENAME")
    cookmfnunique = s.cookies.get("COOKMFNUNIQUE")
    cookcfnunique_dict = {
        "p22070702270333526": "Furnzone",
        "p21072207061389064": "Kawah",
        "p21072204175254054": "Mega",
        "p21060912082024804": "Nova Furnishing",
        "p21072206182039888": "Rozel",
        "p22070702260688329": "Sofaland",
        "p21072205344478142": "Xclusive",
    }

    begin_time = time.time()
    master_data_list = []
    master_column_list = []
    for cookcfnunique, org_name in cookcfnunique_dict.items():
        # cookcfnunique = s.cookies.get("COOKCFNUNIQUE")

        # Because report data is pulled from e11.globe3cloud instead of g3alb.globe3cloud,
        # we have to clone cookies from the g3alb. to e11.
        # (cookies are domain specific)
        new_cookie_jar = requests.cookies.RequestsCookieJar()
        for cookie in s.cookies:
            if cookie.domain.startswith("g3alb."):
                if cookie.name == "COOKCFNUNIQUE":
                    new_cookie_jar.set(
                        cookie.name,
                        cookcfnunique,
                        domain="e11.globe3cloud.com",  # overwrite cookie domain
                        path=cookie.path,
                        secure=cookie.secure,
                        rest={"HttpOnly": cookie.has_nonstandard_attr("HttpOnly")},
                        expires=cookie.expires,
                    )
                else:
                    new_cookie_jar.set(
                        cookie.name,
                        cookie.value,
                        domain="e11.globe3cloud.com",  # overwrite cookie domain
                        path=cookie.path,
                        secure=cookie.secure,
                        rest={"HttpOnly": cookie.has_nonstandard_attr("HttpOnly")},
                        expires=cookie.expires,
                    )
        s.cookies.update(new_cookie_jar)  # update session cookies

        report_type = (
            "fr_so_item_del"  # can be other reports like "fr_do_schedule_driver_info"
        )
        report_url = f"https://e11.globe3cloud.com/v50foldersetadmin/v50stringg3new/v50master/contentadmin/{report_type}.cfm"

        params = {
            "fromreport": report_type,
            "divert_tnxo_cookspecialnum": tnxo_cookspecialnum,
            "divert_cfsqlfilename": cfsqlfilename,
            "divert_cookmfnunique": cookmfnunique,
            "divert_cookcfnunique": cookcfnunique,
        }
        fromyear, frommth, fromday = from_date_str.split("-")  # yyyy-mm-dd
        toyear, tomth, today = to_date_str.split("-")  # yyyy-mm-dd
        form_data = {
            # "str_filter": "n,y,y,n,n,n,n,n,n,n,n,n,n,n,n,n,n,y,y,y,n,n,n,y,y,y,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n",
            "str_filter": "n,y,y,n,n,n,n,n,n,n,n,n,n,n,n,n,n,y,y,y,n,n,n,n,n,n,y,y,y,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n",
            # "str_filter2": "30,70,90,90,90,90,350,90,90,150,90,90,100,150,200,90,120,150,90,90,90,90,90,90,180,150,150,100,100,70,0,0,0,0,0,0,0,0,0,0,0,70,30,90,90,90,70,70,40,100,90,70,70,0,70,0,90,90,90,150,150,70,70,40",
            "str_filter2": "30,70,90,90,90,90,350,90,90,150,90,90,100,150,200,90,120,150,90,90,90,150,90,150,90,150,90,180,150,150,100,100,70,0,0,0,0,0,0,0,0,0,0,0,70,30,90,90,90,70,70,40,100,90,70,70,0,70,0,90,90,90,150,150,70,70,40",
            "fromday": fromday,
            "frommth": frommth,
            "fromyear": fromyear,
            "today": today,
            "tomth": tomth,
            "toyear": toyear,
            "rept_rec_code": "all",
            "sort_order_code": "stkcode",
        }
        time_count = time.time()
        print(f"Posting Generate report request for {org_name}...")
        response = s.post(report_url, params=params, data=form_data)
        print(
            f"Finish first post request in {time.time()-time_count : .2f} seconds. Status code: {response.status_code}"
        )

        # use bs4 to extract data from HTML
        print(f"Extracting Tables for {org_name}...")
        time_count = time.time()
        soup = BeautifulSoup(response.text, "html.parser")
        header_row = soup.find("table", id="rowheader")
        columns = [
            col.get_text(strip=True) for col in header_row.find_all(["td", "th"])
        ]

        rows = soup.find_all("table", id=re.compile(r"^row\d+"))
        print(f"Table extraction complete in {time.time()-time_count : .2f} seconds!")
        data = [
            [col.get_text(strip=True) for col in row.find_all("td")] for row in rows
        ]
        data = data[
            :-2
        ]  # drop last two rows (2nd last is empty, last row is aggregate total 'Qty' and total 'Bal Qnty')

        # Extend to Master data
        master_data_list.extend(data)
        master_column_list = columns

    print(
        f"Total time taken to extract all tables: {time.time() - begin_time:.2f} seconds!",
    )
    return master_data_list, master_column_list


def update_gsheet(data, columns):

    if not NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL:
        logger.error("No Nova Servicing Globe3 Google Sheet URL found!")
        return

    if not NOVA_SERVICING_GSHEET_SERVICE_ACCOUNT_KEY_JSON:
        logger.error("No Nova Servicing Gsheet Service Account JSON found!")
        return

    if len(data) <= 1000:
        logger.error("Data is too small to update to Google Sheet. Skipping...")
        return

    nova_servicing_gsheet_service_account_key_json = json.loads(
        NOVA_SERVICING_GSHEET_SERVICE_ACCOUNT_KEY_JSON
    )

    df = pd.DataFrame(data, columns=columns)
    print("Posting to Google Sheet...")
    time_count = time.time()

    # copy the entire thing to google sheet
    class GoogleSheetHelper:

        def __init__(
            self, nova_servicing_gsheet_service_account_key_json, spreadsheet_url
        ):
            self.spreadsheet_url = spreadsheet_url
            # Dictionary containing your service account credentials

            # Scope for Google Sheets API
            scope = [
                "https://spreadsheets.google.com/feeds",
                "https://www.googleapis.com/auth/spreadsheets",
                "https://www.googleapis.com/auth/drive.file",
                "https://www.googleapis.com/auth/drive",
            ]

            # Authenticate using the credentials dictionary
            creds = Credentials.from_service_account_info(
                nova_servicing_gsheet_service_account_key_json, scopes=scope
            )
            client = gspread.authorize(creds)

            # Open the Google Sheet by URL
            self.spreadsheet = client.open_by_url(self.spreadsheet_url)
            self.worksheet = self.spreadsheet.get_worksheet(0)
            self.last_index = len(self.worksheet.get_all_values())

        # Remove the smallest date in df onwards. Then append the new data in the df to the google sheet
        def update_sheet(self, df: pd.DataFrame):
            # Sort by SO Date
            df["SO Date"] = pd.to_datetime(df["SO Date"], format="%d-%m-%Y")
            df = df.sort_values("SO Date")
            df["SO Date"] = df["SO Date"].dt.strftime("%d-%m-%Y")

            # Smallest Date String
            smallest_date = df["SO Date"].iloc[0]

            # Find the first row number of the smallest date in the Google Sheet
            cell_list = self.worksheet.findall(smallest_date)
            if cell_list:
                first_row = cell_list[0].row
                start_update_time = time.time()
                print("Start to delete and append rows...")
                # # Remove the rows from the first row onwards
                # self.worksheet.delete_rows(first_row, self.worksheet.row_count)

                # # Append new DataFrame into the existing Google Sheet
                # self.worksheet.append_rows(df.values.tolist())

                # Prepare batch update request
                batch_update_request = {
                    "requests": [
                        {
                            "deleteDimension": {
                                "range": {
                                    "sheetId": self.worksheet.id,
                                    "dimension": "ROWS",
                                    "startIndex": first_row
                                    - 1,  # Adjust for 0-based index
                                    "endIndex": self.worksheet.row_count,  # Delete all rows from first_row onwards
                                }
                            }
                        },
                        {
                            "appendCells": {
                                "rows": [
                                    {
                                        "values": [
                                            (
                                                {
                                                    "userEnteredValue": {
                                                        "stringValue": str(value)
                                                    }
                                                }
                                                if isinstance(value, str)
                                                else {
                                                    "userEnteredValue": {
                                                        "numberValue": value
                                                    }
                                                }
                                            )
                                            for value in row
                                        ]
                                    }
                                    for row in df.values.tolist()
                                ],
                                "fields": "*",
                                "sheetId": self.worksheet.id,
                            }
                        },
                    ]
                }

                # Execute batch update request
                self.spreadsheet.batch_update(batch_update_request)

                print(
                    f"Finish deleting and appending rows in {time.time() - start_update_time:.2f} seconds!",
                )
            else:
                logger.error(
                    f"Globe3 update Error - Smallest Date {smallest_date} not found in Google Sheet!"
                )

    nova_globe_3_google_sheet_helper_instance = GoogleSheetHelper(
        nova_servicing_gsheet_service_account_key_json,
        NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL,
    )
    nova_globe_3_google_sheet_helper_instance.update_sheet(df)
    print(f"Finish posting to Google Sheet in {time.time()-time_count : .2f} seconds!")


def extract_so_number(so_number):
    extracted_so_number = str(so_number).strip().upper()
    # Replace out any non-alphanumeric characters
    extracted_so_number = re.sub(r"[^a-zA-Z0-9]", "", extracted_so_number)
    # Grab the longest series of number groups can be found
    matches = re.findall(r"\d+", extracted_so_number)
    if matches:
        return max(matches, key=len)
    return ""


def extract_contact_number(contact_number):
    return contact_number.replace(" ", "").lstrip("+65")


def update_database(data, columns, from_date_str):
    df = pd.DataFrame(data, columns=columns)

    df = df.rename(
        columns={"SO Number": "Full SO Number", "Contact Number": "Full Contact Number"}
    )
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df["SO Number"] = df["Full SO Number"].apply(extract_so_number)
    df["Contact Number"] = df["Full Contact Number"].apply(extract_contact_number)
    # delete customer objects
    with schema_context("nova_servicing"):
        old_customers = Customer.objects.filter(so_date__gte=from_date_str)
        old_customers.delete()

        # SO Date	SO Number	Party Name	Ship To Address	Attention	Contact Number	Stock Code	Stock Description	Stock Remarks

        # Map the columns to the correct field
        df = df.rename(
            columns={
                "SO Date": "so_date",
                "SO Number": "so_number",
                "Full SO Number": "full_so_number",
                "Full Contact Number": "full_contact_number",
                "Party Name": "party_name",
                "Ship To Address": "ship_to_address",
                "Attention": "attention",
                "Contact Number": "contact_number",
                "Stock Code": "stock_code",
                "Stock Description": "stock_description",
                "Stock Remarks": "stock_remarks",
            }
        )
        df["so_date"] = pd.to_datetime(df["so_date"], format="%d-%m-%Y")
        df_data = df.to_dict(orient="records")
        customer_instances = [Customer(**data) for data in df_data]
        Customer.objects.bulk_create(customer_instances)

        # try:

        #     customers = []
        #     for row in rows:
        #         customer = {}
        #         for col, value in zip(columns.keys(), row):
        #             customer[columns[col]] = value
        #         customers.append(Customer(**customer))
        #     Customer.objects.bulk_create(customers)
        # except Exception as e:
        #     logger.error(f"Error updating customer {row[0]}: {e}")
