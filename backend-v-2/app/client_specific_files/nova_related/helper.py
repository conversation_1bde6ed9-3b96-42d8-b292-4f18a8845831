import json
import os
import re
from io import String<PERSON>
from django.db import transaction

import boto3
import gspread
import pandas as pd
from google.oauth2.service_account import Credentials
from app.chats.customer.models import Customer
from tenant_schemas.utils import schema_context
from datetime import datetime

import app.client_specific_files.nova_related.constant as nova_related_constant

nova_servicing_gsheet_service_account_key_json = os.environ.get(
    "NOVA_SERVICING_GSHEET_SERVICE_ACCOUNT_KEY_JSON"
)
NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL = os.environ.get(
    "NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL"
)


# PROCESS CSV
def process_csv(file_content):
    # Read CSV into DataFrame
    # 1. Delete first two and last two rows
    df = pd.read_csv(
        StringIO(file_content), skiprows=2, skipfooter=2, engine="python"
    ).reset_index(drop=True)

    # 2. Filter out rows without Staff Name
    df = df[df["Staff Name"].notna()]

    # 3. Check for Request Date == 1/1/8888
    # Convert 'Request Date' to datetime, coercing errors
    df["Request Date"] = pd.to_datetime(df["Request Date"], errors="coerce")

    # Replace '8888-01-01' with 'Not planned yet'
    df["Request Date"] = df["Request Date"].apply(
        lambda x: (
            x.strftime("%Y-%m-%d")
            if not pd.isnull(x) and x != pd.Timestamp("8888-01-01")
            else "Not planned yet"
        )
    )

    # 4. Filter out text with "REFER" in the Stock Description
    df = df[~df["Stock Description"].str.contains("REFER", na=False)]

    # 5. Filter rows where both 'D/O Date' and 'D/O Number' are blank
    df = df[(df["D/O Date"].isna() & df["D/O Number"].isna())]

    return df


def list_and_process_csv_files():
    bucket_name = os.environ.get("AWS_S3_NOVA_RESOURCES_STORAGE_BUCKET_NAME")
    environment = os.environ.get("ENVIRONMENT")

    if environment in ["Production", "Staging", "Test"]:
        aws_access_key_id = os.environ.get("AWS_ACCESS_KEY_ID")
        aws_secret_access_key = os.environ.get("AWS_SECRET_ACCESS_KEY")
    else:
        aws_access_key_id = os.environ.get("AWS_ACCESS_KEY_ID_UU")
        aws_secret_access_key = os.environ.get("AWS_SECRET_ACCESS_KEY_UU")

    s3 = boto3.client(
        "s3",
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
    )
    try:
        files = s3.list_objects_v2(Bucket=bucket_name)["Contents"]
        csv_files = [file["Key"] for file in files if file["Key"].endswith(".csv")]

        # Ensure the resources directory exists
        os.makedirs(nova_related_constant.CSV_FOLDER_PATH, exist_ok=True)

        for file_key in csv_files:
            obj = s3.get_object(Bucket=bucket_name, Key=file_key)
            file_content = obj["Body"].read().decode("utf-8")
            processed_df = process_csv(file_content)

            # Save the processed DataFrame as CSV
            output_file_path = os.path.join(
                nova_related_constant.CSV_FOLDER_PATH,
                "processed_" + os.path.basename(file_key),
            )
            print(f"{output_file_path=}")
            processed_df.to_csv(output_file_path, index=False)
            print(f"Processed file saved: {output_file_path}")
    except Exception as e:
        print(f"An error occurred when trying to update csv files: {e}")


# USE CSV


def get_delivery_csv_file_path(page_name: str, schema_name: str):
    if schema_name == "novafurnishing":
        shop_csv_path = ""
        if "Nova Furnishing" in page_name:
            shop_csv_path = nova_related_constant.NOVA_FURNISHING_CSV_PATH
        if "Rozel Furnishing" in page_name:
            shop_csv_path = nova_related_constant.ROZEL_FURNISHING_CSV_PATH
        if "X'Clusive Home" in page_name:
            shop_csv_path = nova_related_constant.XCLUSIVE_HOME_CSV_PATH
        if "Kawah Furnishing" in page_name:
            shop_csv_path = nova_related_constant.KAWAH_FURNISHING_CSV_PATH
        if "Mega Home" in page_name:
            shop_csv_path = nova_related_constant.MEGA_HOME_FURNISHING_CSV_PATH

        if shop_csv_path:
            relative_path = os.path.join(
                nova_related_constant.CSV_FOLDER_PATH, shop_csv_path
            )
            absolute_path = os.path.abspath(relative_path)

            # Check if the file exists
            if os.path.exists(absolute_path):
                return absolute_path
    return ""
    ## Not sure
    # if "Sofa Colony" in page_name:
    #     return nova_related_constant.SOFALAND_CSV_PATH
    # if "Musterring" in page_name:
    #     return nova_related_constant.FURNZONE_CREATIONS_CSV_PATH


def normalize_contact(contact):
    """Normalize contact numbers to a consistent format."""
    contact = str(contact).strip()
    # Remove any country code (assuming +65 for Singapore)
    if contact.startswith("+65"):
        contact = contact[3:]
    elif contact.startswith("65") and len(contact) > 8:
        contact = contact[2:]
    # Remove any non-numeric characters
    contact = "".join(filter(str.isdigit, contact))
    return contact


def extract_postal_code(address):
    # Extract the postal code from the address
    # The pattern looks for a series of 5 to 7 digits, preferably towards the end of the address
    matches = re.findall(r"\b\d{5,7}\b", address)
    return matches[-1] if matches else ""


def get_nova_delivery_info_description(
    contact, shipping_address_postal_code, csv_file_path=""
):
    """Find delivery info by contact number."""
    # Read the CSV file
    try:
        df = pd.read_csv(csv_file_path)
    except:
        print(f"Reading csv file path: {csv_file_path} failed...")
        return []
    # Normalize the contact numbers in the DataFrame
    df["Normalized Contact"] = df["Contact Number"].apply(normalize_contact)

    # Extract and add postal code to the DataFrame
    df["Extracted Postal Code"] = df["Ship To Address"].apply(extract_postal_code)
    # Normalize the input contact
    normalized_contact = normalize_contact(contact)

    # Filter the DataFrame by both contact and postal code
    filtered_df = df[
        (df["Normalized Contact"] == normalized_contact)
        & (df["Extracted Postal Code"] == shipping_address_postal_code)
    ]

    # If empty:
    if filtered_df.empty:
        return []

    # Group by 'Ship To Address' and 'Request Date'
    grouped = filtered_df.groupby(["Ship To Address", "Request Date"])

    # Convert each group into a description
    descriptions = []
    for (address, date), group in grouped:
        items_descriptions = [
            f"-{row['Stock Description']} x{row['Qty']})" for _, row in group.iterrows()
        ]
        items_str = "\n".join(items_descriptions)
        description = (
            f"Ship To Address: {address}\nRequest Date: {date}\nItems: {items_str}."
        )
        descriptions.append(description)

    return descriptions


class GoogleSheetHelper:
    def __init__(self, spreadsheet_url, is_globe_3=False):
        self.spreadsheet_url = spreadsheet_url
        # Dictionary containing your service account credentials
        credentials_dict = json.loads(nova_servicing_gsheet_service_account_key_json)

        # Scope for Google Sheets API
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/spreadsheets",
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/drive",
        ]

        # Authenticate using the credentials dictionary
        creds = Credentials.from_service_account_info(credentials_dict, scopes=scope)
        client = gspread.authorize(creds)

        # Open the Google Sheet by URL
        spreadsheet = client.open_by_url(self.spreadsheet_url)
        self.worksheet = spreadsheet.get_worksheet(0)
        self.last_index = len(self.worksheet.get_all_values())
        self.lookup_dict = self.create_customer_lookup_dict()
        # # Print out last 10 lookup_dict
        # print(
        #     "lookup dict",
        #     json.dumps(list(self.lookup_dict.items())[-10:], indent=4),
        # )

    def get_opened_spreadsheet(self):
        return self.client.open_by_url(self.spreadsheet_url)

    def get_worksheet(self, spreadsheet):
        return spreadsheet.get_worksheet(0)

    # ----------------------------------- Nova Servicing Google Sheet -----------------------------------
    def append_new_row(self, new_row_values):
        self.worksheet.append_row(new_row_values)
        print("New row added successfully.")

    def get_so_number_count(self, so_number):
        counter = 1
        records = self.worksheet.get_all_records()
        for record in records:
            existed_so = record["SO number"]
            if str(so_number).upper() == str(existed_so).upper():
                counter += 1
        return counter

    # ----------------------------------- GLOBE 3 -----------------------------------
    # Function to create a lookup dictionary in globe3
    def create_customer_lookup_dict(self):
        records = self.worksheet.get_all_records()
        lookup_dict = {}

        for record in records:
            full_so_number = str(record["SO Number"]).strip().upper()
            extracted_so_number = str(record["SO Number"]).strip().upper()
            # Replace out any non-alphanumeric characters
            extracted_so_number = re.sub(r"[^a-zA-Z0-9]", "", extracted_so_number)
            # Grab the longest series of number groups can be found
            matches = re.findall(r"\d+", extracted_so_number)
            if matches:
                extracted_so_number = max(matches, key=len)
            contact_number = (
                str(record["Contact Number"]).replace(" ", "").lstrip("+65")
            )

            if lookup_dict.get((extracted_so_number, contact_number)):
                lookup_dict[(extracted_so_number, contact_number)][
                    "Stock Description"
                ] += f',{record["Stock Description"]}'
                lookup_dict[(extracted_so_number, contact_number)][
                    "Stock Code"
                ] += f',{record["Stock Code"]}'
            else:
                lookup_dict[(extracted_so_number, contact_number)] = record
                lookup_dict[(extracted_so_number, contact_number)][
                    "Stock Description"
                ] = str(
                    lookup_dict[(extracted_so_number, contact_number)][
                        "Stock Description"
                    ]
                )
                lookup_dict[(extracted_so_number, contact_number)]["Stock Code"] = str(
                    lookup_dict[(extracted_so_number, contact_number)]["Stock Code"]
                )
                lookup_dict[(extracted_so_number, contact_number)][
                    "Full SO Number"
                ] = full_so_number

        return lookup_dict

    # Function to check SO number and Contact number using the lookup dictionary in globe3
    def get_customer_info(self, so_number, contact_number):
        # extract only the number from so_number

        # Clean up "-"
        so_number = re.sub(r"-", "", so_number)
        # Convert inputs to match the longest number
        matches = re.findall(r"\d+", so_number)
        if matches:
            so_number = max(matches, key=len)

        contact_number = contact_number.replace(" ", "").lstrip("+65")
        for key in self.lookup_dict.keys():
            if so_number in key[0] and contact_number == key[1]:
                so_number = key[0]
                break

        return self.lookup_dict.get((so_number, contact_number), {})


def sync_google_sheet():
    with schema_context("nova_servicing"):

        all_customers = Customer.objects.all()
        all_customers.delete()
        google_sheet_helper = GoogleSheetHelper(
            spreadsheet_url=NOVA_SERVICING_GLOBE3_GOOGLE_SHEET_URL
        )
        records = google_sheet_helper.worksheet.get_all_records()
        print(f"found {len(records)} records")

        # Check if the date is in the correct format
        for record in records:
            record["Full SO Number"] = ""
            record["Full Contact Number"] = ""
            if record["SO Number"]:
                record["Full SO Number"] = record["SO Number"]
                extracted_so_number = str(record["SO Number"]).strip().upper()
                # Replace out any non-alphanumeric characters
                extracted_so_number = re.sub(r"[^a-zA-Z0-9]", "", extracted_so_number)
                # Grab the longest series of number groups can be found
                matches = re.findall(r"\d+", extracted_so_number)
                if matches:
                    extracted_so_number = max(matches, key=len)
                record["SO Number"] = extracted_so_number
            if record["Contact Number"]:
                record["Full Contact Number"] = str(record["Contact Number"])
                record["Contact Number"] = (
                    str(record["Contact Number"]).replace(" ", "").lstrip("+65")
                )
            if not record["SO Date"]:
                continue
            try:
                record["SO Date"] = datetime.strptime(record["SO Date"], "%d-%m-%Y")
            except ValueError:
                raise ValueError(
                    f"Invalid date format for SO Date: {record['SO Date']}"
                )

        print("processed data, dumping to database")
        batch_size = 10000  # Adjust batch size based on performance testing
        for i in range(0, len(records), batch_size):
            batch = [
                Customer(
                    so_date=record["SO Date"],
                    so_number=record["SO Number"],
                    full_so_number=record["Full SO Number"],
                    full_contact_number=record["Full Contact Number"],
                    party_name=record["Party Name"],
                    ship_to_address=record["Ship To Address"],
                    attention=record["Attention"],
                    contact_number=record["Contact Number"],
                    stock_code=record["Stock Code"],
                    stock_description=record["Stock Description"],
                    stock_remarks=record["Stock Remarks"],
                )
                for record in records[i : i + batch_size]
            ]
            with transaction.atomic():
                Customer.objects.bulk_create(batch)
